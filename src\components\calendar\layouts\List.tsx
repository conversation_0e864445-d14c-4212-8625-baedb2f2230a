"use client";

import { useTranslations } from "next-intl";

import EventListItem from "../EventListItem";
import { Button } from "@/components/base/button";
import { useCalendarList } from "@/hooks/calendar";

export default function CalendarList() {
  const t = useTranslations("calendar");
  const { groupedEvents, setCurrentFilter } = useCalendarList();

  return (
    <div>
      <div className="mb-8 flex flex-row gap-2">
        <Button onClick={() => setCurrentFilter("all")}>{t("all-events")}</Button>
        <Button onClick={() => setCurrentFilter("upcoming")}>{t("upcoming-events")}</Button>
        <Button onClick={() => setCurrentFilter("past")}>{t("past-events")}</Button>
      </div>
      {groupedEvents.map(([day, events], index) => (
        <div key={index} className="flex flex-col">
          <div className="mb-3 text-2xl font-semibold capitalize">{day}</div>
          <div>
            {events.map((event) => (
              <EventListItem key={event.id} event={event} />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
