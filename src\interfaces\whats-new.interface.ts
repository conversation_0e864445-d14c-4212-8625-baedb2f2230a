import { z } from "zod";

// Base thumbnail image schema
export const ThumbnailImageSchema = z.object({
    url: z.string().url(),
    width: z.number().positive(),
    height: z.number().positive(),
    aspectRatio: z.number().positive().optional(),
});
export type IThumbnailImage = z.infer<typeof ThumbnailImageSchema>;

// Media item schema
export const MediaItemSchema = z.object({
    url: z.string().url(),
    pathname: z.string(),
    uploadedAt: z.string().datetime(),
});
export type IMediaItem = z.infer<typeof MediaItemSchema>;

// Media list response schema
export const MediaListResponseSchema = z.object({
    media: z.array(MediaItemSchema),
    total: z.number().optional(),
});
export type IMediaListResponse = z.infer<typeof MediaListResponseSchema>;

// Media delete response schema
export const MediaDeleteResponseSchema = z.object({
    success: z.boolean(),
    message: z.string().optional(),
});
export type IMediaDeleteResponse = z.infer<typeof MediaDeleteResponseSchema>;

// What's New Version schema
export const WhatsNewVersionSchema = z.object({
    id: z.number(),
    versionCode: z.string(),
    date: z.string().datetime(), // ISO string
    titleEn: z.string(),
    titleFr: z.string(),
    descriptionEn: z.string(),
    descriptionFr: z.string(),
    addedEn: z.array(z.string()).default([]),
    addedFr: z.array(z.string()).default([]),
    changedEn: z.array(z.string()).default([]),
    changedFr: z.array(z.string()).default([]),
    fixedEn: z.array(z.string()).default([]),
    fixedFr: z.array(z.string()).default([]),
    removedEn: z.array(z.string()).default([]),
    removedFr: z.array(z.string()).default([]),
    thumbnails: z.array(ThumbnailImageSchema).default([]),
    createdAt: z.string().datetime().optional(),
    updatedAt: z.string().datetime().optional(),
});
export type IWhatsNewVersion = z.infer<typeof WhatsNewVersionSchema>;

// Create payload schema
export const CreateWhatsNewPayloadSchema = z.object({
    versionCode: z.string().min(1, "Version code is required"),
    date: z.string().datetime(),
    titleEn: z.string().min(1, "English title is required"),
    titleFr: z.string().min(1, "French title is required"),
    descriptionEn: z.string().default(""),
    descriptionFr: z.string().default(""),
    addedEn: z.array(z.string()).default([]),
    addedFr: z.array(z.string()).default([]),
    changedEn: z.array(z.string()).default([]),
    changedFr: z.array(z.string()).default([]),
    fixedEn: z.array(z.string()).default([]),
    fixedFr: z.array(z.string()).default([]),
    removedEn: z.array(z.string()).default([]),
    removedFr: z.array(z.string()).default([]),
    thumbnails: z.array(ThumbnailImageSchema).default([]),
});
export type ICreateWhatsNewPayload = z.infer<typeof CreateWhatsNewPayloadSchema>;

// Update payload schema
export const UpdateWhatsNewPayloadSchema = CreateWhatsNewPayloadSchema.extend({
    id: z.number(),
});
export type IUpdateWhatsNewPayload = z.infer<typeof UpdateWhatsNewPayloadSchema>;

// List response schema
export const WhatsNewListResponseSchema = z.object({
    versions: z.array(WhatsNewVersionSchema),
});
export type IWhatsNewListResponse = z.infer<typeof WhatsNewListResponseSchema>;

// Reaction schema
export const WhatsNewReactionSchema = z.object({
    id: z.number(),
    emoji: z.string(),
    userId: z.string(),
    versionId: z.number(),
    createdAt: z.string().datetime(),
});
export type IWhatsNewReaction = z.infer<typeof WhatsNewReactionSchema>;

// Reaction summary schema
export const ReactionSummarySchema = z.object({
    emoji: z.string(),
    count: z.number().min(0),
    userReacted: z.boolean(),
});
export type IReactionSummary = z.infer<typeof ReactionSummarySchema>;

// Reactions response schema
export const ReactionsResponseSchema = z.object({
    reactions: z.array(ReactionSummarySchema),
});
export type IReactionsResponse = z.infer<typeof ReactionsResponseSchema>;

// Toggle reaction payload schema
export const ToggleReactionPayloadSchema = z.object({
    versionId: z.number(),
    emoji: z.string(),
});
export type IToggleReactionPayload = z.infer<typeof ToggleReactionPayloadSchema>;

// Toggle reaction response schema
export const ToggleReactionResponseSchema = z.object({
    message: z.string(),
    action: z.enum(["added", "removed"]),
});
export type IToggleReactionResponse = z.infer<typeof ToggleReactionResponseSchema>;

// Upload response schema
export const UploadResponseSchema = z.object({
    url: z.string().url(),
    success: z.boolean(),
});
export type IUploadResponse = z.infer<typeof UploadResponseSchema>;

// Localized content helper type
export interface ILocalizedWhatsNew {
    id: number;
    versionCode: string;
    date: string;
    title: string;
    description: string;
    added: string[];
    changed: string[];
    fixed: string[];
    removed: string[];
    thumbnails: IThumbnailImage[];
}

// Helper function to get localized content based on language
export function getLocalizedContent(
    version: IWhatsNewVersion,
    lang: "en" | "fr"
): ILocalizedWhatsNew {
    const fieldMap = {
        en: {
            title: "titleEn" as const,
            description: "descriptionEn" as const,
            added: "addedEn" as const,
            changed: "changedEn" as const,
            fixed: "fixedEn" as const,
            removed: "removedEn" as const,
        },
        fr: {
            title: "titleFr" as const,
            description: "descriptionFr" as const,
            added: "addedFr" as const,
            changed: "changedFr" as const,
            fixed: "fixedFr" as const,
            removed: "removedFr" as const,
        },
    };

    const fields = fieldMap[lang];

    return {
        id: version.id,
        versionCode: version.versionCode,
        date: version.date,
        title: version[fields.title],
        description: version[fields.description],
        added: version[fields.added],
        changed: version[fields.changed],
        fixed: version[fields.fixed],
        removed: version[fields.removed],
        thumbnails: version.thumbnails || [],
    };
}

// Constants
export const ALLOWED_EMOJIS = ["👍", "👎", "❤️", "🎉", "😄", "🚀", "👏", "🔥"] as const;
export type AllowedEmoji = (typeof ALLOWED_EMOJIS)[number];

// Validation helper
export function validateCreatePayload(
    payload: ICreateWhatsNewPayload
): { isValid: boolean; error?: string } {
    try {
        CreateWhatsNewPayloadSchema.parse(payload);
        return { isValid: true };
    } catch (error) {
        if (error instanceof z.ZodError) {
            return {
                isValid: false,
                error: error.errors.map((e) => `${e.path.join(".")}: ${e.message}`).join(", "),
            };
        }
        return {
            isValid: false,
            error: "Invalid payload format",
        };
    }
} 