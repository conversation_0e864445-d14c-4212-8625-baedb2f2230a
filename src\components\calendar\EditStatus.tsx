"use client";

import { <PERSON><PERSON><PERSON><PERSON>, CircleX } from "lucide-react";

import { CalendarEvent } from "@/hooks/store/calendar";

export default function CalendarEditStatus({ event }: { event: CalendarEvent }) {
  const handleConfirm = () => {
    console.log("confirm");
  };

  const handleCancel = () => {
    console.log("cancel");
  };

  return (
    event.status === "pending" && (
      <div className="flex items-center gap-2">
        <CircleX
          width={30}
          height={30}
          strokeWidth={1}
          className="cursor-pointer"
          onClick={handleCancel}
        />
        <CircleCheck
          width={30}
          height={30}
          strokeWidth={1}
          className="cursor-pointer"
          onClick={handleConfirm}
        />
      </div>
    )
  );
}
