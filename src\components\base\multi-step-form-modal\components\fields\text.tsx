import { useFormContext } from "react-hook-form";
import { Input } from "@/components/base/input";
import { Label } from "@/components/ui/label";
import { TextField as TextFieldType } from "../../types";
import { useTranslations } from "next-intl";

interface TextFieldProps {
  field: TextFieldType;
}

export const TextField = ({ field }: TextFieldProps) => {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const error = errors[field.id];
  const t = useTranslations("validation");

  const renderError = () => {
    if (!error) return null;

    if (error.type === "custom" && error.message) {
      return error.message.toString();
    }

    switch (error.type) {
      case "required":
        return t("required");
      default:
        return error.message?.toString() || t("invalid");
    }
  };

  return (
    <div className="w-full space-y-2">
      <Label htmlFor={field.id} className={error ? "text-destructive" : ""}>
        {field.label}
        {field.required && <span className="text-destructive">*</span>}
      </Label>
      {field.description && <p className="text-xs text-muted-foreground">{field.description}</p>}
      <Input
        {...register(field.id, {
          required: field.required,
          onChange: (e) => field.onChange?.(e.target.value),
        })}
        placeholder={field.placeholder}
        disabled={field.disabled}
        aria-invalid={!!error}
        className={error ? "border-destructive" : ""}
      />
      {error && <p className="text-sm font-medium text-destructive">{renderError()}</p>}
    </div>
  );
};
