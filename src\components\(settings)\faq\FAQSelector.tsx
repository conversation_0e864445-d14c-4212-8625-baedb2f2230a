"use client";

import Link from "next/link";

import Button from "@/components/base/button";
import { useFaq } from "@/hooks/use-faq";
import { cn } from "@/lib/utils";

type FAQSelectorProps = {
  isActive?: "pxs" | "pxl" | "goliaths";
};

const activeButtonStyle =
  "border-white bg-transparent text-white hover:bg-white/10 transition-all duration-200 hover:scale-105 disabled:hover:scale-100 disabled:transition-none";
const disabledButtonStyle =
  "bg-white text-black hover:bg-gray-200 transition-all duration-200 hover:scale-105 disabled:hover:scale-100 disabled:transition-none";

export default function FAQSelector({ isActive = "pxs" }: FAQSelectorProps) {
  const { access } = useFaq();

  return (
    (access.hasBothAccess || access.isParadox || access.isPXS || access.isPXL) && (
      <div className="flex items-center gap-2">
        {(access.isPXS || access.isParadox) && (
          <Button
            variant="outline"
            className={cn(activeButtonStyle, isActive === "pxs" && disabledButtonStyle)}
            asChild
            disabled={isActive === "pxs"}
          >
            <Link href="/faq/pxs">PXS</Link>
          </Button>
        )}

        {(access.isPXL || access.isParadox) && (
          <Button
            variant="outline"
            className={cn(activeButtonStyle, isActive === "pxl" && disabledButtonStyle)}
            asChild
            disabled={isActive === "pxl"}
          >
            <Link href="/faq/pxl">PXL</Link>
          </Button>
        )}

        <Button
          variant="outline"
          className={cn(activeButtonStyle, isActive === "goliaths" && disabledButtonStyle)}
          asChild
          disabled={isActive === "goliaths"}
        >
          <Link href="/faq/goliaths">Goliaths</Link>
        </Button>
      </div>
    )
  );
}
