"use client";

/**
 * A cell component for displaying a thumbnail image alongside text
 * Used for items that have both an image and a text label
 * The text is truncated if it exceeds the available space
 */

import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface ThumbTextCellProps {
  /** The text to display next to the thumbnail */
  text: string;
  /** URL of the thumbnail image */
  imageUrl: string;
  /** Optional CSS class name for styling */
  className?: string;
}

export function ThumbTextCell({ text, imageUrl, className }: ThumbTextCellProps) {
  return (
    <div className={cn("flex items-center gap-3", className)}>
      <Avatar className="h-8 w-8 rounded-md">
        <AvatarImage src={imageUrl} alt={text} />
      </Avatar>
      <span className="truncate">{text}</span>
    </div>
  );
}
