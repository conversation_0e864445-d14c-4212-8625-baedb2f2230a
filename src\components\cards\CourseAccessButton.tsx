"use client";

import Link from "next/link";
import { useTranslations } from "next-intl";
import Button from "@/components/base/button";
import { ExternalLink } from "lucide-react";

interface CourseAccessButtonProps {
  className?: string;
}

export function CourseAccessButton({ className }: CourseAccessButtonProps) {
  const t = useTranslations();

  // LearnWorlds base URL from environment or use a default
  const learnWorldsBaseUrl = process.env.NEXT_PUBLIC_LW_BASE_URL || "https://paradox-school.com";

  return (
    <div className="mt-6 flex justify-center px-6">
      <Button className={`w-full gap-2 font-medium text-white ${className}`}>
        <Link
          href={`${learnWorldsBaseUrl}/courses`}
          target="_blank"
          className="flex items-center p-5"
        >
          {t("product.modal.access-all-courses")}
          <ExternalLink className="ml-2 h-4 w-4" />
        </Link>
      </Button>
    </div>
  );
}
