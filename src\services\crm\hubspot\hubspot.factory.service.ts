import { Injectable } from '@nestjs/common';
import { SubscriptionItem } from 'chargebee-typescript/lib/resources/subscription';
import {
  Product,
  ProductPlan,
  ProductPlanPrice,
  Subscription,
  Invoice,
} from '@entities';
import {
  ChargeBeeDurationPeriodUnit,
  HubspotOrderStatus,
  SubscriptionOrderStatus,
} from '@enums';
import { HubspotInvoiceAmountFields } from '@types';
import { UpdateSubscriptionItemDTO } from '@dtos';
import { AppSecrets, ConfigService, HubspotSecrets } from '@config';

@Injectable()
export class HubspotFactoryService {
  private readonly dealPipeline: string;
  private readonly dealClosedWonStage: string;
  private readonly dealClosedLostStage: string;
  private readonly dealOwner: string;
  private readonly hubspotSecrets: HubspotSecrets;
  private readonly appSecrets: AppSecrets;

  constructor(private readonly configService: ConfigService) {
    this.hubspotSecrets = this.configService.hubspotSecrets;
    this.dealPipeline = this.hubspotSecrets.HUBSPOT_DEAL_PIPELINE;
    this.dealClosedWonStage = this.hubspotSecrets.HUBSPOT_DEAL_WON_STAGE;
    this.dealClosedLostStage = this.hubspotSecrets.HUBSPOT_DEAL_LOST_STAGE;
    this.dealOwner = this.hubspotSecrets.HUBSPOT_DEAL_DEFAULT_OWNER;
    this.appSecrets = this.configService.appSecrets;
  }

  generateDealFromSubscription(
    subscription: Subscription,
    dealName: string,
    isExistingDeal: boolean,
  ): Record<string, string> {
    const dealId = subscription?.customFields?.['cf_deal_id'];
    const dealProperties: Record<string, string> = {};

    if (!isExistingDeal) {
      dealProperties[
        'dealname'
      ] = `${dealName} - ${subscription?.metadata?.offerName}`;
      dealProperties['pipeline'] = this.dealPipeline;
      dealProperties['hubspot_owner_id'] = this.dealOwner;

      if (
        subscription?.orderStatus === SubscriptionOrderStatus.PAID ||
        subscription?.orderStatus === SubscriptionOrderStatus.ACTIVE
      ) {
        dealProperties['dealstage'] = this.dealClosedWonStage;
      }
    }

    const amountDue = subscription?.totalDues / 100;
    const amountPaid = subscription?.amountPaid / 100;
    const amountRemaining = subscription?.amountRemaining / 100;
    dealProperties['amount'] = (
      amountPaid +
      amountRemaining +
      amountDue
    )?.toString();
    dealProperties['amount_due'] = amountDue?.toString();
    dealProperties['amount_paid'] = amountPaid.toString();
    dealProperties['amount_remaining_to_be_invoiced'] =
      amountRemaining.toString();
    dealProperties['billing_entity'] = subscription.businessEntityId;
    dealProperties['code_for_bank_transfers'] = subscription.chargebeeId;
    dealProperties['date_purchase'] = new Date(subscription.createdAt)
      .toISOString()
      .slice(0, 10);
    dealProperties['deal_currency_code'] = subscription.currencyCode;
    dealProperties['df_order_id'] = subscription.chargebeeId;
    dealProperties['payment_method'] = 'credit_card';
    dealProperties['order_status'] =
      HubspotOrderStatus[subscription.orderStatus];
    dealProperties['payment_platform'] = 'Chargebee';
    dealProperties[
      'paradox_os_link'
    ] = `${this.appSecrets.PX_OS_URL}/dashboard/subscriptions/${subscription.chargebeeId}`;
    dealProperties['sale_utm_content'] =
      subscription?.metadata?.utm_content || '';
    dealProperties['sale_utm_campaign'] =
      subscription?.metadata?.utm_campaign || '';
    dealProperties['sale_utm_medium'] =
      subscription?.metadata?.utm_medium || '';
    dealProperties['sale_utm_source'] =
      subscription?.metadata?.utm_source || '';
    dealProperties['fp_promoter_id'] = subscription?.metadata?.fpr || '';
    dealProperties['closed_won_reason'] = dealId
      ? undefined
      : 'Automatic import';
    dealProperties['closedate'] = dealId
      ? undefined
      : new Date().toISOString().slice(0, 10);
    dealProperties['offer_id'] = subscription?.customFields?.cf_os_offer_id;
    dealProperties['offer_name'] = subscription?.metadata?.offerName;
    return dealProperties;
  }

  generateDealUpdatesFromSubscription(
    subscriptionUpdates: Partial<Subscription>,
  ): Record<string, string> {
    const dealUpdates: Record<string, string> = {};
    if (subscriptionUpdates?.totalDues) {
      dealUpdates['amount_due'] = (
        subscriptionUpdates?.totalDues / 100
      )?.toString();
    }

    if (subscriptionUpdates?.orderStatus) {
      dealUpdates['order_status'] =
        HubspotOrderStatus[subscriptionUpdates.orderStatus];
    }

    if (subscriptionUpdates?.orderStatus === SubscriptionOrderStatus.STOPPED) {
      dealUpdates['dealstage'] = this.dealClosedLostStage;
    }

    if (subscriptionUpdates?.orderStatus === SubscriptionOrderStatus.REFUNDED) {
      dealUpdates['dealstage'] = this.dealClosedLostStage;
      dealUpdates['refund_status'] = 'Requested & Refunded';
    }

    if (subscriptionUpdates?.amountPaid) {
      dealUpdates['amount_paid'] = (
        subscriptionUpdates.amountPaid / 100
      ).toString();
    }

    if (subscriptionUpdates?.amountRemaining >= 0) {
      dealUpdates['amount_remaining_to_be_invoiced'] = (
        subscriptionUpdates.amountRemaining / 100
      ).toString();
    }

    return dealUpdates;
  }

  /**
   * Generates updated properties for a deal from an updated invoice
   * @param updates Updates for deal
   * @returns Object with updated deal properties
   */
  generateDealUpdatesFromInvoice(
    updates: HubspotInvoiceAmountFields,
  ): Record<string, string> {
    const dealUpdates: Record<string, string> = {};

    if (updates?.amount_due) {
      dealUpdates['amount_due'] = updates.amount_due;
    }

    const amountPaid = Number(updates?.amount_paid || 0);
    const isPaid = amountPaid > 0;

    if (isPaid) {
      dealUpdates['amount_paid'] = amountPaid.toString();
    }

    if (updates?.isFirstInvoice && isPaid) {
      dealUpdates['dealstage'] = this.dealClosedWonStage;
    }

    if (updates?.invoice_amount) {
      const totalAmount = Number(updates.invoice_amount);
      dealUpdates['amount_remaining_to_be_invoiced'] = (
        totalAmount - amountPaid
      ).toString();
    }

    return dealUpdates;
  }

  generateProductFromPlan(
    plan: ProductPlan,
    price: ProductPlanPrice,
    product: Product,
    productLine?: string,
    productFamily?: string,
  ): Record<string, string> {
    const hsProduct = {
      plan_id_in_paradox_os: plan.id.toString(),
      plan_name: plan.internalName,
      product_code_px_os: product.code,
      product_internal_name: product.internalName,
      name: plan.internalName,
      description: product.description,
      plan_full_price: price.amount.toString(),
      hs_recurring_billing_period: `P${price.totalBillingCycles}M`,
      hs_price_eur: price.amountPerBillingCycle.toString(),
      hs_sku: plan.crmSku,
      hs_images: product.image,
      product_id_in_paradox_os: product.id.toString(),
      recurringbillingfrequency: 'monthly',
    };
    if (productLine && productFamily) {
      hsProduct['product_line'] = productLine;
      hsProduct['product_family'] = productFamily;
    }
    return hsProduct;
  }

  /**
   * Generates properties for Hubspot's `Invoice_px` custom object
   * @param invoice Invoice entity from DB
   * @returns An object with properties of `Invoice_px` in Hubspot.
   */
  generateInvoice(invoice: Invoice): Record<string, string> {
    return {
      id: invoice.id.toString(),
      invoice_chargebee_id: invoice.chargebeeId,
      invoice_status: invoice.status,
      price_type: invoice.priceType,
      due_date: new Date(invoice.dueDate * 1000).toISOString().slice(0, 10),
      exchange_rate: invoice.exchangeRate.toString(),
      invoice_amount: (invoice.total / 100).toString(),
      amount_paid: (invoice.amountPaid / 100).toString(),
      amount_adjusted: (invoice.amountAdjusted / 100).toString(),
      write_off_amount: (invoice.writeOffAmount / 100).toString(),
      amount_due: (invoice.amountDue / 100).toString(),
      paid_at: new Date(invoice.paidAt * 1000).toISOString().slice(0, 10),
      currency_code: invoice.currencyCode,
      tax: (invoice.tax / 100).toString(),
      business_entity: invoice.businessEntityId,
      invoice_link: `${process.env.PX_OS_URL}/dashboard/invoices/${invoice.chargebeeId}`,
    };
  }

  /**
   * Generates properties for Hubspot line items
   * @param plan Product plan of the line item
   * @param price Plan Price of the plan
   * @param lineItem Subscription item that corresponds to the line item
   * @param isForever Whether the subscription is forever
   * @param billingPeriodUnit Chargebee billing period unit
   * @returns An object with properties of a line item in Hubspot
   */
  generateLineItem(
    plan: ProductPlan,
    price: ProductPlanPrice,
    lineItem: SubscriptionItem,
    isForever: boolean,
    billingPeriodUnit: ChargeBeeDurationPeriodUnit,
  ): Record<string, string> {
    let recurringbillingfrequency: string;
    let hs_recurring_billing_period: string;
    if (isForever) {
      recurringbillingfrequency =
        this.getRecurringBillingFrequency(billingPeriodUnit);
      hs_recurring_billing_period = `P1${recurringbillingfrequency[0]?.toUpperCase()}`;
    } else {
      recurringbillingfrequency = 'monthly';
      hs_recurring_billing_period = `P${lineItem.billing_cycles + 1}M`;
    }
    if (!!plan?.crmId) {
      return {
        hs_product_id: plan.crmId,
        quantity: lineItem.quantity.toString(),
        price: price.amountPerBillingCycle.toString(),
        amount: price.amount.toString(),
        recurringbillingfrequency,
        hs_recurring_billing_period,
        currency: price.currencyCode,
      };
    } else {
      return {
        name: plan.externalName,
        description: plan.description,
        hs_sku: plan.crmSku,
        quantity: lineItem.quantity.toString(),
        price: price.amountPerBillingCycle.toString(),
        amount: price.amount.toString(),
        currency: price.currencyCode,
        recurringbillingfrequency,
        hs_recurring_billing_period,
      };
    }
  }

  /**
   * Returns the recurring billing frequency for a given billing period unit
   * @param billingPeriodUnit Chargebee billing period unit
   * @returns A string representing the recurring billing frequency
   */
  getRecurringBillingFrequency(
    billingPeriodUnit: ChargeBeeDurationPeriodUnit,
  ): string {
    switch (billingPeriodUnit) {
      case ChargeBeeDurationPeriodUnit.MONTH:
        return 'monthly';
      case ChargeBeeDurationPeriodUnit.YEAR:
        return 'yearly';
      default:
        return 'monthly';
    }
  }

  /**
   * Generates updated properties for Hubspot line items in case of a downsell
   * @param plan Product plan of the line item
   * @param price Plan Price of the plan
   * @param update Updated line item with updated pricing and periodicity info
   * @returns An object with properties of a line item in Hubspot
   */
  generateLineItemUpdateForDownsell(
    plan: ProductPlan,
    price: ProductPlanPrice,
    update: UpdateSubscriptionItemDTO,
  ): Record<string, string> {
    if (plan?.crmId) {
      return {
        hs_product_id: plan.crmId,
        quantity: update.updatedQuantity.toString(),
        price: (update.updatedAmount / 100).toString(),
        amount: (
          update.updatedAmount /
          update.updatedBillingCycles /
          100
        ).toString(),
        currency: price.currencyCode,
        recurringbillingfrequency: 'monthly',
        hs_recurring_billing_period: `P${update.updatedBillingCycles}M`,
      };
    } else {
      return {
        name: plan.externalName,
        description: plan.description,
        hs_sku: plan.crmSku,
        quantity: update.updatedQuantity.toString(),
        price: (update.updatedAmount / 100).toString(),
        amount: (
          update.updatedAmount /
          update.updatedBillingCycles /
          100
        ).toString(),
        currency: price.currencyCode,
        recurringbillingfrequency: 'monthly',
        hs_recurring_billing_period: `P${update.updatedBillingCycles}M`,
      };
    }
  }

  /**
   * Generates updated properties for Invoice_px Hubspot custom object
   * @param updates Updates for the invoice
   * @returns Object with updated Invoice_px properties
   */
  generateInvoice_pxUpdates(
    updates: Partial<Invoice>,
  ): Omit<HubspotInvoiceAmountFields, 'isFirstInvoice'> {
    const invoiceUpdates = {};
    if (updates?.status) {
      invoiceUpdates['invoice_status'] = updates?.status;
    }
    if (updates?.dueDate) {
      invoiceUpdates['due_date'] = new Date(updates?.dueDate * 1000)
        .toISOString()
        .slice(0, 10);
    }
    if (updates?.amountPaid >= 0) {
      invoiceUpdates['amount_paid'] = (updates.amountPaid / 100).toString();
    }
    if (updates?.amountDue >= 0) {
      invoiceUpdates['amount_due'] = (updates.amountDue / 100).toString();
    }
    if (updates?.total) {
      invoiceUpdates['invoice_amount'] = (updates.total / 100).toString();
    }
    if (updates?.amountAdjusted >= 0) {
      invoiceUpdates['amount_adjusted'] = (
        updates.amountAdjusted / 100
      ).toString();
    }
    if (updates?.writeOffAmount >= 0) {
      invoiceUpdates['write_off_amount'] = (
        updates.writeOffAmount / 100
      ).toString();
    }
    if (updates?.amountAdjusted >= 0) {
      invoiceUpdates['amount_adjusted'] = (
        updates.amountAdjusted / 100
      ).toString();
    }
    if (updates?.paidAt) {
      invoiceUpdates['paid_at'] = new Date(updates.paidAt * 1000)
        .toISOString()
        .slice(0, 10);
    }
    return invoiceUpdates as HubspotInvoiceAmountFields;
  }
}
