"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { IBaseCourse } from "@px-shared-account/hermes";

export const useTableActions = () => {
  const t = useTranslations();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<IBaseCourse | null>(null);

  const handleDelete = (course: IBaseCourse) => {
    setSelectedCourse(course);
    setIsDeleteDialogOpen(true);
  };

  const handlePublishStatusChange = (course: IBaseCourse) => {
    // TODO: Implement publish status change
    console.log("Change publish status for course:", course);
  };

  const DeleteConfirmationDialog = () => (
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t("courses.delete.title")}</AlertDialogTitle>
          <AlertDialogDescription>
            {t("courses.delete.description", { name: selectedCourse?.name })}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{t("courses.delete.cancel")}</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              // TODO: Implement delete action
              console.log("Delete course:", selectedCourse);
              setIsDeleteDialogOpen(false);
            }}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {t("courses.delete.confirm")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );

  return {
    handleDelete,
    handlePublishStatusChange,
    DeleteConfirmationDialog,
  };
};
