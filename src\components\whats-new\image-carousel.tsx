"use client";

import { useState } from "react";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { IThumbnailImage } from "@px-shared-account/hermes";

interface ImageCarouselProps {
  thumbnails: IThumbnailImage[];
  onRemove?: (index: number) => void;
  className?: string;
}

export default function ImageCarousel({ thumbnails, onRemove, className }: ImageCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  if (!thumbnails || thumbnails.length === 0) {
    return null;
  }

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? thumbnails.length - 1 : prevIndex - 1));
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === thumbnails.length - 1 ? 0 : prevIndex + 1));
  };

  const currentImage = thumbnails[currentIndex];

  return (
    <div className={cn("relative mx-auto w-full max-w-2xl", className)}>
      <div className="relative aspect-video overflow-hidden rounded-lg">
        <Image
          src={currentImage.url}
          alt={`Thumbnail ${currentIndex + 1}`}
          fill
          className="object-contain"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Navigation buttons */}
        {thumbnails.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute top-1/2 left-2 -translate-y-1/2 transform rounded-full bg-black/50 p-2 text-white transition-colors hover:bg-black/70"
              aria-label="Previous image"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={goToNext}
              className="absolute top-1/2 right-2 -translate-y-1/2 transform rounded-full bg-black/50 p-2 text-white transition-colors hover:bg-black/70"
              aria-label="Next image"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </>
        )}

        {/* Dots indicator */}
        {thumbnails.length > 1 && (
          <div className="absolute bottom-4 left-1/2 flex -translate-x-1/2 transform space-x-2">
            {thumbnails.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={cn(
                  "h-2 w-2 rounded-full transition-colors",
                  index === currentIndex ? "bg-white" : "bg-white/50",
                )}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Remove button */}
        {onRemove && (
          <button
            onClick={() => onRemove(currentIndex)}
            className="absolute top-2 right-2 rounded-full bg-black/50 p-1.5 text-white transition-colors hover:bg-red-500"
            aria-label="Remove image"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Image counter */}
      {thumbnails.length > 1 && (
        <div className="mt-2 text-center text-sm text-gray-400">
          {currentIndex + 1} / {thumbnails.length}
        </div>
      )}
    </div>
  );
}
