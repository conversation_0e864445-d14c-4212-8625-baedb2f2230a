import { Injectable } from '@nestjs/common';
import { CronLogStatus, CronLogType } from '@enums';
import { CronLogResult } from '@types';
import { CronLog } from '@entities';

@Injectable()
export class CronLogFactory {
  /**
   * Generates a cron log entry
   * @param type The type of cron job
   * @param status Status of the cron job
   * @param result Result of the cron job
   * @param startTime Start time of the cron job
   * @param endTime End time of the cron job
   * @param id `id` of the log
   * @param retryCount Number of times the cron job has been retried
   * @param error error (if any)
   */
  generate(
    type: CronLogType,
    status: CronLogStatus,
    result: CronLogResult,
    startTime: Date,
    endTime: Date,
    id: string,
    retryCount?: number,
    error?: any,
  ): CronLog {
    const cronRecord = new CronLog();
    cronRecord.id = id;
    cronRecord.type = type;
    cronRecord.startTime = startTime;
    cronRecord.status = status;
    cronRecord.result = result;
    cronRecord.endTime = endTime;
    cronRecord.retryCount = retryCount;
    if (result.success) {
      cronRecord.error = {};
    } else {
      cronRecord.error = error ?? result.message;
    }
    return cronRecord;
  }
}
