"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useTranslations, useLocale } from "next-intl";
import { useAuth } from "@clerk/nextjs";
import { motion, AnimatePresence } from "framer-motion";
import { ITriadBase } from "@px-shared-account/hermes";
import { AccessInfo } from "@/services/customers/core";
import { useTriadActions } from "@/hooks/useTriadActions";
import { useTriadDialogs } from "@/hooks/useTriadDialogs";
import { useTriadTableColumns } from "./TriadTableColumns";
import { TriadTableSkeleton } from "./shared/TriadSkeletons";
interface TriadsTableProps {
  triads: ITriadBase[];
  triadsReady: boolean;
  accessInfo: AccessInfo;
}

const tableVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
    },
  },
};

const rowVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
  exit: {
    opacity: 0,
    x: 20,
    transition: { duration: 0.2 },
  },
};

export function TriadsTable({ triads, triadsReady, accessInfo }: TriadsTableProps) {
  const t = useTranslations("triad");
  const { userId } = useAuth();
  const locale = useLocale();
  const [sorting, setSorting] = useState<SortingState>([{ id: "datetime", desc: false }]);

  // Use the shared hooks for actions and dialogs
  const triadActions = useTriadActions();

  const {
    isTriadLoading,
    selectedTriadId,
    upcomingTriad,
    handleJoinAttempt,
    handleLeaveAttempt,
    handleDeleteAttempt,
    confirmJoin,
    confirmLeave,
    confirmDelete,
    cancelAction,
  } = triadActions;

  // Use the shared dialogs
  const triadDialogs = useTriadDialogs({
    upcomingTriad,
    locale,
    onJoinConfirm: confirmJoin,
    onLeaveConfirm: confirmLeave,
    onDeleteConfirm: confirmDelete,
    onCancel: cancelAction,
    isLoading: isTriadLoading,
    selectedTriadId,
  });

  // Use the shared table columns
  const columns = useTriadTableColumns({
    accessInfo,
    userId: userId || undefined,
    isTriadLoading,
    onJoinAttempt: async (triadId: number) => {
      const result = await handleJoinAttempt(triadId);
      if (result.needsConfirmation) {
        triadDialogs.openJoinDialog();
      }
    },
    onLeaveAttempt: (triadId: number) => {
      handleLeaveAttempt(triadId);
      triadDialogs.openLeaveDialog();
    },
    onDeleteAttempt: (triadId: number, participantCount: number) => {
      const result = handleDeleteAttempt(triadId, participantCount);
      if (result.needsConfirmation) {
        triadDialogs.openDeleteDialog(participantCount);
      }
    },
  });

  const table = useReactTable({
    data: triads || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  // Early return loading state if access is not ready
  if (!triadsReady) {
    return <TriadTableSkeleton showSessionType={accessInfo.hasBothAccess} />;
  }

  return (
    <>
      <motion.div variants={tableVariants} initial="hidden" animate="visible">
        <div className="bg-background-tertiary overflow-hidden rounded-2xl border border-[#2C2C2C]">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow
                  key={headerGroup.id}
                  className="border-x-none border-collapse border-b-2 border-b-[#111111] hover:bg-transparent"
                >
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      key={header.id}
                      className={cn(
                        "px-6 py-2 text-left text-white",
                        header.id === "actions" && "bg-background-tertiary sticky right-0",
                      )}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel()?.rows?.length ? (
                <AnimatePresence mode="popLayout">
                  {table.getRowModel()?.rows.map((row) => (
                    <motion.tr
                      key={row.id}
                      className={cn(
                        "group border-x-none bg-background-tertiary/80 border-collapse border-y-2 border-[#111] transition-all duration-100 hover:bg-white/10",
                        row.original.organizer?.ssoId === userId &&
                          "!bg-background-quaternary hover:!bg-background-quaternary",
                      )}
                      data-state={row.getIsSelected() && "selected"}
                      variants={rowVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                      layout
                    >
                      {row.getVisibleCells().map((cell) => (
                        <motion.td
                          key={cell.id}
                          className={cn(
                            "px-6 py-3 text-left text-gray-200",
                            cell.column.id === "actions" && "sticky right-0",
                            cell.column.id === "actions" &&
                              "bg-background-tertiary group-hover:!bg-background-quaternary",
                            cell.column.id === "actions" &&
                              row.original.organizer?.ssoId === userId &&
                              "!bg-background-quaternary",
                          )}
                          layout
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </motion.td>
                      ))}
                    </motion.tr>
                  ))}
                </AnimatePresence>
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center text-gray-400">
                    {t("table.no_triads")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </motion.div>

      {triadDialogs.dialogs}
    </>
  );
}
