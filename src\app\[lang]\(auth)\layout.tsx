"use client";

import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { useUser } from "@clerk/nextjs";
import Image from "next/image";
import DefaultLayout from "@/components/templates/DefaultLayout";
import useUserStore from "@/hooks/store/user";
import usePermissionStore from "@/hooks/store/permission";
import { Spinner } from "@/components/ui/spinner";
import { usePermissionsListener } from "@/hooks/use-permissions-listener";
import { updateUserRole } from "@/app/actions/auth-actions";

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  const { user, isLoaded, isSignedIn } = useUser();
  const locale = useLocale();
  usePermissionsListener();
  const { setLanguage } = useUserStore();
  const [isLoading, setIsLoading] = useState(true);

  // Mock handlers - Replace with real implementations
  const handleSearch = (value: string) => {
    console.log("Search:", value);
  };

  const handleSignOut = () => {
    console.log("Sign out");
  };

  const handleProfile = () => {
    console.log("View profile");
  };

  const handleSettings = () => {
    console.log("Open settings");
  };

  const handleMarkNotificationAsRead = (id: string) => {
    console.log("Mark notification as read:", id);
  };

  const handleViewAllNotifications = () => {
    console.log("View all notifications");
  };

  useEffect(() => {
    setLanguage(locale);
  }, [locale]);

  useEffect(() => {
    usePermissionStore
      .getState()
      .fetch()
      .then((fetchedPermissions) => {
        // Renamed for clarity
        usePermissionStore.getState().setPermissions(fetchedPermissions);
      });
  }, []);

  const updateRoleToStudent = async () => {
    try {
      await updateUserRole(user?.id as string, "STUDENT");
      // Refresh the page
      window.location.reload();
    } catch (error) {
      console.error("Error updating role to student", error);
    }
  };

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      const role = user.publicMetadata.role as string;
      if (!role) {
        updateRoleToStudent();
      }
    }
  }, [user, isLoaded, isSignedIn]);

  // Update main loading state based on Clerk user and permissions store
  const permissionsFromStore = usePermissionStore((s) => s.permissions);
  useEffect(() => {
    if (isLoaded && isSignedIn && user && Object.keys(permissionsFromStore).length > 0) {
      setIsLoading(false);
    } else if (isLoaded && !isSignedIn) {
      // If clerk is loaded and user is not signed in, we're not 'loading' app data, we've redirected.
      // Or, if this layout is part of sign-in pages, isLoading might behave differently.
      // For now, assuming redirect handles the !isSignedIn case for app data loading.
      setIsLoading(false);
    }
  }, [isLoaded, isSignedIn, user, permissionsFromStore]);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Image src="/X.gif" alt="Loading..." fill />
      </div>
    );
  }

  return (
    <DefaultLayout
      notifications={[]}
      onSearch={handleSearch}
      onSignOut={handleSignOut}
      onProfile={handleProfile}
      onSettings={handleSettings}
      onMarkNotificationAsRead={handleMarkNotificationAsRead}
      onViewAllNotifications={handleViewAllNotifications}
    >
      {children}
    </DefaultLayout>
  );
}
