"use client";

import { IUpdateRole } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

import { Checkbox } from "@/components/ui/checkbox";
import { permissionsBySection } from "@/lib/permissions/shared";

type PermissionsEditorProps = {
  data?: IUpdateRole["permissions"];
  onChange: (permissions: IUpdateRole["permissions"]) => void;
};

function Section({ section }: { section: string }) {
  const t = useTranslations("gestion.permissions");

  return (
    <div className="flex flex-col gap-2">
      <span className="text-lg font-semibold">{t(section)}</span>
    </div>
  );
}

function Permission({
  permission,
  data,
  onChange,
}: {
  permission: string;
  data?: string[];
  onChange: (permission: string) => void;
}) {
  const t = useTranslations("gestion.permissions");

  return (
    <div className="flex flex-col items-center gap-2">
      <label
        htmlFor={permission}
        className="w-24 cursor-pointer text-center text-xs leading-none font-light select-none"
      >
        {t(permission)}
      </label>
      <Checkbox
        id={permission}
        checked={data?.includes(permission)}
        onCheckedChange={() => onChange(permission)}
      />
    </div>
  );
}

export default function PermissionsEditor({ data, onChange }: PermissionsEditorProps) {
  const [permissions, setPermissions] = useState<IUpdateRole["permissions"]>(data || []);

  const togglePermission = (permission: string) => {
    setPermissions((prev) =>
      prev?.includes(permission)
        ? prev?.filter((p) => p !== permission)
        : [...(prev || []), permission],
    );
  };

  useEffect(() => {
    onChange(permissions);
  }, [permissions, onChange]);

  return Object.keys(permissionsBySection).map((section) => (
    <div
      key={section}
      className="flex flex-row items-center justify-between border-b border-gray-200 py-4"
    >
      <Section section={section} />
      <div className="flex flex-row items-center justify-center gap-2">
        {Object.values(permissionsBySection[section as keyof typeof permissionsBySection]).map(
          (permission) => (
            <Permission
              key={permission}
              permission={permission}
              data={permissions}
              onChange={togglePermission}
            />
          ),
        )}
      </div>
    </div>
  ));
}
