"use client";

import { IUserBase } from "@px-shared-account/hermes";

/**
 * Hook to generate user initials and full name from user data
 * @param user - User object containing firstName, lastName, and email
 * @returns Object containing full name and initials (first letters of first/last name or email)
 */
export const useUserInitials = (user: IUserBase | undefined) => {
  if (!user || !user.email) return { email: "", name: "", firstLetters: "" };
  const { firstName, lastName, email } = user;
  let name = "";
  let firstLetters = "";
  if (firstName && lastName) {
    name = `${firstName} ${lastName}`;
    firstLetters = firstName.charAt(0) + lastName.charAt(0);
  } else if (firstName) {
    name = firstName;
    firstLetters = firstName.charAt(0);
  } else if (lastName) {
    name = lastName;
    firstLetters = lastName.charAt(0);
  } else {
    firstLetters = email.charAt(0);
  }
  return { email, name, firstLetters };
};
