export type ExchangeTokenProof = {
  grant_type: string;
  client_id: string;
  client_secret: string;
  redirect_uri: string;
  refresh_token: string;
};

export type OAuthSuccessResponse = {
  token_type: string;
  refresh_token: string;
  access_token: string;
  expires_in: number;
};

export type HubspotInvoiceAmountFields = {
  invoice_status: string;
  due_date?: string;
  amount_paid: string;
  amount_due: string;
  invoice_amount: string;
  isFirstInvoice?: boolean;
};
