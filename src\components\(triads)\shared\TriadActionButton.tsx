import { Button } from "@/components/ui/button";
import { isTriadOrganizer, isTriadParticipant, isTriadFull } from "@/utils/triadHelpers";
import { ITriadBase } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";

interface TriadActionButtonProps {
  triad: ITriadBase;
  userId?: string;
  isLoading: boolean;
  onJoin: (triadId: number) => void;
  onLeave: (triadId: number) => void;
  onDelete: (triadId: number, participantCount: number) => void;
  variant?: "table" | "list";
  className?: string;
}

export function TriadActionButton({
  triad,
  userId,
  isLoading,
  onJoin,
  onLeave,
  onDelete,
  variant = "table",
  className = "",
}: TriadActionButtonProps) {
  const t = useTranslations("triad");

  const isOrganizer = isTriadOrganizer(triad, userId);
  const isParticipant = isTriadParticipant(triad, userId);
  const isFull = isTriadFull(triad);

  const participantCount = triad.participants?.length || 0;

  // Base classes for different variants
  const baseClasses =
    variant === "list" ? "w-full rounded-full" : "min-w-[100px] whitespace-nowrap";

  // Loading state
  const loadingText = variant === "list" ? t("list.loading") : t("table.loading");

  // Organizer can delete
  if (isOrganizer) {
    const deleteText = variant === "list" ? t("list.delete") : t("table.delete");

    return (
      <Button
        variant={variant === "list" ? "link" : "link"}
        className={`${baseClasses} ${
          variant === "list"
            ? "cursor-pointer text-white"
            : "w-full text-white underline hover:text-white/50"
        } ${className}`}
        onClick={() => onDelete(triad.id, participantCount)}
        disabled={isLoading}
      >
        {isLoading ? loadingText : deleteText}
      </Button>
    );
  }

  // Participant can leave
  if (isParticipant) {
    const leaveText = variant === "list" ? t("list.leave") : t("table.leave");

    return (
      <Button
        variant="outline"
        className={`${baseClasses} border-white bg-transparent text-white hover:bg-transparent hover:text-white/50 ${className}`}
        onClick={() => onLeave(triad.id)}
        disabled={isLoading}
      >
        {isLoading ? loadingText : leaveText}
      </Button>
    );
  }

  // Triad is full
  if (isFull) {
    const fullText = variant === "list" ? t("list.full") : t("table.full");

    return (
      <Button
        variant="outline"
        className={`${baseClasses} border-white bg-transparent text-white hover:bg-white/10 ${className}`}
        disabled
      >
        {fullText}
      </Button>
    );
  }

  // Can join
  const joinText = variant === "list" ? t("list.join") : t("table.join");

  return (
    <Button
      className={`${baseClasses} bg-white text-black hover:bg-gray-200 ${className}`}
      onClick={() => onJoin(triad.id)}
      disabled={isLoading}
    >
      {isLoading ? loadingText : joinText}
    </Button>
  );
}
