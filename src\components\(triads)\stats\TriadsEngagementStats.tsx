"use client";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON>Chart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts";
import { Loader2, Users2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import { anton } from "@/lib/fonts/anton";
import { useTranslations } from "next-intl";
import { usePlatformTriadStats } from "@/services/triad/client";
import { PlatformTriadStats } from "@/types/triad";

interface TimeRange {
  start: Date;
  end: Date;
  type: string;
}

interface TriadsEngagementStatsProps {
  timeRange: TimeRange;
  onDataChange?: (data: PlatformTriadStats | null) => void;
}

const COLORS = ["#f472b6", "#a78bfa", "#60a5fa", "#4ade80", "#facc15"];

export default function TriadsEngagementStats({
  timeRange,
  onDataChange,
}: TriadsEngagementStatsProps) {
  const t = useTranslations("triad.stats");
  const {
    stats: triadStats,
    isLoading,
    error,
  } = usePlatformTriadStats(timeRange.start.toISOString(), timeRange.end.toISOString());

  useEffect(() => {
    if (triadStats && onDataChange) {
      onDataChange(triadStats);
    }
  }, [triadStats, onDataChange]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-white/50" />
      </div>
    );
  }

  if (error) {
    return <div className="text-center text-red-400">{t("engagement.error")}</div>;
  }

  if (!triadStats) return null;

  const { engagement } = triadStats;

  // Format duration distribution data for visualization
  const durationData = engagement.sessionDurationDistribution.map((item) => ({
    name: t("engagement.session_duration.breakdown.hours", {
      hours: item.duration,
    }),
    value: item.count,
  }));

  return (
    <div className="space-y-6">
      <Card className="border-0 bg-gradient-to-br from-pink-500/10 to-purple-600/10">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm font-medium text-pink-400">
                {t("engagement.recurring_participants.title")}
              </p>
              <h3 className={cn("mt-2 text-3xl font-bold text-white", anton.className)}>
                {engagement.recurringParticipants}
              </h3>
              <p className="mt-2 text-sm text-gray-400">
                {t("engagement.recurring_participants.description")}
              </p>
            </div>
            <div className="rounded-xl bg-pink-500/20 p-2">
              <Users2 className="h-5 w-5 text-pink-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card className="overflow-hidden border-0 bg-[#1a1a1a]">
          <CardContent className="p-6">
            <div className="mb-6 flex items-center justify-between">
              <h3 className={cn("text-xl font-semibold text-white", anton.className)}>
                {t("engagement.session_duration.distribution.title")}
              </h3>
              <div className="rounded-full bg-blue-500/20 px-2.5 py-1 text-xs font-medium text-blue-400">
                {t("engagement.session_duration.distribution.by_hours")}
              </div>
            </div>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={durationData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#2a2a2a" />
                  <XAxis dataKey="name" stroke="#666" fontSize={12} />
                  <YAxis stroke="#666" fontSize={12} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "#1a1a1a",
                      border: "1px solid #2a2a2a",
                      borderRadius: "6px",
                    }}
                    labelStyle={{ color: "#fff" }}
                  />
                  <Bar
                    dataKey="value"
                    name={t("engagement.session_duration.distribution.sessions")}
                    fill="#3b82f6"
                    fillOpacity={0.7}
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-0 bg-[#1a1a1a]">
          <CardContent className="p-6">
            <div className="mb-6 flex items-center justify-between">
              <h3 className={cn("text-xl font-semibold text-white", anton.className)}>
                {t("engagement.session_duration.breakdown.title")}
              </h3>
              <div className="rounded-full bg-emerald-500/20 px-2.5 py-1 text-xs font-medium text-emerald-400">
                {t("engagement.session_duration.breakdown.distribution")}
              </div>
            </div>
            <div className="flex h-[300px] items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={durationData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    label={({ name, value }) => `${name} (${value})`}
                    labelLine={{ stroke: "#666" }}
                  >
                    {durationData.map((_, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                        fillOpacity={0.8}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "#1a1a1a",
                      border: "1px solid #2a2a2a",
                      borderRadius: "6px",
                    }}
                    labelStyle={{ color: "#fff" }}
                    itemStyle={{ color: "#e5e7eb" }}
                    formatter={(value: any, name: any) => [
                      t("engagement.session_duration.breakdown.description", {
                        hours: name,
                        count: value,
                      }),
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
