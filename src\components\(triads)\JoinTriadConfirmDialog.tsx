"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { format } from "date-fns";
import { fr, enUS } from "date-fns/locale";

interface JoinTriadConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
  existingTriadDate?: Date;
  loading?: boolean;
  locale?: string;
}

export function JoinTriadConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  existingTriadDate,
  loading,
  locale = "en",
}: JoinTriadConfirmDialogProps) {
  const t = useTranslations("triad");
  const dateLocale = locale === "fr" ? fr : enUS;

  const formattedDate = existingTriadDate
    ? format(existingTriadDate, "dd/MM/yyyy 'à' HH'h'mm", {
        locale: dateLocale,
      })
    : "";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="rounded-3xl border-none bg-[#111111] text-white sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-xl">{t("confirm_join.title")}</DialogTitle>
          <div className="space-y-2 text-center text-gray-400">
            <p>Vous avez déjà un groupe prévu le</p>
            <p className="text-lg font-bold text-white">{formattedDate}</p>
            <p>Êtes-vous sûr de vouloir rejoindre un autre groupe ?</p>
            <p className="mx-auto mt-4 max-w-[90%] text-sm break-words italic">
              Merci de respecter le temps de chacun en ne vous inscrivant que si vous êtes
              certain(e) de pouvoir être présent(e). Votre présence est précieuse pour le groupe !
              ✨
            </p>
          </div>
        </DialogHeader>
        <DialogFooter className="flex flex-row gap-4 sm:gap-4">
          <Button
            variant="outline"
            onClick={onCancel}
            className="flex-1 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black"
          >
            {t("confirm_join.cancel")}
          </Button>
          <Button
            onClick={onConfirm}
            disabled={loading}
            className="flex-1 rounded-full bg-white text-black hover:bg-gray-200"
          >
            {loading ? t("list.loading") : t("confirm_join.confirm")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
