import { MigrationInterface, QueryRunner } from 'typeorm';

export class WhatsNew1748355630264 implements MigrationInterface {
    name = 'WhatsNew1748355630264';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "whats_new_versions" ("id" SERIAL NOT NULL, "versionCode" character varying NOT NULL, "date" TIMESTAMP NOT NULL, "titleEn" character varying NOT NULL, "titleFr" character varying NOT NULL, "descriptionEn" text NOT NULL DEFAULT '', "descriptionFr" text NOT NULL DEFAULT '', "addedEn" jsonb NOT NULL DEFAULT '[]', "addedFr" jsonb NOT NULL DEFAULT '[]', "changedEn" jsonb NOT NULL DEFAULT '[]', "changedFr" jsonb NOT NULL DEFAULT '[]', "fixedEn" jsonb NOT NULL DEFAULT '[]', "fixedFr" jsonb NOT NULL DEFAULT '[]', "removedEn" jsonb NOT NULL DEFAULT '[]', "removedFr" jsonb NOT NULL DEFAULT '[]', "thumbnails" jsonb NOT NULL DEFAULT '[]', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "UQ_7c7ac1935949584f9612490e1cb" UNIQUE ("versionCode"), CONSTRAINT "PK_4800e43b70d5938302aeee45398" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE INDEX "idx_whats_new_version_code" ON "whats_new_versions" ("versionCode") `,
        );
        await queryRunner.query(
            `CREATE INDEX "idx_whats_new_date" ON "whats_new_versions" ("date") `,
        );
        await queryRunner.query(
            `CREATE TABLE "whats_new_reactions" ("id" SERIAL NOT NULL, "emoji" character varying NOT NULL, "userId" character varying NOT NULL, "versionId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_ec695b6d0b712453c69900cb6b1" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE INDEX "idx_whats_new_reactions_user_id" ON "whats_new_reactions" ("userId") `,
        );
        await queryRunner.query(
            `CREATE INDEX "idx_whats_new_reactions_version_id" ON "whats_new_reactions" ("versionId") `,
        );
        await queryRunner.query(
            `ALTER TABLE "whats_new_reactions" ADD CONSTRAINT "FK_9bcb9b55fbd38d39dcc7d86abbf" FOREIGN KEY ("versionId") REFERENCES "whats_new_versions"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "whats_new_reactions" DROP CONSTRAINT "FK_9bcb9b55fbd38d39dcc7d86abbf"`,
        );
        await queryRunner.query(
            `DROP INDEX "public"."idx_whats_new_reactions_version_id"`,
        );
        await queryRunner.query(
            `DROP INDEX "public"."idx_whats_new_reactions_user_id"`,
        );
        await queryRunner.query(`DROP TABLE "whats_new_reactions"`);
        await queryRunner.query(`DROP INDEX "public"."idx_whats_new_date"`);
        await queryRunner.query(`DROP INDEX "public"."idx_whats_new_version_code"`);
        await queryRunner.query(`DROP TABLE "whats_new_versions"`);
    }
}
