import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

type UserTabsProps = {
  activeTab: string;
  onTabChange: (value: string) => void;
};

export default function UserTabs({ activeTab, onTabChange }: UserTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full overflow-scroll">
      <TabsList className="h-auto w-full justify-start border-b bg-transparent p-0">
        <TabsTrigger
          value="calendar"
          className={cn(
            "relative h-10 rounded-none border-b-[3px] border-b-transparent data-[state=active]:border-b-primary data-[state=active]:shadow-none",
            "px-4 pb-3 pt-2 font-medium text-muted-foreground data-[state=active]:text-foreground",
          )}
        >
          {"Calendar"}
        </TabsTrigger>
        <TabsTrigger
          value="analytics"
          className={cn(
            "relative h-10 rounded-none border-b-[3px] border-b-transparent data-[state=active]:border-b-primary data-[state=active]:shadow-none",
            "px-4 pb-3 pt-2 font-medium text-muted-foreground data-[state=active]:text-foreground",
          )}
        >
          {"Analytics"}
        </TabsTrigger>
        <TabsTrigger
          value="notifications"
          className={cn(
            "relative h-10 rounded-none border-b-[3px] border-b-transparent data-[state=active]:border-b-primary data-[state=active]:shadow-none",
            "px-4 pb-3 pt-2 font-medium text-muted-foreground data-[state=active]:text-foreground",
          )}
        >
          {"Notifications"}
        </TabsTrigger>
        <TabsTrigger
          value="information"
          className={cn(
            "relative h-10 rounded-none border-b-[3px] border-b-transparent data-[state=active]:border-b-primary data-[state=active]:shadow-none",
            "px-4 pb-3 pt-2 font-medium text-muted-foreground data-[state=active]:text-foreground",
          )}
        >
          {"Information"}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
