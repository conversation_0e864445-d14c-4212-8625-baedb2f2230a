import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtPermissionsGuard } from '@auth';
import { ListCronLogsDTO, ReprocessCronJobDTO } from '@dtos';
import { CronLogUseCases } from '@useCases';

@ApiTags('cron-logs')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('cron-logs')
export class CronLogController {
  constructor(private readonly cronLogUseCases: CronLogUseCases) {}

  @ApiOperation({
    summary: 'List cron logs',
  })
  @Get('/')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  )
  async list(@Query() query: ListCronLogsDTO) {
    return this.cronLogUseCases.searchAll(
      query?.type,
      query?.status,
      query?.limit,
      query?.page,
      query?.orderBy,
    );
  }

  @ApiOperation({
    summary: 'Get a cron log',
  })
  @Get('/:id')
  async getOne(@Param('id') id: string) {
    return this.cronLogUseCases.getOne(id);
  }

  @ApiOperation({
    summary: 'Re-process a cron job',
  })
  @Post('/:id/reprocess')
  async reProcess(@Param('id') id: string, @Body() data: ReprocessCronJobDTO) {
    return this.cronLogUseCases.delegateToProcessor(
      data.type,
      data.input,
      id,
      data.retryCount,
    );
  }
}
