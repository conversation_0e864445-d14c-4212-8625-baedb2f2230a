"use client";

import { cn } from "@/lib/utils";
import React, { useState, useEffect, Children, useRef } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
  type CarouselApi,
} from "@/components/ui/carousel";
import type useEmblaCarousel from "embla-carousel-react";
import { DotIndicator } from "./DotIndicator";
import { Spinner } from "@/components/ui/spinner";

// CarouselWrapper Props
export interface CarouselWrapperProps {
  children: React.ReactNode;
  opts?: Parameters<typeof useEmblaCarousel>[0];
  itemClassName?: string;
  contentClassName?: string;
  showArrows?: boolean;
  showDots?: boolean;
  arrowLeftClassName?: string;
  arrowRightClassName?: string;
  dotsContainerClassName?: string;
  wrapperClassName?: string;
  onEnd?: () => void;
  isLoadingMore?: boolean;
  hasMore?: boolean;
}

export const CarouselWrapper = ({
  children,
  opts,
  itemClassName,
  contentClassName,
  showArrows = true,
  showDots = true,
  arrowLeftClassName,
  arrowRightClassName,
  dotsContainerClassName,
  wrapperClassName = "relative w-full",
  onEnd,
  isLoadingMore = false,
  hasMore = false,
}: CarouselWrapperProps) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const numChildren = Children.count(children);
  const currentSlideRef = useRef<number>(0);

  useEffect(() => {
    if (!api) return;
    const updateState = () => {
      if (api.scrollSnapList().length === 0 && numChildren > 0) api.reInit();
      setCount(api.scrollSnapList().length);
      const currentSlide = api.selectedScrollSnap() + 1;
      setCurrent(currentSlide);
      // Store current slide index for position restoration
      currentSlideRef.current = api.selectedScrollSnap();
    };
    updateState();
    api.on("select", updateState);
    api.on("slidesChanged", updateState);
    api.on("reInit", updateState);

    // Store current slide when user scrolls
    const onSelect = () => {
      currentSlideRef.current = api.selectedScrollSnap();
    };
    api.on("select", onSelect);

    const timer = setTimeout(updateState, 100);
    return () => {
      clearTimeout(timer);
      api.off("select", updateState);
      api.off("slidesChanged", updateState);
      api.off("reInit", updateState);
      api.off("select", onSelect);
    };
  }, [api, numChildren]);

  useEffect(() => {
    if (api && current !== 0 && current === count) {
      onEnd?.();
    }
  }, [api, current, count, onEnd]);

  // Restore scroll position after new data is loaded
  useEffect(() => {
    if (api && !isLoadingMore && currentSlideRef.current > 0) {
      // Use a small delay to ensure the new items are rendered
      const timer = setTimeout(() => {
        api.scrollTo(currentSlideRef.current, false);
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [api, isLoadingMore, numChildren]);

  if (numChildren === 0) return null;

  return (
    <Carousel
      opts={{
        align: "start",
        loop: opts?.loop !== undefined ? opts.loop : numChildren > 1,
        ...opts,
      }}
      setApi={setApi}
      className={cn(wrapperClassName)}
    >
      <div className="flex flex-col gap-4">
        <CarouselContent className={cn("-ml-4 flex gap-4 p-2", contentClassName)}>
          {React.Children.map(children, (child, index) => (
            <CarouselItem key={index} className={cn("pl-2 select-none", itemClassName)}>
              {child}
            </CarouselItem>
          ))}
          {/* Loading indicator at the end when loading more data */}
          {isLoadingMore && hasMore && (
            <CarouselItem
              className={cn("flex items-center justify-center pl-2 select-none", itemClassName)}
            >
              <div className="flex flex-col items-center justify-center p-8">
                <Spinner size="medium" />
                <span className="text-muted-foreground mt-2 text-sm">Loading more...</span>
              </div>
            </CarouselItem>
          )}
        </CarouselContent>
        <div className="flex items-center justify-between">
          {showArrows && numChildren > 1 && count > 1 && (
            <CarouselPrevious
              className={cn(
                "bg-primary hover:bg-primary/80 static translate-x-0 translate-y-0 disabled:grayscale",
                arrowLeftClassName,
              )}
            />
          )}
          {showDots && (
            <DotIndicator
              count={count}
              current={current}
              onDotClick={(index) => api?.scrollTo(index)}
              className={dotsContainerClassName}
            />
          )}
          {showArrows && numChildren > 1 && count > 1 && (
            <CarouselNext
              className={cn(
                "bg-primary hover:bg-primary/80 static translate-x-0 translate-y-0 disabled:grayscale",
                arrowRightClassName,
              )}
            />
          )}
        </div>
      </div>
    </Carousel>
  );
};

CarouselWrapper.displayName = "CarouselWrapper";
