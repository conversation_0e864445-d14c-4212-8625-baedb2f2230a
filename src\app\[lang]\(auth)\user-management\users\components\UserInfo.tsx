"use client";

import { memo } from "react";
import { Row } from "@tanstack/react-table";
import { IUserBase } from "@px-shared-account/hermes";

import UserAvatar from "./UserAvatar";
import { useUserInitials } from "@/hooks/user";

interface UserInfoProps {
  row: Row<IUserBase>;
}

function UserInfo({ row }: UserInfoProps) {
  const { email, name } = useUserInitials(row.original);

  if (!email && !name) {
    return null;
  }

  return (
    <div className="flex items-center gap-2" role="cell">
      <UserAvatar user={row.original} />
      <div className="flex flex-col">
        <span className="text-sm font-medium" title={name}>
          {name}
        </span>
        <span className="text-xs text-muted-foreground" title={email}>
          {email}
        </span>
      </div>
    </div>
  );
}

export default memo(UserInfo);
