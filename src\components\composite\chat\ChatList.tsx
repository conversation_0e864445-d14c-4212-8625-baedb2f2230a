"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { motion, AnimatePresence } from "framer-motion";

export interface ChatListItem {
  id: string;
  title: string;
  avatar?: string;
  lastMessage?: string;
  timestamp: Date;
  unreadCount?: number;
  online?: boolean;
}

export interface ChatListProps {
  items: ChatListItem[];
  selectedId?: string;
  onSelect: (id: string) => void;
  className?: string;
}

const listVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.02,
    },
  },
};

const itemVariants = {
  hidden: {
    opacity: 0,
    x: -10,
  },
  show: {
    opacity: 1,
    x: 0,
    transition: {
      type: "tween",
      duration: 0.1,
      ease: "easeOut",
    },
  },
};

export function ChatList({ items, selectedId, onSelect, className }: ChatListProps) {
  return (
    <ScrollArea className={cn("h-full", className)}>
      <motion.div
        className="flex flex-col gap-1 p-2"
        variants={listVariants}
        initial="hidden"
        animate="show"
      >
        <AnimatePresence mode="wait">
          {items.map((item) => (
            <motion.div
              key={item.id}
              variants={itemVariants}
              layout
              layoutId={item.id}
              transition={{ duration: 0.1 }}
            >
              <Button
                variant="ghost"
                className={cn(
                  "h-auto w-full justify-start gap-3 p-3",
                  selectedId === item.id && "bg-muted",
                )}
                onClick={() => onSelect(item.id)}
              >
                <div className="relative shrink-0">
                  <Avatar>
                    {item.avatar && <AvatarImage src={item.avatar} alt={item.title} />}
                    <AvatarFallback>{item.title[0]}</AvatarFallback>
                  </Avatar>
                  {typeof item.online === "boolean" && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.1 }}
                      className={cn(
                        "absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-background",
                        item.online ? "bg-green-500" : "bg-muted",
                      )}
                    />
                  )}
                </div>
                <div className="flex flex-1 flex-col items-start gap-1 overflow-hidden">
                  <div className="flex w-full items-center justify-between gap-2">
                    <span className="truncate font-medium">{item.title}</span>
                    <span className="shrink-0 text-xs text-muted-foreground">
                      {format(item.timestamp, "HH:mm")}
                    </span>
                  </div>
                  {item.lastMessage && (
                    <span className="truncate text-left text-sm text-muted-foreground">
                      {item.lastMessage}
                    </span>
                  )}
                </div>
                {item.unreadCount ? (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.1 }}
                    className="shrink-0 rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground"
                  >
                    {item.unreadCount}
                  </motion.span>
                ) : null}
              </Button>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>
    </ScrollArea>
  );
}
