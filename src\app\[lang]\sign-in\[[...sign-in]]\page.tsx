import { SignIn as SignInClerk } from "@clerk/nextjs";
import { getTranslations } from "next-intl/server";

export default function SignInPage() {
  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <SignInClerk />
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "navigation" });
  return {
    title: t("sign-in"),
  };
}
