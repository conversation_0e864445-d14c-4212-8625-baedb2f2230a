import { useFormContext } from "react-hook-form";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { SwitchField as SwitchFieldType } from "../../types";
import { useTranslations } from "next-intl";

interface SwitchFieldProps {
  field: SwitchFieldType;
}

export const SwitchField = ({ field }: SwitchFieldProps) => {
  const {
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();
  const value = watch(field.id);
  const error = errors[field.id];
  const t = useTranslations("validation");

  const renderError = () => {
    if (!error) return null;

    if (error.type === "custom" && error.message) {
      return error.message.toString();
    }

    switch (error.type) {
      case "required":
        return t("required");
      default:
        return error.message?.toString() || t("invalid");
    }
  };

  return (
    <div className="w-full space-y-2">
      <div className="flex items-center gap-2">
        <Switch
          checked={value || false}
          onCheckedChange={(checked) => {
            setValue(field.id, checked, { shouldValidate: true });
            field.onChange?.(checked);
          }}
          disabled={field.disabled}
        />
        <Label htmlFor={field.id} className={error ? "text-destructive" : ""}>
          {field.label}
          {field.required && <span className="text-destructive">*</span>}
        </Label>
      </div>
      {field.description && <p className="text-xs text-muted-foreground">{field.description}</p>}
      {error && <p className="text-sm font-medium text-destructive">{renderError()}</p>}
    </div>
  );
};
