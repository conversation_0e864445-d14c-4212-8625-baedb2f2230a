"use client";

import { useEffect, ReactNode } from "react";
import confetti from "canvas-confetti";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { cn } from "@/lib/utils";
import { anton } from "@/lib/fonts/anton";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

interface ConfettiDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children?: ReactNode;
  onDismissed?: () => void;
  timer?: number; // Duration in milliseconds
}

export function ConfettiDrawer({
  isOpen,
  onClose,
  title,
  children,
  onDismissed,
  timer,
}: ConfettiDrawerProps) {
  const t = useTranslations("actions");

  useEffect(() => {
    if (isOpen && timer) {
      const timeout = setTimeout(() => {
        onClose();
      }, timer);

      return () => clearTimeout(timeout);
    }
  }, [isOpen, timer, onClose]);

  useEffect(() => {
    if (isOpen) {
      const duration = 1000;
      const end = Date.now() + duration;

      const frame = () => {
        confetti({
          particleCount: 2,
          angle: 60,
          spread: 55,
          origin: { x: 0, y: 0.7 },
          colors: ["#8D2146", "#9CA1EB", "#DDD8C4"],
          gravity: 0.8,
        });

        confetti({
          particleCount: 4,
          angle: 120,
          spread: 55,
          origin: { x: 1, y: 0.7 },
          colors: ["#8D2146", "#9CA1EB", "#DDD8C4"],
          gravity: 0.8,
        });

        if (Date.now() < end) {
          requestAnimationFrame(frame);
        }
      };

      frame();
    }
  }, [isOpen]);

  return (
    <Drawer open={isOpen} onOpenChange={onClose} onClose={onDismissed}>
      <DrawerContent className="h-[80dvh] rounded-t-[10px] border-none bg-[#111111] text-white lg:h-[40vh] lg:max-h-[500px]">
        <DrawerHeader className="flex h-full flex-col items-center justify-center space-y-2">
          <DrawerTitle className={cn("text-center text-2xl text-white", anton.className)}>
            {title}
          </DrawerTitle>
          {children && (
            <DrawerDescription className="text-center text-lg text-white/80">
              {children}
            </DrawerDescription>
          )}
        </DrawerHeader>
        <div className="flex justify-center pb-6">
          <Button variant="default" onClick={onClose} className="w-[200px]">
            {t("close")}
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
