"use client";

import { IListR<PERSON>, IRoleB<PERSON>, RoleGroup, Roles } from "@px-shared-account/hermes";
import { ColumnDef } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { useMemo, useState, useCallback, memo } from "react";

import { DataTable } from "@/components/table";
import { useToast } from "@/hooks/use-toast";
import { useConfirmationModal } from "@/hooks/store/confirmationModal";
import { useRolesTable } from "./use-roles-table";
import { useTable } from "@/hooks/use-table";
import { useRoleList, useDeleteRole } from "@/services/role";
import CreateRoleModal from "./CreateRoleModal";
import DuplicateRoleModal from "./DuplicateRoleModal";
import EditRoleModal from "./EditRoleModal";
import RoleActionMenu from "./RoleActionMenu";
import Filter from "./Filter";
import { TableProps } from "@/types/table";

export default function RolesTable() {
  const t = useTranslations("gestion.users.roles");
  const { toast } = useToast();
  const confirmModal = useConfirmationModal();
  const { deleteRole, isLoading: isDeleting } = useDeleteRole();

  // Use the custom hook for table state and configuration
  const { tableConfig, getRowId, itemsPerPage, tableState, handleFilterChange } = useRolesTable();

  // Use the role list service hook for API data fetching with pagination
  const {
    data: roles,
    isLoading: isLoadingRoles,
    mutate,
  } = useRoleList({
    page: tableState.page,
    limit: itemsPerPage,
    ...(tableState.search ? { search: tableState.search } : {}),
    ...(tableState.group ? { group: tableState.group } : {}),
    ...(tableState.sortBy ? { sortBy: tableState.sortBy } : {}),
    ...(tableState.sortOrder ? { sortOrder: tableState.sortOrder } : {}),
  });

  // Use the hook for table row selection
  const { selectedRows, handleRowSelection } = useTable(tableConfig);

  const [modalState, setModalState] = useState<{
    create: boolean;
    edit: IRoleBase | null;
    duplicate: IRoleBase | null;
  }>({
    create: false,
    edit: null,
    duplicate: null,
  });

  const handleDelete = useCallback(
    async (role: IListRole) => {
      confirmModal.openModal(
        {
          title: t("delete-title"),
          description: t("delete-description"),
          type: "warning",
        },
        async () => {
          try {
            await deleteRole(role.id);
            toast({
              title: t("deleted"),
              description: t("deleted-description"),
            });
            mutate();
          } catch (error) {
            toast({
              title: t("error"),
              description: t("error-delete-description"),
              variant: "destructive",
            });
            console.error(error);
          }
        },
      );
    },
    [confirmModal, t, toast, mutate, deleteRole],
  );

  const handleCreate = useCallback(() => {
    setModalState((prev) => ({ ...prev, create: true }));
  }, []);

  const handleFilterValueChange = useCallback(
    (values: Record<string, string>) => {
      // Convert the filter values to our state format
      const filters: Partial<typeof tableState> = {};

      if (values.group) {
        filters.group = values.group as RoleGroup;
      }

      handleFilterChange(filters);
    },
    [handleFilterChange],
  );

  const columns: ColumnDef<IListRole>[] = useMemo(
    () => [
      {
        header: t("group"),
        accessorKey: "group",
      },
      {
        header: t("name"),
        accessorKey: "name",
      },
      {
        header: t("members"),
        accessorKey: "members",
      },
      {
        header: t("permissions"),
        accessorKey: "permissions",
        cell: ({ row }) => (
          <div className="max-w-xs truncate text-xs uppercase">
            {row.original.permissions.map((permission) => (
              <span key={permission}>{permission}&nbsp;</span>
            ))}
          </div>
        ),
      },
      {
        header: "",
        id: "actions",
        cell: ({ row }) => (
          <RoleActionMenu
            role={row.original}
            onEdit={(role) => setModalState((prev) => ({ ...prev, edit: role }))}
            onDuplicate={(role) => setModalState((prev) => ({ ...prev, duplicate: role }))}
            onDelete={handleDelete}
          />
        ),
      },
    ],
    [t, handleDelete],
  );

  // Get current filter values for FilterBar
  const currentFilterValues = useMemo(() => {
    return {
      group: tableState.group || "",
    };
  }, [tableState.group]);

  // Memoize all table-related properties
  const tableProps: TableProps<IListRole> = useMemo(
    () => ({
      columns,
      data: roles?.data ?? [],
      totalRows: roles?.total ?? 0,
      isLoading: isLoadingRoles,
      enableSelection: false,
      enableSearch: true,
      enablePagination: (roles?.total ?? 0) > itemsPerPage,
      enableSorting: true,
      pageSize: itemsPerPage,
      title: `${t("roles")} (${roles?.total ?? 0})`,
      getRowId,
      minSearchLength: 1,
      onStateChange: tableConfig.onStateChange,
      onSelectionChange: handleRowSelection,
      noResults: t("no-results"),
      filters: (
        <Filter
          onCreateClick={handleCreate}
          onChange={handleFilterValueChange}
          value={currentFilterValues}
        />
      ),
    }),
    [
      columns,
      roles?.data,
      roles?.total,
      isLoadingRoles,
      itemsPerPage,
      t,
      getRowId,
      tableConfig.onStateChange,
      handleRowSelection,
      handleCreate,
      handleFilterValueChange,
      currentFilterValues,
    ],
  );

  return (
    <>
      <DataTable {...tableProps} />
      {modalState.create && (
        <CreateRoleModal
          isModalOpen={modalState.create}
          setIsModalOpen={(open) => setModalState((prev) => ({ ...prev, create: open }))}
          onComplete={() => mutate()}
        />
      )}
      {modalState.duplicate && (
        <DuplicateRoleModal
          isModalOpen={modalState.duplicate}
          setIsModalOpen={(open) => setModalState((prev) => ({ ...prev, duplicate: null }))}
          onDuplicate={() => mutate()}
        />
      )}
      {modalState.edit && (
        <EditRoleModal
          role={modalState.edit}
          setIsModalOpen={(open) => setModalState((prev) => ({ ...prev, edit: null }))}
          onUpdate={() => mutate()}
          isModalOpen={modalState.edit !== null}
        />
      )}
    </>
  );
}
