"use client";

import { X } from "lucide-react";
import { useEffect, useCallback } from "react";
import { cn } from "@/lib/utils";
import { Button } from "../button";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOk?: () => void;
  title?: string;
  children?: React.ReactNode;
  className?: string;
  showCloseButton?: boolean;
  showFooter?: boolean;
  okText?: string;
  cancelText?: string;
}

export function Modal({
  isOpen,
  onClose,
  onOk,
  title,
  children,
  className,
  showCloseButton = true,
  showFooter = true,
  okText = "OK",
  cancelText = "Cancel",
}: ModalProps) {
  const handleEscapeKey = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    },
    [onClose],
  );

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
      document.addEventListener("keydown", handleEscapeKey);
    }

    return () => {
      document.body.style.overflow = "unset";
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [isOpen, handleEscapeKey]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black/80" onClick={onClose} />
      <div
        className={cn(
          "bg-background relative z-50 w-full max-w-lg border p-4 shadow-xl sm:rounded-lg",
          "animate-in zoom-in-95",
          className,
        )}
      >
        <div className="mb-4 flex items-center justify-between">
          {title && (
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{title}</h2>
          )}
          {showCloseButton && (
            <button
              onClick={onClose}
              className="rounded-full p-1 transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <X className="h-5 w-5 text-gray-500 dark:text-gray-400" />
            </button>
          )}
        </div>

        <div className="relative max-h-[calc(100vh-200px)] overflow-y-auto">{children}</div>

        {showFooter && (
          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="rounded-full border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              {cancelText}
            </button>
            {onOk && (
              <Button
                onClick={onOk}
                className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-full px-4 py-2 transition-colors"
              >
                {okText}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
