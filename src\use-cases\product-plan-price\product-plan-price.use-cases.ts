import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { FindOptionsWhere } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { ChargebeeBillingService } from '@services/billing';
import { ProductPlan, ProductPlanPrice } from '@entities';
import { ProductPlanPriceFactory } from '.';
import { UpdateResultWithItemInfo } from '@types';
import { CreateProductPlanPriceDTO } from '@dtos';
import { IDataServices } from '@abstracts';

@Injectable()
export class ProductPlanPriceUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly chargeBeeService: ChargebeeBillingService,
    private readonly productPlanPriceFactory: ProductPlanPriceFactory,
  ) {}

  /**
   * Creates a product plan price
   * @param planPriceInfo Info of the product plan price
   * @param planChargebeeId Chargebee id of the plan to which this price will be attached
   * @param taxProfile Tax profile of this item price
   */
  async create(
    priceInfo: CreateProductPlanPriceDTO,
    createdPlan: ProductPlan,
    taxProfile: string,
  ): Promise<ProductPlanPrice> {
    const planPriceInfo = this.productPlanPriceFactory.generate(
      priceInfo,
      createdPlan.id,
      createdPlan.internalName,
    );
    try {
      await this.chargeBeeService.createItemPrice(
        planPriceInfo,
        createdPlan.chargebeeId,
        taxProfile,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to create product plan price.',
        HttpStatus.PRECONDITION_FAILED,
        {
          cause: 'Price',
        },
      );
    }
    const createdPlanPrice = await this.dataServices.productPlanPrice.create(
      planPriceInfo,
    );
    return createdPlanPrice;
  }

  /**
   * Returns all product plan price records from database
   * @returns Product plan price records from database
   */
  async getAll(
    limit: number,
    page: number,
    orderBy: 'DESC' | 'ASC',
  ): Promise<{ items: ProductPlanPrice[]; total: number }> {
    return this.dataServices.productPlanPrice.getAll(limit, page, {
      createdAt: orderBy,
    });
  }

  /**
   * Updates a product plan price
   * @param name Name of the plan price to update
   * @param updates Updates for the product plan price
   */
  async updateById(
    id: number,
    updates: QueryDeepPartialEntity<ProductPlanPrice>,
  ): Promise<UpdateResultWithItemInfo<ProductPlanPrice>> {
    const { updatedItem, success } =
      await this.dataServices.productPlanPrice.updateAndReturnItem(
        'id',
        id.toString(),
        updates,
      );

    await this.chargeBeeService.updateItemPrice(
      updatedItem.chargebeeId,
      updates,
    );
    return {
      updatedItem,
      success,
    };
  }

  /**
   * Retrieves a product plan price matching provided `id``
   * @param id `id` of plan to fetch
   */
  async getOne(id: number): Promise<ProductPlanPrice> {
    return this.dataServices.productPlanPrice.getOneBy({
      id,
    });
  }

  /**
   * Retrieves all product plan price records matching provided filters
   * @param filter Properties of product plan price to search for
   */
  async searchAll(
    filter: FindOptionsWhere<ProductPlanPrice>,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: ProductPlanPrice[]; total: number }> {
    return this.dataServices.productPlanPrice.getAllBy(filter, limit, page, {
      createdAt: orderBy || 'DESC',
    });
  }

  /**
   * Retrieves all unique billing cycles from product plan prices
   * @returns Array of unique billing cycles
   */
  async getUniqueBillingCycles(): Promise<number[]> {
    const queryBuilder = this.dataServices.productPlanPrice
      .getRepository()
      .createQueryBuilder('ProductPlanPrice');

    const uniqueBillingCycles = await queryBuilder
      .select(
        'DISTINCT ProductPlanPrice.totalBillingCycles',
        'totalBillingCycles',
      )
      .getRawMany();
    return uniqueBillingCycles.map((row: any) => row.totalBillingCycles);
  }

  /**
   * Hard deletes a product plan price from database and chargebee
   * @param chargebeeId Chargebee id of the plan to delete
   * @param id `id` of the plan price to delete
   */
  async delete(chargebeeId: string, id: number): Promise<any> {
    try {
      await this.chargeBeeService.deletePrice(chargebeeId);
      await this.dataServices.productPlanPrice.hardDelete({ id: id });
    } catch (error) {
      console.log('Error deleting product plan price: ', error);
    }
  }
}
