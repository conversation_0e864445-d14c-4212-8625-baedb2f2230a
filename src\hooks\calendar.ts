"use client";

import { format, isAfter, isBefore } from "date-fns";
import { useEffect, useState } from "react";

import useCalendarStore from "./store/calendar";
import getSelectedWeekIndex from "@/lib/calendar/getSelectedWeekIndex";
import getWeekDays from "@/lib/calendar/getWeekDays";
import groupEventsByDay from "@/lib/calendar/groupEventsByDay";

/**
 * Provides the currently selected week and date.
 *
 * @returns An object containing the following properties:
 *   - `selectedDate`: The currently selected date.
 *   - `selectedWeek`: The week containing the currently selected date.
 *   - `selectedWeekIndex`: The index of the currently selected week in the
 *     `weeks` array.
 *   - `weeks`: An array of dates representing the 7 days of the week containing
 *     the currently selected date.
 */
export function useSelectedDate() {
  const { selectedDate } = useCalendarStore();

  const weeks = getWeekDays(selectedDate, 7);
  const selectedWeekIndex = getSelectedWeekIndex(selectedDate, weeks, 0);
  const selectedWeek = weeks[selectedWeekIndex];

  return { selectedDate, selectedWeek, selectedWeekIndex, weeks };
}

/**
 * Provides a string indicating the months and years of the currently selected
 * week or month.
 *
 * @returns An object containing the following properties:
 *   - `showMonthsAndYears`: A string containing the months and years of the
 *     currently selected week, if the week spans multiple months and years.
 *   - `showMonthsAndYear`: A string containing the months of the currently
 *     selected week, if the week spans multiple months but not years.
 *   - `showMonthAndYear`: A string containing the month and year of the
 *     currently selected date, if the currently selected date is not in the
 *     same month as the first date of the currently selected week.
 */
export function useShowMonthsAndYears() {
  const { layout } = useCalendarStore();
  const { selectedDate, selectedWeek } = useSelectedDate();

  const len = selectedWeek.length;
  const firstDayOfWeekMonth = format(selectedWeek[0], "MMM");
  const lastDayOfWeekMonth = format(selectedWeek[len - 1], "MMM");
  const firstDayOfWeekYear = format(selectedWeek[0], "yyyy");
  const lastDayOfWeekYear = format(selectedWeek[len - 1], "yyyy");

  const showMonthsAndYears =
    layout === "week" &&
    firstDayOfWeekMonth !== lastDayOfWeekMonth &&
    firstDayOfWeekYear !== lastDayOfWeekYear
      ? `${firstDayOfWeekMonth} ${firstDayOfWeekYear} - ${lastDayOfWeekMonth} ${lastDayOfWeekYear}`
      : false;
  const showMonthsAndYear =
    !showMonthsAndYears && layout === "week" && firstDayOfWeekMonth !== lastDayOfWeekMonth
      ? `${firstDayOfWeekMonth} - ${lastDayOfWeekMonth} ${firstDayOfWeekYear}`
      : false;
  const showMonthAndYear = !showMonthsAndYear ? format(selectedDate, "MMMM yyyy") : false;

  return { showMonthsAndYears, showMonthsAndYear, showMonthAndYear };
}

const defaultCurrentFilter = "all";
type FilterOptions = "all" | "upcoming" | "past";

/**
 * Provides a list of grouped events based on the currently selected filter.
 *
 * @returns An object containing the following properties:
 *   - `groupedEvents`: An object containing arrays of events grouped by day.
 *   - `setCurrentFilter`: A function to set the current filter. The filter
 *     options are "all", "upcoming", or "past".
 */
export function useCalendarList() {
  const { events } = useCalendarStore();
  const [currentFilter, setCurrentFilter] = useState<FilterOptions>(defaultCurrentFilter);
  const [groupedEvents, setGroupedEvents] = useState(groupEventsByDay(events));

  useEffect(() => {
    const now = new Date();
    if (currentFilter === "upcoming") {
      const filtered = events.filter((event) => isAfter(new Date(event.begin), now));
      setGroupedEvents(groupEventsByDay(filtered));
    } else if (currentFilter === "past") {
      const filtered = events.filter((event) => isBefore(new Date(event.begin), now));
      setGroupedEvents(groupEventsByDay(filtered));
    } else {
      setGroupedEvents(groupEventsByDay(events));
    }
  }, [currentFilter]);

  return { groupedEvents, setCurrentFilter };
}
