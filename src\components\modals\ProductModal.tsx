"use client";

import { DialogClose } from "@radix-ui/react-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { XIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { useCallback, useMemo, useState } from "react";

import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import CardRow from "@/components/purchases/CardRow";
import Button from "@/components/base/button";
import { DEFAULT_IMAGES } from "@/constants/images";
import { PurchaseRecord } from "@/services/customers";
import { formatCurrency, formatDate } from "@/lib/utils";
import ParadoxLogo from "../base/logos/Paradox";
import { getCommunityUrl } from "@/lib/products/getCommunityUrls";
import { CourseCard, CourseAccessButton } from "@/components/cards";
import { PageSectionCarousel } from "@/components/base/page-section/PageSectionCarousel";
import { PageSectionBase } from "../base/page-section";

// Determine what to show for date information based on subscription data
const computeDateField = (subscription: PurchaseRecord["subscription"]) => {
  let field = "next-payment";
  let value = "N/A";

  if (subscription.nextBillingAt) {
    value = formatDate(subscription.nextBillingAt);
  } else if (subscription.lastPaymentDate) {
    field = "last-payment";
    value = formatDate(subscription.lastPaymentDate);
  } else if (subscription.cancelledAt && subscription.cancelScheduleCreatedAt) {
    // Yes you read well, we show "Last Payment" as the field, but the value is actually cancelled_at.
    // This is because customers got confused by the "Cancelled at" date.
    field = "last-payment";
    value = formatDate(subscription.cancelledAt);
  }

  return { field, value };
};

interface ProductModalProps {
  children: React.ReactNode;
  subscription: PurchaseRecord;
  searchParams?: Record<string, string | string[] | undefined>;
}

export function ProductModal({ children, subscription, searchParams = {} }: ProductModalProps) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);

  const { plan, item, offer } = subscription;

  const computedDate = computeDateField(subscription.subscription);

  // Determine what banner image to use
  let bannerImage = DEFAULT_IMAGES.banner;
  if (offer?.bannerImage && offer.bannerImage.length) {
    bannerImage = offer.bannerImage;
  }

  // Format monthly recurring revenue if present
  let mrr = null;
  if (subscription.subscription.mrr) {
    mrr = formatCurrency(subscription.subscription.mrr / 100);
  }

  const handleOnClick = useCallback(() => {
    setOpen(true);
  }, []);

  const communityUrl = useMemo(() => getCommunityUrl(subscription), [subscription]);

  // Get courses from the offer
  const courses = offer?.lmsIds?.products || [];
  // Determine if we should use carousel based on course count
  const hasMultipleCourses = courses.length > 1;
  const hasCourses = courses.length > 0;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <VisuallyHidden>
        <DialogTitle>{t("product.modal.title")}</DialogTitle>
      </VisuallyHidden>
      <DialogTrigger asChild onClick={handleOnClick}>
        {children}
      </DialogTrigger>
      <DialogContent className="bg-background flex h-full flex-col border-none p-0 lg:h-5/6 lg:min-w-[840px] lg:rounded-xl">
        <div className="h-[350px] w-full max-w-full lg:h-[425px]">
          <div className="relative flex h-full overflow-hidden lg:rounded-t-xl">
            <Image
              src={bannerImage}
              alt=""
              className="absolute inset-0 z-8 h-full w-full object-cover lg:rounded-t-xl"
              height={418}
              width={288}
            />
            <div className="to-background absolute inset-0 z-9 h-full bg-gradient-to-b from-transparent"></div>
            <DialogClose className="absolute top-4 right-4 z-100 cursor-pointer">
              <XIcon className="text-foreground size-4" />
            </DialogClose>
            <div className="z-10 flex size-full flex-col items-start justify-center gap-4 p-6">
              <ParadoxLogo className="w-24 sm:w-36" />
              <div className="font-anton text-left text-3xl text-white uppercase sm:text-4xl md:text-5xl lg:text-[4rem]">
                {plan.externalName || plan.name}
              </div>
              {offer?.usp ? <span className="text-white">{offer.usp}</span> : null}
              <div className="flex w-full flex-col justify-start gap-4 sm:flex-row">
                <Button className="w-full font-semibold md:w-auto">
                  <Link href={`/payments/${subscription.subscription.id}`} className="p-5">
                    {t("product.modal.payments-button")}
                  </Link>
                </Button>
                <Button
                  className="bg-secondary-foreground text-secondary hover:bg-secondary-foreground/90 w-full font-semibold md:w-auto"
                  disabled={!item || !plan}
                >
                  <Link className="p-5" href={communityUrl ?? ""} target="_blank">
                    {t("product.modal.community-button")}
                  </Link>
                </Button>
              </div>
            </div>
            <div className="from-background absolute inset-0 bg-gradient-to-t from-5% sm:from-45%" />
          </div>
        </div>

        <div className="flex flex-col overflow-auto">
          <PageSectionBase
            title={t("product.modal.details-title")}
            className="mt-10 px-6"
            contentClassName="flex max-w-full flex-col items-start justify-between gap-6 text-white lg:flex-row lg:gap-8"
          >
            <div className="max-w-96 text-justify">
              {offer?.description && offer?.description.length
                ? offer?.description
                : t("product.modal.default-description")}
            </div>
            <div className="mb-2 flex w-full max-w-96 flex-col lg:mb-0">
              {computedDate.field && computedDate.value && (
                <CardRow
                  field={t(`subscription.details.${computedDate.field}`)}
                  value={computedDate.value}
                  hasNext={!!mrr}
                />
              )}
              {mrr ? (
                <CardRow
                  field={t("subscription.details.amount")}
                  value={mrr ?? ""}
                  hasNext={false}
                />
              ) : null}
            </div>
          </PageSectionBase>

          {/* Courses Section */}
          {hasCourses && (
            <>
              {hasMultipleCourses ? (
                <PageSectionCarousel
                  title={t("product.modal.courses-title")}
                  showCarouselArrows
                  showCarouselDots
                  carouselOpts={{ loop: false, align: "start" }}
                  carouselItemsClassName="basis-auto"
                  className="p-6"
                >
                  {courses.map((course, index) => (
                    <CourseCard
                      key={course.id || index}
                      id={course.id}
                      name={course.name}
                      index={index}
                    />
                  ))}
                </PageSectionCarousel>
              ) : (
                <PageSectionBase title={t("product.modal.courses-title")} className="p-6">
                  <CourseCard id={courses[0].id} name={courses[0].name} />
                </PageSectionBase>
              )}
            </>
          )}

          {/* Fallback Button when no specific courses are available */}
          {!hasCourses && <CourseAccessButton />}
        </div>
      </DialogContent>
    </Dialog>
  );
}
