"use client";

import { useRef, useCallback, useState, useMemo } from "react";
import {
  UseFilterBarProps,
  FilterStep,
  Step,
  StepState,
  ProcessedGroup,
  FilterBarState,
} from "./types";

/** Maximum number of retry attempts for failed option fetches */
const MAX_RETRIES = 3;

function processSteps(steps: Step[]): FilterStep[][] {
  return steps.reduce<FilterStep[][]>((acc, step) => {
    if (step.type === "separator") {
      acc.push([]);
    } else {
      if (acc.length === 0) acc.push([]);
      acc[acc.length - 1].push(step as FilterStep);
    }
    return acc;
  }, []);
}

function createInitialState(steps: Step[]): FilterBarState {
  const filterSteps = steps.filter(
    (step): step is FilterStep => step.type === "filter" || step.type === "dateRange",
  );
  const filterGroups = processSteps(steps);

  return {
    values: filterSteps.reduce(
      (acc, step) => ({
        ...acc,
        [step.id]: step.defaultValue || "",
      }),
      {},
    ),
    stepsState: filterSteps.reduce(
      (acc, step) => ({
        ...acc,
        [step.id]: {
          isLoading: false,
          error: null,
          retryCount: 0,
          options: step.options || [],
        },
      }),
      {},
    ),
    groups: filterGroups.map((items) => ({
      items,
      state: {
        scrollable: false,
        gradientState: { left: false, right: false },
        wrapped: false,
      },
    })),
  };
}

export const useFilterBar = ({ steps, value, onChange, onOptionsNeeded }: UseFilterBarProps) => {
  /** Tracks ongoing fetch requests to prevent duplicates */
  const fetchingRef = useRef<Record<string, boolean>>({});
  /** References to group DOM elements for scroll handling */
  const groupRefs = useRef<(HTMLDivElement | null)[]>([]);

  // State
  const [state, setState] = useState<FilterBarState>(() => createInitialState(steps));

  // Sync with external value prop
  useMemo(() => {
    if (value) {
      setState((prev) => ({
        ...prev,
        values: { ...prev.values, ...value },
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(value)]);

  // Sync with steps prop
  useMemo(() => {
    setState(createInitialState(steps));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(steps)]);

  /**
   * Finds all filter steps that depend on a given step
   */
  const getDependentSteps = useCallback(
    (stepId: string) => {
      return steps.filter((step): step is FilterStep => {
        if (step.type !== "filter") return false;
        const dependency = step.dependsOn?.[stepId];
        return dependency !== undefined;
      });
    },
    [steps],
  );

  /**
   * Determines if a filter step should be enabled based on its dependencies
   */
  const isStepEnabled = useCallback(
    (stepId: string) => {
      const step = steps.find(
        (s): s is FilterStep => (s.type === "filter" || s.type === "dateRange") && s.id === stepId,
      );
      if (!step?.dependsOn) return true;

      // Check step's own state
      const stepState = state.stepsState[stepId];
      if (stepState?.error !== null || stepState?.isLoading) {
        return false;
      }

      // Check dependencies
      return Object.entries(step.dependsOn).every(([depId, config]) => {
        if (!config) return true;
        return !config.required || state.values[depId];
      });
    },
    [steps, state],
  );

  /**
   * Fetches options for a filter step
   */
  const fetchOptions = useCallback(
    async (stepId: string, retryCount = 0) => {
      if (!onOptionsNeeded || fetchingRef.current[stepId]) return;

      fetchingRef.current[stepId] = true;
      setState((prev) => ({
        ...prev,
        stepsState: {
          ...prev.stepsState,
          [stepId]: {
            ...prev.stepsState[stepId],
            isLoading: true,
          },
        },
      }));

      try {
        const options = await onOptionsNeeded(stepId, state.values);
        setState((prev) => ({
          ...prev,
          stepsState: {
            ...prev.stepsState,
            [stepId]: {
              ...prev.stepsState[stepId],
              options,
              isLoading: false,
              error: null,
            },
          },
        }));
      } catch (error) {
        if (retryCount < MAX_RETRIES) {
          setTimeout(
            () => {
              fetchOptions(stepId, retryCount + 1);
            },
            Math.pow(2, retryCount) * 1000,
          );
        } else {
          setState((prev) => ({
            ...prev,
            stepsState: {
              ...prev.stepsState,
              [stepId]: {
                ...prev.stepsState[stepId],
                isLoading: false,
                error: "Failed to load options",
              },
            },
          }));
        }
      } finally {
        fetchingRef.current[stepId] = false;
      }
    },
    [onOptionsNeeded, state.values],
  );

  /**
   * Handles changes to filter values
   */
  const handleChange = useCallback(
    (stepId: string, newValue: string | string[]) => {
      setState((prev) => {
        const updatedValues = {
          ...prev.values,
          [stepId]: newValue,
        };
        // Clear dependent steps when parent value changes
        const dependentSteps = getDependentSteps(stepId);
        dependentSteps.forEach((step) => {
          updatedValues[step.id] = "";
        });
        // Notify parent of change with updated values
        onChange?.(updatedValues);
        // Reset dependent step state
        const newStepsState = { ...prev.stepsState };
        dependentSteps.forEach((step) => {
          newStepsState[step.id] = {
            ...newStepsState[step.id],
            options: [],
            error: null,
            retryCount: 0,
          };
        });
        return {
          ...prev,
          values: updatedValues,
          stepsState: newStepsState,
        };
      });

      // Fetch new options for dependent steps if needed
      if (onOptionsNeeded && newValue) {
        const dependentSteps = getDependentSteps(stepId);
        dependentSteps.forEach((step) => {
          if (step.dependsOn?.[stepId]?.required) {
            fetchOptions(step.id);
          }
        });
      }
    },
    [getDependentSteps, onChange, onOptionsNeeded, fetchOptions],
  );

  const clearAll = useCallback(() => {
    setState((prev) => ({
      ...prev,
      values: Object.keys(prev.values).reduce((acc, key) => ({ ...acc, [key]: "" }), {}),
    }));
  }, []);

  return {
    values: state.values,
    stepsState: state.stepsState,
    groups: state.groups,
    handleChange,
    isStepEnabled,
    clearAll,
    groupRefs,
  };
};
