import {
  Controller,
  Get,
  Param,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ListCreditNotesDTO } from '@dtos';
import { CreditNoteUseCases } from '@useCases';

@ApiTags('credit-notes')
@Controller('credit-notes')
export class CreditNotesController {
  constructor(private readonly creditNotesUseCases: CreditNoteUseCases) {}

  @ApiOperation({
    summary: 'List credit notes',
  })
  @Get('/')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  )
  async list(@Query() query: ListCreditNotesDTO) {
    return this.creditNotesUseCases.searchAll(
      query.query,
      query.fiscalEntity,
      query.customerId,
      query.subscriptionId,
      query.status,
      query.limit,
      query.page,
      query.orderBy,
    );
  }

  @ApiOperation({
    summary: 'Get a credit note',
  })
  @Get('/:id')
  async getOne(@Param('id') id: string) {
    return this.creditNotesUseCases.getOne(id);
  }
}
