import {
  InvoiceLinkedPayment,
  InvoiceLineItem,
  InvoiceLineItemTax,
  InvoiceTax,
  InvoiceIssuedCreditNote,
  InvoiceAdjustmentCreditNote,
  InvoiceDiscount,
  InvoiceBillingAddress,
  InvoiceShippingAddress,
  InvoiceLineItemDiscount,
} from 'chargebee-typescript/lib/resources';
import { InvoiceStatus, InvoicePriceType, Currencies } from '@enums';
import { ProductPlanPrice } from '.';

export class Invoice {
  id: number;
  chargebeeId: string;
  customerId: string;
  customerEmail: string;
  customerName: string;
  subscriptionId: string;
  status: InvoiceStatus;
  priceType: InvoicePriceType;
  date: number;
  dueDate: number;
  paidAt?: number;
  netTermDays: number;
  exchangeRate: number;
  total: number;
  amountPaid: number;
  amountAdjusted: number;
  writeOffAmount: number;
  amountDue: number;
  amountToCollect: number;
  currencyCode: Currencies;
  baseCurrencyCode: Currencies;
  generatedAt: number;
  channel: string;
  tax: number;
  lineItems: InvoiceLineItem[];
  discounts?: InvoiceDiscount[];
  lineItemDiscounts?: InvoiceLineItemDiscount[];
  taxes?: InvoiceTax[];
  lineItemTaxes: InvoiceLineItemTax[];
  subTotal: number;
  linkedPayments: InvoiceLinkedPayment[];
  adjustmentCreditNotes: InvoiceAdjustmentCreditNote[];
  issuedCreditNotes: InvoiceIssuedCreditNote[];
  dunningAttempts: any[];
  billingAddress: InvoiceBillingAddress;
  shippingAddress: InvoiceShippingAddress;
  businessEntityId: string;
  crmId?: string;
  overdueCheckPassed: boolean;
  updatedAt?: Date;
  createdAt?: Date;
}

// We are storing the image and name with the line_items in our DB
// but we don't have a type to access them when needed.
export class InvoiceLineItemPX extends InvoiceLineItem {
  image?: string;
  name?: string;
}

// This is specific to the downsell usecase where we need the price
// info along with the line item to compute billing cycles and prices.
export class EditSubscriptionLineItem extends InvoiceLineItemPX {
  billingCyclesRemaining?: number;
  amountPerBillingCycle?: number;
  price?: ProductPlanPrice;
}
