# [1.7.0-beta.4](https://github.com/davidlaroche/paradox-api/compare/v1.7.0-beta.3...v1.7.0-beta.4) (2025-07-10)


### Bug Fixes

* **use-case:** return only `granted` course accesses ([b39e025](https://github.com/davidlaroche/paradox-api/commit/b39e025a4d064a164cd300091a025b73ff4145c0))


### Features

* **course:** enhance `CourseFactory` to include `productFamily` & `lmsId` ([8b8087a](https://github.com/davidlaroche/paradox-api/commit/8b8087a253425373cffc1ec4a53783231e7b7c03))
* **customer-access:** add course access retrieval by email ([2286d94](https://github.com/davidlaroche/paradox-api/commit/2286d9434cff7552f8ed5e0f3c0244a8572df62a))

# [1.7.0-beta.3](https://github.com/davidlaroche/paradox-api/compare/v1.7.0-beta.2...v1.7.0-beta.3) (2025-07-09)


### Features

* **course:** enhance `CourseFactory` to include `productFamily` & `lmsId` ([ab35ad4](https://github.com/davidlaroche/paradox-api/commit/ab35ad4616ebe900e641af40213761b176c2b3f3))
* **customer-access:** add course access retrieval by email ([4dfcfa3](https://github.com/davidlaroche/paradox-api/commit/4dfcfa3a7b9a7271de0a511db8fa0745f157eb01))

# [1.7.0-beta.2](https://github.com/davidlaroche/paradox-api/compare/v1.7.0-beta.1...v1.7.0-beta.2) (2025-06-20)


### Features

* **http-schedule:** implement HTTP scheduling feature with job management capabilities ([89c2f47](https://github.com/davidlaroche/paradox-api/commit/89c2f471c74d24ff92d0ca0ea76c8800ba066bdc))

# [1.7.0-beta.1](https://github.com/davidlaroche/paradox-api/compare/v1.6.0...v1.7.0-beta.1) (2025-06-19)


### Bug Fixes

* **goliaths:** update user ID parameter to use SSO ID in GoliathService notifications ([598e118](https://github.com/davidlaroche/paradox-api/commit/598e1187da3e9ac9e5b41090f44e9b2d9cbcf674))
* **triads:** correct sessionTime timezone handling, update user-specific endpoints ([db0418c](https://github.com/davidlaroche/paradox-api/commit/db0418cd38e949a896e5bd30d7b875c9c4825eaa))
* **usecases:** use `chargebeeId` for getting subscription with invoices ([2e6df98](https://github.com/davidlaroche/paradox-api/commit/2e6df987bbb8ff5de05b71f6d3189f5ef8e2a83c))


### Features

* **user:** add phone number management functionality ([41e6bee](https://github.com/davidlaroche/paradox-api/commit/41e6bee23e941a7b9e60b6ab3f8c8b04952b39f4))

# [1.5.0-beta.1](https://github.com/davidlaroche/paradox-api/compare/v1.4.0...v1.5.0-beta.1) (2025-06-11)


### Bug Fixes

* **goliaths:** update user ID parameter to use SSO ID in GoliathService notifications ([598e118](https://github.com/davidlaroche/paradox-api/commit/598e1187da3e9ac9e5b41090f44e9b2d9cbcf674))
* **triads:** correct sessionTime timezone handling, update user-specific endpoints ([db0418c](https://github.com/davidlaroche/paradox-api/commit/db0418cd38e949a896e5bd30d7b875c9c4825eaa))


### Features

* **billing:** add methods to retrieve invoice PDF link & payment source for subscriptions ([2ee0e2d](https://github.com/davidlaroche/paradox-api/commit/2ee0e2d049dc9386fe542277ac2113d81548280d))
* **cache:** implement custom cache key decorator and interceptor ([8780147](https://github.com/davidlaroche/paradox-api/commit/87801472f196a96808f402e472facb1455e4aa11))
* **goliaths:** implement resendAllNotifications method to notify users with discount codes ([90ba8a6](https://github.com/davidlaroche/paradox-api/commit/90ba8a680b7027ffa41a0dc7912230f4f05fde87))
* **goliaths:** refactor resendAllNotifications to improve user filtering and error handling ([a7653b2](https://github.com/davidlaroche/paradox-api/commit/a7653b2e2e179ecb82a6b54f68d84a4a462a8823))
* **goliaths:** update resendAllNotifications to filter users with non-null discount codes and ssoId ([0df04dd](https://github.com/davidlaroche/paradox-api/commit/0df04dd17b620088a0eb8c1e31f53cb1f81b24f7))
* **helpers:** add centsToDecimal method for converting cents to decimal string ([c0649ce](https://github.com/davidlaroche/paradox-api/commit/c0649ce082812990ad40ae6dbf133b3a1a2160f3))
* **portal-session:** add portal session management functionality ([8008d1d](https://github.com/davidlaroche/paradox-api/commit/8008d1dc458e3bd65d7e4ed2d052b63da86bb276))
* **subscriptions:** add endpoint to retrieve subscription details with invoices ([18d3622](https://github.com/davidlaroche/paradox-api/commit/18d36223f180f7e7bd39c3f3a85f1aa8745e236b))
* **subscriptions:** add endpoints to list user subscriptions ([08ee55c](https://github.com/davidlaroche/paradox-api/commit/08ee55ce246cb68ff82a9c7262ffbb51f18e4663))
* **types:** add PaymentSourceInfo and SubscriptionInvoiceDetails interfaces ([cde1ae6](https://github.com/davidlaroche/paradox-api/commit/cde1ae609c1f12694bb7888f0e9faa029363db46))
* **user:** add phone number management functionality ([41e6bee](https://github.com/davidlaroche/paradox-api/commit/41e6bee23e941a7b9e60b6ab3f8c8b04952b39f4))

# [1.6.0](https://github.com/davidlaroche/paradox-api/compare/v1.5.0...v1.6.0) (2025-06-18)


### Bug Fixes

* **triads:** correct sessionTime timezone handling, update user-specific endpoints ([905d6be](https://github.com/davidlaroche/paradox-api/commit/905d6be09b5ed4dc9e1df99333aaf4e4c3116a74))


### Features

* **billing:** add methods to retrieve invoice PDF link & payment source for subscriptions ([2ee0e2d](https://github.com/davidlaroche/paradox-api/commit/2ee0e2d049dc9386fe542277ac2113d81548280d))
* **cache:** implement custom cache key decorator and interceptor ([8780147](https://github.com/davidlaroche/paradox-api/commit/87801472f196a96808f402e472facb1455e4aa11))
* **helpers:** add centsToDecimal method for converting cents to decimal string ([c0649ce](https://github.com/davidlaroche/paradox-api/commit/c0649ce082812990ad40ae6dbf133b3a1a2160f3))
* **portal-session:** add portal session management functionality ([8008d1d](https://github.com/davidlaroche/paradox-api/commit/8008d1dc458e3bd65d7e4ed2d052b63da86bb276))
* **subscriptions:** add endpoint to retrieve subscription details with invoices ([18d3622](https://github.com/davidlaroche/paradox-api/commit/18d36223f180f7e7bd39c3f3a85f1aa8745e236b))
* **subscriptions:** add endpoints to list user subscriptions ([08ee55c](https://github.com/davidlaroche/paradox-api/commit/08ee55ce246cb68ff82a9c7262ffbb51f18e4663))
* **types:** add PaymentSourceInfo and SubscriptionInvoiceDetails interfaces ([cde1ae6](https://github.com/davidlaroche/paradox-api/commit/cde1ae609c1f12694bb7888f0e9faa029363db46))

# [1.5.0](https://github.com/davidlaroche/paradox-api/compare/v1.4.0...v1.5.0) (2025-06-11)


### Features

* **goliaths:** implement resendAllNotifications method to notify users with discount codes ([90ba8a6](https://github.com/davidlaroche/paradox-api/commit/90ba8a680b7027ffa41a0dc7912230f4f05fde87))
* **goliaths:** refactor resendAllNotifications to improve user filtering and error handling ([a7653b2](https://github.com/davidlaroche/paradox-api/commit/a7653b2e2e179ecb82a6b54f68d84a4a462a8823))
* **goliaths:** update resendAllNotifications to filter users with non-null discount codes and ssoId ([0df04dd](https://github.com/davidlaroche/paradox-api/commit/0df04dd17b620088a0eb8c1e31f53cb1f81b24f7))

# [1.4.0](https://github.com/davidlaroche/paradox-api/compare/v1.3.2...v1.4.0) (2025-06-11)


### Features

* **customer-access:** add GoliathService integration for user discount code retrieval ([3d65c64](https://github.com/davidlaroche/paradox-api/commit/****************************************))

## [1.3.2](https://github.com/davidlaroche/paradox-api/compare/v1.3.1...v1.3.2) (2025-06-10)


### Bug Fixes

* **goliaths:** update user ID parameter to use SSO ID in GoliathService notifications ([196bd33](https://github.com/davidlaroche/paradox-api/commit/196bd33d3a5a92561a1c5c6125cce83bbf46ec0e))


### Features

* **goliaths:** implement resendAllNotifications method to notify users with discount codes ([90ba8a6](https://github.com/davidlaroche/paradox-api/commit/90ba8a680b7027ffa41a0dc7912230f4f05fde87))

# [1.4.0-beta.4](https://github.com/davidlaroche/paradox-api/compare/v1.4.0-beta.3...v1.4.0-beta.4) (2025-06-10)


### Bug Fixes

* **goliaths:** update user ID parameter to use SSO ID in GoliathService notifications ([598e118](https://github.com/davidlaroche/paradox-api/commit/598e1187da3e9ac9e5b41090f44e9b2d9cbcf674))

# [1.4.0-beta.3](https://github.com/davidlaroche/paradox-api/compare/v1.4.0-beta.2...v1.4.0-beta.3) (2025-06-10)


### Bug Fixes

* **triads:** correct sessionTime timezone handling, update user-specific endpoints ([db0418c](https://github.com/davidlaroche/paradox-api/commit/db0418cd38e949a896e5bd30d7b875c9c4825eaa))

# [1.4.0-beta.2](https://github.com/davidlaroche/paradox-api/compare/v1.4.0-beta.1...v1.4.0-beta.2) (2025-06-09)


### Features

* **customer-access:** add GoliathService integration for user discount code retrieval ([3d65c64](https://github.com/davidlaroche/paradox-api/commit/****************************************))

# [1.4.0-beta.1](https://github.com/davidlaroche/paradox-api/compare/v1.3.0...v1.4.0-beta.1) (2025-06-05)


### Bug Fixes

* **use-cases:** lowercase customer email in credit note, invoice, and transaction use cases ([56c8a0a](https://github.com/davidlaroche/paradox-api/commit/56c8a0abe53413e0d109923f6af4dce0f111b09c))
* **use-cases:** trim whitespace from customer email ([b940076](https://github.com/davidlaroche/paradox-api/commit/b94007613f02bbb38bc5142d323a06f3913bf61e))
* **user:** normalize email addresses to lowercase ([1dc09f5](https://github.com/davidlaroche/paradox-api/commit/1dc09f5452635f6ababbca05ca76d7b3cd5368a5))


### Features

* **billing:** add methods to retrieve invoice PDF link & payment source for subscriptions ([2ee0e2d](https://github.com/davidlaroche/paradox-api/commit/2ee0e2d049dc9386fe542277ac2113d81548280d))
* **cache:** implement custom cache key decorator and interceptor ([8780147](https://github.com/davidlaroche/paradox-api/commit/87801472f196a96808f402e472facb1455e4aa11))
* **helpers:** add centsToDecimal method for converting cents to decimal string ([c0649ce](https://github.com/davidlaroche/paradox-api/commit/c0649ce082812990ad40ae6dbf133b3a1a2160f3))
* **portal-session:** add portal session management functionality ([8008d1d](https://github.com/davidlaroche/paradox-api/commit/8008d1dc458e3bd65d7e4ed2d052b63da86bb276))
* **subscriptions:** add endpoint to retrieve subscription details with invoices ([18d3622](https://github.com/davidlaroche/paradox-api/commit/18d36223f180f7e7bd39c3f3a85f1aa8745e236b))
* **subscriptions:** add endpoints to list user subscriptions ([08ee55c](https://github.com/davidlaroche/paradox-api/commit/08ee55ce246cb68ff82a9c7262ffbb51f18e4663))
* **types:** add PaymentSourceInfo and SubscriptionInvoiceDetails interfaces ([cde1ae6](https://github.com/davidlaroche/paradox-api/commit/cde1ae609c1f12694bb7888f0e9faa029363db46))
* **user:** add phone number management functionality ([41e6bee](https://github.com/davidlaroche/paradox-api/commit/41e6bee23e941a7b9e60b6ab3f8c8b04952b39f4))

# [1.3.0](https://github.com/davidlaroche/paradox-api/compare/v1.2.0...v1.3.0) (2025-06-04)


### Bug Fixes

* **goliath:** conditionally send discount email based on POM status ([c484157](https://github.com/davidlaroche/paradox-api/commit/c48415728dce613af978eadd40ff74d07a92856b))
* **goliath:** update seedGoliathsCodes to use databaseService for user queries ([7ee12d1](https://github.com/davidlaroche/paradox-api/commit/7ee12d1e791240524d6265048c059f91f48dad6e))
* **product-offers:** update endpoint for listing all product offers ([5477c58](https://github.com/davidlaroche/paradox-api/commit/5477c587d7ecf92728e610233e6d40739f6b5470))
* **triads:** assign value to `sessions` in `TriadFactory` ([7ef5185](https://github.com/davidlaroche/paradox-api/commit/7ef5185260345c49061de1bb5d31dc547b26008b))
* **whats-new:** resolve toISOString error on update by re-fetching entity ([c6177f4](https://github.com/davidlaroche/paradox-api/commit/c6177f403392ae4ddc62d88b2dd9e32d8f3130e7))
* **whats-new:** resolve toISOString error on update by re-fetching entity ([0a54179](https://github.com/davidlaroche/paradox-api/commit/0a54179cf5eca0955d7920117ba496b6fa9511d7))


### Features

* **auth:** add `CurrentUserInterceptor` for user context retrieval ([3dd31c5](https://github.com/davidlaroche/paradox-api/commit/3dd31c55f420524dae955bc55f4853e3aa732773))
* **auth:** add `CurrentUserInterceptor` for user context retrieval ([e97b3cc](https://github.com/davidlaroche/paradox-api/commit/e97b3cce91a85f43fd057d3583142971173062f9))
* **course:** enhance course management with offers and managers handling ([caeb270](https://github.com/davidlaroche/paradox-api/commit/caeb27096ae3654330718f0709e0c1762de70e05))
* **goliaths:** add seedGoliathsCodes method to initialize discount codes for users ([81ce49f](https://github.com/davidlaroche/paradox-api/commit/81ce49ff3b7210541bf95e5a7517fd3b4911d962))
* **goliath:** update discount tier logic and remove email sending for POM users ([1f09bc2](https://github.com/davidlaroche/paradox-api/commit/1f09bc2fee0c1af5354c39c2c669f796a4e18df4))
* **triads:** Implement triads module with CRUD operations and permissions ([679c503](https://github.com/davidlaroche/paradox-api/commit/679c503ac1a87a43dc7bc1274163f780ae054e53))
* **user:** call generateCodes method after user creation ([67e2189](https://github.com/davidlaroche/paradox-api/commit/67e2189d1f76d3edfe23132d76f6fdfec4546431))
* **whats-new, api:** Implement backend for What's New feature ([ef8c3ea](https://github.com/davidlaroche/paradox-api/commit/ef8c3ea336cfe6d94f4b27754180c45dc78981a7))
* **whats-new, api:** Implement backend for What's New feature ([17ff1df](https://github.com/davidlaroche/paradox-api/commit/17ff1dff81e8ad3ee7f411eff046c98ab5e79913))

# [1.3.0-beta.3](https://github.com/davidlaroche/paradox-api/compare/v1.3.0-beta.2...v1.3.0-beta.3) (2025-06-05)


### Features

* **billing:** add methods to retrieve invoice PDF link & payment source for subscriptions ([2ee0e2d](https://github.com/davidlaroche/paradox-api/commit/2ee0e2d049dc9386fe542277ac2113d81548280d))
* **cache:** implement custom cache key decorator and interceptor ([8780147](https://github.com/davidlaroche/paradox-api/commit/87801472f196a96808f402e472facb1455e4aa11))
* **helpers:** add centsToDecimal method for converting cents to decimal string ([c0649ce](https://github.com/davidlaroche/paradox-api/commit/c0649ce082812990ad40ae6dbf133b3a1a2160f3))
* **subscriptions:** add endpoint to retrieve subscription details with invoices ([18d3622](https://github.com/davidlaroche/paradox-api/commit/18d36223f180f7e7bd39c3f3a85f1aa8745e236b))
* **subscriptions:** add endpoints to list user subscriptions ([08ee55c](https://github.com/davidlaroche/paradox-api/commit/08ee55ce246cb68ff82a9c7262ffbb51f18e4663))
* **types:** add PaymentSourceInfo and SubscriptionInvoiceDetails interfaces ([cde1ae6](https://github.com/davidlaroche/paradox-api/commit/cde1ae609c1f12694bb7888f0e9faa029363db46))

# [1.3.0-beta.2](https://github.com/davidlaroche/paradox-api/compare/v1.3.0-beta.1...v1.3.0-beta.2) (2025-06-05)


### Features

* **user:** add phone number management functionality ([41e6bee](https://github.com/davidlaroche/paradox-api/commit/41e6bee23e941a7b9e60b6ab3f8c8b04952b39f4))

# [1.3.0-beta.1](https://github.com/davidlaroche/paradox-api/compare/v1.2.0...v1.3.0-beta.1) (2025-06-04)


### Bug Fixes

* **goliath:** conditionally send discount email based on POM status ([c484157](https://github.com/davidlaroche/paradox-api/commit/c48415728dce613af978eadd40ff74d07a92856b))
* **goliath:** update seedGoliathsCodes to use databaseService for user queries ([7ee12d1](https://github.com/davidlaroche/paradox-api/commit/7ee12d1e791240524d6265048c059f91f48dad6e))
* **product-offers:** update endpoint for listing all product offers ([5477c58](https://github.com/davidlaroche/paradox-api/commit/5477c587d7ecf92728e610233e6d40739f6b5470))
* **triads:** assign value to `sessions` in `TriadFactory` ([7ef5185](https://github.com/davidlaroche/paradox-api/commit/7ef5185260345c49061de1bb5d31dc547b26008b))
* **whats-new:** resolve toISOString error on update by re-fetching entity ([c6177f4](https://github.com/davidlaroche/paradox-api/commit/c6177f403392ae4ddc62d88b2dd9e32d8f3130e7))
* **whats-new:** resolve toISOString error on update by re-fetching entity ([0a54179](https://github.com/davidlaroche/paradox-api/commit/0a54179cf5eca0955d7920117ba496b6fa9511d7))


### Features

* **auth:** add `CurrentUserInterceptor` for user context retrieval ([3dd31c5](https://github.com/davidlaroche/paradox-api/commit/3dd31c55f420524dae955bc55f4853e3aa732773))
* **auth:** add `CurrentUserInterceptor` for user context retrieval ([e97b3cc](https://github.com/davidlaroche/paradox-api/commit/e97b3cce91a85f43fd057d3583142971173062f9))
* **course:** enhance course management with offers and managers handling ([caeb270](https://github.com/davidlaroche/paradox-api/commit/caeb27096ae3654330718f0709e0c1762de70e05))
* **goliaths:** add seedGoliathsCodes method to initialize discount codes for users ([81ce49f](https://github.com/davidlaroche/paradox-api/commit/81ce49ff3b7210541bf95e5a7517fd3b4911d962))
* **goliath:** update discount tier logic and remove email sending for POM users ([1f09bc2](https://github.com/davidlaroche/paradox-api/commit/1f09bc2fee0c1af5354c39c2c669f796a4e18df4))
* **portal-session:** add portal session management functionality ([8008d1d](https://github.com/davidlaroche/paradox-api/commit/8008d1dc458e3bd65d7e4ed2d052b63da86bb276))
* **triads:** Implement triads module with CRUD operations and permissions ([679c503](https://github.com/davidlaroche/paradox-api/commit/679c503ac1a87a43dc7bc1274163f780ae054e53))
* **user:** call generateCodes method after user creation ([67e2189](https://github.com/davidlaroche/paradox-api/commit/67e2189d1f76d3edfe23132d76f6fdfec4546431))
* **whats-new, api:** Implement backend for What's New feature ([ef8c3ea](https://github.com/davidlaroche/paradox-api/commit/ef8c3ea336cfe6d94f4b27754180c45dc78981a7))
* **whats-new, api:** Implement backend for What's New feature ([17ff1df](https://github.com/davidlaroche/paradox-api/commit/17ff1dff81e8ad3ee7f411eff046c98ab5e79913))

# [1.2.0-beta.10](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.9...v1.2.0-beta.10) (2025-06-03)


### Features

* **portal-session:** add portal session management functionality ([8008d1d](https://github.com/davidlaroche/paradox-api/commit/8008d1dc458e3bd65d7e4ed2d052b63da86bb276))

# [1.2.0-beta.9](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.8...v1.2.0-beta.9) (2025-06-02)


### Bug Fixes

* **goliath:** update seedGoliathsCodes to use databaseService for user queries ([7ee12d1](https://github.com/davidlaroche/paradox-api/commit/7ee12d1e791240524d6265048c059f91f48dad6e))
* **subscription:** remove redundant parameter from goliathService issueAndPush call ([85f3374](https://github.com/davidlaroche/paradox-api/commit/85f3374839971c701b8bd79c4cb5b821f8c14d6d))


### Features

* **goliaths:** add seedGoliathsCodes method to initialize discount codes for users ([81ce49f](https://github.com/davidlaroche/paradox-api/commit/81ce49ff3b7210541bf95e5a7517fd3b4911d962))
* **user:** call generateCodes method after user creation ([67e2189](https://github.com/davidlaroche/paradox-api/commit/67e2189d1f76d3edfe23132d76f6fdfec4546431))

# [1.2.0-beta.8](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.7...v1.2.0-beta.8) (2025-06-02)


### Bug Fixes

* **triads:** assign value to `sessions` in `TriadFactory` ([7ef5185](https://github.com/davidlaroche/paradox-api/commit/7ef5185260345c49061de1bb5d31dc547b26008b))
* **whats-new:** resolve toISOString error on update by re-fetching entity ([0a54179](https://github.com/davidlaroche/paradox-api/commit/0a54179cf5eca0955d7920117ba496b6fa9511d7))


### Features

* **auth:** add `CurrentUserInterceptor` for user context retrieval ([e97b3cc](https://github.com/davidlaroche/paradox-api/commit/e97b3cce91a85f43fd057d3583142971173062f9))
* **triads:** Implement triads module with CRUD operations and permissions ([679c503](https://github.com/davidlaroche/paradox-api/commit/679c503ac1a87a43dc7bc1274163f780ae054e53))
* **whats-new, api:** Implement backend for What's New feature ([17ff1df](https://github.com/davidlaroche/paradox-api/commit/17ff1dff81e8ad3ee7f411eff046c98ab5e79913))

# [1.2.0-beta.7](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.6...v1.2.0-beta.7) (2025-05-30)


### Bug Fixes

* **product-delivery:** fix Clerk invitation bug if already invited ([55b5186](https://github.com/davidlaroche/paradox-api/commit/55b51865d62c2398073d8c562bb455121ab4ef04))

# [1.2.0-beta.6](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.5...v1.2.0-beta.6) (2025-05-28)


### Bug Fixes

* **whats-new:** resolve toISOString error on update by re-fetching entity ([c6177f4](https://github.com/davidlaroche/paradox-api/commit/c6177f403392ae4ddc62d88b2dd9e32d8f3130e7))


### Features

* **auth:** add `CurrentUserInterceptor` for user context retrieval ([3dd31c5](https://github.com/davidlaroche/paradox-api/commit/3dd31c55f420524dae955bc55f4853e3aa732773))
* **whats-new, api:** Implement backend for What's New feature ([ef8c3ea](https://github.com/davidlaroche/paradox-api/commit/ef8c3ea336cfe6d94f4b27754180c45dc78981a7))

# [1.2.0-beta.5](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.4...v1.2.0-beta.5) (2025-05-27)


### Bug Fixes

* **exceptions:** enhance error message in `AllExceptionsFilter` for clarity ([520a880](https://github.com/davidlaroche/paradox-api/commit/520a880c990499216f1387a1f0fce4bfbb82786f))

## [1.1.3](https://github.com/davidlaroche/paradox-api/compare/v1.1.2...v1.1.3) (2025-05-27)


### Bug Fixes

* **product-plans:** increase default limit for product plans and prices queries to 500 ([78125a3](https://github.com/davidlaroche/paradox-api/commit/78125a30932b49be4bc3f5336cd0ab965ba80f61))

# [1.2.0-beta.4](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.3...v1.2.0-beta.4) (2025-05-27)


### Bug Fixes

* **product-plans:** increase default limit for product plans and prices queries to 500 ([78125a3](https://github.com/davidlaroche/paradox-api/commit/78125a30932b49be4bc3f5336cd0ab965ba80f61))

# [1.2.0-beta.3](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.2...v1.2.0-beta.3) (2025-05-27)


### Bug Fixes

* **goliath:** conditionally send discount email based on POM status ([058ac65](https://github.com/davidlaroche/paradox-api/commit/058ac65521ac41956ec0ab5724c86dd950900279))

## [1.1.2](https://github.com/davidlaroche/paradox-api/compare/v1.1.1...v1.1.2) (2025-05-26)


### Bug Fixes

* **goliath:** conditionally send discount email based on POM status ([058ac65](https://github.com/davidlaroche/paradox-api/commit/058ac65521ac41956ec0ab5724c86dd950900279))

# [1.2.0-beta.2](https://github.com/davidlaroche/paradox-api/compare/v1.2.0-beta.1...v1.2.0-beta.2) (2025-05-26)


### Bug Fixes

* **goliath:** conditionally send discount email based on POM status ([c484157](https://github.com/davidlaroche/paradox-api/commit/c48415728dce613af978eadd40ff74d07a92856b))

# [1.2.0-beta.1](https://github.com/davidlaroche/paradox-api/compare/v1.1.1...v1.2.0-beta.1) (2025-05-23)


### Bug Fixes

* **product-offers:** update endpoint for listing all product offers ([5477c58](https://github.com/davidlaroche/paradox-api/commit/5477c587d7ecf92728e610233e6d40739f6b5470))


### Features

* **course:** enhance course management with offers and managers handling ([caeb270](https://github.com/davidlaroche/paradox-api/commit/caeb27096ae3654330718f0709e0c1762de70e05))

## [1.1.1](https://github.com/davidlaroche/paradox-api/compare/v1.1.0...v1.1.1) (2025-05-23)


### Bug Fixes

* **webhooks:** remove duplicate transformation of bank transfer DTO ([1526b7e](https://github.com/davidlaroche/paradox-api/commit/1526b7ea6c34bfdb5b36cfc71f108ae73c89b2c2))

# [1.1.0](https://github.com/davidlaroche/paradox-api/compare/v1.0.0...v1.1.0) (2025-05-22)


### Features

* **course:** enhance course management with offers and managers handling ([fe8e875](https://github.com/davidlaroche/paradox-api/commit/fe8e8756d23164d95121e1272626c3d80718d654))
* **customerio:** integrate Customer.io service for discount email notifications ([6e832c2](https://github.com/davidlaroche/paradox-api/commit/6e832c263aef56492091fc5b642b143e42768545))
* **goliaths:** add code validation and improve discount code generation logic ([8f32987](https://github.com/davidlaroche/paradox-api/commit/8f32987011cb0a8cc9213bf45078b8d25703c9d6))
* **goliaths:** add Goliaths secrets schema and config retrieval ([946580c](https://github.com/davidlaroche/paradox-api/commit/946580cd6ea88b745cf82ab97a778a2bb0fa41d2))
* **subscription:** integrate GoliathService for subscription management and update issueAndPush logic ([2d855f6](https://github.com/davidlaroche/paradox-api/commit/2d855f65e5f8d6491a6424147538c32b004ff612))

# 1.0.0 (2025-05-20)


### Bug Fixes

* :adhesive_bandage: run access suspension cron once at 10AM ([4325344](https://github.com/davidlaroche/paradox-api/commit/4325344d5f5351ae3c0e32f2c798eb0166d8fd29))
* :bug: move deal to lost if a subscription is stopped ([790bb3a](https://github.com/davidlaroche/paradox-api/commit/790bb3a0adf527583bae415de080518af55dcfc6))
* :card_file_box: add `externalName` to `ProductOffer` entity ([864ddac](https://github.com/davidlaroche/paradox-api/commit/864ddacdeba3478d22098d7855bed12e7303f6b2))
* :card_file_box: mark `externalName` of `ProductOffer` entity non-null ([0d3c314](https://github.com/davidlaroche/paradox-api/commit/0d3c3145f069667bdb2a5ca3d60c2403f64601dc))
* :card_file_box: remove `@BeforeUpdate` hook from `CronLog` entity ([f30d5f2](https://github.com/davidlaroche/paradox-api/commit/f30d5f2df62c751e9e65f3567f145aedfe9eb78a))
* :goal_net: catch errors in `updateDealProperties` in HS module ([b08906f](https://github.com/davidlaroche/paradox-api/commit/b08906f3d1bea85ff91a4df0ab7df42552a4e050))
* :zap: manually increment `retryCount` for crons ([df9866e](https://github.com/davidlaroche/paradox-api/commit/df9866e017b38a12ccb38bc678e2007eb692322b))
* :zap: remove user entity & related modules & services ([7a2010e](https://github.com/davidlaroche/paradox-api/commit/7a2010e75573f856b881d510fcbd28d69c0239b6))
* :zap: run `accessSuspensionCron()` only once at 10AM ([1da2a7c](https://github.com/davidlaroche/paradox-api/commit/1da2a7ce6851551046bd0f9198916c745e08ca62))
* **api:** :bug: pass proper payload to `delegateHandler` for CB events ([2631abb](https://github.com/davidlaroche/paradox-api/commit/2631abb6f1646af8202ae380f5c5a47bf325acba))
* **api:** :zap: implement endpoint for updating offer's accesses ([78cf6c5](https://github.com/davidlaroche/paradox-api/commit/78cf6c5ad012a68492312c6a4c8dfeb488224096))
* **AppConfigService:** change condition ([89262d9](https://github.com/davidlaroche/paradox-api/commit/89262d995805daf9f03474e937b269fce31387b0))
* **ci:** :closed_lock_with_key: add circle v2 API keys to Git workflows ([5ebfe2b](https://github.com/davidlaroche/paradox-api/commit/5ebfe2ba54c8a6a1f05e6e71b7f91922b3f2586c))
* **controller:** correct controller path for product offers ([d7b6d4d](https://github.com/davidlaroche/paradox-api/commit/d7b6d4dda951b3c14db6cc2edc8b96dcd782663d))
* **controller:** correct controller path for product offers ([57cd1e9](https://github.com/davidlaroche/paradox-api/commit/57cd1e989b539e0e72b22da0fd7711a0d1b3b55a))
* **controllers:** added new controllers to the routing and user listing endpoint needed by Sirius ([3fc418d](https://github.com/davidlaroche/paradox-api/commit/3fc418d0d6f88d7ffa79053fa0ab333ec782a90a))
* **controller:** update endpoint path for fetching offers by chargebeeId ([480d251](https://github.com/davidlaroche/paradox-api/commit/480d251b6c97def426fcb845ed35cdbefebd000d))
* **crons:** :bug: improve webhooks & webhook crons processing ([397ba9a](https://github.com/davidlaroche/paradox-api/commit/397ba9ab59b046b3d555610cc6faac30e6563db4))
* **crons:** :bug: use correct path & name for CSV in crons service ([62a9883](https://github.com/davidlaroche/paradox-api/commit/62a9883c63d90ec97d7deaf6580b244f37ef2ba4))
* **crons:** :zap: disable cron jobs in local environment ([df50882](https://github.com/davidlaroche/paradox-api/commit/df50882615c10f2b7d9b5682be84c1d377551736))
* **customer-access:** correct email reference in query ([5baaaca](https://github.com/davidlaroche/paradox-api/commit/5baaaca50cefddbc64a84a3e05c035ed7f23ee33))
* **customer:** :bug: return empty `CustomerChargebeeInfo` is `businessEntityId` is missing ([d1e0496](https://github.com/davidlaroche/paradox-api/commit/d1e04966d6ee036a1eab41ca9cfd8276e310ad8e))
* **db:** :bug: add missing dependency `BankTransferModule` to `WebhooksUseCasesModule` ([92e341c](https://github.com/davidlaroche/paradox-api/commit/92e341c7c0167abcbf58fded5c8e188e5948b4c4))
* **db:** :bug: fix issues with `occuredAt` column of webhooks ([fcec614](https://github.com/davidlaroche/paradox-api/commit/fcec614d722c61d6d67bc8d934e2c1910500e2f6))
* **db:** :bug: make `chargebeeId` unique for all CB entities ([f81dbae](https://github.com/davidlaroche/paradox-api/commit/f81dbae20b5fc1e139f3566073327705546477b5))
* **db:** :bug: make `senderWebhookId` unique for webhooks table ([a66ff26](https://github.com/davidlaroche/paradox-api/commit/a66ff2604bb9603318de1fb649b7d585a9f865bf))
* **invoice:** :bug: correctly update subscription ststus fields on invoice update ([1710aa2](https://github.com/davidlaroche/paradox-api/commit/1710aa24da7df0a5f8859d0fec93f0f2e6654e03))
* **jwt-permissions:** log warning for missing client name in request ([8e59c9d](https://github.com/davidlaroche/paradox-api/commit/8e59c9d4c9ef322beeb26531508657f0f7ff62b3))
* **migrations:** revert changes in `user_access_purchases_view` ([e409b4d](https://github.com/davidlaroche/paradox-api/commit/e409b4da3f032180bc839c37316628217e195000))
* **migrations:** standardize data structure in user_access_purchases_view ([661c167](https://github.com/davidlaroche/paradox-api/commit/661c16794cc2f2cf039a0589d97fb3122265b9ec))
* module import path not resolving, should we specify a path alias in TS? ([8312964](https://github.com/davidlaroche/paradox-api/commit/8312964d09657f89f15376cf3c9db8e4f179767c))
* naming convention ([c7289df](https://github.com/davidlaroche/paradox-api/commit/c7289df5731093debdf03aa4e5cdba266b49af1a))
* nestJS test module is not able to resolve stripe provider ([7b53b0d](https://github.com/davidlaroche/paradox-api/commit/7b53b0d1dde71030844fe30c8597389bc691e9c2))
* **schema:** :card_file_box: add default value `0` to `amountUnused` in `Transaction` ([7b3425b](https://github.com/davidlaroche/paradox-api/commit/7b3425be1f8c97b12d3929ba2d6ba9a59733820a))
* **schema:** :card_file_box: make `exchangeRate` of `Transaction` `nullable` ([04cf4c6](https://github.com/davidlaroche/paradox-api/commit/04cf4c6718b35774e1c92b318657edf0773926c7))
* **schema:** :card_file_box: make `status` of `BankTransfer` `NOT NULL` ([1daae8b](https://github.com/davidlaroche/paradox-api/commit/1daae8b6e32370ec045c69e0171ebc262b7edba7))
* **schema:** :card_file_box: update all entities by marking certain fields optional ([bcb0e4f](https://github.com/davidlaroche/paradox-api/commit/bcb0e4f9d45fdd88c3bb37eaa8932ecaea425cb0))
* **schema:** :card_file_box: update implementation of all DB tables ([c5f15e2](https://github.com/davidlaroche/paradox-api/commit/c5f15e28f10143d3e78b66af174a54288dd0aba2))
* **service:** :bug: use `0` if `retryCount` is undefined ([e7fd791](https://github.com/davidlaroche/paradox-api/commit/e7fd7912c2c1a8f7285ddff2e165621ac9850149))
* **service:** :bug: use `string` for `id` in `CronLog` table ([cdd0da9](https://github.com/davidlaroche/paradox-api/commit/cdd0da93c4de4e17dadeac7596f38491edb38555))
* **subscription:** :bug: `Math.floor()` `amountToDiscount` in `applyDiscountToAmountRemaining()` ([48066ae](https://github.com/davidlaroche/paradox-api/commit/48066ae52ed97b26c4f7f8e638806fb8a96dc1ad))
* **subscription:** :bug: fix query in subscription export use-case ([e0d3521](https://github.com/davidlaroche/paradox-api/commit/e0d3521127df03cb63a51cbd5b6237abd8dd5c41))
* **subscription:** add next billing date to subscription updates ([7c3b5e8](https://github.com/davidlaroche/paradox-api/commit/7c3b5e85909e6af7bbadc4a5c65dc13c117c0810))
* **subscription:** only sync downsell with HS if `crmId` present ([bf871fd](https://github.com/davidlaroche/paradox-api/commit/bf871fd25acdc6ced2bab081d8ac40a5b2eeca9b))
* **subscription:** update subscription use cases to use `success` instead of `itemUpdated` ([3070f21](https://github.com/davidlaroche/paradox-api/commit/3070f21432442d1e79155fb52d939d50b83c9ba2))
* **table:** update foreign key constraint name in `CourseTable` ([c635a12](https://github.com/davidlaroche/paradox-api/commit/c635a1241c13ee85e65938da481cc96e90d19abc))
* update Dockerfile and staging deploy workflow to use correct secret syntax for .npmrc ([8c79af6](https://github.com/davidlaroche/paradox-api/commit/8c79af6b8a82b9adda473234ceb46bf4aedbc6be))
* **use-cases:** fix `getUniqueBillingCycles` in `ProductPlanPriceUseCases` ([a2cf25d](https://github.com/davidlaroche/paradox-api/commit/a2cf25d4ddaaab9edd08927aab1162f4f74e0e7f))
* **usecase:** :bug: use correct time value for `receivedAt` in `WebhookUseCases` ([8c65426](https://github.com/davidlaroche/paradox-api/commit/8c65426e14f2ccd6f2a491b2e5abe764f6e53844))
* **usecases:** :bug: add handler for `transaction_created` CB events ([cf8d6ba](https://github.com/davidlaroche/paradox-api/commit/cf8d6ba61301c03d8b9af2a5d07137b6d7959d9c))
* **usecases:** :bug: add missing `ProductOffer` import ([77c402b](https://github.com/davidlaroche/paradox-api/commit/77c402b4395d6c3cb86a63b0528a683f49c46143))
* **usecases:** :bug: add missing enum imports ([148a33c](https://github.com/davidlaroche/paradox-api/commit/148a33c356b845464cd3c0554a127608f148079e))
* **usecases:** :bug: add missing usecase `updateAccessConfig()` ([6ced3b7](https://github.com/davidlaroche/paradox-api/commit/6ced3b7b2cb745dd0f191a3c44f7e25bbb9821c6))
* **usecases:** :bug: add missing webhook handler for bank transfers ([5bd1a9d](https://github.com/davidlaroche/paradox-api/commit/5bd1a9d747bd759138164c2a1284a7467b9170f9))
* **usecases:** :bug: always return a customer from `createOrInvite` ([80cfc57](https://github.com/davidlaroche/paradox-api/commit/80cfc5793d4195074e4b22d5c724c713c97cb2af))
* **usecases:** :bug: always sync a deal if `syncedDeal?.id` exists ([4472cce](https://github.com/davidlaroche/paradox-api/commit/4472ccee9e9861ff9ec99d9e54bd50291b197929))
* **usecases:** :bug: call `sendSubscriptionToMake` only in `production` ([3830be2](https://github.com/davidlaroche/paradox-api/commit/3830be20db3933b9956d6491b8fad02dd4e49c15))
* **usecases:** :bug: disable searching an offer based on `offerName` in `getOfferDetails()` ([812ebba](https://github.com/davidlaroche/paradox-api/commit/812ebbadd49f6227914a861b1d908f7fd5d79620))
* **usecases:** :bug: filter `accessesToSuspend` to exclude community accesses ([dad05f7](https://github.com/davidlaroche/paradox-api/commit/dad05f75231b27cba77611a6b3ff3791d421f025))
* **usecases:** :bug: fix ts path issue leading to invalid dependencies ([ed34a08](https://github.com/davidlaroche/paradox-api/commit/ed34a08e70e2dfc979a77aa7139cbad2ce1f0b2e))
* **usecases:** :bug: improve check for `cancellationReasonCode` in `getOrderStatus()` ([96b6298](https://github.com/davidlaroche/paradox-api/commit/96b62985bdc214ae83a2c4153deaaf43adfe3326))
* **usecases:** :bug: include `retryCount` in `ReprocessCronJobDTO` ([a8b69ce](https://github.com/davidlaroche/paradox-api/commit/a8b69ce1e36263b129e7e2b378539e85950a2b18))
* **usecases:** :bug: increase default `limit` to `500` in `searchAll` for offers ([99c1eb8](https://github.com/davidlaroche/paradox-api/commit/99c1eb878a0700962a152f895322996e0acc8c47))
* **usecases:** :bug: only update specific fields in `resyncWithChargebee()` ([5c41798](https://github.com/davidlaroche/paradox-api/commit/5c41798fbbf858641ff69989bb344f641a818378))
* **usecases:** :bug: remove duplicate check for existing Circle Space members ([63017ad](https://github.com/davidlaroche/paradox-api/commit/63017ad6ebc0e0e0da836beb387b9fe396ed214e))
* **usecases:** :bug: remove duplicate handling of webhook events ([623487f](https://github.com/davidlaroche/paradox-api/commit/623487faf6d1a2d14cca8a215633caed5f29e660))
* **usecases:** :bug: rewrite `getWebhookPayload()` in `ChargebeeBillingService` ([e0145ba](https://github.com/davidlaroche/paradox-api/commit/e0145bacf61daef9467b7373934cf74ebfa49537))
* **usecases:** :bug: set `withProductDelivery` to `true` if access config is set ([b114fff](https://github.com/davidlaroche/paradox-api/commit/b114fff4ed6ecb8f30be6be0b0ef22049115ade6))
* **usecases:** :bug: update access status only if successful ([8d8e39e](https://github.com/davidlaroche/paradox-api/commit/8d8e39e8b303f1a473d3603d12e8e2ee45c8e015))
* **usecases:** :goal_net: catch hubspot sync errors ([11e1031](https://github.com/davidlaroche/paradox-api/commit/11e1031160c2f03226795e9a4cab64a28d368865))
* **usecases:** :zap: avoid reprocessing same webhook ([724b916](https://github.com/davidlaroche/paradox-api/commit/724b9166e6822c7fac79eafd89e120607dfd99b9))
* **usecases:** :zap: increase default limit of lisitng products to `500` ([3c6fb20](https://github.com/davidlaroche/paradox-api/commit/3c6fb205ba3d7118b55974281bacb4f86b5123d3))
* **usecases:** :zap: remove `CHARGEBEE` prefix from CB webhook IDs ([e5a92c4](https://github.com/davidlaroche/paradox-api/commit/e5a92c4311175938097c78ce3ce7772ba4fa48ae))
* **usecases:** :zap: update `metaData` if `handleFailedCustomerAccess()` succeeds ([402cef7](https://github.com/davidlaroche/paradox-api/commit/402cef722e63b8b88351152ee4c0eaa1449176a9))
* **usecases:** :zap: use `findOrCreate` for CB webhook entities creation ([0f8f3e9](https://github.com/davidlaroche/paradox-api/commit/0f8f3e982fbffbaeb9ba4e3aa50b6ee06687df31))
* **usecases:** 🐛 avoid throwing if user already enrolled in course ([cdd7254](https://github.com/davidlaroche/paradox-api/commit/cdd7254c892490cfcf456a4c30353b326d3f974b))
* **usecases:** refactor subscription order status calculation logic ([d2e3e82](https://github.com/davidlaroche/paradox-api/commit/d2e3e824394db4b27dd8c42d87c2a38a5b0ae46c))
* **user:** update `metadata` to `metaData` in `UserEntity` to match `CustomerTable` schema ([fc32f07](https://github.com/davidlaroche/paradox-api/commit/fc32f0778453543d8bfc519d8b5d1311c140f3ce))


### Features

* :card_file_box: add `retryCount` to `CronLog` entity ([360316a](https://github.com/davidlaroche/paradox-api/commit/360316a9627a7f954532c9c36741faed82dd342a))
* :passport_control: add HTTP requests support from `px-app` client ([3872bbd](https://github.com/davidlaroche/paradox-api/commit/3872bbdfa2aa44a8da7176ab481f0d1cd503cc59))
* :sparkles: implement `GET: /offers/list` endpoint ([ca8e38c](https://github.com/davidlaroche/paradox-api/commit/ca8e38c9f2c7cac33004788475b0c4832cc273e3))
* :sparkles: implement retryCount check & Slack notifications for crons ([043f4df](https://github.com/davidlaroche/paradox-api/commit/043f4df2caa7048525c31e017351285c373f4e9a))
* :sparkles: publish offer updates to KV store ([68b345d](https://github.com/davidlaroche/paradox-api/commit/68b345d5c6880b9d0932ee75897860bbcfbc378a))
* **access:** add user access details endpoint and migration for user access purchases view ([4728db9](https://github.com/davidlaroche/paradox-api/commit/4728db99edf357da22c7de8e93c90fa87136f3c1))
* **access:** add user access details endpoint and migration for user access purchases view ([ab5891f](https://github.com/davidlaroche/paradox-api/commit/ab5891fd45d902376ee730d97480124612f89fa0))
* **api:** :bug: prefix `id` of Clerk & Bank Transfer webhooks ([9037775](https://github.com/davidlaroche/paradox-api/commit/903777508748dd1588286c62f7fb3b718cf34191))
* **api:** :sparkles: implement `POST: subscriptions/resync/:chargebeeId` endpoint ([fd69b91](https://github.com/davidlaroche/paradox-api/commit/fd69b91b819a7383fdec84af8794e4535d30b3eb))
* **auth:** implement JWT permissions guard and update controllers ([71d51b6](https://github.com/davidlaroche/paradox-api/commit/71d51b6d19653e81b5df5c2ac301647f7f9d35ca))
* **cohort, course, role:** implement controllers & use cases for cohort, course, & role management ([bf4f16f](https://github.com/davidlaroche/paradox-api/commit/bf4f16f2b8bca925461ba6f6a57f0c6279e049cc))
* **config:** add secret loading validation and allSecretsLoaded method ([841aeb5](https://github.com/davidlaroche/paradox-api/commit/841aeb5c529033b637f46d2e2a750307da7fa85f))
* **config:** integrate Infisical for secret management & refactor configuration handling ([1a449e6](https://github.com/davidlaroche/paradox-api/commit/1a449e6163e0bb2a1d71f0607c027bd22bcbe02e))
* **controllers:** split product controller into entity specific controllers ([f6924f3](https://github.com/davidlaroche/paradox-api/commit/f6924f3b1846541c1633738925782be26eb250c4))
* **cron:** implement reprocessing of failed HubSpot syncs ([3c9c685](https://github.com/davidlaroche/paradox-api/commit/3c9c685f5fc2e796f2f8f5ab04e8ad73aab9542d))
* **crons:** :zap: use CSV for overdue subscription notification ([c4441c7](https://github.com/davidlaroche/paradox-api/commit/c4441c7b189319bbb4aaa7c39184b8ad8e5f09aa))
* **customers:** refactor email handling in CustomersController and add validation DTO ([8247fb5](https://github.com/davidlaroche/paradox-api/commit/8247fb53ff12ad892566e14cd58bf7351baa655e))
* **customers:** refactor email handling in CustomersController and add validation DTO ([0876dd4](https://github.com/davidlaroche/paradox-api/commit/0876dd434e2a51cb9cc0cbd41dfbed037c3c6a5a))
* **database:** :sparkles: add getDataSource method to IDataServices and PostgresDataServices ([52152cb](https://github.com/davidlaroche/paradox-api/commit/52152cb6a7207e6d658e3ef04b174687527e8d31))
* **database:** add cohort, course, role, user, & user profile tables & services ([a96456d](https://github.com/davidlaroche/paradox-api/commit/a96456d0c4b8a5619344c2d1a8e212ff0e118793))
* **db:** :card_file_box: add `FAILED_HUBSPOT_SYNC` to `CronLogType` ([4a033e5](https://github.com/davidlaroche/paradox-api/commit/4a033e521d7fec77dc581c04aea33b0d9f58478e))
* **dtos:** enhance CreateProductDTO and UpdateProductDTO with conditional validation ([bf2316f](https://github.com/davidlaroche/paradox-api/commit/bf2316fe23859e63b2b9fc7a33207222e98fb7f6))
* **dtos:** migrate cohort, course, role, & user DTOs from `vega` ([d820cb6](https://github.com/davidlaroche/paradox-api/commit/d820cb6a7d07e968331bbf5e0b6fbfa1414113f3))
* **entities:** add M-M relation between `ProductOffer` & `CourseEntity` ([d4eecc7](https://github.com/davidlaroche/paradox-api/commit/d4eecc7bb4905276c9223c78ad905f148d7acb07))
* **entities:** enforce required fields in ProductOffer entity ([9252dfd](https://github.com/davidlaroche/paradox-api/commit/9252dfdddc44382ea10db2294c9c84f9bfdadb3a))
* **entities:** migrate entities from vega ([e1ed82d](https://github.com/davidlaroche/paradox-api/commit/e1ed82d3a439288249163a512e73d4af3f901f8f))
* **goliaths:** :sparkles: add Goliaths module and controller ([a7f4af3](https://github.com/davidlaroche/paradox-api/commit/a7f4af3b21677b2fe6ee63fef8cc3e9b00f22182))
* **goliaths:** :sparkles: update isPOMStudent method to use direct database query ([90f85ff](https://github.com/davidlaroche/paradox-api/commit/90f85ffaed20c285bb6edbfe0ee324cd4e287c30))
* **hubspot:** enhance createMissingMetadataProperties to return response status and error details ([1fc15a4](https://github.com/davidlaroche/paradox-api/commit/1fc15a431f93871ee404b7cd559a333f5c4df81e))
* **migrations:** enhance user_access_purchases_view with customer access data ([6821bdc](https://github.com/davidlaroche/paradox-api/commit/6821bdc7390c03deff7077d7885f4a0f935f8ace))
* **migrations:** enhance user_access_purchases_view with customer access data ([4a05dfe](https://github.com/davidlaroche/paradox-api/commit/4a05dfe235d3e3e0279f925c5a8f459348fb9549))
* **migrations:** move migrations to new folder & add vega migration ([4740c58](https://github.com/davidlaroche/paradox-api/commit/4740c581e27bcd15eb9cbf2b1cf6ee7ceda59306))
* **monitoring:** implement comprehensive error handling and Sentry integration ([3d58aba](https://github.com/davidlaroche/paradox-api/commit/3d58abaa8b5ce0d9262e5b033453027e3590447e))
* **products:** add endpoint to retrieve product offer and checkout page by chargebeeId ([12026f0](https://github.com/davidlaroche/paradox-api/commit/12026f0de9a03d62a059ab94564aa8c6986aa577))
* **redis:** refactor Redis publisher and introduce Redis clients module ([2dc0537](https://github.com/davidlaroche/paradox-api/commit/2dc05378f4375efad0cb8ce7b54786f20054b7e0))
* **refactor:** replace `PlanAndOfferStatus` & `ProductOfferVersion` with `hermes` versions ([ed0e298](https://github.com/davidlaroche/paradox-api/commit/ed0e2984faa6caf4a5fa768cf60401a9cae66ffe))
* **repository:** add search functionality to generic repository ([6b463e3](https://github.com/davidlaroche/paradox-api/commit/6b463e3cb8deb6e73e15dc9da5fc1c4269aed6c9))
* **role:** Implement search for role listing and fix Redis publishing ([9125225](https://github.com/davidlaroche/paradox-api/commit/912522507b9e92bcb8128e5010ae7790f5cf9c6a))
* **service:** :sparkles: add `offer_id` and `offer_name` to Hubspot deal sync ([ad2ae6a](https://github.com/davidlaroche/paradox-api/commit/ad2ae6a8a98703b90e51c157929b38d76aa80442))
* **slack:** :sparkles: add method to send messages with attachments ([580e03b](https://github.com/davidlaroche/paradox-api/commit/580e03b9a2adba39bd8e8c51b93048c7b22df192))
* **stats:** add StatsController and StatsUseCases for platform statistics ([13d1c38](https://github.com/davidlaroche/paradox-api/commit/13d1c38cd8efd98a956dbbf12b0a67370b4c6ef9))
* **subscriptions:** add endpoint to import subscription data from JSON ([4ba8b65](https://github.com/davidlaroche/paradox-api/commit/4ba8b6527336f1769246f80e587391e6c72ff450))
* **subscriptions:** add endpoint to retrieve webhook events by chargebeeId ([f5fb3dc](https://github.com/davidlaroche/paradox-api/commit/f5fb3dc9e7ed20e2f0ca8c48c0cf7014de6e8d53))
* **subscriptions:** enhance importSubscriptionData method for comprehensive data handling ([2b10d9f](https://github.com/davidlaroche/paradox-api/commit/2b10d9f694bf53b7a06d87323b3dc982dff2fabe))
* **subscriptions:** restore import endpoint for subscription data ([ac99c39](https://github.com/davidlaroche/paradox-api/commit/ac99c39083ef985cde24a14afe6291eb6e73cd09))
* **usecases:** :sparkles: add `offer.id` & `offer.name` to subscription export ([e0435d8](https://github.com/davidlaroche/paradox-api/commit/e0435d8035a599ef17e8adfcabeb9c3a56ac29af))
* **usecases:** :sparkles: add endpoints for granting/revoking accesses ([eb3f56a](https://github.com/davidlaroche/paradox-api/commit/eb3f56a84eeb415d45c298b7029ed1b996f96efc))
* **usecases:** :sparkles: implement `resyncWithChargebee()` in `SubscriptionUseCases` ([ef8857a](https://github.com/davidlaroche/paradox-api/commit/ef8857a116136521776f94b076864b33510bc569))
* **usecases:** :sparkles: implement `Webhook` table and basic use-cases ([0a11027](https://github.com/davidlaroche/paradox-api/commit/0a110279ddb3deefe7da0f930980b15efb34964f))
* **usecases:** :sparkles: implement list & get one endpoints for `Webhooks` ([ff6e158](https://github.com/davidlaroche/paradox-api/commit/ff6e158eb33982176875345a510bf88b09c369fc))
* **usecases:** add support for metadata in customer creation process ([5172970](https://github.com/davidlaroche/paradox-api/commit/51729704536b7683d7014ba6010a066900e0422e))
* **usecases:** make email queries case insensitive using lower() for safety in user access details and POM student checks ([25afd93](https://github.com/davidlaroche/paradox-api/commit/25afd9392658dac23399890ae481cc36b25a3292))
* **user:** refactor customer management to user management ([00da30f](https://github.com/davidlaroche/paradox-api/commit/00da30fea9e47f6b901ff7284f1e100a7f3af640))
* **webhooks:** implement centralized webhook handling with retry mechanism ([a086000](https://github.com/davidlaroche/paradox-api/commit/a0860002a08f809266677db9043e249dc8eb0ddd))


### Performance Improvements

* :construction: `wip` improved Sentry integration & custom exceptions ([5382c54](https://github.com/davidlaroche/paradox-api/commit/5382c54dc48d74056a1391853b2d6c4d0e983018))

# [1.0.0-beta.14](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.13...v1.0.0-beta.14) (2025-05-19)


### Bug Fixes

* **subscription:** add next billing date to subscription updates ([7c3b5e8](https://github.com/davidlaroche/paradox-api/commit/7c3b5e85909e6af7bbadc4a5c65dc13c117c0810))
* **subscription:** only sync downsell with HS if `crmId` present ([bf871fd](https://github.com/davidlaroche/paradox-api/commit/bf871fd25acdc6ced2bab081d8ac40a5b2eeca9b))

# [1.0.0-beta.13](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.12...v1.0.0-beta.13) (2025-05-18)


### Features

* **cron:** implement reprocessing of failed HubSpot syncs ([3c9c685](https://github.com/davidlaroche/paradox-api/commit/3c9c685f5fc2e796f2f8f5ab04e8ad73aab9542d))

# [1.0.0-beta.12](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.11...v1.0.0-beta.12) (2025-05-15)


### Features

* **course:** enhance course management with offers and managers handling ([caeb270](https://github.com/davidlaroche/paradox-api/commit/caeb27096ae3654330718f0709e0c1762de70e05))

# [1.0.0-beta.11](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.10...v1.0.0-beta.11) (2025-05-15)


### Features

* **dtos:** enhance CreateProductDTO and UpdateProductDTO with conditional validation ([bf2316f](https://github.com/davidlaroche/paradox-api/commit/bf2316fe23859e63b2b9fc7a33207222e98fb7f6))

# [1.0.0-beta.10](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.9...v1.0.0-beta.10) (2025-05-15)


### Bug Fixes

* **use-cases:** fix `getUniqueBillingCycles` in `ProductPlanPriceUseCases` ([a2cf25d](https://github.com/davidlaroche/paradox-api/commit/a2cf25d4ddaaab9edd08927aab1162f4f74e0e7f))

# [1.0.0-beta.9](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.8...v1.0.0-beta.9) (2025-05-15)


### Bug Fixes

* **product-offers:** update endpoint for listing all product offers ([5477c58](https://github.com/davidlaroche/paradox-api/commit/5477c587d7ecf92728e610233e6d40739f6b5470))

# [1.0.0-beta.8](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.7...v1.0.0-beta.8) (2025-05-14)


### Features

* **role:** Implement search for role listing and fix Redis publishing ([9125225](https://github.com/davidlaroche/paradox-api/commit/912522507b9e92bcb8128e5010ae7790f5cf9c6a))

# [1.0.0-beta.7](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.6...v1.0.0-beta.7) (2025-05-14)


### Bug Fixes

* **controller:** correct controller path for product offers ([d7b6d4d](https://github.com/davidlaroche/paradox-api/commit/d7b6d4dda951b3c14db6cc2edc8b96dcd782663d))
* **controller:** update endpoint path for fetching offers by chargebeeId ([480d251](https://github.com/davidlaroche/paradox-api/commit/480d251b6c97def426fcb845ed35cdbefebd000d))

# [1.0.0-beta.6](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.5...v1.0.0-beta.6) (2025-05-14)


### Bug Fixes

* **controller:** correct controller path for product offers ([57cd1e9](https://github.com/davidlaroche/paradox-api/commit/57cd1e989b539e0e72b22da0fd7711a0d1b3b55a))

# [1.0.0-beta.5](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.4...v1.0.0-beta.5) (2025-05-14)


### Features

* **controllers:** split product controller into entity specific controllers ([f6924f3](https://github.com/davidlaroche/paradox-api/commit/f6924f3b1846541c1633738925782be26eb250c4))

# [1.0.0-beta.4](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.3...v1.0.0-beta.4) (2025-05-14)


### Bug Fixes

* **controllers:** added new controllers to the routing and user listing endpoint needed by Sirius ([3fc418d](https://github.com/davidlaroche/paradox-api/commit/3fc418d0d6f88d7ffa79053fa0ab333ec782a90a))

# [1.0.0-beta.3](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.2...v1.0.0-beta.3) (2025-05-13)


### Bug Fixes

* **customer-access:** correct email reference in query ([5baaaca](https://github.com/davidlaroche/paradox-api/commit/5baaaca50cefddbc64a84a3e05c035ed7f23ee33))
* **jwt-permissions:** log warning for missing client name in request ([8e59c9d](https://github.com/davidlaroche/paradox-api/commit/8e59c9d4c9ef322beeb26531508657f0f7ff62b3))

# [1.0.0-beta.2](https://github.com/davidlaroche/paradox-api/compare/v1.0.0-beta.1...v1.0.0-beta.2) (2025-05-12)


### Bug Fixes

* update Dockerfile and staging deploy workflow to use correct secret syntax for .npmrc ([8c79af6](https://github.com/davidlaroche/paradox-api/commit/8c79af6b8a82b9adda473234ceb46bf4aedbc6be))

# 1.0.0-beta.1 (2025-05-12)


### Bug Fixes

* :adhesive_bandage: run access suspension cron once at 10AM ([4325344](https://github.com/davidlaroche/paradox-api/commit/4325344d5f5351ae3c0e32f2c798eb0166d8fd29))
* :bug: move deal to lost if a subscription is stopped ([790bb3a](https://github.com/davidlaroche/paradox-api/commit/790bb3a0adf527583bae415de080518af55dcfc6))
* :card_file_box: add `externalName` to `ProductOffer` entity ([864ddac](https://github.com/davidlaroche/paradox-api/commit/864ddacdeba3478d22098d7855bed12e7303f6b2))
* :card_file_box: mark `externalName` of `ProductOffer` entity non-null ([0d3c314](https://github.com/davidlaroche/paradox-api/commit/0d3c3145f069667bdb2a5ca3d60c2403f64601dc))
* :card_file_box: remove `@BeforeUpdate` hook from `CronLog` entity ([f30d5f2](https://github.com/davidlaroche/paradox-api/commit/f30d5f2df62c751e9e65f3567f145aedfe9eb78a))
* :goal_net: catch errors in `updateDealProperties` in HS module ([b08906f](https://github.com/davidlaroche/paradox-api/commit/b08906f3d1bea85ff91a4df0ab7df42552a4e050))
* :zap: manually increment `retryCount` for crons ([df9866e](https://github.com/davidlaroche/paradox-api/commit/df9866e017b38a12ccb38bc678e2007eb692322b))
* :zap: remove user entity & related modules & services ([7a2010e](https://github.com/davidlaroche/paradox-api/commit/7a2010e75573f856b881d510fcbd28d69c0239b6))
* :zap: run `accessSuspensionCron()` only once at 10AM ([1da2a7c](https://github.com/davidlaroche/paradox-api/commit/1da2a7ce6851551046bd0f9198916c745e08ca62))
* **api:** :bug: pass proper payload to `delegateHandler` for CB events ([2631abb](https://github.com/davidlaroche/paradox-api/commit/2631abb6f1646af8202ae380f5c5a47bf325acba))
* **api:** :zap: implement endpoint for updating offer's accesses ([78cf6c5](https://github.com/davidlaroche/paradox-api/commit/78cf6c5ad012a68492312c6a4c8dfeb488224096))
* **AppConfigService:** change condition ([89262d9](https://github.com/davidlaroche/paradox-api/commit/89262d995805daf9f03474e937b269fce31387b0))
* **ci:** :closed_lock_with_key: add circle v2 API keys to Git workflows ([5ebfe2b](https://github.com/davidlaroche/paradox-api/commit/5ebfe2ba54c8a6a1f05e6e71b7f91922b3f2586c))
* **crons:** :bug: improve webhooks & webhook crons processing ([397ba9a](https://github.com/davidlaroche/paradox-api/commit/397ba9ab59b046b3d555610cc6faac30e6563db4))
* **crons:** :bug: use correct path & name for CSV in crons service ([62a9883](https://github.com/davidlaroche/paradox-api/commit/62a9883c63d90ec97d7deaf6580b244f37ef2ba4))
* **crons:** :zap: disable cron jobs in local environment ([df50882](https://github.com/davidlaroche/paradox-api/commit/df50882615c10f2b7d9b5682be84c1d377551736))
* **customer:** :bug: return empty `CustomerChargebeeInfo` is `businessEntityId` is missing ([d1e0496](https://github.com/davidlaroche/paradox-api/commit/d1e04966d6ee036a1eab41ca9cfd8276e310ad8e))
* **db:** :bug: add missing dependency `BankTransferModule` to `WebhooksUseCasesModule` ([92e341c](https://github.com/davidlaroche/paradox-api/commit/92e341c7c0167abcbf58fded5c8e188e5948b4c4))
* **db:** :bug: fix issues with `occuredAt` column of webhooks ([fcec614](https://github.com/davidlaroche/paradox-api/commit/fcec614d722c61d6d67bc8d934e2c1910500e2f6))
* **db:** :bug: make `chargebeeId` unique for all CB entities ([f81dbae](https://github.com/davidlaroche/paradox-api/commit/f81dbae20b5fc1e139f3566073327705546477b5))
* **db:** :bug: make `senderWebhookId` unique for webhooks table ([a66ff26](https://github.com/davidlaroche/paradox-api/commit/a66ff2604bb9603318de1fb649b7d585a9f865bf))
* **invoice:** :bug: correctly update subscription ststus fields on invoice update ([1710aa2](https://github.com/davidlaroche/paradox-api/commit/1710aa24da7df0a5f8859d0fec93f0f2e6654e03))
* **migrations:** revert changes in `user_access_purchases_view` ([e409b4d](https://github.com/davidlaroche/paradox-api/commit/e409b4da3f032180bc839c37316628217e195000))
* **migrations:** standardize data structure in user_access_purchases_view ([661c167](https://github.com/davidlaroche/paradox-api/commit/661c16794cc2f2cf039a0589d97fb3122265b9ec))
* module import path not resolving, should we specify a path alias in TS? ([8312964](https://github.com/davidlaroche/paradox-api/commit/8312964d09657f89f15376cf3c9db8e4f179767c))
* naming convention ([c7289df](https://github.com/davidlaroche/paradox-api/commit/c7289df5731093debdf03aa4e5cdba266b49af1a))
* nestJS test module is not able to resolve stripe provider ([7b53b0d](https://github.com/davidlaroche/paradox-api/commit/7b53b0d1dde71030844fe30c8597389bc691e9c2))
* **schema:** :card_file_box: add default value `0` to `amountUnused` in `Transaction` ([7b3425b](https://github.com/davidlaroche/paradox-api/commit/7b3425be1f8c97b12d3929ba2d6ba9a59733820a))
* **schema:** :card_file_box: make `exchangeRate` of `Transaction` `nullable` ([04cf4c6](https://github.com/davidlaroche/paradox-api/commit/04cf4c6718b35774e1c92b318657edf0773926c7))
* **schema:** :card_file_box: make `status` of `BankTransfer` `NOT NULL` ([1daae8b](https://github.com/davidlaroche/paradox-api/commit/1daae8b6e32370ec045c69e0171ebc262b7edba7))
* **schema:** :card_file_box: update all entities by marking certain fields optional ([bcb0e4f](https://github.com/davidlaroche/paradox-api/commit/bcb0e4f9d45fdd88c3bb37eaa8932ecaea425cb0))
* **schema:** :card_file_box: update implementation of all DB tables ([c5f15e2](https://github.com/davidlaroche/paradox-api/commit/c5f15e28f10143d3e78b66af174a54288dd0aba2))
* **service:** :bug: use `0` if `retryCount` is undefined ([e7fd791](https://github.com/davidlaroche/paradox-api/commit/e7fd7912c2c1a8f7285ddff2e165621ac9850149))
* **service:** :bug: use `string` for `id` in `CronLog` table ([cdd0da9](https://github.com/davidlaroche/paradox-api/commit/cdd0da93c4de4e17dadeac7596f38491edb38555))
* **subscription:** :bug: `Math.floor()` `amountToDiscount` in `applyDiscountToAmountRemaining()` ([48066ae](https://github.com/davidlaroche/paradox-api/commit/48066ae52ed97b26c4f7f8e638806fb8a96dc1ad))
* **subscription:** :bug: fix query in subscription export use-case ([e0d3521](https://github.com/davidlaroche/paradox-api/commit/e0d3521127df03cb63a51cbd5b6237abd8dd5c41))
* **subscription:** update subscription use cases to use `success` instead of `itemUpdated` ([3070f21](https://github.com/davidlaroche/paradox-api/commit/3070f21432442d1e79155fb52d939d50b83c9ba2))
* **table:** update foreign key constraint name in `CourseTable` ([c635a12](https://github.com/davidlaroche/paradox-api/commit/c635a1241c13ee85e65938da481cc96e90d19abc))
* **usecase:** :bug: use correct time value for `receivedAt` in `WebhookUseCases` ([8c65426](https://github.com/davidlaroche/paradox-api/commit/8c65426e14f2ccd6f2a491b2e5abe764f6e53844))
* **usecases:** :bug: add handler for `transaction_created` CB events ([cf8d6ba](https://github.com/davidlaroche/paradox-api/commit/cf8d6ba61301c03d8b9af2a5d07137b6d7959d9c))
* **usecases:** :bug: add missing `ProductOffer` import ([77c402b](https://github.com/davidlaroche/paradox-api/commit/77c402b4395d6c3cb86a63b0528a683f49c46143))
* **usecases:** :bug: add missing enum imports ([148a33c](https://github.com/davidlaroche/paradox-api/commit/148a33c356b845464cd3c0554a127608f148079e))
* **usecases:** :bug: add missing usecase `updateAccessConfig()` ([6ced3b7](https://github.com/davidlaroche/paradox-api/commit/6ced3b7b2cb745dd0f191a3c44f7e25bbb9821c6))
* **usecases:** :bug: add missing webhook handler for bank transfers ([5bd1a9d](https://github.com/davidlaroche/paradox-api/commit/5bd1a9d747bd759138164c2a1284a7467b9170f9))
* **usecases:** :bug: always return a customer from `createOrInvite` ([80cfc57](https://github.com/davidlaroche/paradox-api/commit/80cfc5793d4195074e4b22d5c724c713c97cb2af))
* **usecases:** :bug: always sync a deal if `syncedDeal?.id` exists ([4472cce](https://github.com/davidlaroche/paradox-api/commit/4472ccee9e9861ff9ec99d9e54bd50291b197929))
* **usecases:** :bug: call `sendSubscriptionToMake` only in `production` ([3830be2](https://github.com/davidlaroche/paradox-api/commit/3830be20db3933b9956d6491b8fad02dd4e49c15))
* **usecases:** :bug: disable searching an offer based on `offerName` in `getOfferDetails()` ([812ebba](https://github.com/davidlaroche/paradox-api/commit/812ebbadd49f6227914a861b1d908f7fd5d79620))
* **usecases:** :bug: filter `accessesToSuspend` to exclude community accesses ([dad05f7](https://github.com/davidlaroche/paradox-api/commit/dad05f75231b27cba77611a6b3ff3791d421f025))
* **usecases:** :bug: fix ts path issue leading to invalid dependencies ([ed34a08](https://github.com/davidlaroche/paradox-api/commit/ed34a08e70e2dfc979a77aa7139cbad2ce1f0b2e))
* **usecases:** :bug: improve check for `cancellationReasonCode` in `getOrderStatus()` ([96b6298](https://github.com/davidlaroche/paradox-api/commit/96b62985bdc214ae83a2c4153deaaf43adfe3326))
* **usecases:** :bug: include `retryCount` in `ReprocessCronJobDTO` ([a8b69ce](https://github.com/davidlaroche/paradox-api/commit/a8b69ce1e36263b129e7e2b378539e85950a2b18))
* **usecases:** :bug: increase default `limit` to `500` in `searchAll` for offers ([99c1eb8](https://github.com/davidlaroche/paradox-api/commit/99c1eb878a0700962a152f895322996e0acc8c47))
* **usecases:** :bug: only update specific fields in `resyncWithChargebee()` ([5c41798](https://github.com/davidlaroche/paradox-api/commit/5c41798fbbf858641ff69989bb344f641a818378))
* **usecases:** :bug: remove duplicate check for existing Circle Space members ([63017ad](https://github.com/davidlaroche/paradox-api/commit/63017ad6ebc0e0e0da836beb387b9fe396ed214e))
* **usecases:** :bug: remove duplicate handling of webhook events ([623487f](https://github.com/davidlaroche/paradox-api/commit/623487faf6d1a2d14cca8a215633caed5f29e660))
* **usecases:** :bug: rewrite `getWebhookPayload()` in `ChargebeeBillingService` ([e0145ba](https://github.com/davidlaroche/paradox-api/commit/e0145bacf61daef9467b7373934cf74ebfa49537))
* **usecases:** :bug: set `withProductDelivery` to `true` if access config is set ([b114fff](https://github.com/davidlaroche/paradox-api/commit/b114fff4ed6ecb8f30be6be0b0ef22049115ade6))
* **usecases:** :bug: update access status only if successful ([8d8e39e](https://github.com/davidlaroche/paradox-api/commit/8d8e39e8b303f1a473d3603d12e8e2ee45c8e015))
* **usecases:** :goal_net: catch hubspot sync errors ([11e1031](https://github.com/davidlaroche/paradox-api/commit/11e1031160c2f03226795e9a4cab64a28d368865))
* **usecases:** :zap: avoid reprocessing same webhook ([724b916](https://github.com/davidlaroche/paradox-api/commit/724b9166e6822c7fac79eafd89e120607dfd99b9))
* **usecases:** :zap: increase default limit of lisitng products to `500` ([3c6fb20](https://github.com/davidlaroche/paradox-api/commit/3c6fb205ba3d7118b55974281bacb4f86b5123d3))
* **usecases:** :zap: remove `CHARGEBEE` prefix from CB webhook IDs ([e5a92c4](https://github.com/davidlaroche/paradox-api/commit/e5a92c4311175938097c78ce3ce7772ba4fa48ae))
* **usecases:** :zap: update `metaData` if `handleFailedCustomerAccess()` succeeds ([402cef7](https://github.com/davidlaroche/paradox-api/commit/402cef722e63b8b88351152ee4c0eaa1449176a9))
* **usecases:** :zap: use `findOrCreate` for CB webhook entities creation ([0f8f3e9](https://github.com/davidlaroche/paradox-api/commit/0f8f3e982fbffbaeb9ba4e3aa50b6ee06687df31))
* **usecases:** 🐛 avoid throwing if user already enrolled in course ([cdd7254](https://github.com/davidlaroche/paradox-api/commit/cdd7254c892490cfcf456a4c30353b326d3f974b))
* **usecases:** refactor subscription order status calculation logic ([d2e3e82](https://github.com/davidlaroche/paradox-api/commit/d2e3e824394db4b27dd8c42d87c2a38a5b0ae46c))
* **user:** update `metadata` to `metaData` in `UserEntity` to match `CustomerTable` schema ([fc32f07](https://github.com/davidlaroche/paradox-api/commit/fc32f0778453543d8bfc519d8b5d1311c140f3ce))


### Features

* :card_file_box: add `retryCount` to `CronLog` entity ([360316a](https://github.com/davidlaroche/paradox-api/commit/360316a9627a7f954532c9c36741faed82dd342a))
* :passport_control: add HTTP requests support from `px-app` client ([3872bbd](https://github.com/davidlaroche/paradox-api/commit/3872bbdfa2aa44a8da7176ab481f0d1cd503cc59))
* :sparkles: implement `GET: /offers/list` endpoint ([ca8e38c](https://github.com/davidlaroche/paradox-api/commit/ca8e38c9f2c7cac33004788475b0c4832cc273e3))
* :sparkles: implement retryCount check & Slack notifications for crons ([043f4df](https://github.com/davidlaroche/paradox-api/commit/043f4df2caa7048525c31e017351285c373f4e9a))
* :sparkles: publish offer updates to KV store ([68b345d](https://github.com/davidlaroche/paradox-api/commit/68b345d5c6880b9d0932ee75897860bbcfbc378a))
* **access:** add user access details endpoint and migration for user access purchases view ([4728db9](https://github.com/davidlaroche/paradox-api/commit/4728db99edf357da22c7de8e93c90fa87136f3c1))
* **access:** add user access details endpoint and migration for user access purchases view ([ab5891f](https://github.com/davidlaroche/paradox-api/commit/ab5891fd45d902376ee730d97480124612f89fa0))
* **api:** :bug: prefix `id` of Clerk & Bank Transfer webhooks ([9037775](https://github.com/davidlaroche/paradox-api/commit/903777508748dd1588286c62f7fb3b718cf34191))
* **api:** :sparkles: implement `POST: subscriptions/resync/:chargebeeId` endpoint ([fd69b91](https://github.com/davidlaroche/paradox-api/commit/fd69b91b819a7383fdec84af8794e4535d30b3eb))
* **auth:** implement JWT permissions guard and update controllers ([71d51b6](https://github.com/davidlaroche/paradox-api/commit/71d51b6d19653e81b5df5c2ac301647f7f9d35ca))
* **cohort, course, role:** implement controllers & use cases for cohort, course, & role management ([bf4f16f](https://github.com/davidlaroche/paradox-api/commit/bf4f16f2b8bca925461ba6f6a57f0c6279e049cc))
* **config:** add secret loading validation and allSecretsLoaded method ([841aeb5](https://github.com/davidlaroche/paradox-api/commit/841aeb5c529033b637f46d2e2a750307da7fa85f))
* **config:** integrate Infisical for secret management & refactor configuration handling ([1a449e6](https://github.com/davidlaroche/paradox-api/commit/1a449e6163e0bb2a1d71f0607c027bd22bcbe02e))
* **crons:** :zap: use CSV for overdue subscription notification ([c4441c7](https://github.com/davidlaroche/paradox-api/commit/c4441c7b189319bbb4aaa7c39184b8ad8e5f09aa))
* **customers:** refactor email handling in CustomersController and add validation DTO ([8247fb5](https://github.com/davidlaroche/paradox-api/commit/8247fb53ff12ad892566e14cd58bf7351baa655e))
* **customers:** refactor email handling in CustomersController and add validation DTO ([0876dd4](https://github.com/davidlaroche/paradox-api/commit/0876dd434e2a51cb9cc0cbd41dfbed037c3c6a5a))
* **database:** :sparkles: add getDataSource method to IDataServices and PostgresDataServices ([52152cb](https://github.com/davidlaroche/paradox-api/commit/52152cb6a7207e6d658e3ef04b174687527e8d31))
* **database:** add cohort, course, role, user, & user profile tables & services ([a96456d](https://github.com/davidlaroche/paradox-api/commit/a96456d0c4b8a5619344c2d1a8e212ff0e118793))
* **db:** :card_file_box: add `FAILED_HUBSPOT_SYNC` to `CronLogType` ([4a033e5](https://github.com/davidlaroche/paradox-api/commit/4a033e521d7fec77dc581c04aea33b0d9f58478e))
* **dtos:** migrate cohort, course, role, & user DTOs from `vega` ([d820cb6](https://github.com/davidlaroche/paradox-api/commit/d820cb6a7d07e968331bbf5e0b6fbfa1414113f3))
* **entities:** add M-M relation between `ProductOffer` & `CourseEntity` ([d4eecc7](https://github.com/davidlaroche/paradox-api/commit/d4eecc7bb4905276c9223c78ad905f148d7acb07))
* **entities:** enforce required fields in ProductOffer entity ([9252dfd](https://github.com/davidlaroche/paradox-api/commit/9252dfdddc44382ea10db2294c9c84f9bfdadb3a))
* **entities:** migrate entities from vega ([e1ed82d](https://github.com/davidlaroche/paradox-api/commit/e1ed82d3a439288249163a512e73d4af3f901f8f))
* **goliaths:** :sparkles: add Goliaths module and controller ([a7f4af3](https://github.com/davidlaroche/paradox-api/commit/a7f4af3b21677b2fe6ee63fef8cc3e9b00f22182))
* **goliaths:** :sparkles: update isPOMStudent method to use direct database query ([90f85ff](https://github.com/davidlaroche/paradox-api/commit/90f85ffaed20c285bb6edbfe0ee324cd4e287c30))
* **hubspot:** enhance createMissingMetadataProperties to return response status and error details ([1fc15a4](https://github.com/davidlaroche/paradox-api/commit/1fc15a431f93871ee404b7cd559a333f5c4df81e))
* **migrations:** enhance user_access_purchases_view with customer access data ([6821bdc](https://github.com/davidlaroche/paradox-api/commit/6821bdc7390c03deff7077d7885f4a0f935f8ace))
* **migrations:** move migrations to new folder & add vega migration ([4740c58](https://github.com/davidlaroche/paradox-api/commit/4740c581e27bcd15eb9cbf2b1cf6ee7ceda59306))
* **monitoring:** implement comprehensive error handling and Sentry integration ([3d58aba](https://github.com/davidlaroche/paradox-api/commit/3d58abaa8b5ce0d9262e5b033453027e3590447e))
* **products:** add endpoint to retrieve product offer and checkout page by chargebeeId ([12026f0](https://github.com/davidlaroche/paradox-api/commit/12026f0de9a03d62a059ab94564aa8c6986aa577))
* **redis:** refactor Redis publisher and introduce Redis clients module ([2dc0537](https://github.com/davidlaroche/paradox-api/commit/2dc05378f4375efad0cb8ce7b54786f20054b7e0))
* **refactor:** replace `PlanAndOfferStatus` & `ProductOfferVersion` with `hermes` versions ([ed0e298](https://github.com/davidlaroche/paradox-api/commit/ed0e2984faa6caf4a5fa768cf60401a9cae66ffe))
* **repository:** add search functionality to generic repository ([6b463e3](https://github.com/davidlaroche/paradox-api/commit/6b463e3cb8deb6e73e15dc9da5fc1c4269aed6c9))
* **service:** :sparkles: add `offer_id` and `offer_name` to Hubspot deal sync ([ad2ae6a](https://github.com/davidlaroche/paradox-api/commit/ad2ae6a8a98703b90e51c157929b38d76aa80442))
* **slack:** :sparkles: add method to send messages with attachments ([580e03b](https://github.com/davidlaroche/paradox-api/commit/580e03b9a2adba39bd8e8c51b93048c7b22df192))
* **stats:** add StatsController and StatsUseCases for platform statistics ([13d1c38](https://github.com/davidlaroche/paradox-api/commit/13d1c38cd8efd98a956dbbf12b0a67370b4c6ef9))
* **subscriptions:** add endpoint to import subscription data from JSON ([4ba8b65](https://github.com/davidlaroche/paradox-api/commit/4ba8b6527336f1769246f80e587391e6c72ff450))
* **subscriptions:** add endpoint to retrieve webhook events by chargebeeId ([f5fb3dc](https://github.com/davidlaroche/paradox-api/commit/f5fb3dc9e7ed20e2f0ca8c48c0cf7014de6e8d53))
* **subscriptions:** enhance importSubscriptionData method for comprehensive data handling ([2b10d9f](https://github.com/davidlaroche/paradox-api/commit/2b10d9f694bf53b7a06d87323b3dc982dff2fabe))
* **subscriptions:** restore import endpoint for subscription data ([ac99c39](https://github.com/davidlaroche/paradox-api/commit/ac99c39083ef985cde24a14afe6291eb6e73cd09))
* **usecases:** :sparkles: add `offer.id` & `offer.name` to subscription export ([e0435d8](https://github.com/davidlaroche/paradox-api/commit/e0435d8035a599ef17e8adfcabeb9c3a56ac29af))
* **usecases:** :sparkles: add endpoints for granting/revoking accesses ([eb3f56a](https://github.com/davidlaroche/paradox-api/commit/eb3f56a84eeb415d45c298b7029ed1b996f96efc))
* **usecases:** :sparkles: implement `resyncWithChargebee()` in `SubscriptionUseCases` ([ef8857a](https://github.com/davidlaroche/paradox-api/commit/ef8857a116136521776f94b076864b33510bc569))
* **usecases:** :sparkles: implement `Webhook` table and basic use-cases ([0a11027](https://github.com/davidlaroche/paradox-api/commit/0a110279ddb3deefe7da0f930980b15efb34964f))
* **usecases:** :sparkles: implement list & get one endpoints for `Webhooks` ([ff6e158](https://github.com/davidlaroche/paradox-api/commit/ff6e158eb33982176875345a510bf88b09c369fc))
* **usecases:** add support for metadata in customer creation process ([5172970](https://github.com/davidlaroche/paradox-api/commit/51729704536b7683d7014ba6010a066900e0422e))
* **usecases:** make email queries case insensitive using lower() for safety in user access details and POM student checks ([25afd93](https://github.com/davidlaroche/paradox-api/commit/25afd9392658dac23399890ae481cc36b25a3292))
* **user:** refactor customer management to user management ([00da30f](https://github.com/davidlaroche/paradox-api/commit/00da30fea9e47f6b901ff7284f1e100a7f3af640))
* **webhooks:** implement centralized webhook handling with retry mechanism ([a086000](https://github.com/davidlaroche/paradox-api/commit/a0860002a08f809266677db9043e249dc8eb0ddd))


### Performance Improvements

* :construction: `wip` improved Sentry integration & custom exceptions ([5382c54](https://github.com/davidlaroche/paradox-api/commit/5382c54dc48d74056a1391853b2d6c4d0e983018))
