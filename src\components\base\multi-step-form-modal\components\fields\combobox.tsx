"use client";

import { useFormContext } from "react-hook-form";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { ComboboxField as ComboboxFieldType } from "../../types";
import { Combobox, ComboboxOption } from "@/components/base/combobox";
import { MultiSelect } from "@/components/ui/multi-select";

interface ComboboxFieldProps {
  field: ComboboxFieldType;
}

export const ComboboxField = ({ field }: ComboboxFieldProps) => {
  const {
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();

  const value = watch(field.id);
  const error = errors[field.id];
  const t = useTranslations("validation");
  const tc = useTranslations("common");

  const renderError = () => {
    if (!error) return null;

    if (error.type === "custom" && error.message) {
      return error.message.toString();
    }

    switch (error.type) {
      case "required":
        return t("required");
      default:
        return error.message?.toString() || t("invalid");
    }
  };

  // Convert options to ComboboxOption format while preserving render function
  const options = field.options.map((option) => ({
    value: option.value,
    label: option.label,
    render: option.render,
  }));

  return (
    <div className="w-full space-y-2">
      {field.isMulti ? (
        <MultiSelect
          value={Array.isArray(value) ? value : value ? [value] : []}
          onChange={(values) => {
            setValue(field.id, values, { shouldValidate: true });
            field.onChange?.(values);
          }}
          options={options}
          placeholder={field.placeholder}
          className={error ? "border-destructive" : ""}
          emptyMessage={tc("no_results")}
        />
      ) : (
        <Combobox
          id={field.id}
          value={value}
          onChange={(val) => {
            setValue(field.id, val, { shouldValidate: true });
            field.onChange?.(val);
          }}
          options={options}
          placeholder={field.placeholder}
          disabled={field.disabled}
          className={error ? "border-destructive" : ""}
          label={field.label}
        />
      )}
    </div>
  );
};
