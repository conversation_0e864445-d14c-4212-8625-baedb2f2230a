"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { AvailabilityBadge } from "./AvailabilityBadge";
import { SessionBadge } from "./SessionBadge";
import { SessionTypeBadge } from "./SessionTypeBadge";
import { TriadParticipants } from "./shared/TriadParticipants";
import { TriadAccess } from "./shared/TriadAccess";
import { TriadActionButton } from "./shared/TriadActionButton";
import { cn } from "@/lib/utils";
import { anton } from "@/lib/fonts/anton";
import { useTranslations } from "next-intl";
import { ITriadBase } from "@px-shared-account/hermes";
import { AccessInfo } from "@/services/customers";
import { formatTriadDateMobile } from "@/utils/triadHelpers";

interface TriadCardProps {
  triad: ITriadBase;
  index: number;
  totalLength: number;
  locale: string;
  accessInfo: AccessInfo;
  userId?: string;
  isLoading: boolean;
  onJoin: (triadId: number) => Promise<void>;
  onLeave: (triadId: number) => void;
  onDelete: (triadId: number, participantCount: number) => void;
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
  exit: {
    opacity: 0,
    x: 20,
    transition: { duration: 0.2 },
  },
};

export function TriadCard({
  triad,
  index,
  totalLength,
  locale,
  accessInfo,
  userId,
  isLoading,
  onJoin,
  onLeave,
  onDelete,
}: TriadCardProps) {
  const t = useTranslations("triad");

  return (
    <motion.div variants={itemVariants} initial="hidden" animate="visible" exit="exit" layout>
      <Card
        className={cn(
          "bg-background-tertiary text-background-tertiary-foreground hover:bg-background-tertiary/80 border-none shadow-lg transition-all duration-100",
          triad.organizer?.ssoId === userId && "!bg-background-quaternary",
          index === 0 && "rounded-t-3xl rounded-b-none",
          index === totalLength - 1 && index !== 0 && "rounded-t-none rounded-b-3xl",
          index !== 0 && index !== totalLength - 1 && "rounded-none",
        )}
      >
        <CardContent className="space-y-4 p-4">
          <div
            className={cn("text-3xl tracking-wide text-white uppercase", anton.className)}
            suppressHydrationWarning
          >
            {(() => {
              const { day, time } = formatTriadDateMobile(new Date(triad.sessionTime), locale);
              return (
                <>
                  {day} {t("list.at")} {time}
                  <div className="mt-1 text-xs font-normal text-gray-400 normal-case">
                    {t("time_picker.local_time")}
                  </div>
                </>
              );
            })()}
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center space-x-2">
              {accessInfo.hasBothAccess && <SessionTypeBadge type={triad.sessionType} />}
              {triad.sessions && triad.sessions.length > 0 ? (
                <div className="flex items-center gap-1">
                  <SessionBadge session={triad.sessions[0]} />
                  {triad.sessions.length > 1 && (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="ghost"
                          className="h-auto rounded-full bg-white/10 px-2 py-0.5 text-xs font-medium text-white hover:bg-white/20"
                        >
                          +{triad.sessions.length - 1}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="bg-background w-auto border-[#2C2C2C] p-2">
                        <div className="flex flex-col gap-1">
                          {triad.sessions.slice(1).map((session) => (
                            <SessionBadge key={session} session={session} />
                          ))}
                        </div>
                      </PopoverContent>
                    </Popover>
                  )}
                </div>
              ) : null}
              <AvailabilityBadge
                availableSpots={triad.maxParticipants - (triad.participants?.length || 0) + 1}
                maxSpots={triad.maxParticipants}
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold text-white">{t("list.title")}</span>
                <span className="text-sm text-gray-400">
                  {triad.title || triad.organizer?.firstName}
                </span>
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold text-white">{t("list.duration")}</span>
                <span className="text-sm text-gray-400">
                  {t("list.duration_value", {
                    hours: triad.duration,
                  })}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-bold text-white">{t("list.participants")}</span>
              <TriadParticipants triad={triad} variant="list" />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-bold text-white">{t("list.access")}</span>
              <TriadAccess triad={triad} userId={userId} variant="list" />
            </div>
          </div>

          <div className="pt-2">
            <TriadActionButton
              triad={triad}
              userId={userId}
              isLoading={isLoading}
              onJoin={onJoin}
              onLeave={onLeave}
              onDelete={onDelete}
              variant="list"
            />
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
