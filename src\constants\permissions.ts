import { Permissions } from "../enums";

export const UserPermissionsDescriptions = {
  [Permissions.User.CREATE]: "Allows permission owner to create new users",
  [Permissions.User.READ]: "Allows permission owner to read an existing user",
  [Permissions.User.UPDATE]: "Allows permission owner to update an existing user",
  [Permissions.User.DELETE]: "Allows permission owner to delete an existing user",
} as const;

export const RolePermissionsDescriptions = {
  [Permissions.Role.CREATE]: "Allows permission owner to create a new role",
  [Permissions.Role.READ]: "Allows permission owner to read an existing role",
  [Permissions.Role.UPDATE]: "Allows permission owner to update an existing role",
  [Permissions.Role.DELETE]: "Allows permission owner to delete an existing role",
} as const;

export const CoursePermissionsDescriptions = {
  [Permissions.Course.CREATE]: "Allows permission owner to create a new course",
  [Permissions.Course.READ]: "Allows permission owner to read an existing course",
  [Permissions.Course.UPDATE]: "Allows permission owner to update an existing course",
  [Permissions.Course.ARCHIVE]: "Allows permission owner to archive an existing course",
} as const;

export const CohortPermissionsDescriptions = {
  [Permissions.Cohort.CREATE]: "Allows permission owner to create a new cohort",
  [Permissions.Cohort.READ]: "Allows permission owner to read an existing cohort",
  [Permissions.Cohort.UPDATE]: "Allows permission owner to update an existing cohort",
  [Permissions.Cohort.DELETE]: "Allows permission owner to delete an existing cohort",
} as const;

export const TriadPermissionsDescriptions = {
  [Permissions.Triad.CREATE]: "Allows permission owner to create a new triad",
  [Permissions.Triad.READ]: "Allows permission owner to read an existing triad",
  [Permissions.Triad.UPDATE]: "Allows permission owner to update an existing triad",
  [Permissions.Triad.DELETE]: "Allows permission owner to delete an existing triad",
  [Permissions.Triad.JOIN]: "Allows permission owner to join an existing triad",
} as const;