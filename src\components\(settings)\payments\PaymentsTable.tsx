"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import { ColumnDef } from "@tanstack/react-table";
import { useCallback, useMemo, useState } from "react";
import { ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { DataTable } from "@/components/table";
import { Button } from "@/components/ui/button";
import { useTable } from "@/hooks/use-table";
import { Badge, BadgeProps } from "@/components/ui/badge";
import { TableProps, TableState } from "@/types/table";

interface PaymentsTableProps {
  payments: any[];
  isLoading: boolean;
  getStatusBadgeVariant: (status: string) => BadgeProps["variant"];
}

export default function PaymentsTable({
  payments,
  isLoading,
  getStatusBadgeVariant,
}: PaymentsTableProps) {
  const t = useTranslations("payments");
  const router = useRouter();

  const [tableState, setTableState] = useState<Omit<TableState, "searchQuery">>({
    pageIndex: 1,
    pageSize: 10,
  });

  const handleViewPaymentDetails = useCallback(
    (paymentId: number) => {
      router.push(`/payments/${paymentId}`);
    },
    [router],
  );

  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        id: "name",
        header: t("table.name"),
        accessorKey: "name",
        cell: ({ row }) => (
          <div className="flex items-center gap-2 font-medium">
            <Image src={row.original.image} alt={row.original.name} width={40} height={40} />
            <span>{row.original.name}</span>
          </div>
        ),
        enableSorting: true,
      },
      {
        id: "status",
        header: t("table.status.title"),
        accessorKey: "status",
        cell: ({ row }) => (
          <Badge
            variant={getStatusBadgeVariant(row.original.orderStatus)}
            className="rounded-md font-light uppercase"
          >
            {t(`table.status.options.${row.original.orderStatus.toLowerCase()}`)}
          </Badge>
        ),
        enableSorting: true,
      },
      {
        id: "startDate",
        header: t("table.started-at"),
        accessorKey: "startedAt",
        cell: ({ row }) => new Date(row.original.startedAt).toLocaleDateString(),
        enableSorting: true,
        sortingFn: "datetime",
      },
      {
        id: "actions",
        header: t("table.actions.header"),
        cell: ({ row }) => {
          return (
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => handleViewPaymentDetails(row.original.chargebeeid)}
            >
              <span className="sr-only">
                {t("table.actions.view-details", { name: row.original.name })}
              </span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          );
        },
      },
    ],
    [t, handleViewPaymentDetails],
  );

  const handleTableStateChange = useCallback((newState: Partial<TableState>) => {
    setTableState((prev) => ({
      ...prev,
      ...newState,
    }));
  }, []);

  const tableConfig = useMemo(
    () => ({
      onStateChange: handleTableStateChange,
      data: payments,
      columns,
    }),
    [payments, columns, handleTableStateChange],
  );

  const { selectedRows, handleRowSelection } = useTable(tableConfig);

  const dataTableProps = useMemo<TableProps<any>>(
    () => ({
      columns,
      data: payments,
      isLoading,
      selectedRows,
      enablePagination: true,
      enableSorting: true,
      pageSize: tableState.pageSize,
      totalRows: payments.length,
      noResults: t("errors.no-results"),
      onStateChange: handleTableStateChange,
      onSelectionChange: handleRowSelection,
      getRowId: (row) => row.id.toString(),
      title: t("table.title", { count: payments.length }),
    }),
    [
      payments,
      isLoading,
      selectedRows,
      tableState.pageSize,
      t,
      columns,
      handleRowSelection,
      handleTableStateChange,
      handleViewPaymentDetails,
    ],
  );

  return (
    <div className="overflow-x-auto">
      <DataTable {...dataTableProps} />
    </div>
  );
}
