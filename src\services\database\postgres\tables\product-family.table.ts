import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProductLineTable } from './product-line.table';
import { ProductFamily } from '@entities';
import { CourseTable } from './course.table';

@Entity({
  name: 'productFamilies',
})
export class ProductFamilyTable implements ProductFamily {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'varchar',
    unique: true,
  })
  name: string;

  @Column({
    type: 'varchar',
  })
  description: string;

  @ManyToOne(() => ProductLineTable, (productLine) => productLine.families)
  productLine: ProductLineTable;

  @OneToMany(() => CourseTable, (course) => course.productFamily)
  courses: CourseTable[];

  @Column({
    type: 'varchar',
  })
  status: string;

  @Column({
    type: 'varchar',
    length: 100,
  })
  chargebeeId: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deletedAt?: Date;
}
