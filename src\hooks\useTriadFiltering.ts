import { useState, useEffect, useMemo } from "react";
import { useFilterBar } from "@/components/base/filter-bar-new";
import { ITriadBase } from "@px-shared-account/hermes";
import { createTriadFilterSteps } from "@/utils/triadFilterSteps";
import { filterTriads } from "@/utils/triadFilterLogic";

interface UseTriadFilteringProps {
  triads: ITriadBase[];
  showSessionTypeFilter: boolean;
  userId?: string;
  t: (key: string) => string;
}

export function useTriadFiltering({
  triads,
  showSessionTypeFilter,
  userId,
  t,
}: UseTriadFilteringProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isFilterReady, setIsFilterReady] = useState(false);

  // Create localized filter steps
  const localizedFilterSteps = useMemo(
    () => createTriadFilterSteps(showSessionTypeFilter, t),
    [showSessionTypeFilter, t],
  );

  // Set filter ready state when steps are initialized
  useEffect(() => {
    setIsFilterReady(true);
  }, []);

  // Initialize filter bar
  const {
    values: filterValues,
    stepsState,
    handleChange: handleFilterChange,
    isStepEnabled,
    clearAll,
  } = useFilterBar({
    steps: localizedFilterSteps,
  });

  // Apply filtering logic
  const filteredTriads = useMemo(
    () => filterTriads(triads, searchQuery, filterValues, userId),
    [triads, searchQuery, filterValues, userId],
  );

  return {
    searchQuery,
    setSearchQuery,
    isFilterReady,
    localizedFilterSteps,
    filterValues,
    stepsState,
    handleFilterChange,
    isStepEnabled,
    clearAll,
    filteredTriads,
  };
}
