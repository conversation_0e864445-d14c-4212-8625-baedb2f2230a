import {
  Addon,
  ChargedItem,
  ContractTerm,
  Coupon,
  Discount,
  ItemTier,
  ReferralInfo,
  ShippingAddress,
  SubscriptionItem,
} from 'chargebee-typescript/lib/resources/subscription';
import {
  ChargeBeeChannel,
  ChargeBeeDurationPeriodUnit,
  SubscriptionCancelReason,
  SubscriptionOrderStatus,
  SubscriptionStatus,
  TrialEndAction,
} from '@enums';
import { ProductPlanPrice } from '.';

export class Subscription {
  id?: number;
  chargebeeId: string;
  customFields?: Record<string, any>;
  currencyCode: string;
  startDate?: number;
  trialEnd?: number;
  billingPeriod?: number;
  billingPeriodUnit?: ChargeBeeDurationPeriodUnit;
  initialBillingCycles?: number;
  totalBillingCycles?: number;
  remainingBillingCycles?: number;
  poNumber?: string;
  planId?: string;
  planQuantity?: number;
  planQuantityInDecimal?: string;
  planUnitPriceInDecimal?: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  status: SubscriptionStatus;
  trialStart?: number;
  trialEndAction?: TrialEndAction;
  currentTermStart?: number;
  currentTermEnd?: number;
  nextBillingAt?: number;
  autoCollection?: string;
  createdAt: number;
  startedAt?: number;
  activatedAt?: number;
  contractTermBillingCycleOnRenewal?: number;
  overrideRelationship?: boolean;
  pauseDate?: number;
  resumeDate?: number;
  cancelledAt?: number;
  cancelReason?: SubscriptionCancelReason;
  createdFromIp?: string;
  resourceVersion?: number;
  updatedAt?: number;
  hasScheduledAdvanceInvoices?: boolean;
  hasScheduledChanges?: boolean;
  paymentSourceId?: string;
  planFreeQuantityInDecimal?: string;
  planAmountInDecimal?: string;
  cancelScheduleCreatedAt?: number;
  channel?: ChargeBeeChannel;
  netTermDays?: number;
  activeId?: string;
  dueInvoicesCount?: number;
  dueSince?: number;
  totalDues?: number;
  mrr?: number;
  exchangeRate?: number;
  baseCurrencyCode?: string;
  invoiceNotes?: string;
  metadata?: Record<string, any>;
  deleted?: boolean;
  changesScheduledAt?: number;
  cancelReasonCode?: string;
  freePeriod?: number;
  freePeriodUnit?: ChargeBeeDurationPeriodUnit;
  createPendingInvoices?: boolean;
  autoCloseInvoices?: boolean;
  businessEntityId: string;
  subscriptionItems?: SubscriptionItem[];
  itemTiers?: ItemTier[];
  chargedItems?: ChargedItem[];
  coupons?: Coupon[];
  shippingAddress?: ShippingAddress;
  referralInfo?: ReferralInfo;
  contractTerm?: ContractTerm;
  discounts?: Discount[];
  addons?: Addon[];
  attachedPrices?: ProductPlanPrice[];
  crmId?: string;
  orderStatus?: SubscriptionOrderStatus;
  amountPaid?: number;
  amountRemaining?: number;
  totalOrderAmount?: number;
  amountRefunded?: number;
  isForever?: boolean;
}
