import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { ProductLine } from '@entities';
import { ProductCatalogEntityStatus } from '@enums';

export class CreateProductFamilyDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ type: ProductLine })
  @IsObject()
  @IsNotEmpty()
  productLine: ProductLine;
}

export class UpdateProductFamilyDTO extends PartialType(
  CreateProductFamilyDTO,
) {
  @ApiProperty({ enum: ProductCatalogEntityStatus })
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ProductCatalogEntityStatus)
  status?: ProductCatalogEntityStatus;
}
