import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

type SettingsTabsProps = {
  activeTab: string;
  onTabChange: (value: string) => void;
};

export default function SettingsTabs({ activeTab, onTabChange }: SettingsTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full overflow-scroll">
      <TabsList className="h-auto w-full justify-start border-b bg-transparent p-0">
        <TabsTrigger
          value="roles"
          className={cn(
            "relative h-10 rounded-none border-b-[3px] border-b-transparent data-[state=active]:border-b-primary data-[state=active]:shadow-none",
            "px-4 pb-3 pt-2 font-medium text-muted-foreground data-[state=active]:text-foreground",
          )}
        >
          {"Roles"}
        </TabsTrigger>
        <TabsTrigger
          value="profiles"
          className={cn(
            "relative h-10 rounded-none border-b-[3px] border-b-transparent data-[state=active]:border-b-primary data-[state=active]:shadow-none",
            "px-4 pb-3 pt-2 font-medium text-muted-foreground data-[state=active]:text-foreground",
          )}
        >
          {"Profiles"}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
