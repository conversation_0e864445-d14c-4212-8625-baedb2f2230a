import { useFormContext } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { NumberField as NumberFieldType } from "../../types";
import { useTranslations } from "next-intl";

interface NumberFieldProps {
  field: NumberFieldType;
}

export const NumberField = ({ field }: NumberFieldProps) => {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const error = errors[field.id];
  const t = useTranslations("validation");

  const renderError = () => {
    if (!error) return null;

    if (error.type === "custom" && error.message) {
      return error.message.toString();
    }

    switch (error.type) {
      case "required":
        return t("required");
      case "min":
        return t("min", { min: error.message?.toString() });
      case "max":
        return t("max", { max: error.message?.toString() });
      default:
        return error.message?.toString() || t("invalid");
    }
  };

  return (
    <div className="w-full space-y-2">
      <Label htmlFor={field.id} className={error ? "text-destructive" : ""}>
        {field.label}
        {field.required && <span className="text-destructive">*</span>}
      </Label>
      {field.description && <p className="text-xs text-muted-foreground">{field.description}</p>}
      <Input
        {...register(field.id, {
          required: field.required,
          valueAsNumber: true,
          onChange: (e) => field.onChange?.(parseFloat(e.target.value)),
        })}
        type="number"
        placeholder={field.placeholder}
        disabled={field.disabled}
        aria-invalid={!!error}
        className={error ? "border-destructive" : ""}
      />
      {error && <p className="text-sm font-medium text-destructive">{renderError()}</p>}
    </div>
  );
};
