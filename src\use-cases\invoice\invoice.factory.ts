import {
  Invoice as ChargeBeeInvoice,
  InvoiceAdjustmentCreditNote,
  InvoiceDiscount,
  InvoiceIssuedCreditNote,
} from 'chargebee-typescript/lib/resources';
import _ from 'lodash';
import { Injectable } from '@nestjs/common';
import { LineItem } from 'chargebee-typescript/lib/resources/invoice';
import { Currencies, InvoicePriceType, InvoiceStatus } from '@enums';
import { IDataServices } from '@abstracts';
import { Invoice } from '@entities';

@Injectable()
export class InvoiceFactoryService {
  /**
   * Generates a new invoice based on invoice data coming from ChargeBee Webhook Notification
   * @param chargebeeInvoice Invoice data from ChargeBee
   * @param paymentId ID of the `payment` related to this `refund`
   */
  generateFromChargebeeEvent(chargebeeInvoice: ChargeBeeInvoice): Invoice {
    const invoice = new Invoice();
    invoice.chargebeeId = chargebeeInvoice.id;
    invoice.amountDue = chargebeeInvoice.amount_due;
    invoice.amountPaid = chargebeeInvoice.amount_paid;
    invoice.amountAdjusted = chargebeeInvoice.amount_adjusted;
    invoice.amountToCollect = chargebeeInvoice.amount_to_collect;
    invoice.adjustmentCreditNotes = chargebeeInvoice.adjustment_credit_notes;
    invoice.currencyCode =
      Currencies[chargebeeInvoice.currency_code.toUpperCase()];
    invoice.baseCurrencyCode =
      Currencies[chargebeeInvoice?.['base_currency_code'].toUpperCase()];
    invoice.customerId = chargebeeInvoice.customer_id;
    invoice.billingAddress = chargebeeInvoice.billing_address;
    invoice.businessEntityId = chargebeeInvoice.business_entity_id;
    invoice.channel = chargebeeInvoice.channel;
    invoice.date = chargebeeInvoice.date;
    invoice.discounts = chargebeeInvoice.discounts;
    invoice.dueDate = chargebeeInvoice.due_date;
    invoice.dunningAttempts = chargebeeInvoice.dunning_attempts;
    invoice.exchangeRate = chargebeeInvoice.exchange_rate;
    invoice.generatedAt = chargebeeInvoice.generated_at;
    invoice.issuedCreditNotes = chargebeeInvoice.issued_credit_notes;
    invoice.lineItemDiscounts = chargebeeInvoice.line_item_discounts;
    invoice.lineItemTaxes = chargebeeInvoice.line_item_taxes;
    invoice.lineItems = chargebeeInvoice.line_items;
    invoice.linkedPayments = chargebeeInvoice.linked_payments;
    invoice.netTermDays = chargebeeInvoice.net_term_days;
    invoice.paidAt = chargebeeInvoice.paid_at;
    invoice.priceType =
      InvoicePriceType[chargebeeInvoice.price_type.toUpperCase()];
    invoice.shippingAddress = chargebeeInvoice.shipping_address;
    invoice.status = InvoiceStatus[chargebeeInvoice.status.toUpperCase()];
    invoice.subscriptionId = chargebeeInvoice.subscription_id;
    invoice.subTotal = chargebeeInvoice.sub_total;
    invoice.tax = chargebeeInvoice.tax;
    invoice.taxes = chargebeeInvoice.taxes;
    invoice.total = chargebeeInvoice.total;
    invoice.updatedAt = new Date(chargebeeInvoice.updated_at * 1000);
    invoice.writeOffAmount = chargebeeInvoice.write_off_amount;
    return invoice;
  }

  /**
   * Adds product name and image to line items
   * @param dataServices Data services
   * @param lineItems Line items
   * @returns Line items with product name and image
   */
  async addImageAndDescriptionToLineItems(
    dataServices: IDataServices,
    lineItems: LineItem[],
  ) {
    for (const lineItem of lineItems) {
      const lineItemId = lineItem.entity_id;
      const productInfo = await dataServices.productPlanPrice
        .getRepository()
        .createQueryBuilder('productPlanPrice')
        .leftJoinAndSelect('productPlanPrice.productPlan', 'productPlan')
        .leftJoinAndSelect('productPlan.product', 'product')
        .where('productPlanPrice.chargebeeId = :lineItemId', { lineItemId })
        .getOne();
      if (productInfo) {
        console.log('\n\n\n\nProductInfo: ', productInfo, '\n\n\n\n');
        lineItem['name'] = productInfo?.productPlan?.product?.externalName;
        lineItem['image'] = productInfo?.productPlan?.product?.image;
        lineItem.description = productInfo?.productPlan?.product?.description;
      }
    }
    return lineItems;
  }

  /**
   * Returns the fields that were updated in an Invoice. It only checks specific
   * fields that are important for Hubspot and other purposes, rather than all fields.
   * @param updatedVersion Updated version of the Invoice, from Chargebee
   * @param currentVersion Current version of the Invoice, stored in our own DB
   * @returns A Invoice containing only the updated fields of interest
   */
  getUpdatedFields(
    updatedVersion: ChargeBeeInvoice,
    currentVersion: Invoice,
  ): Partial<Invoice> {
    const updatedInvoice = new Invoice();

    const fieldsToUpdate: Partial<Invoice> = {
      status: InvoiceStatus[updatedVersion?.status?.toUpperCase()],
      amountPaid: updatedVersion.amount_paid,
      amountAdjusted: updatedVersion.amount_adjusted,
      amountDue: updatedVersion.amount_due,
      writeOffAmount: updatedVersion.write_off_amount,
      paidAt: updatedVersion.paid_at,
      updatedAt: new Date(updatedVersion.updated_at * 1000),
      currencyCode: Currencies[updatedVersion.currency_code.toUpperCase()],
      subTotal: updatedVersion.sub_total,
    };

    for (const [fieldToUpdate, updatedValue] of Object.entries(
      fieldsToUpdate,
    )) {
      if (currentVersion[fieldToUpdate] !== updatedValue) {
        updatedInvoice[fieldToUpdate] = updatedValue;
      }
    }

    if (updatedVersion.discounts) {
      updatedInvoice.discounts = this.getUpdatedDiscounts(
        currentVersion.discounts,
        updatedVersion.discounts,
      );
    }

    if (updatedVersion.issued_credit_notes) {
      updatedInvoice.issuedCreditNotes = this.getUpdatedCreditNotes(
        currentVersion.issuedCreditNotes,
        updatedVersion.issued_credit_notes,
      );
    }

    if (updatedVersion.adjustment_credit_notes) {
      updatedInvoice.adjustmentCreditNotes = this.getUpdatedCreditNotes(
        currentVersion.adjustmentCreditNotes,
        updatedVersion.adjustment_credit_notes,
      );
    }

    return updatedInvoice;
  }

  /**
   * Finds the difference between existing and new discounts on an Invoice and returns
   * the updated
   * @param currentDiscounts Current discounts of the Invoice from database
   * @param newDiscounts Updated discounts from Chargebee
   * @returns Updated invoice discounts
   */
  getUpdatedDiscounts(
    currentDiscounts: InvoiceDiscount[],
    newDiscounts: InvoiceDiscount[],
  ): InvoiceDiscount[] {
    if (currentDiscounts?.length !== newDiscounts?.length) {
      return newDiscounts;
    }

    const updatedDiscounts = newDiscounts.filter(
      (newItem) =>
        !currentDiscounts.some((currentItem) =>
          _.isEqual(newItem, currentItem),
        ),
    );

    if (updatedDiscounts?.length === 0) {
      return currentDiscounts;
    }

    const existingDiscountsNotChanged = newDiscounts.filter((newItem) =>
      currentDiscounts.some((currentItem) => _.isEqual(newItem, currentItem)),
    );
    return [...updatedDiscounts, ...existingDiscountsNotChanged];
  }

  /**
   * Finds the difference between existing and new credit notes on
   * an Invoice and returns the updated
   * @param currentLineItems Current credit notes of the Invoice from database
   * @param newLineItems Updated credit notes from Chargebee
   * @returns Updated invoice credit notes
   */
  getUpdatedCreditNotes(
    currentCreditNotes:
      | InvoiceIssuedCreditNote[]
      | InvoiceAdjustmentCreditNote[],
    newCreditNotes: InvoiceIssuedCreditNote[] | InvoiceAdjustmentCreditNote[],
  ): InvoiceIssuedCreditNote[] | InvoiceAdjustmentCreditNote[] {
    if (currentCreditNotes?.length !== newCreditNotes?.length) {
      return newCreditNotes;
    }

    const updatedCreditNotes = newCreditNotes.filter(
      (newItem) =>
        !currentCreditNotes.some((currentItem) =>
          _.isEqual(newItem, currentItem),
        ),
    );

    if (updatedCreditNotes?.length === 0) {
      return currentCreditNotes;
    }

    const existingCreditNotesNotChanged = newCreditNotes.filter((newItem) =>
      currentCreditNotes.some((currentItem) => _.isEqual(newItem, currentItem)),
    );
    return [...updatedCreditNotes, ...existingCreditNotesNotChanged];
  }
}
