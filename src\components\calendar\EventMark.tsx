"use client";

import { differenceInMinutes, format } from "date-fns";

import useCalendarStore, { CalendarEvent } from "@/hooks/store/calendar";
import { cn } from "@/lib/utils";
import CalendarEditStatus from "./EditStatus";

type EventMarkProps = {
  calendarEvent: CalendarEvent;
  sq: number;
  len: number;
};

function getStyles(partOfStyle: React.CSSProperties): React.CSSProperties {
  return {
    transform: "initial",
    WebkitTransform: "initial",
    height: "",
    ...partOfStyle,
  };
}

export default function EventMark({ calendarEvent, sq, len }: EventMarkProps) {
  const { setDialog } = useCalendarStore();
  const beginDate = new Date(calendarEvent.begin);
  const endDate = new Date(calendarEvent.end);
  const beginDateFormatted = format(beginDate, format(beginDate, "mm") === "00" ? "HH" : "HH:mm");
  const endDateFormatted = format(endDate, format(endDate, "mm") === "00" ? "HH" : "HH:mm");

  const currentDay = beginDate;
  const initTime = new Date(format(currentDay, "yyyy/MM/dd 00:00:00"));
  // Position should remove 8 hours to avoid overlap with the time column
  const position = differenceInMinutes(currentDay, initTime) - 7 * 60;
  const duration = differenceInMinutes(endDate, beginDate) - 3; // -3 to avoid overlap with the time column

  const partOfStyle: React.CSSProperties = {
    marginTop: position,
    height: duration,
    width: `calc((100% / ${len}) - 2px)`,
    marginLeft: `calc(100% / ${len} * ${sq})`,
    zIndex: "30",
  };

  const viewEvent = (props: any) => {
    const { calendarEvent } = props;
    setDialog({ id: calendarEvent.id, top: position, len, sq });
  };

  return (
    <div
      id={calendarEvent.id.toString()}
      style={getStyles(partOfStyle)}
      className={cn("absolute z-30 transform rounded-md p-1.5 hover:z-40", {
        "bg-[#1818180A border-2 border-dotted border-[#848585]": calendarEvent.status === "pending",
        "cursor-pointer bg-[#E1E1E1] hover:bg-gray-300":
          calendarEvent.status === "confirmed" || calendarEvent.status === "cancelled",
      })}
      onClick={(eventEl) =>
        viewEvent({
          eventEl,
          calendarEvent,
        })
      }
    >
      <div className="overflow-hidden text-ellipsis whitespace-nowrap">{calendarEvent.title}</div>
      <div className="overflow-hidden whitespace-nowrap text-sm">
        <span>{beginDateFormatted}</span>
        <span> - </span>
        <span>{endDateFormatted}</span>
      </div>
      <CalendarEditStatus event={calendarEvent} />
    </div>
  );
}
