import { MigrationInterface, QueryRunner } from 'typeorm';

export class HsSyncCron1744918992626 implements MigrationInterface {
  name = 'HsSyncCron1744918992626';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."cronLogs_type_enum" RENAME TO "cronLogs_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum" AS ENUM('OVERDUE_INVOICE_CHECK', 'ACCESS_SUSPENSION_CHECK', 'ERRORED_CUSTOMER_ACCESS_CHECK', 'EXCHANGE_RATE_CRAWLER', 'FAILED_WEBHOOKS', 'FAILED_HUBSPOT_SYNC')`,
    );
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ALTER COLUMN "type" TYPE "public"."cronLogs_type_enum" USING "type"::"text"::"public"."cronLogs_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum_old" AS ENUM('OVERDUE_INVOICE_CHECK', 'ACCESS_SUSPENSION_CHECK', 'ERRORED_CUSTOMER_ACCESS_CHECK', 'EXCHANGE_RATE_CRAWLER', 'FAILED_WEBHOOKS')`,
    );
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ALTER COLUMN "type" TYPE "public"."cronLogs_type_enum_old" USING "type"::"text"::"public"."cronLogs_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."cronLogs_type_enum_old" RENAME TO "cronLogs_type_enum"`,
    );
  }
}
