import { Module } from '@nestjs/common';
import { DataServicesModule } from '@services/database';
import { ProductFamilyFactory, ProductFamilyUseCases } from '.';
import { HubspotModule } from '@services/crm';

@Module({
  imports: [DataServicesModule, HubspotModule],
  providers: [ProductFamilyUseCases, ProductFamilyFactory],
  exports: [ProductFamilyUseCases, ProductFamilyFactory],
})
export class ProductFamilyUseCasesModule {}
