"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useApi } from "@/hooks/store/use-api";
import { useToast } from "@/hooks/use-toast";
import { COURSE_API_ENDPOINTS, CourseApiTypes, ListCoursesParams, CourseApiPayloads } from "./core";
import { IListCourseCatalogParams } from "@px-shared-account/hermes";

/**
 * Hook for fetching and managing course lists
 * @param params Filter and pagination parameters
 * @returns SWR response with course list data
 */
export function useCourseList(params: ListCoursesParams = {}) {
  // Create a memoized API key that includes all filter parameters
  const apiConfig = useMemo(() => {
    const { url } = COURSE_API_ENDPOINTS.list(params);
    return url;
  }, [params]);

  return useApi<CourseApiTypes["list"]>(apiConfig);
}

export function useCourseCatalog(params: IListCourseCatalogParams = { limit: 50 }) {
  const apiConfig = useMemo(() => {
    const { url } = COURSE_API_ENDPOINTS.listCatalog(params);
    return url;
  }, [params]);

  return useApi<CourseApiTypes["listCatalog"]>(apiConfig);
}

/**
 * Hook for fetching a specific course by ID
 * @param id Course ID to fetch
 * @returns SWR response with course data
 */
export function useCourseDetails(id?: number | null) {
  const apiConfig = useMemo(() => {
    if (!id) return null;
    const { url } = COURSE_API_ENDPOINTS.getById(id);
    return url;
  }, [id]);

  return useApi<CourseApiTypes["getById"]>(apiConfig);
}

/**
 * Hook for creating a new course
 * @returns Functions and state for course creation
 */
export function useCreateCourse() {
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = COURSE_API_ENDPOINTS.create();
  const { trigger, isLoading } = useApi<CourseApiTypes["create"]>(url, {
    method: method as "POST",
  });

  const create = useCallback(
    async (data: CourseApiPayloads["create"]) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        const result = await trigger(data);
        toast({
          title: "Course created",
          description: "The course has been created successfully",
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: "Error creating course",
          description: errorMessage || "An error occurred while creating the course",
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger],
  );

  return { create, isLoading, error };
}

/**
 * Hook for updating an existing course
 * @param id Optional initial course ID
 * @returns Functions and state for course updates
 */
export function useUpdateCourse(id?: number | null) {
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const [courseId, setCourseId] = useState<number | null>(id || null);
  const [updateData, setUpdateData] = useState<CourseApiPayloads["update"] | null>(null);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  const { trigger, isLoading } = useApi<CourseApiTypes["update"]>(
    courseId ? COURSE_API_ENDPOINTS.update(courseId).url : null,
    { method: "PATCH" },
  );

  const performUpdate = async (data?: CourseApiPayloads["update"]) => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!courseId) throw new Error("Course ID is required");
      if (!updateData && !data) throw new Error("Update data is required");

      const result = await trigger(data || updateData);
      toast({
        title: "Course updated",
        description: "The course has been updated successfully",
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: "Error updating course",
        description: errorMessage || "An error occurred while updating the course",
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldUpdate(false);
      setUpdateData(null);
    }
  };

  useEffect(() => {
    if (shouldUpdate && updateData) {
      performUpdate();
    }
  }, [shouldUpdate, courseId, updateData]);

  const update = useCallback(
    async (overrideId: number | null, data: CourseApiPayloads["update"]) => {
      setError(null);
      setUpdateData(data);
      if (courseId !== overrideId) {
        setCourseId(overrideId || id || null);
        setShouldUpdate(true);
      } else {
        await performUpdate(data);
      }
    },
    [courseId, id],
  );

  return { update, isLoading, error };
}

/**
 * Hook for publishing a course
 * @param id Optional initial course ID
 * @returns Functions and state for course publishing
 */
export function usePublishCourse(id?: number | null) {
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const [courseId, setCourseId] = useState<number | null>(id || null);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  const { trigger, isLoading } = useApi<CourseApiTypes["publish"]>(
    courseId ? COURSE_API_ENDPOINTS.publish(courseId).url : null,
    { method: "POST" },
  );

  const publish = async () => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!courseId) throw new Error("Course ID is required");

      const result = await trigger({});
      toast({
        title: "Course published",
        description: "The course has been published successfully",
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: "Error publishing course",
        description: errorMessage || "An error occurred while publishing the course",
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldUpdate(false);
    }
  };

  useEffect(() => {
    if (shouldUpdate) {
      publish();
    }
  }, [shouldUpdate, courseId]);

  const publishCourse = useCallback(
    async (overrideId?: number | null) => {
      setError(null);
      if (courseId !== overrideId) {
        setCourseId(overrideId || id || null);
        setShouldUpdate(true);
      } else {
        await publish();
      }
    },
    [courseId, id],
  );

  return { publishCourse, isLoading, error };
}

/**
 * Hook for archiving a course
 * @param id Optional initial course ID
 * @returns Functions and state for course archiving
 */
export function useArchiveCourse(id?: number | null) {
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const [courseId, setCourseId] = useState<number | null>(id || null);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  const { trigger, isLoading } = useApi<CourseApiTypes["archive"]>(
    courseId ? COURSE_API_ENDPOINTS.archive(courseId).url : null,
    { method: "POST" },
  );

  const archive = async () => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!courseId) throw new Error("Course ID is required");

      const result = await trigger({});
      toast({
        title: "Course archived",
        description: "The course has been archived successfully",
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: "Error archiving course",
        description: errorMessage || "An error occurred while archiving the course",
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldUpdate(false);
    }
  };

  useEffect(() => {
    if (shouldUpdate) {
      archive();
    }
  }, [shouldUpdate, courseId]);

  const archiveCourse = useCallback(
    async (overrideId?: number | null) => {
      setError(null);
      if (courseId !== overrideId) {
        setCourseId(overrideId || id || null);
        setShouldUpdate(true);
      } else {
        await archive();
      }
    },
    [courseId, id],
  );

  return { archiveCourse, isLoading, error };
}

/**
 * Hook for duplicating a course
 * @param id Optional initial course ID
 * @returns Functions and state for course duplication
 */
export function useDuplicateCourse(id?: number | null) {
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const [courseId, setCourseId] = useState<number | null>(id || null);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  // Get the course details
  const { data: courseDetails, isLoading: isLoadingDetails } = useApi<CourseApiTypes["getById"]>(
    courseId ? COURSE_API_ENDPOINTS.getById(courseId).url : null,
  );

  // Create course hook
  const { url, method } = COURSE_API_ENDPOINTS.create();
  const { trigger: createTrigger, isLoading: isCreating } = useApi<CourseApiTypes["create"]>(url, {
    method: method as "POST",
  });

  const duplicate = async () => {
    try {
      if (!createTrigger) throw new Error("API not initialized");
      if (!courseId) throw new Error("Course ID is required");
      if (!courseDetails) throw new Error("Course details not loaded");

      // Create new course data with copied fields and modified name
      const duplicateData: CourseApiPayloads["create"] = {
        name: `${courseDetails.name} - copy`,
        description: courseDetails.description,
        bannerImage: courseDetails.bannerImage,
        cardImage: courseDetails.cardImage,
        thumbnail: courseDetails.thumbnail,
        lmsId: courseDetails.lmsId,
        productFamilyId: courseDetails.productFamily.id,
        managers:
          courseDetails.managers?.map((manager) =>
            typeof manager === "string" ? manager : manager.id,
          ) || [],
        offers: courseDetails.offers || [],
      };

      const result = await createTrigger(duplicateData);
      toast({
        title: "Course duplicated",
        description: "The course has been duplicated successfully",
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: "Error duplicating course",
        description: errorMessage || "An error occurred while duplicating the course",
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldUpdate(false);
    }
  };

  useEffect(() => {
    if (shouldUpdate && courseDetails) {
      duplicate();
    }
  }, [shouldUpdate, courseDetails]);

  const duplicateCourse = useCallback(
    async (overrideId?: number | null) => {
      setError(null);
      if (courseId !== overrideId) {
        setCourseId(overrideId || id || null);
        setShouldUpdate(true);
      } else if (courseDetails) {
        await duplicate();
      }
    },
    [courseId, id, courseDetails],
  );

  return {
    duplicateCourse,
    isLoading: isLoadingDetails || isCreating,
    error,
  };
}

/**
 * Hook for fetching students for a course
 * @param courseId Course ID to fetch students for
 * @param params Pagination and search parameters
 * @returns SWR response with students data
 */
export function useGetStudentsForCourse(courseId?: number | null, params?: ListCoursesParams) {
  const apiConfig = useMemo(() => {
    if (!courseId) return null;
    const { url } = COURSE_API_ENDPOINTS.getStudents(courseId, params);
    return url;
  }, [courseId, params]);

  return useApi<CourseApiTypes["getStudents"]>(apiConfig);
}

/**
 * Hook for fetching courses by offer ID
 * @param offerId Offer ID to fetch courses for
 * @returns SWR response with courses data
 */
export function useGetCoursesByOffer(offerId?: number | null) {
  const apiConfig = useMemo(() => {
    if (!offerId) return null;
    const { url } = COURSE_API_ENDPOINTS.getByOfferId(offerId);
    return url;
  }, [offerId]);

  return useApi<CourseApiTypes["getByOfferId"]>(apiConfig);
}
