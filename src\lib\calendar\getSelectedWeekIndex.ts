"use client";

import { getYear, getMonth, getDate, getTime } from "date-fns";

/**
 * Finds the index of the week that contains the specified selected date.
 * The function searches through an array of weeks, where each week is an array of days.
 * Additionally, it checks if the start time (hour) matches on the selected date.
 *
 * @param {Date} selectedDate - The specific date to locate within the array of weeks.
 * @param {Date[][]} weeks - An array of weeks, where each week is an array of day objects.
 * @param {number} startTime - The starting hour to match for the selected date.
 * @returns {number} The index of the week containing the selected date, or -1 if not found.
 */
export default function getSelectedWeekIndex(
  selectedDate: Date, // The selected date to search for in the weeks
  weeks: Date[][], // An array of weeks (each week is an array of days)
  startTime: number, // The start time (hour) to match on the selected date
) {
  // Get the year, month, and day from the selected date
  const _year = getYear(selectedDate); // Extract the year from the selected date
  const _month = getMonth(selectedDate); // Extract the month from the selected date (0-indexed)
  const _day = getDate(selectedDate); // Extract the day of the month from the selected date

  // Use reduce to iterate over each week and find the index of the week containing the selected date
  return weeks.reduce(
    (position: number, week: any, index: number) =>
      // Check if any day in the current week matches the selected date with the specified startTime
      week.find(
        (day: Date) =>
          // Compare the time of each day in the week to the time of the selected date with the startTime
          getTime(day) === getTime(new Date(_year, _month, _day, startTime, 0, 0)), // Create a new date with the selected year, month, day, and startTime (hour)
      )
        ? (position = index) // If a match is found, set the position (index) to the current week index
        : position, // If no match is found, retain the previous position
    0, // Initial value of position is 0, indicating the first week
  );
}
