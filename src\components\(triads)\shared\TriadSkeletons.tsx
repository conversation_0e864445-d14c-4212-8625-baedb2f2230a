import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TriadTableSkeletonProps {
  showSessionType?: boolean;
}

export function TriadTableSkeleton({ showSessionType = false }: TriadTableSkeletonProps) {
  // Calculate number of columns based on whether session type is shown
  const columnCount = showSessionType ? 7 : 6;

  return (
    <div className="bg-card overflow-hidden rounded-2xl border border-[#2C2C2C]">
      <Table>
        <TableHeader>
          <TableRow className="border-x-none border-collapse border-b-2 border-b-[#111111] hover:bg-transparent">
            {Array.from({ length: columnCount }).map((_, index) => (
              <TableHead
                key={`skeleton-header-${index}`}
                className={`px-6 py-2 text-white ${
                  index === columnCount - 1 ? "bg-background-tertiary sticky right-0" : ""
                }`}
              >
                <Skeleton
                  className={`h-4 ${index === columnCount - 1 ? "w-16" : "w-24"} bg-white/10`}
                />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 5 }).map((_, rowIndex) => (
            <TableRow
              key={`skeleton-row-${rowIndex}`}
              className={`transition-all duration-100 ${
                rowIndex % 2 === 0 ? "bg-background-tertiary/80" : "bg-background-tertiary/60"
              } border-x-none border-collapse border-y-2 border-[#111]`}
            >
              {Array.from({ length: columnCount }).map((_, cellIndex) => {
                // Vary widths based on column type
                const getSkeletonContent = () => {
                  // Title column
                  if (cellIndex === 0) {
                    return <Skeleton className="h-4 w-32 bg-white/10" />;
                  }
                  // Availability column
                  if (cellIndex === 1) {
                    return <Skeleton className="h-6 w-16 rounded-full bg-white/10" />;
                  }
                  // Date/time column
                  if (cellIndex === 2) {
                    return (
                      <>
                        <Skeleton className="h-4 w-36 bg-white/10" />
                        <Skeleton className="mt-2 h-3 w-16 bg-white/10" />
                      </>
                    );
                  }
                  // Duration column
                  if (cellIndex === 3) {
                    return <Skeleton className="h-4 w-16 bg-white/10" />;
                  }
                  // Session type column (if shown)
                  if (cellIndex === 4 && showSessionType) {
                    return <Skeleton className="h-6 w-12 rounded-full bg-white/10" />;
                  }
                  // Sessions/Participants column
                  if (
                    (cellIndex === 4 && !showSessionType) ||
                    (cellIndex === 5 && showSessionType)
                  ) {
                    return (
                      <div className="flex space-x-1">
                        <Skeleton className="h-6 w-12 rounded-full bg-white/10" />
                        {rowIndex % 2 === 0 && (
                          <Skeleton className="h-6 w-6 rounded-full bg-white/10" />
                        )}
                      </div>
                    );
                  }
                  // Participants column
                  if (
                    (cellIndex === 5 && !showSessionType) ||
                    (cellIndex === 6 && showSessionType)
                  ) {
                    return (
                      <>
                        <Skeleton className="h-4 w-28 bg-white/10" />
                        <Skeleton className="mt-2 h-3 w-12 bg-white/10" />
                      </>
                    );
                  }
                  // Action column (last)
                  if (cellIndex === columnCount - 1) {
                    return <Skeleton className="mx-auto h-8 w-24 rounded-full bg-white/10" />;
                  }

                  return <Skeleton className="h-4 w-20 bg-white/10" />;
                };

                return (
                  <TableCell
                    key={`skeleton-cell-${rowIndex}-${cellIndex}`}
                    className={`px-6 py-3 text-left text-gray-200 ${
                      cellIndex === columnCount - 1
                        ? "bg-background-tertiary/80 sticky right-0"
                        : ""
                    }`}
                  >
                    {getSkeletonContent()}
                  </TableCell>
                );
              })}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

export function TriadListSkeleton() {
  return (
    <div className="space-y-4 py-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card
          key={`skeleton-${i}`}
          className={cn(
            "bg-background/80 border-none shadow-lg",
            i === 0 && "rounded-t-3xl rounded-b-none",
            i === 2 && "rounded-t-none rounded-b-3xl",
            i === 1 && "rounded-none",
          )}
        >
          <CardContent className="space-y-4 p-4">
            <Skeleton className="h-8 w-2/3 bg-white/10" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-24 rounded-full bg-white/10" />
              <Skeleton className="h-5 w-24 rounded-full bg-white/10" />
            </div>
            <div className="space-y-3">
              <Skeleton className="h-4 w-full bg-white/10" />
              <Skeleton className="h-4 w-3/4 bg-white/10" />
              <Skeleton className="h-4 w-5/6 bg-white/10" />
            </div>
            <Skeleton className="mt-2 h-9 w-full rounded-full bg-white/10" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

interface TriadViewSkeletonProps {
  isMobile: boolean;
  showSessionType?: boolean;
}

export function TriadViewSkeleton({ isMobile, showSessionType = false }: TriadViewSkeletonProps) {
  return (
    <div className="space-y-4">
      {/* Header skeleton based on screen size */}
      {isMobile ? (
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-32 bg-white/10" />
          <div className="flex items-center gap-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-10 w-10 rounded-full bg-white/10" />
            ))}
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="flex items-start justify-between">
            <div>
              <Skeleton className="mb-2 h-8 w-36 bg-white/10" />
              <Skeleton className="h-4 w-64 bg-white/10" />
            </div>
            <div className="flex flex-col items-end gap-4 lg:flex-row">
              <Skeleton className="bg-primary/50 h-10 w-[300px] lg:order-2" />
              <div className="flex items-center gap-4 lg:order-1">
                <Skeleton className="h-10 w-10 rounded-full bg-white/10" />
                <Skeleton className="h-10 w-10 rounded-full bg-white/10" />
                <Skeleton className="h-10 flex-1 rounded-full bg-white/10" />
              </div>
            </div>
          </div>
          {/* Filter bar skeleton */}
          <div className="scrollbar-hidden flex gap-2 overflow-x-auto py-1">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-8 w-24 flex-shrink-0 rounded-full bg-white/10" />
            ))}
          </div>
        </div>
      )}

      {/* Content skeleton based on screen size */}
      {isMobile ? <TriadListSkeleton /> : <TriadTableSkeleton showSessionType={showSessionType} />}
    </div>
  );
}
