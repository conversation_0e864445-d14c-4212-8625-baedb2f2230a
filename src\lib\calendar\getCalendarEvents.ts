"use client";

import { compareAsc, format } from "date-fns";

import { CalendarEvent } from "@/hooks/store/calendar";

/**
 * Retrieves and organizes calendar events for a specified day.
 *
 * @param events - An array of `CalendarEvent` objects representing all available events.
 * @param day - A `Date` object representing the day for which to retrieve events.
 * @returns An object containing:
 *   - `dayEvents`: An array of events occurring on the specified day, sorted by start time.
 *   - `eventsByHour`: An array of objects, each representing an hour of the day with the count of events occurring at that hour.
 *   - `monthEvents`: An array of all events sorted by start time, irrespective of the day.
 */

export default function getCalendarEvent(events: CalendarEvent[], day: Date) {
  // Sort the events by their start time (`begin`) in ascending order.
  const monthEvents =
    events.sort((a: CalendarEvent, b: CalendarEvent) => {
      return compareAsc(new Date(a.begin), new Date(b.begin));
    }) || [];

  // Filter the events that occur on the given `day` (ignoring the time).
  const dayEvents = monthEvents.filter(
    (event: CalendarEvent) => format(new Date(event.begin), "yyyyMMdd") === format(day, "yyyyMMdd"),
  );

  // Extract the hour from the `begin` time of each event and sort the hours in ascending order.
  const dayHoursEvents = dayEvents
    .map((event: any) => new Date(event.begin).getHours())
    .sort((numberA: number, numberB: number) => numberA - numberB);

  // Group events by the hour they occur, counting the number of events per hour.
  const eventsByHour = dayHoursEvents.reduce((acc: any[], hour: number) => {
    // Calculate the number of events that occur at the same hour.
    const len = dayHoursEvents.filter((eventHour: number) => eventHour === hour).length;
    // If this hour is not already in the accumulator, add it with its event count.
    !acc.some((accItem: any) => accItem.hour === hour) && acc.push({ hour, len });

    return acc;
  }, []);

  // Return the filtered events for the day, the grouped events by hour, and all sorted events for the month
  return { dayEvents, eventsByHour, monthEvents };
}
