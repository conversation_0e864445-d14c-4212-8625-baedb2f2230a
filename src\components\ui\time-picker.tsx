"use client";

import * as React from "react";
import { Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useTranslations } from "next-intl";

interface TimePickerProps {
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  className?: string;
}

export function TimePicker({ value, onChange, className }: TimePickerProps) {
  const t = useTranslations();
  const [selectedTime, setSelectedTime] = React.useState<Date | undefined>(value);

  // Sync internal state with external value
  React.useEffect(() => {
    setSelectedTime(value);
  }, [value]);

  const hours = Array.from({ length: 24 }, (_, i) => i);
  const minutes = Array.from({ length: 12 }, (_, i) => i * 5);

  const handleHourClick = (hour: number) => {
    const newDate = new Date();
    newDate.setHours(hour, selectedTime?.getMinutes() || 0, 0, 0);
    setSelectedTime(newDate);
    onChange?.(newDate);
  };

  const handleMinuteClick = (minute: number) => {
    const newDate = new Date();
    newDate.setHours(selectedTime?.getHours() || 0, minute, 0, 0);
    setSelectedTime(newDate);
    onChange?.(newDate);
  };

  return (
    <div className={cn("grid w-full gap-2", className)}>
      <Popover modal={true}>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal",
              "bg-background rounded-full border-none font-medium text-white",
              !selectedTime && "text-muted-foreground",
            )}
          >
            <Clock className="mr-2 h-4 w-4" />
            {selectedTime ? format(selectedTime, "HH'H'mm") : t("time_picker.select_time")}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="bg-background w-[var(--radix-popover-trigger-width)] rounded-2xl border-none p-0 text-white">
          <div className="flex gap-2 p-2">
            <div className="flex flex-1 flex-col">
              <div className="px-2 py-1 text-xs text-gray-400">{t("time_picker.hours")}</div>
              <ScrollArea className="h-52">
                <div className="flex flex-col gap-1">
                  {hours.map((hour) => (
                    <Button
                      key={hour}
                      variant="ghost"
                      className={cn(
                        "h-8 px-3 py-1 hover:bg-white/10 hover:text-white",
                        selectedTime?.getHours() === hour && "bg-primary hover:bg-primary/50",
                      )}
                      onClick={() => handleHourClick(hour)}
                    >
                      {hour.toString().padStart(2, "0")}
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>
            <div className="flex flex-1 flex-col">
              <div className="px-2 py-1 text-xs text-gray-400">{t("time_picker.minutes")}</div>
              <ScrollArea className="h-52">
                <div className="flex flex-col gap-1">
                  {minutes.map((minute) => (
                    <Button
                      key={minute}
                      variant="ghost"
                      className={cn(
                        "h-8 px-3 py-1 hover:bg-white/10",
                        selectedTime?.getMinutes() === minute && "bg-primary hover:bg-primary/50",
                      )}
                      onClick={() => handleMinuteClick(minute)}
                    >
                      {minute.toString().padStart(2, "0")}
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
