import { Module } from '@nestjs/common';
import {
  CreditNoteUseCasesModule,
  TransactionUseCasesModule,
  ProductUseCasesModule,
  ProductFamilyUseCasesModule,
  ProductPlanUseCasesModule,
  ProductPlanPriceUseCasesModule,
  ProductLineUseCasesModule,
  DiscountUseCasesModule,
  WebhooksUseCasesModule,
  CheckoutPageUseCasesModule,
  ProductOfferUseCasesModule,
  WarrantySectionUseCasesModule,
  InvoiceUseCasesModule,
  BankTransferModule,
  SubscriptionUseCasesModule,
  CronLogUseCasesModule,
  CustomerAccessModule,
  ProductDeliveryModule,
  ExchangeRateModule,
  GoliathsModule,
  StatsUseCasesModule,
  CourseModule,
  CohortModule,
  RoleModule,
  UserModule,
  TriadsModule,
  WhatsNewModule,
  PortalSessionUseCasesModule,
  HttpScheduleModule,
} from '@useCases';
import * as allControllers from '.';
import { HubspotModule } from '@services/crm';
import { AuthModule } from '@auth';

@Module({
  imports: [
    AuthModule,
    BankTransferModule,
    CheckoutPageUseCasesModule,
    UserModule,
    CustomerAccessModule,
    ProductDeliveryModule,
    ProductUseCasesModule,
    ProductFamilyUseCasesModule,
    ProductLineUseCasesModule,
    ProductPlanUseCasesModule,
    ProductPlanPriceUseCasesModule,
    TransactionUseCasesModule,
    CreditNoteUseCasesModule,
    DiscountUseCasesModule,
    GoliathsModule,
    WebhooksUseCasesModule,
    ProductOfferUseCasesModule,
    WarrantySectionUseCasesModule,
    InvoiceUseCasesModule,
    HubspotModule,
    SubscriptionUseCasesModule,
    CronLogUseCasesModule,
    ExchangeRateModule,
    StatsUseCasesModule,
    WebhooksUseCasesModule,
    CourseModule,
    CohortModule,
    RoleModule,
    TriadsModule,
    WhatsNewModule,
    PortalSessionUseCasesModule,
    HttpScheduleModule,
  ],
  providers: [],
  controllers: [...Object.values(allControllers)],
})
export class ControllersModule {}
