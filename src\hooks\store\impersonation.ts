import { create } from "zustand";
import { clientImpersonation, ImpersonationData } from "@/lib/impersonation-utils";

interface ImpersonationState extends ImpersonationData {
  setImpersonation: (email: string, originalEmail: string) => void;
  clearImpersonation: () => void;
}

// Initial state from cookie
const initialState = clientImpersonation.getData() || {
  isImpersonating: false,
  impersonatedEmail: null,
  originalEmail: null,
};

/**
 * Store for managing impersonation state
 * Maintains state sync with browser cookies via clientImpersonation utilities
 */
export const useImpersonationStore = create<ImpersonationState>((set) => ({
  ...initialState,

  setImpersonation: (email: string, originalEmail: string) => {
    const newState = {
      isImpersonating: true,
      impersonatedEmail: email,
      originalEmail,
    };

    // Update both store and cookie
    set(newState);
    clientImpersonation.setData(newState);
  },

  clearImpersonation: () => {
    const newState = {
      isImpersonating: false,
      impersonatedEmail: null,
      originalEmail: null,
    };

    // Update both store and cookie
    set(newState);
    clientImpersonation.clearImpersonation();
  },
}));
