"use client";

import { create } from "zustand";

/** Represents a mapping of permission keys to arrays of allowed values */
type Permissions = Record<string, string>;

/** Fetches permissions from the API endpoint */
const fetchPermissions = (): Promise<Permissions> =>
  fetch("/api/permissions").then((res) => res.json());

/**
 * Store interface for managing permissions
 * @interface PermissionStore
 * @property {Permissions} permissions - Current permissions state
 * @property {() => Promise<Permissions>} fetch - Function to fetch permissions from API
 * @property {(permissions: Permissions) => void} setPermissions - Updates the permissions state
 */
export type PermissionStore = {
  permissions: Permissions;
  fetch: () => Promise<Permissions>;
  setPermissions: (permissions: Permissions) => void;
};

/**
 * Zustand store for managing application permissions
 * Provides methods to fetch and update permissions state
 */
const usePermissionStore = create<PermissionStore>((set) => ({
  permissions: {},
  fetch: fetchPermissions,
  setPermissions: (permissions: Permissions) => {
    set({ permissions });
  },
}));

export default usePermissionStore;
