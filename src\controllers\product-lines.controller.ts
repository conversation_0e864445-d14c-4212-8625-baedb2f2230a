import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateProductLineDTO, UpdateProductLineDTO } from '@dtos';
import { ProductLineUseCases } from '@useCases';
import { JwtPermissionsGuard } from '@auth';

@ApiTags('product-lines')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('product-lines')
export class ProductLinesController {
  constructor(private readonly productLineUseCases: ProductLineUseCases) {}

  @ApiOperation({ summary: 'Create a product line' })
  @Post('/')
  async createProductLine(@Body() productLine: CreateProductLineDTO) {
    return this.productLineUseCases.create(productLine);
  }

  @ApiOperation({ summary: 'List all product lines' })
  @Get('/')
  async getProductLines(
    @Query('name') name: string,
    @Query('status') status: string,
    @Query('limit') limit = 500,
    @Query('page') page = 1,
    @Query('order') orderBy?: 'DESC' | 'ASC',
  ) {
    return this.productLineUseCases.searchByNameOrStatus(
      name,
      status,
      limit,
      page,
      orderBy,
    );
  }

  @ApiOperation({ summary: 'Get product line for the specified `id`' })
  @Get('/:id')
  async getProductLine(@Param('id') id: string) {
    return this.productLineUseCases.getOne(Number(id));
  }

  @ApiOperation({ summary: 'Update a product line for the specified `id`' })
  @Patch('/:id')
  async updateProductLine(
    @Param('id') id: string,
    @Body() updates: UpdateProductLineDTO,
  ) {
    return this.productLineUseCases.updateById(Number(id), updates);
  }
}
