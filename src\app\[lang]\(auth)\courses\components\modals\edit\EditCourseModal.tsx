"use client";

import { MultiStepFormModal } from "@/components/base/multi-step-form-modal";
import { ModalConfig } from "@/components/base/multi-step-form-modal/types";
import { useTranslations } from "next-intl";
import { useUpdateCourse } from "@/services/course";
import { useListOffers } from "@/services/product-offers";
import { createCourseValidationSchema } from "../create/validation";
import { useToast } from "@/hooks/use-toast";
import { useMemo } from "react";
import { IBaseCourse } from "@px-shared-account/hermes";
import { ICourseOffer } from "@px-shared-account/hermes";

interface EditCourseModalProps {
  course: IBaseCourse;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onComplete?: (data: any) => Promise<void>;
}

export const EditCourseModal = ({
  course,
  isOpen,
  onOpenChange,
  onComplete,
}: EditCourseModalProps) => {
  const tc = useTranslations("courses");
  const { toast } = useToast();
  const { update } = useUpdateCourse(course.id);

  // Get all offers without backend sorting (we'll sort on client side)
  const { data: offersData, isLoading: isLoadingOffers } = useListOffers();

  const validationSchema = createCourseValidationSchema(tc);

  // Create options for the MultiSelect component from the offers data
  const offerOptions = useMemo(() => {
    if (!offersData?.items) return [];

    // Sort offers by createdAt in descending order (newest first)
    const sortedOffers = [...offersData.items].sort((a, b) => {
      const dateA = new Date(a.createdAt || 0).getTime();
      const dateB = new Date(b.createdAt || 0).getTime();
      return dateB - dateA; // Descending order (newest first)
    });

    return sortedOffers.map((offer) => ({
      value: String(offer.id),
      label: offer.name,
    }));
  }, [offersData]);

  // Prepare the currently selected offers for initialData
  const selectedOffers = useMemo(() => {
    if (!course.offers || !Array.isArray(course.offers)) return [];

    // Handle both array of objects with id property and array of id values
    return course.offers.map((offer) => {
      if (typeof offer === "object" && offer !== null && "id" in offer) {
        return String(offer.id);
      } else {
        return String(offer);
      }
    });
  }, [course]);

  const config = useMemo<ModalConfig>(
    () => ({
      key: "edit-course",
      title: tc("edit.title"),
      confirmClose: true,
      confirmReset: true,
      initialData: {
        name: course.name,
        description: course.description,
        bannerImage: course.bannerImage,
        cardImage: course.cardImage,
        thumbnail: course.thumbnail,
        managers: course.managers,
        offers: selectedOffers, // Add the selected offers to initialData
      },
      submitButtons: [
        {
          label: tc("edit.submit.save"),
          variant: "default",
          className: "bg-burgundy hover:bg-burgundy/90",
          isPrimary: true,
        },
      ],
      steps: [
        {
          id: "course-details",
          title: tc("edit.title"),
          fields: [
            {
              id: "images-layout",
              type: "layout",
              label: "",
              meta: {
                layout: {
                  type: "grid",
                  className: "flex flex-row",
                  fields: [
                    {
                      id: "cardImage",
                      type: "image-upload",
                      label: tc("fields.card_image"),
                      required: true,
                      meta: {
                        aspectRatio: "9/16",
                        maxSize: 5,
                        acceptedTypes: [".jpeg", ".jpg", ".png"],
                        className: "aspect-[9/16] overflow-hidden",
                      },
                      validation: validationSchema.shape.card,
                    },
                    {
                      id: "right-column-layout",
                      type: "layout",
                      label: "",
                      meta: {
                        layout: {
                          type: "grid",
                          className: "flex flex-col",
                          fields: [
                            {
                              id: "bannerImage",
                              type: "image-upload",
                              label: tc("fields.cover_image"),
                              required: true,
                              meta: {
                                aspectRatio: "21/9",
                                maxSize: 5,
                                acceptedTypes: [".jpeg", ".jpg", ".png"],
                                className: "aspect-[2/1] overflow-hidden",
                              },
                              validation: validationSchema.shape.cover,
                            },
                            {
                              id: "thumbnail",
                              type: "image-upload",
                              label: tc("fields.thumbnail_image"),
                              required: true,
                              meta: {
                                aspectRatio: "1/1",
                                maxSize: 5,
                                acceptedTypes: [".jpeg", ".jpg", ".png"],
                                className: "aspect-square max-w-64 overflow-hidden",
                              },
                              validation: validationSchema.shape.thumbnail,
                            },
                          ],
                        },
                      },
                    },
                  ],
                },
              },
            },
            {
              id: "basic-info-layout",
              type: "layout",
              label: "",
              meta: {
                layout: {
                  type: "grid",
                  className: "grid grid-cols-1",
                  fields: [
                    {
                      id: "name",
                      type: "text",
                      label: tc("fields.name"),
                      required: true,
                      validation: validationSchema.shape.name,
                    },
                    {
                      id: "description",
                      type: "long-text",
                      label: tc("fields.description"),
                      required: true,
                      validation: validationSchema.shape.description,
                    },
                    // Add the offers field
                    {
                      id: "offers",
                      type: "combobox",
                      label: tc("fields.offers"),
                      isMulti: true,
                      options: offerOptions,
                      placeholder: tc("fields.offers_placeholder"),
                      disabled: isLoadingOffers,
                      description: tc("fields.offers_description"),
                    },
                  ],
                },
              },
            },
          ],
        },
      ],
    }),
    [tc, validationSchema.shape, course, offerOptions, isLoadingOffers, selectedOffers],
  );

  const handleComplete = async (data: any) => {
    try {
      // Get the full offer objects from the offersData based on selected IDs
      const selectedOfferObjects = data.offers
        ? data.offers
            .map((id: string) => {
              // Find the full offer object from offersData
              const offer = offersData?.items?.find((item: ICourseOffer) => item.id === Number(id));

              if (offer) {
                // Return only the necessary offer fields without the image
                return {
                  id: offer.id,
                  name: offer.name,
                  status: offer.status,
                };
              } else {
                // If we can't find the offer, it's better to skip it than provide invalid data
                console.warn(`Offer with ID ${id} not found in available offers`);
                return null;
              }
            })
            .filter(Boolean) // Remove any null entries
        : course.offers || [];

      // Format the course data according to the API spec
      const courseData = {
        name: data.name || course.name,
        description: data.description || course.description,
        bannerImage: data.bannerImage || course.bannerImage,
        cardImage: data.cardImage || course.cardImage,
        thumbnail: data.thumbnail || course.thumbnail,
        managers: data.managers || course.managers,
        offers: selectedOfferObjects,
      };

      // Update the course using the real API
      const updatedCourse = await update(course.id, courseData);

      toast({
        title: tc("edit.success.title"),
        description: tc("edit.success.description"),
        variant: "default",
      });

      // Call the onComplete callback if provided
      if (onComplete) {
        await onComplete(updatedCourse);
      }
    } catch (error) {
      toast({
        title: tc("edit.error.title"),
        description: error instanceof Error ? error.message : tc("edit.error.description"),
        variant: "destructive",
      });
      throw error;
    }
  };

  return (
    <MultiStepFormModal
      config={config}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      events={{
        onComplete: handleComplete,
      }}
    />
  );
};
