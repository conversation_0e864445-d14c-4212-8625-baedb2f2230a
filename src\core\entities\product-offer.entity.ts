import {
  ProductOfferStatus,
  ProductOfferVersion,
} from '@px-shared-account/hermes';
import { FiscalEntity } from '@enums';
import { CheckoutPage, Product, ProductPlan } from '.';
import { CommunityAccesses, LMSAccesses, OfferConfig } from '@types';

export class ProductOffer {
  id: number;
  name: string;
  externalName: string;
  image: string;
  cardImage: string;
  bannerImage?: string;
  description?: string;
  usp?: string;
  currency: string;
  fiscalEntity: FiscalEntity;
  status: ProductOfferStatus;
  redirectUrl: string;
  redirectIfDisabled?: string;
  checkoutPage: CheckoutPage;
  chargebeeId: string;
  history?: Omit<this, 'id' | 'history' | 'updateHistory'>[];
  version: ProductOfferVersion;
  config?: OfferConfig;
  paymentGateway?: string;
  slug: string;
  products?: Product[];
  plans?: ProductPlan[];
  isForever?: boolean;
  trialDaysMonthly?: number;
  trialDaysYearly?: number;
  withProductDelivery?: boolean;
  suspendable?: boolean;
  delayPeriod?: number;
  lmsIds?: LMSAccesses;
  communityIds?: CommunityAccesses;
  createdAt: Date;
  updatedAt: Date;
}
