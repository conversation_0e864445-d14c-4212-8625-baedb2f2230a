"use client";

import { motion } from "framer-motion";
import { useEffect, useMemo, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  avatarVariants,
  cardVariants,
  containerVariants,
  itemVariants,
  radialChartConfig,
  radialChartData,
} from "@/lib/goliaths";
import { statsVariants } from "@/lib/goliaths";
import { ChartContainer } from "../ui/chart";
import { Pie, PieChart } from "recharts";
import { Avatar, AvatarImage } from "../ui/avatar";
import { AnimatedNumber } from "./AnimatedNumber";
import GoliathModal from "./GoliathsModal";
import { useTranslations } from "next-intl";
import { useUser } from "@clerk/nextjs";
import { useUserDetailsBySSOId } from "@/services/user/client";
import DiscountCard from "./DiscountCard";
import ChartsSection from "./ChartsSection";
import { BreadcrumbSettings } from "../(settings)/BreadcrumbSettings";

export default function GoliathsLandingPage() {
  const t = useTranslations("goliaths");
  const [open, setOpen] = useState(false);
  const [values, setValues] = useState({
    traders: 0,
    followers: 0,
    following: 0,
  });

  const handleOpenModal = () => setOpen(true);

  const { user } = useUser();
  const { data: detailedUser } = useUserDetailsBySSOId(user?.id);
  const discountCode = detailedUser?.metaData?.discountCode;

  const chartsSection = useMemo(
    () => <ChartsSection onOpenModal={handleOpenModal} />,
    [handleOpenModal],
  );

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      traders: 89,
      followers: 27650,
      following: 3,
    }));
  }, []);

  return (
    <>
      <div className="px-4">
        <BreadcrumbSettings active="/goliaths" />
        <div className="my-4 text-2xl font-bold text-white">Goliaths</div>
      </div>
      <motion.div
        className="flex flex-col items-center pt-6"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.div variants={avatarVariants}>
          <Avatar className="h-24 w-24">
            <AvatarImage src="/davidlaroche.png" />
          </Avatar>
        </motion.div>
        <motion.div variants={itemVariants}>
          <div className="font-anton my-3 text-4xl font-bold uppercase">David Laroche</div>
        </motion.div>
        <motion.div className="my-5 flex gap-2 text-white" variants={itemVariants}>
          <motion.div className="flex items-center gap-2" variants={statsVariants}>
            <AnimatedNumber value={values.traders} />
            <span>{t("trades")}</span>
            <span className="mx-2">|</span>
          </motion.div>
          <motion.div className="flex items-center gap-2" variants={statsVariants}>
            <AnimatedNumber value={values.followers} />
            <span>{t("followers")}</span>
            <span className="mx-2">|</span>
          </motion.div>
          <motion.div className="flex items-center gap-2" variants={statsVariants}>
            <AnimatedNumber value={values.following} />
            <span>{t("following")}</span>
          </motion.div>
        </motion.div>
        <DiscountCard discountCode={discountCode} onOpenModal={handleOpenModal} />
        <motion.div className="my-3 w-10/12" variants={cardVariants}>
          <Card className="w-full bg-[#171717]">
            <CardHeader>
              <CardTitle>
                <div className="font-anton text-2xl font-medium tracking-wide uppercase">
                  {t("products")}
                </div>
                <p className="text-base text-white">{t("products-description")}</p>
              </CardTitle>
            </CardHeader>
            <CardContent>{chartsSection}</CardContent>
          </Card>
        </motion.div>
        <motion.hr className="w-10/12" variants={itemVariants} />
        <motion.div className="my-3 w-10/12" variants={cardVariants}>
          <Card className="w-full bg-[#171717]">
            <CardHeader>
              <CardTitle>
                <div className="font-anton text-2xl font-medium tracking-wide uppercase">
                  {t("strategy")}
                </div>
                <p className="text-base text-white">{t("strategy-description")}</p>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex w-full items-center justify-between gap-8">
                <motion.div
                  className="max-h-[300px] w-full"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3, duration: 0.3 }}
                  style={{ width: "100%" }}
                >
                  <ChartContainer config={radialChartConfig} className="max-h-[300px] w-full">
                    <PieChart>
                      <Pie data={radialChartData} dataKey="visitors" />
                    </PieChart>
                  </ChartContainer>
                </motion.div>
                <div className="flex flex-col gap-4">
                  <motion.div
                    className="flex items-center gap-3"
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4, duration: 0.2 }}
                  >
                    <div className="h-3 w-3 rounded-full bg-[#8D2146]" />
                    <p className="text-sm font-semibold text-white">
                      {t("radial-chart.base-protective")}
                    </p>
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-3"
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.45, duration: 0.2 }}
                  >
                    <div className="h-3 w-3 rounded-full bg-[#5865F2]" />
                    <p className="text-sm font-semibold text-white">
                      {t("radial-chart.controlled-growth")}
                    </p>
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-3"
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5, duration: 0.2 }}
                  >
                    <div className="h-3 w-3 rounded-full bg-[#C6712B]" />
                    <p className="text-sm font-semibold text-white">
                      {t("radial-chart.ambitious-returns")}
                    </p>
                  </motion.div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
      <GoliathModal open={open} onOpenChange={setOpen} />
    </>
  );
}
