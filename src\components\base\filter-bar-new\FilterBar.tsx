/**
 * A responsive filter bar component that adapts between desktop and mobile views.
 *
 * @component
 * @example
 * ```tsx
 * <FilterBar
 *   steps={[
 *     { type: 'filter', id: 'program', label: 'Program', options: [] },
 *     { type: 'separator' },
 *     { type: 'filter', id: 'course', label: 'Course', options: [] }
 *   ]}
 *   onChange={(values) => console.log(values)}
 * />
 * ```
 *
 * @remarks
 * The component automatically switches between:
 * - A horizontal bar layout for desktop views
 * - A modal interface for mobile views
 *
 * Features include:
 * - Grouped filters with scroll indicators
 * - Dependent filters with automatic option fetching
 * - Error and loading state handling
 * - Responsive design with breakpoint detection
 */
"use client";

import * as React from "react";
import { FilterBarModal } from "./FilterBarModal";
import { cn } from "@/lib/utils";
import { FilterBarProps, Step, FilterStep, DateRangeStep, TimeRangeStep } from "./types";
import { useFilterBar } from "./useFilterBar";
import { GroupCarousel } from "./GroupCarousel";
import { Combobox } from "@/components/base/combobox";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { TimeRangePicker } from "@/components/ui/time-range-picker";
import { useBreakpoints } from "@/hooks/breakpoints";

export const FilterBar = React.forwardRef<HTMLDivElement, FilterBarProps>(
  ({ className, steps, value, onChange, onOptionsNeeded, forceModal, forceBar, ...props }, ref) => {
    /** Loading state for the modal view's apply action */
    const [loading, setLoading] = React.useState(false);

    /** Determines if the current viewport is desktop size */
    const sizes = useBreakpoints();
    const isDesktop = sizes !== "xs" && sizes !== "sm" && sizes !== "md";

    /** Hook providing filter bar state and logic */
    const { values, stepsState, groups, handleChange, isStepEnabled, clearAll, groupRefs } =
      useFilterBar({
        steps,
        value,
        onChange: (values) => {
          // Map the values change to individual step changes
          Object.entries(values).forEach(([stepId, value]) => {
            onChange?.(stepId, value);
          });
        },
        onOptionsNeeded,
      });

    /** Whether to show the modal view based on viewport and props */
    const showModal = forceModal || (!forceBar && !isDesktop);

    if (showModal) {
      return (
        <FilterBarModal
          steps={steps}
          values={values}
          stepsState={stepsState}
          handleChange={handleChange}
          isStepEnabled={isStepEnabled}
          clearAll={clearAll}
          loading={loading}
          onApply={async () => {
            setLoading(true);
            await new Promise((resolve) => setTimeout(resolve, 1000));
            setLoading(false);
          }}
          {...props}
        />
      );
    }

    return (
      <div
        ref={ref}
        className={cn(
          "divide-muted-foreground/20 flex w-full flex-wrap gap-4 divide-x-2",
          className,
        )}
      >
        {groups.map((group, groupIndex) => (
          <React.Fragment key={groupIndex}>
            <div
              className={cn(
                "min-w-[228px]",
                group.items.length > 1 ? "flex-[2_2_40%]" : "flex-[1_1_20%]",
              )}
            >
              <GroupCarousel
                group={group}
                groupRef={(el) => (groupRefs.current[groupIndex] = el)}
                wrapped={group.state.wrapped}
              >
                {group.items.map((step) => {
                  if (step.type === "separator") return null;
                  const stepState = stepsState[step.id];
                  const isEnabled = isStepEnabled(step.id);

                  if (step.type === "dateRange") {
                    const dateRangeStep = step as DateRangeStep;
                    return (
                      <div key={dateRangeStep.id}>
                        <DatePickerWithRange
                          value={
                            values[dateRangeStep.id]
                              ? JSON.parse(values[dateRangeStep.id].toString())
                              : undefined
                          }
                          onChange={(value) =>
                            handleChange(dateRangeStep.id, value ? JSON.stringify(value) : "")
                          }
                          placeholder={dateRangeStep.placeholder}
                          label={dateRangeStep.label}
                        />
                      </div>
                    );
                  }

                  if (step.type === "timeRange") {
                    const timeRangeStep = step as TimeRangeStep;
                    return (
                      <div key={timeRangeStep.id}>
                        <TimeRangePicker
                          value={
                            values[timeRangeStep.id]
                              ? JSON.parse(values[timeRangeStep.id].toString())
                              : undefined
                          }
                          onChange={(value) =>
                            handleChange(timeRangeStep.id, value ? JSON.stringify(value) : "")
                          }
                          placeholder={timeRangeStep.placeholder}
                        />
                      </div>
                    );
                  }

                  if (step.type === "filter") {
                    const filterStep = step as FilterStep;
                    return (
                      <div key={filterStep.id}>
                        <Combobox
                          value={values[filterStep.id] || ""}
                          onChange={(value) => handleChange(filterStep.id, value)}
                          placeholder={filterStep.placeholder}
                          options={stepState?.options || []}
                          error={stepState?.error}
                          disabled={!isEnabled}
                          label={filterStep.label}
                          triggerClassName="min-w-40"
                          contentClassName="z-[60]"
                          multiple={filterStep.multiple}
                        />
                      </div>
                    );
                  }

                  return null;
                })}
              </GroupCarousel>
            </div>
          </React.Fragment>
        ))}
      </div>
    );
  },
);

FilterBar.displayName = "FilterBar";
