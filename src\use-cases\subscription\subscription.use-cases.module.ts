import { Module } from '@nestjs/common';
import { SubscriptionFactoryService, SubscriptionUseCases } from '.';
import { ChargebeeBillingModule } from '@services/billing';
import { DataServicesModule } from '@services/database';
import { HubspotModule } from '@services/crm';
import { InvoiceUseCasesModule } from '../invoice';
import { ProductOfferUseCasesModule } from '../product-offers';
import { ProductDeliveryModule } from '../product-delivery';
import { CustomerAccessModule } from '../customer-access';
import { SlackModule } from '@services/notification';
import { UserModule } from '../user';
import { GoliathsModule } from '../goliaths/goliaths.module';

@Module({
  imports: [
    DataServicesModule,
    ChargebeeBillingModule,
    HubspotModule,
    InvoiceUseCasesModule,
    ProductOfferUseCasesModule,
    ProductDeliveryModule,
    CustomerAccessModule,
    SlackModule,
    UserModule,
    GoliathsModule,
  ],
  providers: [SubscriptionFactoryService, SubscriptionUseCases],
  exports: [SubscriptionFactoryService, SubscriptionUseCases],
})
export class SubscriptionUseCasesModule {}
