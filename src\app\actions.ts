"use server";

import { auth } from "@clerk/nextjs/server";
import {
  ICreate<PERSON><PERSON>,
  IUpdateRole,
  IUpdate<PERSON>ser,
  IBulkUpdateRole,
  ICreateCohort,
  ICreateCourse,
  IUpdateCourse,
} from "@px-shared-account/hermes";

const baseUrl = process.env.NEXT_PUBLIC_API_URL;

/**
 * Makes an authenticated HTTP request to the API
 * @param {string} url - The endpoint URL (will be appended to baseUrl)
 * @param {string} method - The HTTP method to use (GET, POST, PATCH, etc.)
 * @param {any} [body=null] - Optional request body data to send
 * @returns {Promise<any>} The parsed JSON response from the API
 * @throws {Error} If the fetch request fails
 */
const fetcher = async (url: string, method: string, body: any = null) => {
  const { getToken } = await auth();
  const token = await getToken();
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  console.log("DEBUG-fetcher", { url, method, body, headers });
  const response = await fetch(`${baseUrl}${url}`, {
    headers,
    method,
    body: body ? JSON.stringify(body) : null,
  });
  return response.json();
};
