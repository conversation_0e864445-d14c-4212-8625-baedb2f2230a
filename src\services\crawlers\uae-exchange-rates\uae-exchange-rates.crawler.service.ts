import * as cheerio from 'cheerio';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@config';
import { ExchangeRateLastUpdated, ExchangeRates } from '@types';

@Injectable()
export class UaeExchangeRatesCrawlerService {
  private readonly bankURL: string;
  private static readonly CurrencyArabicNames = {
    USD: 'دولار امريكي',
    EUR: 'يورو',
    GBP: 'جنيه استرليني',
    CAD: 'دولار كندي',
    CHF: 'فرنك سويسري',
  };

  private static readonly ArabicMonths = {
    يناير: 'January',
    فبراير: 'February',
    مارس: 'March',
    أبريل: 'April',
    مايو: 'May',
    يونيو: 'June',
    يوليو: 'July',
    أغسطس: 'August',
    سبتمبر: 'September',
    أكتوبر: 'October',
    نوفمبر: 'November',
    ديسمبر: 'December',
  };

  constructor(private readonly configService: ConfigService) {
    this.bankURL = this.configService.appSecrets.UAE_BANK_EXR_URL;
  }

  /**
   * Fetches exchange rates from UAE Central Bank
   * @returns Exchange rates
   */
  async getExchangeRates(): Promise<{
    rates: ExchangeRates;
    lastUpdated: ExchangeRateLastUpdated;
  }> {
    try {
      const response = await fetch(this.bankURL);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch data from ${this.bankURL}: ${response.statusText}`,
        );
      }

      const htmlText = await response.text();
      const cheerioDOM = cheerio.load(htmlText);
      const ExchangeRateLastUpdatedHtml = cheerioDOM('p:nth-child(4)');
      const exchangeRateTableRows = cheerioDOM('table tbody tr');

      if (!exchangeRateTableRows.length) {
        throw new Error('Exchange rate table not found');
      }

      const exchangeRates = {} as ExchangeRates;
      exchangeRateTableRows.each((_, row) => {
        const rowHTML = cheerio.load(row);
        const rowText = rowHTML.text();

        for (const [currency, arabicName] of Object.entries(
          UaeExchangeRatesCrawlerService.CurrencyArabicNames,
        )) {
          if (rowText.includes(arabicName)) {
            const rate = rowHTML('td:nth-child(3)').text().trim();
            exchangeRates[currency] = rate;
          }
        }
      });

      const lastUpdated = this.convertArabicDate(
        ExchangeRateLastUpdatedHtml.text(),
      );

      return { rates: exchangeRates, lastUpdated };
    } catch (err) {
      console.error(err);
      return null;
    }
  }

  /**
   * Converts an Arabic date string to English components
   * @param arabicDateString Date string in Arabic
   * @returns Object with date, year, and time in English
   */
  convertArabicDate(arabicDateString: string): ExchangeRateLastUpdated | null {
    const regex = /(\d{1,2})\s(\S+)\s(\d{4})\s(\d{2}):(\d{2}):(\d{2})\s([صم])/;
    const match = arabicDateString.match(regex);

    if (!match) {
      console.error('Unable to parse the Arabic date string.');
      return null;
    }

    const [, day, arabicMonth, year, hours, minutes, seconds, period] = match;

    const month =
      UaeExchangeRatesCrawlerService.ArabicMonths[arabicMonth] || 'Unknown';

    let hour = parseInt(hours, 10);
    if (period === 'م' && hour !== 12) {
      hour += 12;
    } else if (period === 'ص' && hour === 12) {
      hour = 0;
    }
    const time24 = `${hour.toString().padStart(2, '0')}:${minutes}:${seconds}`;

    return {
      date: `${day} ${month}`,
      year,
      time: time24,
    };
  }
}
