import { currentUser } from "@clerk/nextjs/server";
import <PERSON>is from "ioredis";

import { getUserBySSOId } from "@/services/user/server"; // Import server-side user service
import { existingPermissions } from "./shared";

// Ensure KV_URL is defined in your environment variables
const redisUrl = process.env.KV_URL;
if (!redisUrl) {
  if (process.env.NODE_ENV === "development") {
    console.warn(
      "KV_URL is not defined. Server-side permission checks will be disabled in development.",
    );
  } else {
    throw new Error("KV_URL is not defined. Server-side permission checks will fail.");
  }
}

// Initialize Redis client, allow it to be undefined in dev if KV_URL is not set
const redisClient = redisUrl ? new Redis(redisUrl, { connectTimeout: 10000 }) : undefined;

const permissionsHashKey = "permissions"; // The HASH key in Redis where keys are role names

/**
 * Checks if the current authenticated user has a specific permission on the server-side.
 * Relies on permissions being stored in a Redis HASH under the key "permissions",
 * where each field in the HASH is a user.id and its value is a JSON stringified array of permission strings.
 *
 * @param {string} permissionToCheck - The permission string (from Hermes definitions) to check for.
 * @returns {Promise<boolean>} True if the user has the permission, false otherwise.
 */
export const hasServerPermission = async (
  permissionToCheck: (typeof existingPermissions)[number],
): Promise<boolean> => {
  if (!redisClient) {
    // If Redis client is not initialized (e.g., KV_URL not set in dev), default to no permission.
    // Or, for development, you might want to default to true for certain checks if that helps.
    // For production, this case should not be reached due to the throw in initialization.
    console.warn("Redis client not available for hasServerPermission check.");
    return false;
  }

  const clerkUser = await currentUser();
  if (!clerkUser || !clerkUser.id) {
    return false; // No Clerk user or user.id, no permissions
  }

  // Validate if the permission string is a known one (good practice)
  if (!existingPermissions.includes(permissionToCheck)) {
    console.warn(`Attempted to check for an unknown server-side permission: ${permissionToCheck}`);
    return false;
  }

  try {
    // Fetch detailed user from our backend to get their role
    const detailedUser = await getUserBySSOId(clerkUser.id);
    if (!detailedUser || !detailedUser.role || typeof detailedUser.role.name !== "string") {
      console.warn(`Could not retrieve detailed user or role name for user ID: ${clerkUser.id}`);
      return false;
    }
    const userRoleName = detailedUser.role.name; // Assuming role.name is the string role key

    // Fetch the permissions string for this specific role name from the Redis HASH
    const rolePermissionsString = await redisClient.hget(permissionsHashKey, userRoleName);

    if (!rolePermissionsString) {
      // Role has no specific entry in the permissions hash
      console.warn(`No permissions found in Redis for role: ${userRoleName}`);
      return false;
    }

    let roleActualPermissions: string[] = [];
    try {
      roleActualPermissions = JSON.parse(rolePermissionsString);
      if (!Array.isArray(roleActualPermissions)) {
        console.error(
          `Parsed server permissions are not an array for role: ${userRoleName}, Value: ${rolePermissionsString}`,
        );
        return false;
      }
    } catch (parseError) {
      console.error(
        `Failed to parse server permissions string for role: ${userRoleName}, Error: ${parseError}, Value: ${rolePermissionsString}`,
      );
      return false;
    }

    return roleActualPermissions.includes(permissionToCheck);
  } catch (error) {
    // Catch errors from getUserBySSOId or Redis
    console.error(
      `Error during server permission check for user: ${clerkUser.id}, Role: [unknown], Permission: ${permissionToCheck}`,
      error,
    );
    return false;
  }
};
