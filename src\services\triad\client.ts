/* eslint-disable max-lines */
"use client";

import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import { useApi } from "@/hooks/store/use-api";
import { toast, useToast } from "@/hooks/use-toast";
import { TRIAD_API_ENDPOINTS, TriadApiTypes, ListTriadsParams, TriadApiPayloads } from "./core";
import { ITriadBase } from "@px-shared-account/hermes";
import { PlatformTriadStats } from "@/types/triad";

/**
 * Hook for listing triads
 * @param params - The parameters for the list of triads
 * @returns The list of triads
 */
export function useTriadList(params: ListTriadsParams = {}) {
  const apiConfig = useMemo(() => {
    const { url } = TRIAD_API_ENDPOINTS.list(params);
    return url;
  }, [params]);
  const { data, isLoading, error, mutate } = useApi<TriadApiTypes["list"]>(apiConfig);
  return { triads: data?.items, isLoading, error, fetchTriads: mutate };
}

/**
 * Hook for creating a triad
 * @returns The created triad
 */
export function useCreateTriad() {
  const t = useTranslations("triad");
  const { toast } = useToast();
  const { url, method } = TRIAD_API_ENDPOINTS.create();
  const { trigger, isLoading } = useApi<TriadApiTypes["create"]>(url, { method: method as "POST" });
  const create = useCallback(
    async (data: TriadApiPayloads["create"]): Promise<TriadApiTypes["create"]> => {
      if (!trigger) throw new Error("API not initialized");
      try {
        const result = await trigger(data);
        return result;
      } catch (error: any) {
        const { translationKey, translationParams } = getErrorMessage(error);
        const errorMessage = t(translationKey, translationParams);
        toast({
          title: errorMessage,
          description: errorMessage || t(`errors.unknown`),
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger, t],
  );

  return { create, isLoading };
}

/**
 * Hook for joining a triad
 * @returns The joined triad
 */
export function useJoinTriad() {
  const t = useTranslations("triad");
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const { url, method } = TRIAD_API_ENDPOINTS.join();
  const { trigger, isLoading } = useApi<TriadApiTypes["join"]>(url, { method: method as "POST" });

  const join = useCallback(
    async (data: TriadApiPayloads["join"]) => {
      if (!trigger) throw new Error("API not initialized");
      setError(null);
      try {
        const result = await trigger(data);
        toast({
          title: t("join.celebration.title"),
          description: t("join.celebration.description"),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("errors.failed_to_join"),
          description: errorMessage || t("errors.failed_to_join"),
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger, t],
  );

  return { join, isLoading, error };
}

/**
 * Hook for leaving a triad
 * @returns The left triad
 */
export function useLeaveTriad() {
  const t = useTranslations("triad");
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const { url, method } = TRIAD_API_ENDPOINTS.leave();
  const { trigger, isLoading } = useApi<TriadApiTypes["leave"]>(url, { method: method as "POST" });

  const leave = useCallback(
    async (data: TriadApiPayloads["leave"]) => {
      if (!trigger) throw new Error("API not initialized");
      setError(null);
      try {
        const result = await trigger(data);
        toast({
          title: t("leave.celebration.title"),
          description: t("leave.celebration.description"),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("errors.failed_to_leave"),
          description: errorMessage || t("errors.failed_to_leave"),
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger, t],
  );

  return { leave, isLoading, error };
}

/**
 * Hook for deleting a triad
 * @param id - The ID of the triad to delete
 * @returns The deleted triad
 */
export function useDeleteTriad(id?: number) {
  const t = useTranslations("triad");
  const { toast } = useToast();
  const [triadId, setTriadId] = useState<number | null>(id || null);
  const [shouldDelete, setShouldDelete] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { trigger, isLoading } = useApi<TriadApiTypes["delete"]>(
    triadId ? TRIAD_API_ENDPOINTS.delete(triadId).url : null,
    { method: "DELETE" },
  );

  const performDelete = useCallback(async () => {
    if (!trigger) throw new Error("API not initialized");
    if (!triadId) throw new Error("Triad ID is required");
    setError(null);
    try {
      const result = await trigger();
      toast({
        title: t("delete.celebration.title"),
        description: t("delete.celebration.description"),
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: t("errors.failed_to_delete"),
        description: errorMessage || t("errors.failed_to_delete"),
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldDelete(false);
    }
  }, [trigger, t, toast]);

  useEffect(() => {
    if (shouldDelete) {
      performDelete();
    }
  }, [shouldDelete, performDelete]);

  const deleteTriad = useCallback(
    async (id: number) => {
      setError(null);
      if (triadId !== id) {
        setTriadId(id);
        setShouldDelete(true);
      } else {
        await performDelete();
      }
    },
    [triadId],
  );

  return { deleteTriad, isLoading, error };
}

/**
 * Hook for getting the upcoming triad
 * @param userId - The ID of the user
 * @returns The upcoming triad
 */
export function useGetUpcomingTriad() {
  const { url, method } = TRIAD_API_ENDPOINTS.getUpcoming();
  const { data, isLoading, mutate } = useApi<TriadApiTypes["getUpcoming"]>(url, {
    method: method as "GET",
  });

  return { upcomingTriad: data, isLoading, refetch: mutate };
}

/**
 * Hook for getting the user stats
 * @param userId - The ID of the user
 * @returns The user stats
 */
export function useTriadStats() {
  const { url, method } = TRIAD_API_ENDPOINTS.getUserStats();
  return useApi<TriadApiTypes["getUserStats"]>(url, { method: method as "GET" });
}

/**
 * Hook for getting the platform triad stats
 * @param start - The start date
 * @param end - The end date
 * @returns The platform triad stats
 */
export function usePlatformTriadStats(
  start?: string,
  end?: string,
): {
  stats: PlatformTriadStats | null;
  isLoading: boolean;
  error: Error | null;
} {
  const { url, method } = TRIAD_API_ENDPOINTS.getPlatformStats(start, end);
  const { data, isLoading, error } = useApi<PlatformTriadStats>(url, { method: method as "GET" });

  return { stats: data as PlatformTriadStats, isLoading, error };
}

interface TriadError extends Error {
  code?: string;
  maxDays?: number;
  violatedLimit?: {
    weekNumber?: number;
    days?: number;
    maxTriads?: number;
    currentTriads?: number;
  };
}

/**
 * Get the error message for a triad error
 * @param error - The error
 * @returns The error message
 */
function getErrorMessage(error: TriadError) {
  let translationKey = "errors.failed_to_create";
  let translationParams = {};
  if (error.message === "date_in_past") {
    translationKey = "errors.date_in_past";
  } else if (error.message === "invalid_meeting_link") {
    translationKey = "errors.invalid_meeting_link";
  } else if (error.message === "missing_fields") {
    translationKey = "errors.missing_fields";
  } else if (error.message === "triad_limit_exceeded") {
    if (error?.code === "session_too_far_in_future") {
      translationKey = "errors.session_too_far_in_future";
      translationParams = { days: error.maxDays };
    } else if (error.code === "too_many_open_triads") {
      if (error.violatedLimit?.weekNumber) {
        translationKey = "errors.too_many_open_triads_weekly";
      } else {
        translationKey = "errors.too_many_open_triads_with_limit";
        translationParams = {
          days: error.violatedLimit?.days,
          max: error.violatedLimit?.maxTriads,
          current: error.violatedLimit?.currentTriads,
        };
      }
    }
  } else {
    translationKey = `errors.unknown`;
  }
  return { translationKey, translationParams };
}
