import { Button } from "@/components/ui/button";
import { CalendarPlus } from "lucide-react";
import {
  getFormattedMeetingLink,
  generateTriadCalendarUrl,
  isUserInTriad,
} from "@/utils/triadHelpers";
import { ITriadBase } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";

interface TriadAccessProps {
  triad: ITriadBase;
  userId?: string;
  variant?: "table" | "list";
  className?: string;
}

export function TriadAccess({ triad, userId, variant = "table", className }: TriadAccessProps) {
  const t = useTranslations("triad");

  // Check if user has access to the triad
  const hasAccess = isUserInTriad(triad, userId);

  if (!hasAccess) {
    return (
      <span className={`text-gray-400 ${variant === "list" ? "text-sm" : ""} ${className}`}>-</span>
    );
  }

  const meetingLink = getFormattedMeetingLink(triad.meetingLink);
  const calendarUrl = generateTriadCalendarUrl(triad, t);

  const linkClasses =
    variant === "list"
      ? "h-auto p-0 text-sm text-gray-400 underline hover:text-gray-300"
      : "h-auto p-0 text-white underline hover:text-gray-300";

  const iconClasses =
    variant === "list"
      ? "h-auto p-0 text-gray-400 hover:text-gray-300"
      : "h-auto p-0 text-white hover:text-gray-300";

  if (variant === "table") {
    return (
      <div className={`flex items-center justify-center gap-2 ${className}`}>
        <Button variant="link" className={linkClasses} asChild>
          <a href={meetingLink} target="_blank" rel="noopener noreferrer">
            {t("table.meeting_link")}
          </a>
        </Button>
        <Button variant="link" className={iconClasses} asChild>
          <a href={calendarUrl} target="_blank" rel="noopener noreferrer">
            <CalendarPlus className="h-4 w-4" />
          </a>
        </Button>
      </div>
    );
  }

  // List variant
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Button variant="link" className={linkClasses} asChild>
        <a href={meetingLink} target="_blank" rel="noopener noreferrer">
          {t("list.meeting_link")}
        </a>
      </Button>
      <Button variant="link" className={iconClasses} asChild>
        <a href={calendarUrl} target="_blank" rel="noopener noreferrer">
          <CalendarPlus className="h-4 w-4" />
        </a>
      </Button>
    </div>
  );
}
