import { Suspense } from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { CourseInformation } from "../components/details/information";
import { CourseStudents } from "../components/details/students";
import { CourseOffers } from "../components/details/offers";
import { CourseCohorts } from "../components/details/cohorts";
import { CourseTabs } from "../components/details/CourseTabs";
import { CourseHeader } from "../components/details/CourseHeader";
import { getTranslations } from "next-intl/server";
import { getCourseDetails } from "@/services/course/server";

type CourseDetailsProps = {
  params: Promise<{
    id: number;
  }>;
  searchParams: Promise<{
    tab?: string;
  }>;
};

export default async function CourseDetailsPage({ params, searchParams }: CourseDetailsProps) {
  const { id } = await params;
  const { tab = "information" } = await searchParams;
  const course = await getCourseDetails(id);

  let TabContent;
  switch (tab) {
    case "information":
      TabContent = (
        <Suspense fallback={<Skeleton className="h-[200px] w-full" />}>
          <CourseInformation id={id} />
        </Suspense>
      );
      break;
    case "students":
      TabContent = (
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <CourseStudents id={id} />
        </Suspense>
      );
      break;
    case "offers":
      TabContent = (
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <CourseOffers id={id} />
        </Suspense>
      );
      break;
    case "cohorts":
      TabContent = (
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <CourseCohorts id={id} />
        </Suspense>
      );
      break;
    default:
      TabContent = null;
  }

  if (!course) {
    return (
      <div className="flex h-[200px] items-center justify-center">
        <p className="text-muted-foreground">Course not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <CourseHeader course={course} />
      <div className="space-y-8">
        <CourseTabs activeTab={tab} />
        {TabContent}
      </div>
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "courses" });

  return {
    title: t("details.title"),
  };
}
