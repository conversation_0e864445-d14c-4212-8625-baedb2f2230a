import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { auth } from "@clerk/nextjs/server";
import { PurchaseDetails } from "@/components/purchases";
import {
  getCustomerAccessDetails,
  getInvoices,
  isSubscriptionOwner,
} from "@/services/customers/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: string; id: string }>;
}) {
  const { lang } = await params;
  const t = await getTranslations({ locale: lang, namespace: "subscription" });

  return {
    title: t("details.title"),
  };
}

export default async function SubscriptionDetailsPage({
  params,
}: {
  params: Promise<{ id: string; lang: string }>;
}) {
  const { id } = await params;
  const { userId } = await auth();

  if (!userId) {
    return redirect("/sign-in");
  }

  // Verify subscription ownership
  const isOwner = await isSubscriptionOwner(id);
  if (!isOwner) {
    return redirect("/dashboard");
  }

  // Get customer access details
  const customerAccessDetails = await getCustomerAccessDetails();
  if (!customerAccessDetails) {
    return redirect("/dashboard");
  }

  // Find the subscription in the access details
  const subscription = customerAccessDetails.purchases.find(
    (purchase) => purchase.subscription.id === id,
  );

  if (!subscription) {
    return redirect("/dashboard");
  }

  // Get invoices for the subscription
  const invoices = await getInvoices(id);

  // Mock payment method for demo purposes (typically would come from an API)
  const mockPaymentMethod = {
    brand: "visa",
    last4: "4242",
    expMonth: 12,
    expYear: 2025,
  };

  return (
    <PurchaseDetails
      subscription={subscription}
      invoices={invoices}
      customerAccessDetails={customerAccessDetails}
      paymentMethod={mockPaymentMethod}
    />
  );
}
