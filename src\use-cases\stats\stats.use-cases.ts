import { IDataServices } from '@abstracts';
import { Injectable } from '@nestjs/common';

@Injectable()
export class StatsUseCases {
  constructor(private readonly dataServices: IDataServices) {}

  /**
   * Get main stats about the platform
   * (use the enum PGSQL 'subscriptions_orderstatus_enum')
   * @returns stats by order status
   */
  async getStats() {
    const dataSource = this.dataServices.getDataSource();
    const result = await dataSource.query(
      // Get the count of subscriptions by order status
      `SELECT * FROM subscriptions_by_order_status;`,
    );

    return {
      data: {
        subscriptionsCount: result,
      },
    };
  }
}
