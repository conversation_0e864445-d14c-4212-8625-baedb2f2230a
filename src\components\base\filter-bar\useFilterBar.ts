"use client";

import { useState, useEffect, useRef } from "react";
import {
  UseFilterBarProps,
  FilterBarState,
  Step,
  FilterStep,
  ProcessedGroup,
  GroupState,
} from "./types";

/** Maximum number of retry attempts for failed option fetches */
const MAX_RETRIES = 3;

/**
 * Processes an array of filter steps into groups separated by separators
 *
 * @param steps - Array of filter steps and separators to be processed
 * @returns Array of filter step groups, each containing related filters
 * @internal
 */
function processSteps(steps: Step[]): FilterStep[][] {
  return steps.reduce<FilterStep[][]>((acc, step) => {
    if (step.type === "separator") {
      acc.push([]);
    } else {
      if (acc.length === 0) acc.push([]);
      acc[acc.length - 1].push(step);
    }
    return acc;
  }, []);
}

/**
 * Custom hook for managing filter bar state and logic
 *
 * @param props - Configuration options for the filter bar
 * @param props.steps - Array of filter steps and separators defining the filter structure
 * @param props.value - Optional external value to control the filters
 * @param props.onChange - Callback fired when filter values change
 * @param props.onOptionsNeeded - Callback to fetch options for dependent filters
 *
 * @returns Object containing filter state and control functions
 *
 * @example
 * ```tsx
 * const {
 *   values,
 *   stepsState,
 *   handleChange,
 *   isStepEnabled
 * } = useFilterBar({
 *   steps: [
 *     { type: 'filter', id: 'program', label: 'Program', options: [] }
 *   ],
 *   onChange: (values) => console.log(values)
 * });
 * ```
 */
export const useFilterBar = ({ steps, value, onChange, onOptionsNeeded }: UseFilterBarProps) => {
  /** Tracks whether the component has mounted */
  const isMounted = useRef(false);
  /** Tracks ongoing fetch requests to prevent duplicates */
  const fetchingRef = useRef<Record<string, boolean>>({});
  /** References to group DOM elements for scroll handling */
  const groupRefs = useRef<(HTMLDivElement | null)[]>([]);

  const filterGroups = processSteps(steps);

  /**
   * Initialize filter state with default values and empty options
   * Creates state structure for values, loading states, errors, and groups
   */
  const [state, setState] = useState<FilterBarState>(() => {
    const filterSteps = steps.filter((step): step is FilterStep => step.type === "filter");
    return {
      values: filterSteps.reduce(
        (acc, step) => ({
          ...acc,
          [step.id]: step.defaultValue || "",
        }),
        {},
      ),
      stepsState: filterSteps.reduce(
        (acc, step) => ({
          ...acc,
          [step.id]: {
            isLoading: false,
            error: null,
            retryCount: 0,
            options: step.options,
          },
        }),
        {},
      ),
      groups: filterGroups.map((items) => ({
        items,
        state: {
          scrollable: false,
          gradientState: { left: false, right: false },
          wrapped: false,
        },
      })),
    };
  });

  /** Track open/closed state of filter dropdowns */
  const [openStates, setOpenStates] = useState<Record<string, boolean>>(() =>
    steps.reduce(
      (acc, step) => ({
        ...acc,
        [step.type === "filter" ? step.id : ""]: false,
      }),
      {},
    ),
  );

  /**
   * Finds all filter steps that depend on a given step
   * @param stepId - ID of the step to find dependents for
   * @returns Array of dependent filter steps
   */
  function getDependentSteps(stepId: string) {
    return steps.filter((step): step is FilterStep => {
      if (step.type !== "filter") return false;
      const dependency = step.dependsOn?.[stepId];
      return dependency !== undefined;
    });
  }

  /**
   * Determines if a filter step should be enabled based on its dependencies
   * @param stepId - ID of the step to check
   * @returns boolean indicating if the step should be enabled
   */
  function isStepEnabled(stepId: string) {
    const step = steps.find((s): s is FilterStep => s.type === "filter" && s.id === stepId);
    if (!step?.dependsOn) return true;

    // Check step's own state
    const stepState = state.stepsState[stepId];
    if (stepState?.error !== null || stepState?.isLoading) {
      return false;
    }

    // Check dependencies' states
    const hasDependencyError = Object.keys(step.dependsOn).some(
      (depId) => state.stepsState[depId]?.error !== null,
    );
    if (hasDependencyError) {
      return false;
    }

    const hasDependencyLoading = Object.keys(step.dependsOn).some(
      (depId) => state.stepsState[depId]?.isLoading,
    );
    if (hasDependencyLoading) {
      return false;
    }

    // Check if required dependencies have values
    return Object.entries(step.dependsOn).every(([depId, config]) => {
      if (!config) return true;
      return !config.required || state.values[depId];
    });
  }

  /**
   * Propagates error states to dependent filters
   * When a filter encounters an error, all dependent filters are disabled and show the error
   * @param stepId - ID of the step that encountered the error
   * @param error - Error message to propagate, or null to clear errors
   */
  function propagateError(stepId: string, error: string | null) {
    const dependentSteps = getDependentSteps(stepId);
    if (dependentSteps.length === 0) return;

    setState((prev) => {
      const newStepsState = { ...prev.stepsState };
      dependentSteps.forEach((step) => {
        newStepsState[step.id] = {
          ...newStepsState[step.id],
          error: error,
          options: [],
        };
        // Recursively propagate error
        const nestedDependents = getDependentSteps(step.id);
        nestedDependents.forEach((nestedStep) => {
          newStepsState[nestedStep.id] = {
            ...newStepsState[nestedStep.id],
            error: error,
            options: [],
          };
        });
      });
      return {
        ...prev,
        stepsState: newStepsState,
      };
    });
  }

  /**
   * Clears values and states of all dependent filters
   * Called when a parent filter's value changes or is cleared
   * @param stepId - ID of the parent step that changed
   */
  function clearDependentSteps(stepId: string) {
    const dependentSteps = getDependentSteps(stepId);
    if (dependentSteps.length === 0) return;

    setState((prev) => {
      const newValues = { ...prev.values };
      const newStepsState = { ...prev.stepsState };

      dependentSteps.forEach((step) => {
        newValues[step.id] = "";
        newStepsState[step.id] = {
          ...newStepsState[step.id],
          options: [],
          error: null,
          retryCount: 0,
        };
        // Recursively clear dependent steps
        const nestedDependents = getDependentSteps(step.id);
        nestedDependents.forEach((nestedStep) => {
          newValues[nestedStep.id] = "";
          newStepsState[nestedStep.id] = {
            ...newStepsState[nestedStep.id],
            options: [],
            error: null,
            retryCount: 0,
          };
        });
      });

      return {
        ...prev,
        values: newValues,
        stepsState: newStepsState,
      };
    });
  }

  /**
   * Fetches options for a filter step based on its dependencies
   * Includes retry logic with exponential backoff for failed requests
   * @param stepId - ID of the step to fetch options for
   * @param retryCount - Current retry attempt number
   */
  async function fetchOptions(stepId: string, retryCount = 0) {
    if (!onOptionsNeeded || fetchingRef.current[stepId]) return;

    const step = steps.find((s): s is FilterStep => s.type === "filter" && s.id === stepId);
    if (!step?.dependsOn) return;

    // Get dependency values
    const dependencies = Object.keys(step.dependsOn).reduce<Record<string, string>>(
      (acc, depId) => ({
        ...acc,
        [depId]: state.values[depId],
      }),
      {},
    );

    // Check if any required dependency is missing
    const hasMissingDependencies = Object.entries(step.dependsOn).some(([depId, config]) => {
      if (!config) return false;
      return config.required && !dependencies[depId];
    });
    if (hasMissingDependencies) {
      return;
    }

    fetchingRef.current[stepId] = true;

    // Update loading state
    setState((prev) => ({
      ...prev,
      stepsState: {
        ...prev.stepsState,
        [stepId]: {
          ...prev.stepsState[stepId],
          isLoading: true,
          error: null,
        },
      },
    }));

    try {
      const options = await onOptionsNeeded(stepId, dependencies);

      // If we get an empty array for a step that has dependencies with values,
      // treat it as an error
      const hasFilledDependencies = Object.entries(dependencies).some(([, value]) => value);
      if (options.length === 0 && hasFilledDependencies) {
        throw new Error("No options available");
      }

      setState((prev) => ({
        ...prev,
        stepsState: {
          ...prev.stepsState,
          [stepId]: {
            ...prev.stepsState[stepId],
            isLoading: false,
            options,
            error: null,
            retryCount: 0,
          },
        },
      }));
      propagateError(stepId, null);
      fetchingRef.current[stepId] = false;
    } catch (error) {
      if (retryCount < MAX_RETRIES) {
        // Retry after a delay
        setTimeout(
          () => {
            fetchingRef.current[stepId] = false;
            fetchOptions(stepId, retryCount + 1);
          },
          Math.min(1000 * Math.pow(2, retryCount), 5000),
        ); // Exponential backoff
      } else {
        const errorMessage = "Filtres indisponibles";
        setState((prev) => ({
          ...prev,
          stepsState: {
            ...prev.stepsState,
            [stepId]: {
              ...prev.stepsState[stepId],
              isLoading: false,
              error: errorMessage,
              retryCount: retryCount,
            },
          },
        }));
        propagateError(stepId, errorMessage);
        fetchingRef.current[stepId] = false;
      }
    }
  }

  /**
   * Updates the value of a filter step and manages dependent steps
   * @param stepId - ID of the step to update
   * @param newValue - New value to set
   */
  function handleChange(stepId: string, newValue: string) {
    setState((prev) => {
      const newState = {
        ...prev,
        values: {
          ...prev.values,
          [stepId]: newValue,
        },
      };

      // If the value is cleared or changed, clear dependent steps
      if (newValue !== prev.values[stepId]) {
        clearDependentSteps(stepId);
      }

      return newState;
    });
  }

  /**
   * Fetches options for filters when their dependencies change
   * Only runs after initial mount and when values change
   */
  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true;
      return;
    }

    steps.forEach((step) => {
      if (
        step.type === "filter" &&
        step.dependsOn &&
        isStepEnabled(step.id) &&
        !fetchingRef.current[step.id]
      ) {
        fetchOptions(step.id);
      }
    });
  }, [steps, state.values]);

  /**
   * Synchronizes internal state with external value prop
   * Only runs on initial mount if external value is provided
   */
  useEffect(() => {
    if (value && !isMounted.current) {
      setState((prev) => ({
        ...prev,
        values: value,
      }));
    }
  }, [value]);

  /**
   * Notifies parent component of value changes
   * Only runs after initial mount when values change
   */
  useEffect(() => {
    if (isMounted.current) {
      onChange?.(state.values);
    }
  }, [state.values]);

  /**
   * Public interface of the hook
   */
  return {
    values: state.values,
    stepsState: state.stepsState,
    groups: state.groups,
    openStates,
    isStepEnabled,
    handleChange,
    setOpen: (stepId: string, isOpen: boolean) => {
      setOpenStates((prev) => ({
        ...prev,
        [stepId]: isOpen,
      }));
    },
    groupRefs,
    clearAll: () => {
      setState((prev) => ({
        ...prev,
        values: {},
      }));
    },
  };
};
