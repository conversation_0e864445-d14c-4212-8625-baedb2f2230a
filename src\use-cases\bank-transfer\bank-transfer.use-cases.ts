import { Injectable } from '@nestjs/common';
import {
  BankTransferPayment,
  CreateBankTransferDTO,
  UpdateTransferStatusDTO,
} from '@dtos';
import { BankTransferFactory } from './bank-transfer.factory';
import { ChargebeeBillingService } from '@services/billing';
import { BankTransferStatus, Currencies } from '@enums';
import { PXActionResult, UpdateResultWithItemInfo } from '@types';
import { IDataServices } from '@abstracts';
import { BankTransfer } from '@entities';

@Injectable()
export class BankTransferUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly bankTransferFactory: BankTransferFactory,
    private readonly chargebeeService: ChargebeeBillingService,
  ) {}

  /**
   * Handles the creation of bank transfers based on the provided payload.
   * @param payload - The payload containing the information for creating bank transfers.
   * @returns A promise that resolves to the created bank transfers.
   */
  async handle(payload: CreateBankTransferDTO): Promise<PXActionResult> {
    try {
      const bankStatements: Record<string, BankTransferPayment[]> = {};
      for (const statement of payload.banksStatements) {
        bankStatements[`${statement.bankName}_${statement.entityId}`] =
          statement.payments;
      }
      const unProcessedBankTransfers = Object.entries(bankStatements).flatMap(
        ([bankNameWithEntityId, payments]) =>
          payments.map((payment) =>
            this.bankTransferFactory.generate(bankNameWithEntityId, payment),
          ),
      );

      const processedBankTransfers: BankTransfer[] = [];
      const existingTransfers: string[] = [];
      for (const bankTransfer of unProcessedBankTransfers) {
        const existingTransfer = await this.getByPaymentId(
          bankTransfer?.paymentId,
        );
        if (existingTransfer) {
          existingTransfers.push(existingTransfer.paymentId);
          continue;
        }
        const result = await this.autoMatch(bankTransfer);
        processedBankTransfers.push(result);
      }
      const newlyCreated = await this.createMany(processedBankTransfers);
      return {
        success: true,
        message: 'Bank transfers handled successfully',
        data: { newlyCreated, existingTransfers },
      };
    } catch (err) {
      return {
        success: false,
        message: `Error occurred while processing bank transfers: ${err}`,
      };
    }
  }

  /**
   * Matches a bank transfer with an overdue subscription based on the subscription id present in `paymentDescription`
   * @param bankTransfer Bank transfer object to match with a subscription
   * @returns Bank transfer object with a status of `matched` if match was successful, `unmatched` otherwise
   */
  async autoMatch(bankTransfer: BankTransfer): Promise<BankTransfer> {
    const regex = /px[\s-]?sub[\s-]?(\d{10})\s*\|/i;

    const id = bankTransfer?.paymentDescription?.match(regex)?.[1];

    if (!id) return bankTransfer;
    const subscriptionId = `PX-SUB-${id}`;
    const overdueInvoices =
      await this.chargebeeService.getOverdueInvoicesForSubscription(
        subscriptionId,
      );

    if (!overdueInvoices || overdueInvoices?.length === 0) {
      return bankTransfer;
    }

    const oldestOverdueInvoice = overdueInvoices[0].invoice;
    const paymentTime = new Date(bankTransfer.paymentDate).getTime() / 1000;
    const result = await this.chargebeeService.recordOfflinePaymentForInvoice(
      oldestOverdueInvoice.id,
      bankTransfer.paymentAmount,
      bankTransfer.paymentId,
      bankTransfer.bankName,
      paymentTime,
    );
    if (result?.invoice && result?.transaction) {
      bankTransfer.status = BankTransferStatus.MATCHED;
      return bankTransfer;
    }
    return bankTransfer;
  }

  /**
   * Creates multiple bank transfers.
   *
   * @param bankTransfers - An array of bank transfers to be created.
   * @returns A promise that resolves to the created bank transfers.
   */
  async createMany(bankTransfers: BankTransfer[]) {
    const repository = this.dataServices.bankTransfer.getRepository();
    return repository.save(bankTransfers);
  }

  /**
   * Creates a bank transfer.
   *
   * @param bankTransfer - The bank transfer entity.
   * @returns A promise that resolves to the created bank transfer.
   */
  async create(bankTransfer: BankTransfer): Promise<BankTransfer> {
    return this.dataServices.bankTransfer.create(bankTransfer);
  }

  /**
   * Retrieves a list of bank transfers based on the provided filters.
   *
   * @param query - Any search query to search for customer email or transfer `paymentId`
   * @param bankName - The name of the bank.
   * @param paymentDate - The payment date.
   * @param currency - The currency of the bank transfer.
   * @param status - The status of the bank transfer.
   * @param senderName - The name of the sender.
   * @param limit - The maximum number of bank transfers to retrieve.
   * @param page - The page number of the results.
   * @param orderBy - The order in which the bank transfers should be sorted.
   *                  Valid values are 'DESC' (descending) and 'ASC' (ascending).
   * @returns A promise that resolves to an object containing the retrieved bank transfers and the total count.
   */
  async list(
    query?: string,
    bankName?: string,
    paymentDate?: string,
    currency?: Currencies,
    status?: BankTransferStatus,
    senderName?: string,
    limit = 100,
    page = 1,
    orderBy: 'DESC' | 'ASC' = 'DESC',
  ): Promise<{ items: BankTransfer[]; total: number }> {
    const queryBuilder = this.dataServices.bankTransfer
      .getRepository()
      .createQueryBuilder('bankTransfer');

    if (query) {
      queryBuilder.where(
        'bankTransfer.paymentId ILIKE :query OR bankTransfer.senderEmail ILIKE :query',
        { query: `%${query}%` },
      );
    }

    if (senderName) {
      queryBuilder.andWhere(
        "CONCAT(bankTransfer.senderFirstName, ' ', bankTransfer.senderLastName) ILIKE :senderName",
        { senderName: `%${senderName}%` },
      );
    }

    if (bankName) {
      queryBuilder.andWhere('bankTransfer.bankName ILIKE :bankName', {
        bankName: `%${bankName}%`,
      });
    }

    if (status) {
      queryBuilder.andWhere('bankTransfer.status = :status', { status });
    } else {
      queryBuilder.andWhere('bankTransfer.status != :ignored', {
        ignored: BankTransferStatus.IGNORED,
      });
    }

    if (paymentDate) {
      queryBuilder.andWhere('bankTransfer.paymentDate = :paymentDate', {
        paymentDate: new Date(paymentDate),
      });
    }

    if (currency) {
      queryBuilder.andWhere('bankTransfer.currency = :currency', { currency });
    }

    queryBuilder.orderBy('bankTransfer.createdAt', orderBy);
    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);

    const [items, total] = await queryBuilder.getManyAndCount();
    return {
      items,
      total,
    };
  }

  /**
   * Retrieves a bank transfer by its ID.
   * @param id - The ID of the bank transfer to retrieve.
   * @returns A Promise that resolves to the retrieved BankTransfer object.
   */
  async getById(id: number): Promise<BankTransfer> {
    return await this.dataServices.bankTransfer.getOneBy({ id });
  }

  /**
   * Retrieves a bank transfer by its `paymentId`.
   * @param paymentId - The `paymentId` of the bank transfer to retrieve.
   * @returns A Promise that resolves to the retrieved BankTransfer object.
   */
  async getByPaymentId(paymentId: string): Promise<BankTransfer> {
    return this.dataServices.bankTransfer.getOneBy({ paymentId });
  }

  /**
   * Updates the status of a bank transfer.
   *
   * @param id - The ID of the bank transfer.
   * @param updates - The updates to be applied to the bank transfer status.
   * @returns A Promise that resolves to an UpdateResult object.
   */
  async updateStatus(
    id: number,
    updates: UpdateTransferStatusDTO,
  ): Promise<UpdateResultWithItemInfo<BankTransfer>> {
    return this.dataServices.bankTransfer.updateAndReturnItem(
      'id',
      id.toString(),
      {
        status: updates.status,
      },
    );
  }
}
