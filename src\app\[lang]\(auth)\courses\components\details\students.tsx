"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import { format } from "date-fns";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowRight } from "lucide-react";
import { ICourseStudent } from "@px-shared-account/hermes";
import { useRouter } from "next/navigation";

import { DataTable } from "@/components/table";
import { Button } from "@/components/ui/button";
import { useTable } from "@/hooks/use-table";
import { TableProps } from "@/types/table";
import { useGetStudentsForCourse } from "@/services/course/client";
import { StatusBadge } from "@/components/base/courses/status-badge";
import StudentInfo from "./StudentInfo";

interface CourseStudentsProps {
  id: number;
}

type TableStateType = {
  pageIndex: number;
  pageSize: number;
  searchQuery: string;
  sortBy?: {
    id: string;
    desc: boolean;
  };
};

export function CourseStudents({ id }: CourseStudentsProps) {
  const t = useTranslations("courses.details");
  const router = useRouter();

  // Table state management with proper typing
  const [tableState, setTableState] = useState<TableStateType>({
    pageIndex: 0,
    pageSize: 10,
    searchQuery: "",
  });

  // Fetch students data using the service
  const { data: apiResponse, isLoading } = useGetStudentsForCourse(id, {
    page: tableState.pageIndex + 1,
    limit: tableState.pageSize,
    search: tableState.searchQuery,
  });

  // Extract students from the API response
  const students = useMemo(() => {
    if (!apiResponse) return [];
    return apiResponse.data || [];
  }, [apiResponse]);

  // Define table columns with proper typing
  const columns = useMemo<ColumnDef<ICourseStudent>[]>(
    () => [
      {
        id: "user",
        header: t("students.name"),
        cell: ({ row }) => <StudentInfo row={row} />,
      },
      {
        id: "cohort",
        header: t("students.cohort"),
        accessorKey: "cohortName",
        cell: ({ row }) => (
          <div className="flex items-center justify-start">
            <StatusBadge status={row.original.cohortName} />
          </div>
        ),
      },
      {
        id: "createdAt",
        header: t("students.joined_at"),
        accessorKey: "createdAt",
        cell: ({ row }) => (
          <span>
            {row.original.createdAt
              ? format(new Date(row.original.createdAt), "MMM dd, yyyy")
              : "-"}
          </span>
        ),
      },
      {
        id: "actions",
        header: t("students.actions"),
        cell: ({ row }) => {
          const student = row.original;
          return (
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => {
                // Navigate to the cohort details page
                router.push(`/cohorts/${student.cohortId}`);
              }}
            >
              <span className="sr-only">
                {t("students.view_cohort", { name: student.cohortName })}
              </span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          );
        },
      },
    ],
    [t, router],
  );

  // Handle table state changes
  const handleTableStateChange = useCallback((newState: TableStateType) => {
    setTableState(newState);
  }, []);

  // Use the table hook
  const { selectedRows, handleRowSelection } = useTable({
    initialState: tableState,
    onStateChange: handleTableStateChange,
  });

  // Memoize DataTable props
  const dataTableProps = useMemo<TableProps<ICourseStudent>>(
    () => ({
      columns,
      data: students as ICourseStudent[],
      isLoading,
      selectedRows,
      enableSearch: true,
      enablePagination: true,
      pageSize: tableState.pageSize,
      totalRows: apiResponse?.total || 0,
      noResults: t("students.no_results"),
      onStateChange: handleTableStateChange,
      onSelectionChange: handleRowSelection,
      getRowId: (row) => row.id.toString(),
      minSearchLength: 1,
      title: `${t("students.title")} (${apiResponse?.total || 0})`,
    }),
    [
      columns,
      students,
      apiResponse,
      isLoading,
      selectedRows,
      tableState.pageSize,
      t,
      handleTableStateChange,
      handleRowSelection,
    ],
  );

  return (
    <div className="overflow-x-auto">
      <DataTable {...dataTableProps} />
    </div>
  );
}
