import { format, addHours } from "date-fns";
import { fr, enUS } from "date-fns/locale";
import { generateGoogleCalendarUrl } from "@/lib/utils";
import { ITriadBase } from "@px-shared-account/hermes";

/**
 * Date formatting utilities for triads
 */
export const formatTriadDate = (date: Date, locale: string = "en") => {
  const dateLocale = locale === "fr" ? fr : enUS;
  return format(date, "dd/MM/yyyy à HH'H'mm", { locale: dateLocale });
};

export const formatTriadDateMobile = (date: Date, locale: string = "en") => {
  const dateLocale = locale === "fr" ? fr : enUS;
  return {
    day: format(date, "EEEE d MMM", { locale: dateLocale }),
    time: format(date, "HH'H'mm", { locale: dateLocale }),
  };
};

/**
 * Access and permission utilities
 */
export const isTriadOrganizer = (triad: ITriadBase, userId?: string) => {
  return triad.organizer?.ssoId === userId;
};

export const isTriadParticipant = (triad: ITriadBase, userId?: string) => {
  return triad.participants?.some((p) => p?.user.ssoId === userId);
};

export const isUserInTriad = (triad: ITriadBase, userId?: string) => {
  return isTriadOrganizer(triad, userId) || isTriadParticipant(triad, userId);
};

export const isTriadFull = (triad: ITriadBase) => {
  return (triad.participants?.length || 0) >= triad.maxParticipants - 1; // -1 because organizer is counted
};

/**
 * Availability calculations
 */
export const getAvailableSpots = (triad: ITriadBase) => {
  return triad.maxParticipants - ((triad.participants?.length || 0) + 1); // +1 for organizer
};

/**
 * Calendar integration utilities
 */
export const generateTriadCalendarUrl = (triad: ITriadBase, t: (key: string) => string) => {
  const meetingLink =
    triad.meetingLink.startsWith("http://") || triad.meetingLink.startsWith("https://")
      ? triad.meetingLink
      : `https://${triad.meetingLink}`;

  const organizerName = triad.organizer?.firstName || triad.organizer?.lastName;
  const title = triad.title || `${t("session")} ${organizerName}`;

  const startTime = new Date(triad.sessionTime).toISOString();
  const endTime = addHours(new Date(triad.sessionTime), triad.duration).toISOString();

  return generateGoogleCalendarUrl({
    title,
    description: `${t("table.meeting_link")}: ${meetingLink}`,
    startTime,
    endTime,
    location: meetingLink,
  });
};

/**
 * Meeting link utilities
 */
export const getFormattedMeetingLink = (meetingLink: string) => {
  return meetingLink.startsWith("http://") || meetingLink.startsWith("https://")
    ? meetingLink
    : `https://${meetingLink}`;
};

/**
 * Participant utilities
 */
export const getAllParticipants = (triad: ITriadBase) => {
  const organizer = triad.organizer
    ? {
        id: triad.organizer.id,
        name: `${triad.organizer.firstName} ${triad.organizer.lastName}`,
        email: triad.organizer.email,
        ssoId: triad.organizer.ssoId,
        isOrganizer: true,
      }
    : null;

  const participants = (triad.participants || []).map((p) => ({
    id: p.id,
    name: `${p.user.firstName} ${p.user.lastName}`,
    email: p.user.email,
    ssoId: p.user.ssoId,
    isOrganizer: false,
  }));

  return [organizer, ...participants].filter(Boolean);
};

/**
 * Error message mapping for triad operations
 */
export const getTriadErrorMessage = (error: Error, operation: "join" | "leave" | "delete") => {
  const errorMessage = error.message;

  if (operation === "join") {
    if (errorMessage.includes("cannot_join_own_triad")) return "errors.cannot_join_own_triad";
    if (errorMessage.includes("already_joined")) return "errors.already_joined";
    if (errorMessage.includes("triad_full")) return "errors.triad_full";
    return "errors.failed_to_join";
  }

  if (operation === "leave") {
    if (errorMessage.includes("unauthorized")) return "table.errors.unauthorized";
    if (errorMessage.includes("forbidden")) return "table.errors.forbidden";
    if (errorMessage.includes("not_found")) return "table.errors.not_found";
    return "table.errors.failed_to_leave";
  }

  if (operation === "delete") {
    if (errorMessage.includes("unauthorized")) return "table.errors.unauthorized";
    if (errorMessage.includes("forbidden")) return "table.errors.forbidden";
    if (errorMessage.includes("not_found")) return "table.errors.not_found";
    return "table.errors.failed_to_delete";
  }

  return "errors.general";
};
