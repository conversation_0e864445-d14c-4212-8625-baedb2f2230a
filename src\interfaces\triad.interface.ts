import z from "zod";
import { PXSSession, SessionType } from "../enums";
import { UserBaseSchema } from "./user.interface";

export const TriadParticipantBaseSchema = z.object({
    id: z.number().int().positive(),
    user: UserBaseSchema,
    // Why any here? because the TriadBaseSchema type is not defined yet,
    // lazy is used to avoid circular dependency but without type annotation, the typescript compiler will throw an error.
    triad: z.lazy((): any => TriadBaseSchema),
    joinedAt: z.coerce.date(),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
    deletedAt: z.coerce.date().nullable(),
});
export type ITriadParticipantBase = z.infer<typeof TriadParticipantBaseSchema>;

export const TriadBaseSchema = z.object({
    id: z.number().int().positive("Triad ID is required and must be positive"),
    title: z.string(),
    duration: z.number().positive("Duration is required and must be positive"),
    meetingLink: z.string().url().nonempty("Meeting link is required and must be a valid URL"),
    sessionTime: z.coerce.date(),
    organizerId: z.number().int().positive("Organizer ID is required and must be positive"),
    organizer: UserBaseSchema.pick({
        id: true,
        ssoId: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
    }).optional(),
    maxParticipants: z.number().int().max(3),
    participants: z.array(TriadParticipantBaseSchema).optional(),
    sessions: z.array(z.nativeEnum(PXSSession)).optional(),
    sessionType: z.nativeEnum(SessionType),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date().nullable(),
    deletedAt: z.coerce.date().nullable(),
});
export type ITriadBase = z.infer<typeof TriadBaseSchema>;

export const CreateTriadSchema = TriadBaseSchema.omit({
    id: true,
    createdAt: true,
    updatedAt: true,
    deletedAt: true,
    organizerId: true,
    organizer: true,
    participants: true,
});
export type ICreateTriad = z.infer<typeof CreateTriadSchema>;

export const UpdateTriadSchema = z
    .object({
        title: z.string().optional(),
        maxParticipants: z.number().int().max(3).optional(),
        meetingLink: z.string().url().nonempty("Meeting link is required and must be a valid URL").optional(),
        sessionTime: z.coerce.date().optional(),
        duration: z.number().positive("Duration is required and must be positive").optional(),
    })
    .strict();
export type IUpdateTriad = z.infer<typeof UpdateTriadSchema>;

export const JoinTriadSchema = z.object({
    triadId: z.number().int().positive("Triad ID is required and must be positive"),
});
export type IJoinTriad = z.infer<typeof JoinTriadSchema>;

export const LeaveTriadSchema = z.object({
    triadId: z.number().int().positive("Triad ID is required and must be positive"),
});
export type ILeaveTriad = z.infer<typeof LeaveTriadSchema>;

export const UserStatsSchema = z.object({
    totalTriadsOrganized: z.number().int().positive("Total triads organized is required and must be positive"),
    totalParticipants: z.number().int().positive("Total participants is required and must be positive"),
});
export type IUserStats = z.infer<typeof UserStatsSchema>;

