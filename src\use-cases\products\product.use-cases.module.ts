import { Module } from '@nestjs/common';
import { ProductFactory, ProductUseCases } from '.';
import {
  ProductPlanUseCasesModule,
  ProductPlanUseCases,
} from '../product-plan';
import { ProductPlanPriceUseCasesModule } from '../product-plan-price';
import {
  ChargebeeBillingModule,
  ChargebeeBillingService,
} from '@services/billing';
import { DataServicesModule } from '@services/database';
import { HubspotModule } from '@services/crm';

@Module({
  imports: [
    DataServicesModule,
    ProductPlanUseCasesModule,
    ProductPlanPriceUseCasesModule,
    ChargebeeBillingModule,
    HubspotModule,
  ],
  providers: [
    ProductUseCases,
    ProductFactory,
    ProductPlanUseCases,
    ChargebeeBillingService,
  ],
  exports: [ProductUseCases, ProductFactory, ProductPlanUseCases],
})
export class ProductUseCasesModule {}
