"use client";

import useSWR, { mutate as globalMutate } from "swr";
import { useAuth } from "@clerk/nextjs";

import fetcher from "@/lib/api-fetcher";

// Used to debug the API requests
const DEBUG = false;

type RequestMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

interface RequestConfig {
  method?: RequestMethod;
  body?: any;
}

/**
 * A custom hook for making API requests using SWR.
 * @template T The expected type of the API response data
 * @param {string} url The API endpoint URL to fetch from
 * @param {RequestConfig} config Optional configuration for the request
 * @returns {Object} An object containing:
 *   - data: The fetched data of type T
 *   - isLoading: <PERSON><PERSON>an indicating if the request is in progress
 *   - error: Any error that occurred during the request
 *   - mutate: A function to mutate the data
 *   - trigger: A function to trigger the mutation (only for mutation methods)
 */
export const useApi = <T>(url: string | null, config: RequestConfig = {}) => {
  const { getToken } = useAuth();
  const isMutation = config.method && config.method !== "GET";

  // Create the fetcher instance
  const apiFetcher = fetcher(getToken);

  // For mutations, we create a trigger function
  const trigger = async (data?: any) => {
    if (!url) throw new Error("URL is required for mutations");

    try {
      const result = await apiFetcher(url, {
        method: config.method || "POST",
        body: data || config.body,
      });

      // Extract the service prefix from the URL (e.g., /whats-new/reactions -> /whats-new/)
      const urlParts = url.split("/");
      if (urlParts.length >= 2) {
        const servicePrefix = `/${urlParts[1]}`;

        // Only invalidate cache for endpoints related to the same service
        await globalMutate(
          // Match keys that start with the same service prefix
          (key) => typeof key === "string" && key.startsWith(servicePrefix),
          undefined,
          { revalidate: true },
        );
      }

      return result;
    } catch (error) {
      console.error(`#(useApi): ${url}`, { error });
      throw error;
    }
  };

  // For GET requests, we use SWR with a key function to handle null URLs
  const response = useSWR<T>(isMutation ? null : url, url ? () => apiFetcher(url) : null);

  if (DEBUG) {
    console.log(`#(useApi): ${url}`, { response, config });
  }

  return {
    ...(response || { data: undefined, error: undefined, isLoading: false }),
    trigger: isMutation ? trigger : undefined,
  };
};
