import { motion, AnimatePresence } from "framer-motion";

interface AnimatedNumberProps {
  number: number;
  className?: string;
}

export function AnimatedNumber({ number, className }: AnimatedNumberProps) {
  return (
    <span className={`inline-flex h-[1.1em] overflow-hidden ${className || ""}`}>
      <AnimatePresence mode="wait">
        <motion.span
          key={number}
          initial={{ y: "100%" }}
          animate={{ y: "0%" }}
          exit={{ y: "-100%" }}
          transition={{
            duration: 0.05,
            ease: "backOut",
            type: "tween",
          }}
          className="inline-block"
        >
          {number}
        </motion.span>
      </AnimatePresence>
    </span>
  );
}
