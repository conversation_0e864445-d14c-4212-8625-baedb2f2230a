diff --git a/src/enums/customer-access.enum.ts b/src/enums/customer-access.enum.ts
new file mode 100644
index 0000000..a75cbba
--- /dev/null
+++ b/src/enums/customer-access.enum.ts
@@ -0,0 +1,7 @@
+export enum CustomerAccessStatus {
+    ERROR = 'error',
+    GRANTED = 'granted',
+    REVOKED = 'revoked',
+    SUSPENDED = 'suspended',
+    PENDING = 'pending',
+}
diff --git a/src/enums/index.ts b/src/enums/index.ts
index 32fae86..0c887e3 100644
--- a/src/enums/index.ts
+++ b/src/enums/index.ts
@@ -4,3 +4,4 @@ export * from "./user.enum";
 export * from "./product-offer.enum";
 export * from "./permission.enum";
 export * from "./triad.enum";
+export * from "./customer-access.enum";
diff --git a/src/interfaces/course.interface.ts b/src/interfaces/course.interface.ts
index eb8446e..733e60a 100644
--- a/src/interfaces/course.interface.ts
+++ b/src/interfaces/course.interface.ts
@@ -1,6 +1,7 @@
 import { z } from "zod";
 import { UserBaseSchema } from "./user.interface";
-import { CourseStatus, ProductOfferStatus } from "../enums";
+import { CourseStatus, CustomerAccessStatus, ProductOfferStatus } from "../enums";
+import { ProductFamilyBaseSchema } from "./product-family.interface";
 
 export const DefaultCourseOfferSchema = z.object({
   id: z.number().positive(),
@@ -25,6 +26,8 @@ export const CourseBaseSchema = z.object({
   thumbnail: z.string().url().nonempty(),
   managers: z.array(UserBaseSchema),
   lmsId: z.string().nonempty(),
+  productFamily: ProductFamilyBaseSchema,
+  ctaLink: z.string().url().nullable().optional(),
   status: z.nativeEnum(CourseStatus).nullable(),
   offers: z.array(CourseOfferSchema),
   createdAt: z.date(),
@@ -38,15 +41,19 @@ export const CreateUpdateCourseSchema = CourseBaseSchema.extend({
 export type IBaseCourse = z.infer<typeof CourseBaseSchema>;
 export type ICreateUpdateCourse = z.infer<typeof CreateUpdateCourseSchema>;
 
-export const CreateCourseSchema = CreateUpdateCourseSchema.partial({
-  id: true,
-  status: true,
-  createdAt: true,
-  updatedAt: true,
-}).extend({
-  managers: z.array(z.number().positive()),
-  familyId: z.number().positive(),
-});
+export const CreateCourseSchema = CreateUpdateCourseSchema.omit({
+  productFamily: true,
+})
+  .partial({
+    id: true,
+    status: true,
+    createdAt: true,
+    updatedAt: true,
+  })
+  .extend({
+    managers: z.array(z.number().positive()),
+    productFamilyId: z.number().positive(),
+  });
 export type ICreateCourse = z.infer<typeof CreateCourseSchema>;
 
 export const UpdateCourseSchema = z.object({
@@ -58,9 +65,38 @@ export const UpdateCourseSchema = z.object({
   managers: z.array(z.number().positive()).optional(),
   status: z.nativeEnum(CourseStatus).optional(),
   offers: z.array(DefaultCourseOfferSchema).optional(),
+  ctaLink: z.string().url().nullable().optional(),
+  productFamilyId: z.number().positive().optional(),
 });
 export type IUpdateCourse = z.infer<typeof UpdateCourseSchema>;
 
+// Course Catalog Types
+export const CourseCatalogItemSchema = CourseBaseSchema.extend({
+  owned: z.boolean(),
+  accessStatus: z.nativeEnum(CustomerAccessStatus).nullable().optional(),
+  lmsUrl: z.string().url().optional(),
+});
+export type ICourseCatalogItem = z.infer<typeof CourseCatalogItemSchema>;
+
+export const ListCourseCatalogParamsSchema = z.object({
+  limit: z.coerce.number().min(1).max(100).default(50),
+  cursor: z.number().optional(),
+  search: z.string().optional(),
+  status: z.nativeEnum(CourseStatus).optional(),
+  owned: z.boolean().optional(),
+});
+export type IListCourseCatalogParams = z.infer<
+  typeof ListCourseCatalogParamsSchema
+>;
+
+export const ListCourseCatalogResponseSchema = z.object({
+  data: z.array(CourseCatalogItemSchema),
+  nextCursor: z.number().nullable(),
+});
+export type IListCourseCatalogResponse = z.infer<
+  typeof ListCourseCatalogResponseSchema
+>;
+
 export const GetCourseByIdSchema = z.object({
   id: z.number().positive(),
 });
diff --git a/src/interfaces/index.ts b/src/interfaces/index.ts
index 2586616..e01f8d9 100644
--- a/src/interfaces/index.ts
+++ b/src/interfaces/index.ts
@@ -5,3 +5,4 @@ export * from "./role.interface";
 export * from "./user.interface";
 export * from "./triad.interface";
 export * from "./whats-new.interface";
+export * from "./product-family.interface";
diff --git a/src/interfaces/product-family.interface.ts b/src/interfaces/product-family.interface.ts
new file mode 100644
index 0000000..212920e
--- /dev/null
+++ b/src/interfaces/product-family.interface.ts
@@ -0,0 +1,41 @@
+import { z } from "zod";
+
+export const ProductLineBaseSchema = z.object({
+    id: z.number().positive(),
+    name: z.string().nonempty(),
+    description: z.string(),
+    status: z.string(),
+    chargebeeId: z.string(),
+    createdAt: z.date().optional(),
+});
+export type IProductLineBase = z.infer<typeof ProductLineBaseSchema>;
+
+export const ProductFamilyBaseSchema = z.object({
+    id: z.number().positive(),
+    name: z.string().nonempty(),
+    description: z.string(),
+    status: z.string(),
+    chargebeeId: z.string(),
+    productLine: ProductLineBaseSchema,
+    createdAt: z.date().optional(),
+});
+export type IProductFamilyBase = z.infer<typeof ProductFamilyBaseSchema>;
+
+export const GetProductFamilyByIdSchema = z.object({
+    id: z.number().positive(),
+});
+export type IGetProductFamilyById = z.infer<typeof GetProductFamilyByIdSchema>;
+
+export const ListProductFamiliesSchema = z.object({
+    search: z.string().optional(),
+    status: z.string().optional(),
+    productLineId: z.number().positive().optional(),
+});
+export type IListProductFamilies = z.infer<typeof ListProductFamiliesSchema>;
+
+export const ListProductFamiliesResponseSchema = z.object({
+    data: z.array(ProductFamilyBaseSchema),
+    page: z.number(),
+    total: z.number(),
+});
+export type IListProductFamiliesResponse = z.infer<typeof ListProductFamiliesResponseSchema>; 
\ No newline at end of file
