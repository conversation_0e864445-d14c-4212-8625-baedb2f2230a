"use client";

import { useTranslations } from "next-intl";
import { useState } from "react";
import { IUserBase } from "@px-shared-account/hermes";
import { Button } from "@/components/ui/button";
import { RemoveStudentsConfirmation } from "../modals/remove-students/RemoveStudentsConfirmation";
import { MoveStudentsModal } from "../modals/move-students/MoveStudentsModal";
import { Trash2, MoveRight, CheckSquare, X } from "lucide-react";

interface CohortStudentsSelectionActionsProps {
  cohortId: number;
  selectedStudents: IUserBase[];
  onStudentsRemoved?: () => void;
  onStudentsMoved?: () => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  isAllSelected?: boolean;
  totalStudents: number;
}

export function CohortStudentsSelectionActions({
  cohortId,
  selectedStudents,
  onStudentsRemoved,
  onStudentsMoved,
  onSelectAll,
  onClearSelection,
  isAllSelected = false,
  totalStudents,
}: CohortStudentsSelectionActionsProps) {
  const t = useTranslations("cohorts");
  const [isRemoveStudentsModalOpen, setIsRemoveStudentsModalOpen] = useState(false);
  const [isMoveStudentsModalOpen, setIsMoveStudentsModalOpen] = useState(false);

  return (
    <div className="mb-4 mt-2 flex flex-wrap items-center gap-2">
      <Button
        variant="secondary"
        size="sm"
        onClick={() => setIsMoveStudentsModalOpen(true)}
        disabled={!isAllSelected && selectedStudents.length === 0}
        className="flex items-center gap-2 hover:bg-accent"
      >
        <MoveRight className="h-4 w-4" />
        {t("students.move_to_cohort")}
      </Button>

      <Button
        variant="secondary"
        size="sm"
        onClick={() => setIsRemoveStudentsModalOpen(true)}
        disabled={!isAllSelected && selectedStudents.length === 0}
        className="flex items-center gap-2 hover:bg-accent"
      >
        <Trash2 className="h-4 w-4" />
        {t("students.remove")}
      </Button>

      {selectedStudents.length === 0 ? (
        <Button
          variant="secondary"
          size="sm"
          onClick={onSelectAll}
          className="flex items-center gap-2 hover:bg-accent"
        >
          <CheckSquare className="h-4 w-4" />
          {t("students.select_all")}
        </Button>
      ) : isAllSelected || selectedStudents.length === totalStudents ? (
        <Button
          variant="secondary"
          size="sm"
          onClick={onClearSelection}
          className="flex items-center gap-2 hover:bg-accent"
        >
          <X className="h-4 w-4" />
          {t("students.clear_selection")}
        </Button>
      ) : (
        <Button
          variant="secondary"
          size="sm"
          onClick={onSelectAll}
          className="flex items-center gap-2 hover:bg-accent"
        >
          <CheckSquare className="h-4 w-4" />
          {t("students.select_all")}
        </Button>
      )}

      <span className="ml-2 text-sm text-muted-foreground">
        <strong>{selectedStudents.length}</strong> {t("students.selected")}
      </span>

      {(isAllSelected || selectedStudents.length > 0) && (
        <>
          <RemoveStudentsConfirmation
            cohortId={cohortId}
            students={selectedStudents}
            isOpen={isRemoveStudentsModalOpen}
            onOpenChange={setIsRemoveStudentsModalOpen}
            onComplete={onStudentsRemoved}
          />

          <MoveStudentsModal
            sourceCohortId={cohortId}
            students={selectedStudents}
            isOpen={isMoveStudentsModalOpen}
            onOpenChange={setIsMoveStudentsModalOpen}
            onComplete={onStudentsMoved}
          />
        </>
      )}
    </div>
  );
}
