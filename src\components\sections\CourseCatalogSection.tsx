"use client";

import { useCallback, useEffect, useState } from "react";
import { useCourseCatalog } from "@/services/course";
import { IOwnedCourseCatalogItem, IPurchasableCourseCatalogItem } from "@px-shared-account/hermes";
import { ProductCard, ProductCardSkeleton } from "@/components/cards/ProductCard";
import { PageSectionBase, PageSectionCarousel } from "../base/page-section";
import { useTranslations } from "next-intl";

export function CourseCatalogSection() {
  const t = useTranslations("dashboard");
  const [cursor, setCursor] = useState<number | undefined>(0);
  const [ownedCourses, setOwnedCourses] = useState<IOwnedCourseCatalogItem[]>([]);
  const [allPurchasableCourses, setAllPurchasableCourses] = useState<
    IPurchasableCourseCatalogItem[]
  >([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { data, isLoading } = useCourseCatalog({ limit: 20, cursor });

  // Handle new response structure with separate owned and purchasable arrays
  useEffect(() => {
    if (data) {
      // Always update owned courses (they're returned in full every time)
      if (data.ownedCourses) {
        setOwnedCourses(data.ownedCourses);
      }

      // Handle purchasable courses with windowed pagination
      if (data.purchasableCourses) {
        if (cursor === 0) {
          // Initial load - replace all purchasable courses
          setAllPurchasableCourses(data.purchasableCourses);
        } else {
          // Windowed pagination load - append new purchasable courses
          setAllPurchasableCourses((prev) => {
            const existingIds = new Set(prev.map((course) => course.id));
            const newCourses = data.purchasableCourses.filter(
              (course) => !existingIds.has(course.id),
            );
            return [...prev, ...newCourses];
          });
        }
      }
      setIsLoadingMore(false);
    }
  }, [data, cursor]);

  const loadMoreCourses = useCallback(() => {
    if (
      data?.nextCursor !== null &&
      data?.nextCursor !== undefined &&
      !isLoadingMore &&
      !isLoading
    ) {
      setIsLoadingMore(true);
      setCursor(data.nextCursor);
    }
  }, [data, isLoadingMore, isLoading]);

  if (isLoading && ownedCourses.length === 0 && allPurchasableCourses.length === 0) {
    return (
      <PageSectionCarousel title={t("courses.title")} description={t("courses.sectionSubtitle")}>
        {Array.from({ length: 12 }).map((_, index) => (
          <ProductCardSkeleton key={index} />
        ))}
      </PageSectionCarousel>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      {ownedCourses.length === 1 ? (
        <PageSectionBase title={t("purchases.title")} description={t("purchases.sectionSubtitle")}>
          <div className="flex justify-center py-4">
            <ProductCard course={ownedCourses[0]} />
          </div>
        </PageSectionBase>
      ) : ownedCourses.length > 1 ? (
        <PageSectionCarousel
          title={t("purchases.title")}
          description={t("purchases.sectionSubtitle")}
          carouselItemsClassName="basis-full sm:basis-1/2 md:basis-auto"
          carouselOpts={{ loop: ownedCourses.length > 3 }}
        >
          {ownedCourses.map((course) => (
            <ProductCard key={course.id} course={course} />
          ))}
        </PageSectionCarousel>
      ) : null}

      {isLoading && allCourses.length === 0 ? (
        <PageSectionCarousel title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          {Array.from({ length: 12 }).map((_, index) => (
            <ProductCardSkeleton key={index} />
          ))}
        </PageSectionCarousel>
      ) : allPurchasableCourses.length === 0 ? (
        <PageSectionBase title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          <div className="p-8 text-center">
            <p>{t("courses.empty-state")}</p>
          </div>
        </PageSectionBase>
      ) : allPurchasableCourses.length === 1 ? (
        <PageSectionBase title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          <div className="flex justify-center py-4">
            <ProductCard course={allPurchasableCourses[0]} />
          </div>
        </PageSectionBase>
      ) : (
        <PageSectionCarousel
          title={t("courses.title")}
          description={t("courses.sectionSubtitle")}
          carouselItemsClassName="basis-full sm:basis-1/2 md:basis-auto"
          carouselOpts={{ loop: false }}
          isLoadingMore={isLoadingMore}
          hasMore={data?.nextCursor !== null && data?.nextCursor !== undefined}
          onEnd={() => {
            if (data?.nextCursor !== null && data?.nextCursor !== undefined && !isLoadingMore) {
              loadMoreCourses();
            }
          }}
        >
          {allPurchasableCourses.map((course) => (
            <ProductCard key={`course-${course.id}`} course={course} />
          ))}
        </PageSectionCarousel>
      )}
    </div>
  );
}
