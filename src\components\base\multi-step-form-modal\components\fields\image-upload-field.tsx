"use client";

import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { ImageUploadField as ImageUploadFieldType } from "../../types";
import { useFormContext } from "react-hook-form";
import { useCallback, useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { Button } from "@/components/ui/button";
import { X, Edit, Loader2, Upload } from "lucide-react";
import Image from "next/image";
import { uploadImageToS3 } from "@/app/actions/s3-actions";
import { useToast } from "@/hooks/use-toast";

interface ImageUploadFieldProps {
  field: ImageUploadFieldType;
  error?: string;
}

/**
 * Image upload field component for multi-step forms
 *
 * @known_issue There is a validation issue where the field may show "Image is required"
 * even when a value exists. This occurs may be due to a race condition between field registration
 * and initial value setting. The field registers with react-hook-form before the initial
 * value is properly set, causing validation to fail. A temporary fix is in place using
 * currentValue during registration, but this is not completely functional.
 * This issue needs to be fixed in the future.
 *
 * Related components to check when fixing:
 * - field-component.tsx (field registration)
 * - useMultiStepModal.ts (validation schema)
 * - MultiStepFormModal.tsx (form state management)
 * - ModalContent.tsx (error display)
 */
export const ImageUploadField = ({ field, error }: ImageUploadFieldProps) => {
  const t = useTranslations("components.multi-step-modal.image-upload");
  const [preview, setPreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const { register, setValue, watch } = useFormContext();
  const currentValue = watch(field.id);
  const { toast } = useToast();

  // Register the field with react-hook-form
  useEffect(() => {
    register(field.id, {
      value: currentValue,
    });
    if (currentValue && typeof currentValue === "string") {
      setPreview(currentValue);
    }
  }, [currentValue]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      if (file) {
        try {
          setIsUploading(true);
          // Show local preview immediately
          const localPreview = URL.createObjectURL(file);
          setPreview(localPreview);

          // Create FormData and append file
          const formData = new FormData();
          formData.append("file", file);

          // Upload to S3
          const imageUrl = await uploadImageToS3(formData);

          // Set the image URL as the field value
          setValue(field.id, imageUrl, {
            shouldValidate: true,
            shouldDirty: true,
          });

          // Clean up local preview URL
          URL.revokeObjectURL(localPreview);
          // Update preview with the actual S3 URL
          setPreview(imageUrl);

          toast({
            title: t("success"),
          });
        } catch (error) {
          toast({
            title: t("error"),
            variant: "destructive",
          });
          // Reset preview on error
          setPreview(null);
          setValue(field.id, undefined, {
            shouldValidate: true,
            shouldDirty: true,
          });
        } finally {
          setIsUploading(false);
        }
      }
    },
    [field, setValue, t],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": field.meta.acceptedTypes || [],
    },
    maxSize: (field.meta.maxSize || 5) * 1024 * 1024,
    multiple: false,
  });

  const handleRemove = () => {
    setPreview(null);
    setValue(field.id, undefined, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  return (
    <div className={cn("w-full flex-1", field.meta.className)}>
      <div
        className={cn(
          "relative h-full w-full overflow-hidden rounded-3xl border-2",
          error ? "border-destructive" : isDragActive ? "border-primary" : "border-input",
          "bg-background",
        )}
      >
        <div className={"relative h-full"}>
          {preview ? (
            <>
              <Image
                src={preview}
                alt={field.label}
                className="absolute inset-0 h-full w-full object-cover"
                fill
              />
              <Button
                variant="destructive"
                size="icon"
                className="absolute right-2 top-2 z-10"
                onClick={handleRemove}
                disabled={isUploading}
                title={t("remove")}
              >
                <X className="h-4 w-4" />
              </Button>
              {isUploading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                  <Loader2 className="h-6 w-6 animate-spin text-white" />
                </div>
              )}
            </>
          ) : (
            <div {...getRootProps()} className="absolute inset-0 cursor-pointer">
              <input {...getInputProps()} />
              <Button
                variant="secondary"
                size="icon"
                className="absolute bottom-2 right-2 z-10 h-12 w-12 rounded-full bg-background-secondary backdrop-blur-sm"
              >
                <Edit />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
