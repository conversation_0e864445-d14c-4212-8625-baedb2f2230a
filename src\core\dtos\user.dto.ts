import { IsEnum, IsInt, IsOptional, IsString, IsEmail } from 'class-validator';
import { Transform } from 'class-transformer';
import { createZodDto } from 'nestjs-zod';
import {
  CreateUserSchema,
  GetUserByIdSchema,
  SearchUserSchema,
  UpdateUserSchema,
  ListUsersResponseSchema,
  PaginationParamsSchema,
  UserType,
} from '@px-shared-account/hermes';
import { FiscalEntity } from '@enums';

export class CreateUserDto extends createZodDto(CreateUserSchema) {}
export class UpdateUserDto extends createZodDto(UpdateUserSchema) {}
export class GetUserByIdDto extends createZodDto(GetUserByIdSchema) {}
export class ListUsersDTO extends createZodDto(
  SearchUserSchema.merge(PaginationParamsSchema),
) {}
export class ListUsersResponseDto extends createZodDto(
  ListUsersResponseSchema,
) {}

/**
 * DTOs for Paradox OS
 */

export class ListOSUsersDTO {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsEnum(FiscalEntity)
  fiscalEntity?: FiscalEntity;

  @IsOptional()
  @IsEnum(UserType)
  type?: UserType;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  limit?: number;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  page?: number;

  @IsOptional()
  @IsEnum(['DESC', 'ASC'])
  orderBy?: 'DESC' | 'ASC';
}

export class GetOSUserByEmailDTO {
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;
}
