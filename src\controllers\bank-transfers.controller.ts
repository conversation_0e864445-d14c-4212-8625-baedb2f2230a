import { ApiTags, ApiOperation } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ListBankTransfersDTO, UpdateTransferStatusDTO } from '@dtos';
import { BankTransferUseCases } from '@useCases';

@ApiTags('bank-transfers')
@Controller('bank-transfers')
export class BankTransfersController {
  constructor(private readonly bankTransferUseCases: BankTransferUseCases) {}

  @ApiOperation({
    summary: 'List bank transfers',
  })
  @Get('/')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  )
  async list(@Query() query: ListBankTransfersDTO) {
    return this.bankTransferUseCases.list(
      query.query,
      query.bankName,
      query.paymentDate,
      query.currency,
      query.status,
      query.senderName,
      query.limit,
      query.page,
      query.orderBy,
    );
  }

  @ApiOperation({
    summary: 'Get a bank transfer',
  })
  @Get('/:id')
  async getOne(@Param('id') id: number) {
    return this.bankTransferUseCases.getById(id);
  }

  @ApiOperation({
    summary: 'Update the status of a bank transfer',
  })
  @Post('/:id/updateStatus')
  async updateStatus(
    @Param('id') id: number,
    @Body() updates: UpdateTransferStatusDTO,
  ) {
    return this.bankTransferUseCases.updateStatus(id, updates);
  }
}
