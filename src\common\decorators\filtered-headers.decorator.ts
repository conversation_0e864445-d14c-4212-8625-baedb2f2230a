import { createParamDecorator, ExecutionContext } from '@nestjs/common';

// Headers to remove (internal, sensitive, or routing-related) - lowercase for efficient Set lookup
const HEADERS_TO_REMOVE_SET = new Set([
    'host',
    'connection',
    'content-length',
    'accept-encoding',
    'user-agent',
    'x-forwarded-for',
    'x-forwarded-proto',
    'x-forwarded-port',
    'x-real-ip',
    'cf-ray',
    'cf-visitor',
    'cf-connecting-ip',
    'x-request-id',
    'x-correlation-id',
    // pg-boss headers
    'pg-boss-job-id',
    'pg-boss-retry-count',
]);

export const FilteredHeaders = createParamDecorator(
    (data: unknown, ctx: ExecutionContext) => {
        const request = ctx.switchToHttp().getRequest();
        const headers = request.headers;

        // Filter headers immutably using Set lookup
        const filtered = Object.fromEntries(
            Object.entries(headers).filter(
                ([key]) => !HEADERS_TO_REMOVE_SET.has(key.toLowerCase())
            )
        );

        return filtered;
    },
); 