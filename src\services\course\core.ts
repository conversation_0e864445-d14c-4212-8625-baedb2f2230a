import {
  CourseStatus,
  IBaseCourse,
  ICreateCourse,
  IListCoursesResponse,
  IUpdateCourse,
  PXActionResult,
  UpdateResultWithItemInfo,
  IPaginationParams,
  IGetStudentsForCourseResponse,
  IListCourseCatalogParams,
  IListCourseCatalogResponse
} from "@px-shared-account/hermes";

/**
 * Type for course list parameters
 */
export type ListCoursesParams = IPaginationParams & {
  search?: string;
  status?: CourseStatus;
};

/**
 * Core API endpoints for course operations
 * These are the base paths and methods used by both client and server implementations
 */
export const COURSE_API_ENDPOINTS = {
  /**
   * List courses endpoint
   * @param params Query parameters for filtering and pagination
   * @returns Formatted URL with query parameters
   */
  list: (params: ListCoursesParams = {}) => {
    const { search, status, page = 1, limit = 10 } = params;
    const queryParams = new URLSearchParams();

    if (search) queryParams.append("search", search);
    if (status) queryParams.append("status", status);
    queryParams.append("page", String(page));
    queryParams.append("limit", String(limit));

    return {
      url: `/course/list?${queryParams.toString()}`,
      method: "GET",
    };
  },

  /**
   * List course catalog endpoint
   * @param params Query parameters for filtering and pagination
   * @returns Formatted URL with query parameters
   */
  listCatalog: (params: IListCourseCatalogParams = { limit: 50 }) => {
    const { limit, cursor, search, status, owned } = params;
    const queryParams = new URLSearchParams();

    if (search) queryParams.append("search", search);
    if (status) queryParams.append("status", status);
    if (owned) queryParams.append("owned", owned.toString());
    if (cursor) queryParams.append("cursor", cursor.toString());
    queryParams.append("limit", String(limit));

    return {
      url: `/course/catalog?${queryParams.toString()}`,
      method: "GET",
    };
  },

  /**
   * Get course by ID endpoint
   * @param id Course ID
   * @returns Endpoint configuration
   */
  getById: (id: number) => ({
    url: `/course/${id}`,
    method: "GET",
  }),

  /**
   * Create course endpoint
   * @returns Endpoint configuration
   */
  create: () => ({
    url: "/course",
    method: "POST",
  }),

  /**
   * Update course endpoint
   * @param id Course ID
   * @returns Endpoint configuration
   */
  update: (id: number) => ({
    url: `/course/${id}`,
    method: "PATCH",
  }),

  /**
   * Publish course endpoint
   * @param id Course ID
   * @returns Endpoint configuration
   */
  publish: (id: number) => ({
    url: `/course/${id}/publish`,
    method: "POST",
  }),

  /**
   * Archive course endpoint
   * @param id Course ID
   * @returns Endpoint configuration
   */
  archive: (id: number) => ({
    url: `/course/${id}/archive`,
    method: "POST",
  }),

  /**
   * Get students for course endpoint
   * @param courseId Course ID
   * @returns Endpoint configuration
   */
  getStudents: (courseId: number, params?: IPaginationParams & { search?: string }) => {
    const queryParams = new URLSearchParams();
    const { search, page = 1, limit = 10 } = params || {};

    if (search) queryParams.append("search", search);
    queryParams.append("page", String(page));
    queryParams.append("limit", String(limit));

    return {
      url: `/course/${courseId}/students?${queryParams.toString()}`,
      method: "GET",
    };
  },

  /**
   * Get courses by offer ID endpoint
   * @param offerId Offer ID
   * @returns Endpoint configuration
   */
  getByOfferId: (offerId: number) => ({
    url: `/course/offer/${offerId}`,
    method: "GET",
  }),
};

/**
 * Type definitions for API responses
 */
export type CourseApiTypes = {
  list: IListCoursesResponse;
  listCatalog: IListCourseCatalogResponse;
  getById: IBaseCourse;
  create: IBaseCourse;
  update: UpdateResultWithItemInfo<IBaseCourse>;
  publish: PXActionResult;
  archive: PXActionResult;
  getStudents: IGetStudentsForCourseResponse;
  getByOfferId: IBaseCourse[];
};

/**
 * Type definitions for API request payloads
 */
export type CourseApiPayloads = {
  create: ICreateCourse;
  update: IUpdateCourse;
  publish: Record<string, never>;
  archive: Record<string, never>;
};
