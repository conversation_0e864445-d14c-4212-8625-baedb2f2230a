import { Injectable } from '@nestjs/common';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import {
  ProductOfferStatus,
  ProductOfferVersion,
} from '@px-shared-account/hermes';
import { ProductPlanUseCases } from '../product-plan';
import { CheckoutPage, ProductOffer } from '@entities';
import { IDataServices } from '@abstracts';
import {
  UpdateResultWithItemInfo,
  ForeverCheckoutProduct,
  ItemsWithBillingCycle,
  ProductQuantities,
  AttachedProduct,
  CheckoutProduct,
  CheckoutConfig,
  CycleWithPlan,
  OfferConfig,
} from '@types';

@Injectable()
export class CheckoutPageUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly productPlanUseCases: ProductPlanUseCases,
  ) {}

  /**
   * Creates a new checkout page in database
   * @param checkoutPage Checkout Page entity
   * @returns Checkout Page record from database
   */
  async create(checkoutPage: CheckoutPage): Promise<CheckoutPage> {
    return this.dataServices.checkoutPage.create(checkoutPage);
  }

  /**
   * Updates a checkout page in database
   * @param id `id` of the Checkout Page to update
   * @param updates Updates for the Checkout page
   * @param linkedOfferId `id` of the offer linked to this `CheckoutPage`
   */
  async update(
    id: number,
    updates: QueryDeepPartialEntity<CheckoutPage>,
    linkedOfferId?: number,
  ): Promise<UpdateResultWithItemInfo<CheckoutPage>> {
    const { success, updatedItem } =
      await this.dataServices.checkoutPage.updateAndReturnItem(
        'id',
        id.toString(),
        updates,
      );
    if (linkedOfferId) {
      await this.dataServices.productOffer.update(
        { id: linkedOfferId },
        {
          version: ProductOfferVersion.PREVIEW,
        },
      );
    }
    return { success, updatedItem };
  }

  /**
   * Retrieves the checkout configuration for a given product offer.
   * @param offer - The product offer for which to retrieve the checkout configuration.
   * @returns A promise that resolves to the checkout configuration.
   */
  async getCheckoutConfig(offer: ProductOffer): Promise<CheckoutConfig> {
    const config: CheckoutConfig = {
      name: offer.name,
      template: {
        id: offer?.checkoutPage?.template,
        bannerImg: offer?.checkoutPage?.bannerImage,
        primaryColor: offer?.checkoutPage?.mainColor,
        warranty: offer?.checkoutPage?.warrantySections,
      },
      currency: offer.currency,
      display: offer?.config?.display,
      usp: offer?.usp,
      planId: offer.chargebeeId,
      cancellationPolicy: offer?.checkoutPage?.cancellationPolicy,
      returnUrl: offer.redirectUrl,
      offerImg: offer.image,
      status: ProductOfferStatus.ACTIVE,
      gateway_account_id: offer.paymentGateway,
    };
    if (offer?.checkoutPage?.template === 'B') {
      config.template.testimonial = offer?.checkoutPage?.testimony;
    }
    if (offer.isForever) {
      config.isForever = true;
      config.subscriptionPlan = offer.chargebeeId;
      config.foreverItems = await this.getForeverCheckoutItems(
        offer.trialDaysMonthly,
        offer.trialDaysYearly,
        offer.config,
      );
    } else {
      config.items = await this.extractItemsWithBillingCycle(offer.config);
      config.selectedBillingCycle = offer.config.defaultBillingCycle.toString();
    }
    return config;
  }

  /**
   * Extracts items with their billing cycles based on the provided configuration.
   * @param config - The offer configuration containing products, recommended items, and quantity information.
   * @returns A promise that resolves to an object representing the items with their billing cycles.
   */
  async extractItemsWithBillingCycle(
    config: OfferConfig,
  ): Promise<ItemsWithBillingCycle> {
    const {
      products: productsConfig,
      recommended: recommendedConfig,
      quantityInfo: {
        products: productsQuantity,
        recommended: recommendedQuantity,
      },
    } = config;

    const attachedProducts = await this.setCheckoutItems(
      productsConfig,
      productsQuantity,
      'products',
    );

    const checkoutItems: ItemsWithBillingCycle = {};
    for (const key in attachedProducts) {
      checkoutItems[key] = {
        products: [...(attachedProducts[key]?.products || [])],
      };
    }

    let attachedRecommended: ItemsWithBillingCycle = {};
    if (!!recommendedConfig) {
      attachedRecommended = await this.setCheckoutItems(
        recommendedConfig,
        recommendedQuantity,
        'recommended',
      );
    }

    for (const key in attachedRecommended) {
      const products = checkoutItems[key]?.products || [];
      checkoutItems[key] = {
        products,
        recommended: [...(attachedRecommended[key]?.recommended || [])],
      };
    }

    return checkoutItems;
  }

  /**
   * Sets the checkout items based on the provided parameters.
   * @param productsConfig - The configuration of the attached products.
   * @param productsQuantity - The quantities of the products.
   * @param cyclesMap - The map of items with their billing cycles.
   * @param productsType - The type of products ('products' or 'recommended').
   * @returns A Promise that resolves when the checkout items are set.
   */
  async setCheckoutItems(
    productsConfig: AttachedProduct = {},
    productsQuantity: ProductQuantities = {},
    productsType: 'products' | 'recommended',
  ): Promise<ItemsWithBillingCycle> {
    const productMap = this.createProductMap(productsQuantity);
    const cyclesMap: ItemsWithBillingCycle = {};
    const configProductEntries = Object.entries(productsConfig);

    for (const [productId, cyclesWithPlans] of configProductEntries) {
      const cyclesMapForCurrentProduct = await this.setBillingCyclesMap(
        productId,
        cyclesWithPlans,
        productMap,
        productsType,
      );
      this.updateBillingCyclesMap(
        cyclesMap,
        cyclesMapForCurrentProduct,
        productsType,
      );
    }

    return cyclesMap;
  }

  /**
   * Creates a product map based on the given products and their quantities.
   * @param productsQuantity - An object containing the quantities of each product.
   * @returns A map where the keys are the product IDs and the values are the corresponding CheckoutProduct objects.
   */
  createProductMap(
    productsQuantity: ProductQuantities,
  ): Map<string, CheckoutProduct> {
    const productMap = new Map<string, CheckoutProduct>();
    Object.entries(productsQuantity).forEach(([id, allowQuantity]) => {
      productMap.set(id, {
        id,
        img: '',
        name: '',
        allowQuantity,
        isEvent: false,
        eventCountry: '',
      });
    });
    return productMap;
  }

  /**
   * Updates the billing cycles map with the provided billing cycles map for the current product.
   * @param cyclesMap - The original cycles map.
   * @param cyclesMapForCurrentProduct - The cycles map for the current product.
   * @param productsType - The type of products ('products' or 'recommended').
   */
  updateBillingCyclesMap(
    cyclesMap: ItemsWithBillingCycle,
    cyclesMapForCurrentProduct: ItemsWithBillingCycle,
    productsType: 'products' | 'recommended',
  ): void {
    for (const billingCycle in cyclesMapForCurrentProduct) {
      if (cyclesMapForCurrentProduct.hasOwnProperty(billingCycle)) {
        if (!cyclesMap[billingCycle]) {
          cyclesMap[billingCycle] = {
            products: [],
            recommended: [],
          };
        }

        if (productsType === 'products') {
          cyclesMap[billingCycle].products.push(
            ...(cyclesMapForCurrentProduct[billingCycle]?.products || []),
          );
        } else if (productsType === 'recommended') {
          cyclesMap[billingCycle].recommended.push(
            ...(cyclesMapForCurrentProduct[billingCycle]?.recommended || []),
          );
        }
      }
    }
  }

  /**
   * Sets the billing cycles map for a given product and its plans.
   * @param productId - The ID of the product.
   * @param cyclesWithPlans - An array of cycles with their corresponding plans.
   * @param productMap - A map of products.
   * @param itemType - The type of item ('products' or 'recommended').
   * @param cyclesMap - The map of items with their billing cycles.
   */
  async setBillingCyclesMap(
    productId: string,
    cyclesWithPlans: CycleWithPlan[],
    productMap: Map<string, CheckoutProduct>,
    itemType: 'products' | 'recommended',
  ): Promise<ItemsWithBillingCycle> {
    const cyclesMap: ItemsWithBillingCycle = {};
    const product = productMap.get(productId);
    const productFromDB = await this.dataServices.product.getOneBy({
      id: Number(productId),
    });

    for (const cycleWithPlan of cyclesWithPlans) {
      const billingCycle = Object.keys(cycleWithPlan)[0];
      const planId = Object.values(cycleWithPlan)[0];
      product.name = productFromDB.externalName;
      product.img = productFromDB.image;
      product.isEvent = productFromDB?.isEvent ?? false;
      product.eventCountry = productFromDB?.eventLocation ?? '';
      const planFromDB = await this.dataServices.productPlan.getOneBy({
        chargebeeId: planId,
      });
      product.id = planFromDB.prices[0].chargebeeId;
      let currentCycleConfig = { ...cyclesMap[billingCycle] };
      if (!cyclesMap[billingCycle]) {
        currentCycleConfig = {
          products: [],
          recommended: [],
        };
      }

      if (itemType === 'recommended') {
        currentCycleConfig.recommended = currentCycleConfig.recommended || [];
        currentCycleConfig.recommended.push({ ...product });
      }
      if (itemType === 'products') {
        currentCycleConfig.products.push({ ...product });
      }

      cyclesMap[billingCycle] = {
        ...currentCycleConfig,
        ...cyclesMap[billingCycle],
      };
    }
    return cyclesMap;
  }

  /**
   * Generates an array of products with forever billing cycles.
   * @param trialDaysMonthly Number of trial days for monthly forever items
   * @param trialDaysYearly Number of trial days for yearly forever items
   * @param offerConfig Offer configuration including addon-ids attached to this offer
   * @returns A promise that resolves to an array of `ForeverCheckoutProduct`s
   */
  async getForeverCheckoutItems(
    trialDaysMonthly: number,
    trialDaysYearly: number,
    offerConfig: OfferConfig,
  ): Promise<ForeverCheckoutProduct[]> {
    const foreverItems: ForeverCheckoutProduct[] = [];
    const monthlyAddons = offerConfig?.monthly || [];
    const yearlyAddons = offerConfig?.yearly || [];

    for (const addon of monthlyAddons) {
      const item = await this.productPlanUseCases.getProductInfoForForeverOffer(
        addon,
      );
      foreverItems.push({
        id: item?.prices[0]?.chargebeeId,
        img: item?.product?.image,
        name: item?.product?.externalName,
        periodicity: 'monthly',
        trialDuration: trialDaysMonthly,
      });
    }

    for (const addon of yearlyAddons) {
      const item = await this.productPlanUseCases.getProductInfoForForeverOffer(
        addon,
      );
      foreverItems.push({
        id: item?.prices[0]?.chargebeeId,
        img: item?.product?.image,
        name: item?.product?.externalName,
        periodicity: 'yearly',
        trialDuration: trialDaysYearly,
      });
    }

    return foreverItems;
  }

  /**
   * Retrieves the linked product offer for a given checkout page ID.
   * @param checkoutPageId The ID of the checkout page.
   * @returns A promise that resolves to the linked product offer.
   */
  async getLinkedOffer(checkoutPageId: number): Promise<ProductOffer> {
    const checkoutPage = await this.dataServices.checkoutPage
      .getRepository()
      .createQueryBuilder('checkoutPage')
      .leftJoin('checkoutPage.productOffer', 'productOffer')
      .where('checkoutPage.id = :checkoutPageId', { checkoutPageId })
      .getOne();
    return checkoutPage?.productOffer;
  }
}
