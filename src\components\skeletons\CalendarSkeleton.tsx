import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { BaseProps } from "@/types";

export const CalendarSkeleton = ({ className }: BaseProps) => {
  // Helper to generate random width for event skeletons
  const randomWidth = () => `${Math.floor(Math.random() * 40 + 60)}%`;

  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* Calendar Header */}
      <div className="mb-8 flex items-center justify-between">
        <Skeleton className="h-8 w-32" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-9 w-9 rounded-lg" />
          <Skeleton className="h-9 w-9 rounded-lg" />
          <Skeleton className="h-9 w-24 rounded-lg" />
        </div>
      </div>

      {/* Week Days */}
      <div className="mb-4 grid grid-cols-7 gap-4">
        {Array.from({ length: 7 }).map((_, i) => (
          <Skeleton key={i} className="h-6" />
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-4">
        {Array.from({ length: 35 }).map((_, i) => (
          <div key={i} className="aspect-square rounded-lg border p-2">
            {/* Date */}
            <Skeleton className="mb-2 h-6 w-6 rounded-full" />

            {/* Events */}
            <div className="space-y-1">
              {Math.random() > 0.5 && (
                <Skeleton className="h-5 rounded" style={{ width: randomWidth() }} />
              )}
              {Math.random() > 0.7 && (
                <Skeleton className="h-5 rounded" style={{ width: randomWidth() }} />
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

CalendarSkeleton.displayName = "CalendarSkeleton";
