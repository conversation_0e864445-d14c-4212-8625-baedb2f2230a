import { IsNotEmpty, IsString, <PERSON>N<PERSON>ber, IsOptional } from 'class-validator';
import {
  ApiExtraModels,
  ApiProperty,
  ApiPropertyOptional,
  PartialType,
} from '@nestjs/swagger';
import { CreateProductPlanPriceDTO, UpdateProductPlanPriceDTO } from '@dtos';
import { ProductCatalogEntityStatus } from '@enums';

export class CreateProductPlanDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  internalName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  externalName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  crmSku: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  productId: number;
}

export class UpdateProductPlanDTO extends PartialType(CreateProductPlanDto) {
  @ApiProperty()
  id: number;

  @ApiPropertyOptional()
  @ApiProperty({ enum: ProductCatalogEntityStatus })
  @IsOptional()
  status?: ProductCatalogEntityStatus;
}

export class CreatePlanWithPriceDTO {
  @ApiProperty()
  planInfo: CreateProductPlanDto;
  @ApiProperty()
  priceInfo: CreateProductPlanPriceDTO;
}

@ApiExtraModels(CreateProductPlanDto, CreateProductPlanPriceDTO)
export class UpdatePlanWithPriceDTO {
  @ApiProperty({ type: () => UpdateProductPlanDTO })
  planInfo: UpdateProductPlanDTO;

  @ApiProperty({ type: () => UpdateProductPlanPriceDTO })
  priceInfo: UpdateProductPlanPriceDTO;
}
