import { CohortStatus, ICommunityInfo } from '@px-shared-account/hermes';
import { BaseEntity } from './base.entity';
import { CourseEntity } from './course.entity';
import { UserEntity } from './user.entity';

export class CohortEntity extends BaseEntity {
  name: string;
  description: string;
  maxParticipants: number;
  currentParticipants: number;
  startDate: Date;
  endDate: Date;
  status: CohortStatus;
  course: CourseEntity;
  students: UserEntity[];
  communityInfo: ICommunityInfo;
}
