import { auth } from "@clerk/nextjs/server";
import server<PERSON>etcher from "@/lib/server/server-fetcher";
import { COURSE_API_ENDPOINTS, CourseApiTypes, ListCoursesParams, CourseApiPayloads } from "./core";
import { IListCourseCatalogParams } from "@px-shared-account/hermes";

/**
 * Creates a server-side token getter for the fetcher
 * @returns Promise with authentication token or null
 */
const getServerToken = async () => {
  const session = await auth();
  return session?.getToken() || null;
};

// Initialize the fetcher with server-side token getter
const apiFetcher = serverFetcher(getServerToken);

/**
 * Server-side function for fetching course list
 * @param params Filter and pagination parameters
 * @returns Promise with course list data
 */
export async function getCourseList(
  params: ListCoursesParams = {},
): Promise<CourseApiTypes["list"]> {
  const { url } = COURSE_API_ENDPOINTS.list(params);
  return apiFetcher(url);
}

export async function getCourseCatalog(
  params: IListCourseCatalogParams = { limit: 50 },
): Promise<CourseApiTypes["listCatalog"]> {
  const { url } = COURSE_API_ENDPOINTS.listCatalog(params);
  return apiFetcher(url);
}

/**
 * Server-side function for fetching a specific course by ID
 * @param id Course ID to fetch
 * @returns Promise with course data
 */
export async function getCourseById(id: number): Promise<CourseApiTypes["getById"]> {
  const { url } = COURSE_API_ENDPOINTS.getById(id);
  return apiFetcher(url);
}

/**
 * Alias for getCourseById to maintain compatibility with existing code
 * @param id Course ID to fetch
 * @returns Promise with course data
 */
export const getCourseDetails = getCourseById;

/**
 * Server-side function for creating a new course
 * @param data Course creation data
 * @returns Promise with created course data
 */
export async function createCourse(
  data: CourseApiPayloads["create"],
): Promise<CourseApiTypes["create"]> {
  const { url, method } = COURSE_API_ENDPOINTS.create();
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for updating an existing course
 * @param id Course ID to update
 * @param data Course update data
 * @returns Promise with update result
 */
export async function updateCourse(
  id: number,
  data: CourseApiPayloads["update"],
): Promise<CourseApiTypes["update"]> {
  const { url, method } = COURSE_API_ENDPOINTS.update(id);
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for publishing a course
 * @param id Course ID to publish
 * @returns Promise with action result
 */
export async function publishCourse(id: number): Promise<CourseApiTypes["publish"]> {
  const { url, method } = COURSE_API_ENDPOINTS.publish(id);
  return apiFetcher(url, { method });
}

/**
 * Server-side function for archiving a course
 * @param id Course ID to archive
 * @returns Promise with action result
 */
export async function archiveCourse(id: number): Promise<CourseApiTypes["archive"]> {
  const { url, method } = COURSE_API_ENDPOINTS.archive(id);
  return apiFetcher(url, { method });
}

/**
 * Server-side function for duplicating a course
 * This is a utility function that gets a course and creates a new one with modified data
 * @param id Course ID to duplicate
 * @returns Promise with the newly created course
 */
export async function duplicateCourse(id: number): Promise<CourseApiTypes["create"]> {
  // First, get the course details
  const sourceCourse = await getCourseById(id);

  if (!sourceCourse) {
    throw new Error("Course not found");
  }

  // Create new course data with copied fields and modified name
  const duplicateData: CourseApiPayloads["create"] = {
    name: `${sourceCourse.name} - copy`,
    description: sourceCourse.description,
    bannerImage: sourceCourse.bannerImage,
    cardImage: sourceCourse.cardImage,
    thumbnail: sourceCourse.thumbnail,
    lmsId: sourceCourse.lmsId,
    productFamilyId: sourceCourse.productFamily.id,
    managers:
      sourceCourse.managers?.map((manager) =>
        typeof manager === "string" ? manager : manager.id,
      ) || [],
    offers: sourceCourse.offers || [],
  };

  // Create the new course
  return createCourse(duplicateData);
}

/**
 * Server-side function for fetching students for a course
 * @param courseId Course ID to fetch students for
 * @param params Pagination and search parameters
 * @returns Promise with students data
 */
export async function getStudentsForCourse(
  courseId: number,
  params?: ListCoursesParams,
): Promise<CourseApiTypes["getStudents"]> {
  const { url } = COURSE_API_ENDPOINTS.getStudents(courseId, params);
  return apiFetcher(url);
}

/**
 * Server-side function for fetching courses by offer ID
 * @param offerId Offer ID to fetch courses for
 * @returns Promise with courses data
 */
export async function getCoursesByOffer(offerId: number): Promise<CourseApiTypes["getByOfferId"]> {
  const { url } = COURSE_API_ENDPOINTS.getByOfferId(offerId);
  return apiFetcher(url);
}
