import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ChargeBeeWebhookPayload, PXActionResult } from '@types';
import { DiscountUseCases } from '../discount';
import { CreditNoteUseCases } from '../credit-note';
import { InvoiceUseCases } from '../invoice';
import { SubscriptionUseCases } from '../subscription';
import { TransactionUseCases } from '../transaction';

@Injectable()
export class ChargeBeeWebhooksHandler {
  private eventHandlers: Record<
    string,
    (payload: any, isCron?: boolean) => Promise<PXActionResult>
  >;

  constructor(
    private readonly discountUseCases: DiscountUseCases,
    private readonly transactionUseCases: TransactionUseCases,
    private readonly creditNoteUseCases: CreditNoteUseCases,
    private readonly subscriptionUseCases: SubscriptionUseCases,
    private readonly invoiceUseCases: InvoiceUseCases,
  ) {
    this.eventHandlers = {
      coupon_updated: (payload) =>
        this.discountUseCases.syncDiscountWithChargeBee(payload.coupon),
      payment_succeeded: (payload) =>
        this.transactionUseCases.createOrUpdateFromChargebeeEvent(
          payload,
          'create',
        ),
      payment_failed: (payload) =>
        this.transactionUseCases.createOrUpdateFromChargebeeEvent(
          payload,
          'create',
        ),
      payment_refunded: async (payload) => {
        const { transaction, ...output } =
          await this.transactionUseCases.createOrUpdateFromChargebeeEvent(
            payload,
            'create',
          );
        await this.subscriptionUseCases.recalculateAmountsAfterRefunded(
          transaction?.subscriptionId,
          transaction?.amount,
        );
        return { ...output };
      },
      transaction_created: (payload) =>
        this.transactionUseCases.createOrUpdateFromChargebeeEvent(
          payload,
          'create',
        ),
      transaction_updated: (payload) =>
        this.transactionUseCases.createOrUpdateFromChargebeeEvent(
          payload,
          'update',
        ),
      credit_note_created: (payload) =>
        this.creditNoteUseCases.createOrUpdateFromChargebeeEvent(
          payload,
          'create',
        ),
      credit_note_updated: (payload) =>
        this.creditNoteUseCases.createOrUpdateFromChargebeeEvent(
          payload,
          'update',
        ),
      invoice_generated: (payload) =>
        this.invoiceUseCases.createFromChargebeeEvent(payload),
      invoice_updated: (payload) =>
        this.invoiceUseCases.updateFromChargebeeEvent(payload),
      subscription_created: (payload, isCron) =>
        this.subscriptionUseCases.createFromChargebeeEvent(payload, isCron),
      subscription_changed: (payload) =>
        this.subscriptionUseCases.updateFromChargebeeEvent(payload),
      subscription_cancelled: (payload) =>
        this.subscriptionUseCases.cancelFromChargebeeEvent(payload),
      subscription_renewed: (payload) =>
        this.subscriptionUseCases.updateFromChargebeeEvent(payload),
    };
  }

  /**
   * Validates and processes ChargeBee's webhook events
   * @param payload ChargeBee's webhook event payload
   */
  async handle(
    payload: ChargeBeeWebhookPayload,
    isCron?: boolean,
  ): Promise<PXActionResult> {
    this.validateEvent(payload);
    const eventType = payload.event_type;
    const handler = this.eventHandlers[eventType];
    const result: PXActionResult = handler
      ? await handler(payload.content, isCron)
      : {
          success: true,
          message: `No handler for event type ${eventType}`,
          data: null,
        };
    return result;
  }

  /**
   * Validates ChargeBee's event payload by checking API version & valid payload.`content`
   * @param payload ChargeBee's webhook event payload
   * @throws HttpException if the event payload is invalid
   */
  private validateEvent(payload: ChargeBeeWebhookPayload) {
    if (payload.api_version !== 'v2') {
      throw new HttpException(
        {
          message: `Expected ChargeBee API version v2, but received ${payload.api_version}`,
          error: 'API version mismatch',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!payload.content) {
      throw new HttpException(
        {
          message: `No valid content provided`,
          error: `payload.content cannot be empty`,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
