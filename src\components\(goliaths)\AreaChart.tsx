"use client";

import { Area, AreaChart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts";

import { ChartContainer } from "../ui/chart";
import { chartConfig, chartData1 } from "@/lib/goliaths";
import Button from "../base/button";

type AreaChartProps = {
  color: string;
  blurred?: boolean;
  data: typeof chartData1;
  onButtonClick?: () => void;
  title: string;
};

export default function AreaChart({
  color,
  blurred = false,
  data,
  onButtonClick,
  title,
}: AreaChartProps) {
  return (
    <div className="relative w-full">
      <div className="font-anton text-lg font-normal uppercase">{title}</div>
      <ChartContainer
        config={chartConfig}
        className={`h-[256px] w-full ${blurred ? "blur-sm filter" : ""}`}
      >
        <RechartsAreaChart data={data} width={500} height={250}>
          <defs>
            <linearGradient id={`${color}`} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={color} stopOpacity={0.8} />
              <stop offset="95%" stopColor={color} stopOpacity={0.05} />
            </linearGradient>
          </defs>
          <CartesianGrid vertical={false} horizontal={false} />
          <YAxis hide={true} tickLine={false} strokeWidth={0} />
          <XAxis
            dataKey="month"
            hide={true}
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => value.slice(0, 3)}
          />
          <Area
            dataKey="desktop"
            type="natural"
            fill={`url(#${color})`}
            stroke={color}
            strokeWidth={2}
          />
        </RechartsAreaChart>
      </ChartContainer>
      {blurred && onButtonClick && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Button variant="secondary" onClick={onButtonClick}>
            Pour les membres
          </Button>
        </div>
      )}
    </div>
  );
}
