import {
  BankTransfer,
  CustomerAccess,
  CronLog,
  Discount,
  Invoice,
  CreditNote,
  Transaction,
  ProductLine,
  ProductFamily,
  Product,
  ProductPlan,
  ProductPlanPrice,
  CheckoutPage,
  ProductOffer,
  WarrantySection,
  Subscription,
  ProductOfferAccess,
  ExchangeRate,
  Webhook,
  CohortEntity,
  CourseEntity,
  RoleEntity,
  UserEntity,
  UserProfileEntity,
  TriadEntity,
  TriadParticipantEntity,
  WhatsNewVersionEntity,
  WhatsNewReactionEntity,
} from '@entities';
import { IGenericRepository } from './generic-repository.abstract';
import { DataSource } from 'typeorm';

export abstract class IDataServices {
  abstract bankTransfer: IGenericRepository<BankTransfer>;
  abstract checkoutPage: IGenericRepository<CheckoutPage>;
  abstract creditNote: IGenericRepository<CreditNote>;
  abstract cronLog: IGenericRepository<CronLog>;
  abstract customerAccess: IGenericRepository<CustomerAccess>;
  abstract discount: IGenericRepository<Discount>;
  abstract exchangeRate: IGenericRepository<ExchangeRate>;
  abstract invoice: IGenericRepository<Invoice>;
  abstract productLine: IGenericRepository<ProductLine>;
  abstract productFamily: IGenericRepository<ProductFamily>;
  abstract product: IGenericRepository<Product>;
  abstract productOffer: IGenericRepository<ProductOffer>;
  abstract productOfferAccess: IGenericRepository<ProductOfferAccess>;
  abstract productPlan: IGenericRepository<ProductPlan>;
  abstract productPlanPrice: IGenericRepository<ProductPlanPrice>;
  abstract subscription: IGenericRepository<Subscription>;
  abstract transaction: IGenericRepository<Transaction>;
  abstract warrantySection: IGenericRepository<WarrantySection>;
  abstract webhook: IGenericRepository<Webhook>;
  abstract whatsNewVersion: IGenericRepository<WhatsNewVersionEntity>;
  abstract whatsNewReaction: IGenericRepository<WhatsNewReactionEntity>;

  abstract cohort: IGenericRepository<CohortEntity>;
  abstract course: IGenericRepository<CourseEntity>;
  abstract role: IGenericRepository<RoleEntity>;
  abstract user: IGenericRepository<UserEntity>;
  abstract userProfile: IGenericRepository<UserProfileEntity>;
  abstract triad: IGenericRepository<TriadEntity>;
  abstract triadParticipant: IGenericRepository<TriadParticipantEntity>;

  abstract resetDatabase(): Promise<any>;
  abstract getDataSource(): DataSource;
}
