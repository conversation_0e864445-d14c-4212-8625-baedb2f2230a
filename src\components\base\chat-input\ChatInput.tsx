"use client";

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { PaperclipIcon, SendIcon, SmileIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRef, useState } from "react";

export interface ChatInputProps {
  onSend: (message: string) => void;
  onAttachment?: (file: File) => void;
  disabled?: boolean;
  className?: string;
}

export function ChatInput({ onSend, onAttachment, disabled, className }: ChatInputProps) {
  const [message, setMessage] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const t = useTranslations("messages");

  const handleSend = () => {
    if (message.trim()) {
      onSend(message.trim());
      setMessage("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && onAttachment) {
      onAttachment(file);
    }
  };

  return (
    <div className={cn("flex w-full items-end gap-2 border-t bg-background p-4", className)}>
      <Button
        variant="ghost"
        size="icon"
        className="shrink-0"
        onClick={() => fileInputRef.current?.click()}
        disabled={disabled}
      >
        <PaperclipIcon className="h-5 w-5" />
      </Button>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
        accept="image/*,application/pdf"
      />
      <Button variant="ghost" size="icon" className="shrink-0" disabled={disabled}>
        <SmileIcon className="h-5 w-5" />
      </Button>
      <Textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={t("type-message")}
        className="min-h-[2.5rem] resize-none"
        rows={1}
        disabled={disabled}
      />
      <Button className="shrink-0" onClick={handleSend} disabled={disabled || !message.trim()}>
        <SendIcon className="h-5 w-5" />
      </Button>
    </div>
  );
}
