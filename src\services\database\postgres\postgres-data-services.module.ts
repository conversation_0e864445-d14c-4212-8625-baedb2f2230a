import { Logger, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as tables from '@tables';
import { PostgresDataServices } from './postgres-database-services.service';
import { IDataServices } from '@abstracts';
import { ConfigService } from '@config';

@Module({
  imports: [
    TypeOrmModule.forFeature(Object.values(tables)),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        if (!configService.dbSecrets) {
          Logger.error('DB secrets not found');
          throw new Error('DB secrets not found');
        }

        const {
          HOST: host,
          PORT: port,
          USERNAME: username,
          PASSWORD: password,
          DATABASE: database,
        } = configService.dbSecrets;

        Logger.log(`Attempting to connect to the database...`);

        return {
          type: 'postgres',
          host,
          port: Number(port),
          username,
          password,
          database,
          autoLoadEntities: true,
          synchronize: false,
          ssl: {
            rejectUnauthorized: false,
          },
          entities: Object.values(tables),
          logging: true,
        };
      },
    }),
  ],
  providers: [
    {
      provide: IDataServices,
      useClass: PostgresDataServices,
    },
  ],
  exports: [IDataServices],
})
export class PostgresDataServicesModule {}
