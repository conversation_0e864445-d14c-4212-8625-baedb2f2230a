"use client";

import Image from "next/image";
import { Card } from "@/components/ui/card";
import Button from "../base/button";
import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  CustomerAccessStatus,
  IOwnedCourseCatalogItem,
  IPurchasableCourseCatalogItem,
} from "@px-shared-account/hermes";
import BadgePurchasedCourseStatus from "../purchases/badge/purchasedCourseStatus";
import Link from "next/link";
import { Skeleton } from "@/components/ui/skeleton";

interface ProductCardProps {
  course: IOwnedCourseCatalogItem | IPurchasableCourseCatalogItem;
  isInactive?: boolean;
  useBanner?: boolean;
  isNew?: boolean;
  className?: string;
  isOwned?: boolean;
}

export function ProductCardSkeleton() {
  return (
    <Card className="relative aspect-[5/4] cursor-pointer rounded-3xl border-none text-white transition sm:aspect-[9/16]">
      <Skeleton className="absolute h-full w-full rounded-3xl object-cover" />
      <Skeleton className="from-background absolute inset-0 rounded-3xl bg-gradient-to-t to-transparent" />
      <Skeleton className="relative z-10 flex h-full flex-col justify-between p-4">
        <Skeleton className="flex items-center justify-between gap-2">
          <Skeleton className="h-6 w-6 rounded-md" />
          <Skeleton className="h-6 w-6 rounded-md" />
        </Skeleton>
        <Skeleton className="flex flex-col gap-2">
          <Skeleton className="h-6 w-full rounded-md" />
          <Skeleton className="h-4 w-full rounded-md" />
        </Skeleton>
      </Skeleton>
    </Card>
  );
}

export function ProductCard({
  course,
  isInactive = false,
  useBanner = false,
  isNew = false,
  isOwned = false,
  className = "",
}: ProductCardProps) {
  const t = useTranslations();
  // Determine default image based on product line

  const newBadgeText = t("components.badge.new");

  return (
    <Card
      className={cn(
        "relative aspect-[5/4] cursor-pointer rounded-3xl border-none text-white transition sm:aspect-[9/16]",
        useBanner
          ? "aspect-[4/3] sm:aspect-[2/1]"
          : "w-full hover:-translate-y-1 hover:shadow-lg sm:w-72",
        isInactive ? "grayscale filter" : "",
        className,
      )}
    >
      <Image
        src={course.cardImage}
        alt={course.name}
        className="absolute h-full w-full rounded-3xl object-cover"
        height={418}
        width={useBanner ? 898 : 288}
        priority={useBanner}
      />

      {/* Gradient overlay for better text readability */}
      <div className="from-background absolute inset-0 rounded-3xl bg-gradient-to-t to-transparent" />
      <div className="relative z-10 flex h-full flex-col justify-between p-4">
        <div className="flex items-center justify-between gap-2">
          {isOwned && (
            <BadgePurchasedCourseStatus
              status={course.accessStatus || CustomerAccessStatus.PENDING}
            />
          )}
          {isNew && (
            <Badge className="bg-primary text-primary-foreground h-6 rounded-md px-2.5 py-0.5 text-xs font-light uppercase">
              {newBadgeText}
            </Badge>
          )}
        </div>

        <div className="flex flex-col gap-2">
          <h2 className={`font-anton text-shadow-xl mt-0 mb-2 text-3xl leading-snug`}>
            {course.name.toUpperCase()}
          </h2>
          {course.description && (
            <span className="text-shadow-xl text-sm font-light">
              {`${course.description?.slice(0, 128)}${course.description?.length > 128 ? "..." : ""}`}
            </span>
          )}

          <Link
            target="_blank"
            href={isOwned ? course.lmsUrl || "#" : course.ctaLink || "https://paradox.io"}
          >
            <Button
              variant="outline"
              className="bg-background/20 hover:bg-background/40 mt-4 drop-shadow-xl"
            >
              {isOwned
                ? t("dashboard.purchases.card.action.consume")
                : t("dashboard.purchases.card.action.purchase")}
            </Button>
          </Link>
        </div>
      </div>
    </Card>
  );
}
