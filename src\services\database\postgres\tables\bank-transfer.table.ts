import { BankTransfer } from '@entities';
import { BankTransferStatus, Currencies } from '@enums';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity({
  name: 'bankTransfers',
})
export class BankTransferTable implements BankTransfer {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar' })
  bankName: string;

  @Column({ type: 'enum', enum: Currencies })
  currency: Currencies;

  @Column({ type: 'varchar', unique: true })
  paymentId: string;

  @Column({ type: 'timestamp' })
  paymentDate: Date;

  @Column({ type: 'varchar' })
  senderEmail: string;

  @Column({ type: 'decimal' })
  paymentAmount: number;

  @Column({ type: 'varchar' })
  senderAddress: string;

  @Column({ type: 'varchar' })
  senderLastName: string;

  @Column({ type: 'varchar' })
  senderFirstName: string;

  @Column({ type: 'varchar' })
  paymentDescription: string;

  @Column({
    type: 'enum',
    enum: BankTransferStatus,
    default: BankTransferStatus.UNMATCHED,
  })
  status: BankTransferStatus;

  @Column({ type: 'varchar', nullable: true })
  entityId: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deletedAt?: Date;
}
