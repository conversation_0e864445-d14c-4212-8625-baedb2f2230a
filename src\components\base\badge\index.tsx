"use client";

import { cn } from "@/lib/utils";
import { BaseProps } from "@/types";

export interface BadgeProps extends BaseProps {
  variant?: "default" | "secondary" | "destructive" | "outline";
  size?: "default" | "sm" | "lg";
  count?: number;
  max?: number;
  dot?: boolean;
}

export const Badge = ({
  children,
  className,
  variant = "default",
  size = "default",
  count,
  max = 99,
  dot,
  ...props
}: BadgeProps) => {
  const content = count ? (count > max ? `${max}+` : count) : children;

  return (
    <span
      className={cn(
        "inline-flex items-center justify-center rounded-pill",
        // Size variants
        size === "sm" && "h-4 min-w-4 px-1 text-xs",
        size === "default" && "h-5 min-w-5 px-1.5 text-sm",
        size === "lg" && "h-6 min-w-6 px-2 text-base",
        // Color variants
        variant === "default" && "bg-primary text-primary-foreground",
        variant === "secondary" && "bg-secondary text-secondary-foreground",
        variant === "destructive" && "bg-destructive text-destructive-foreground",
        variant === "outline" && "border border-primary text-primary",
        // Dot style
        dot && "h-2 w-2 min-w-0 rounded-full p-0",
        className,
      )}
      {...props}
    >
      {!dot && content}
    </span>
  );
};

Badge.displayName = "Badge";
