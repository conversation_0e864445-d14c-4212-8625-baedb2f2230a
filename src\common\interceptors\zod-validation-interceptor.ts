import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ZodError } from 'zod';
import { ValidationError } from '@errors';

@Injectable()
export class ZodValidationInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((err) => {
        if (err instanceof ZodError) {
          return throwError(() => ValidationError.fromZodError(err));
        }

        return throwError(() => err);
      }),
    );
  }
}
