import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { StatsUseCases } from '@useCases';

@ApiTags('stats')
@Controller('stats')
export class StatsController {
  constructor(private readonly statsUseCases: StatsUseCases) {}

  @ApiOperation({
    summary: 'Get stats about the platform',
  })
  @Get('/')
  async getStats() {
    return this.statsUseCases.getStats();
  }
}
