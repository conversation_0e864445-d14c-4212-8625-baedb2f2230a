"use server";

import Redis from "ioredis";

const baseURL = process.env.NEXT_PUBLIC_CIRCLE_API_URL;
const apiKeyPXL = process.env.CIRCLE_ACCESS_TOKEN_PXL;
const apiKeyPXS = process.env.CIRCLE_ACCESS_TOKEN_PXS;

const redisClient = new Redis(process.env.KV_URL!, {
  connectTimeout: 10000,
});

/**
 * Makes a request to the Circle API
 * @param {string} url - The endpoint URL to request
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {string} community - Community identifier (PXS or PXL)
 * @param {any} [body] - Optional request body for non-GET requests
 * @returns {Promise<any>} The JSON response from the API or undefined if request failed
 */
const requestCircle = async (url: string, method: string, community: string, body?: any) => {
  const requestInit: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${community === "PXS" ? apiKeyPXS : apiKeyPXL}`,
    },
  };
  if (method !== "GET" && body) {
    requestInit.body = JSON.stringify(body);
  }
  const res = await fetch(`${baseURL}${url}`, requestInit);
  if (res.ok) {
    return res.json();
  }
  return undefined;
};

type CommunityInfo = {
  communities: any[];
  spaceGroups: any[];
  spaces: any[];
};

/**
 * Retrieves community information including communities, space groups, and spaces
 * @param {boolean} [forceUpdate] - If true, bypasses cache and fetches fresh data
 * @returns {Promise<{communities: any[], spaceGroups: any[], spaces: any[]}>} Object containing community data
 */
export const getCommunityInfo = async (forceUpdate?: boolean): Promise<CommunityInfo> => {
  if (!forceUpdate) {
    const communitiesExists = await redisClient.exists("communities");
    const spaceGroupsExists = await redisClient.exists("spaceGroups");
    const spacesExists = await redisClient.exists("spaces");

    if (communitiesExists && spaceGroupsExists && spacesExists) {
      const communities = JSON.parse((await redisClient.get("communities")) || "[]");
      const spaceGroups = JSON.parse((await redisClient.get("spaceGroups")) || "[]");
      const spaces = JSON.parse((await redisClient.get("spaces")) || "[]");
      return { communities, spaceGroups, spaces };
    }
  }
  const PXLCommunities = await requestCircle("/communities", "GET", "PXL");
  const PXLSpaceGroups = await requestCircle(
    `/space_groups?community_id=${PXLCommunities[0].id}&per_page=100`,
    "GET",
    "PXL",
  );
  const PXLSpaces = await requestCircle(
    `/spaces?community_id=${PXLCommunities[0].id}&per_page=100`,
    "GET",
    "PXL",
  );
  const PXSCommunities = await requestCircle("/communities", "GET", "PXS");
  const PXSSpaceGroups = await requestCircle(
    `/space_groups?community_id=${PXSCommunities[0].id}&per_page=100`,
    "GET",
    "PXS",
  );
  const PXSSpaces = await requestCircle(
    `/spaces?community_id=${PXSCommunities[0].id}&per_page=100`,
    "GET",
    "PXS",
  );
  let communities = [...PXLCommunities, ...PXSCommunities];
  communities = [...new Map(communities.map((c) => [c.id, c])).values()];
  let spaceGroups = [...PXLSpaceGroups, ...PXSSpaceGroups];
  spaceGroups = [...new Map(spaceGroups.map((c) => [c.id, c])).values()];
  let spaces = [...PXLSpaces, ...PXSSpaces];
  spaces = [...new Map(spaces.map((c) => [c.id, c])).values()];

  if (communities && communities.length > 0) {
    await redisClient.set("communities", JSON.stringify(communities));
  }
  if (spaceGroups && spaceGroups.length > 0) {
    await redisClient.set("spaceGroups", JSON.stringify(spaceGroups));
  }
  if (spaces && spaces.length > 0) {
    await redisClient.set("spaces", JSON.stringify(spaces));
  }
  return { communities, spaceGroups, spaces };
};
