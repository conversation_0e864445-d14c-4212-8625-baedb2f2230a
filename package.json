{"name": "@px-shared-account/hermes", "version": "1.3.1", "description": "Shared TypeScript types for the Paradox App", "type": "commonjs", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "tscBuild": "tsc --build", "prepare": "npm run build", "build": "npm run clean && npm run tscBuild"}, "publishConfig": {"access": "restricted"}, "devDependencies": {"rimraf": "^5.0.5", "typescript": "^5.5.3"}, "dependencies": {"zod": "^3.24.1"}}