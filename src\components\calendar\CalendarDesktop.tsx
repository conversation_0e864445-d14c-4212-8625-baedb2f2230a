"use client";

import useCalendarStore from "@/hooks/store/calendar";
import { useSelectedDate } from "@/hooks/calendar";
import LayoutDayWeek from "./layouts/LayoutDayWeek";
import LayoutMonth from "./layouts/LayoutMonth";

export default function CalendarDesktop() {
  const { layout } = useCalendarStore();
  const { selectedWeek, selectedWeekIndex, weeks } = useSelectedDate();

  return (
    <>
      {layout === "month" && <LayoutMonth weeks={weeks} />}
      {(layout === "day" || layout === "week") && (
        <LayoutDayWeek selectedWeekIndex={selectedWeekIndex} selectedWeek={selectedWeek} />
      )}
    </>
  );
}
