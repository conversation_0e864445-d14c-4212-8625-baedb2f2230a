import serverFetcher from "@/lib/server/server-fetcher";
import { TRIAD_API_ENDPOINTS, ListTriadsParams, TriadApiTypes, TriadApiPayloads } from "./core";

// Placeholder for server-side token getter if needed
const getServerToken = async () => null;
const apiFetcher = serverFetcher(getServerToken);

export async function getTriadList(params: ListTriadsParams = {}): Promise<TriadApiTypes["list"]> {
  const { url } = TRIAD_API_ENDPOINTS.list(params);
  return apiFetcher(url);
}

export async function getTriadById(id: number): Promise<TriadApiTypes["getById"]> {
  const { url } = TRIAD_API_ENDPOINTS.getById(id);
  return apiFetcher(url);
}

export async function createTriad(
  data: TriadApiPayloads["create"],
): Promise<TriadApiTypes["create"]> {
  const { url, method } = TRIAD_API_ENDPOINTS.create();
  return apiFetcher(url, { method, body: data });
}

export async function updateTriad(
  id: number,
  data: TriadApiPayloads["update"],
): Promise<TriadApiTypes["update"]> {
  const { url, method } = TRIAD_API_ENDPOINTS.update(id);
  return apiFetcher(url, { method, body: data });
}

export async function joinTriad(data: TriadApiPayloads["join"]): Promise<TriadApiTypes["join"]> {
  const { url, method } = TRIAD_API_ENDPOINTS.join();
  return apiFetcher(url, { method, body: data });
}

export async function leaveTriad(data: TriadApiPayloads["leave"]): Promise<TriadApiTypes["leave"]> {
  const { url, method } = TRIAD_API_ENDPOINTS.leave();
  return apiFetcher(url, { method, body: data });
}

export async function getUpcomingTriad(): Promise<TriadApiTypes["getUpcoming"]> {
  const { url, method } = TRIAD_API_ENDPOINTS.getUpcoming();
  return apiFetcher(url, { method });
}

export async function getUserStats(): Promise<TriadApiTypes["getUserStats"]> {
  const { url, method } = TRIAD_API_ENDPOINTS.getUserStats();
  return apiFetcher(url, { method });
}
