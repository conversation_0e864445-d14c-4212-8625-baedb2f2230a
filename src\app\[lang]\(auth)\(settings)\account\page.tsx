import { <PERSON><PERSON><PERSON><PERSON>, Shield } from "lucide-react";
import { useTranslations } from "next-intl";
import { getTranslations } from "next-intl/server";

import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";
import { UpdateAccountInfoForm } from "@/components/(settings)/account/UpdateAccountInfoForm";

export default function Page() {
  const t = useTranslations("profile");

  return (
    <div className="flex flex-col gap-4 px-4 pt-6">
      <BreadcrumbSettings active="/account" />
      <h1 className="text-2xl font-bold">{t("settings.account.title")}</h1>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div className="col-span-2">
          <UpdateAccountInfoForm />
        </div>
        <div className="flex flex-col gap-4">
          <div className="flex flex-col">
            <Shield />
            <p className="my-2 text-lg font-semibold">{t("settings.account.info.one.title")}</p>
            <p className="text-sm text-gray-500">{t("settings.account.info.one.description")}</p>
          </div>
          <div className="flex flex-col">
            <CircleAlert />
            <p className="my-2 text-lg font-semibold">{t("settings.account.info.two.title")}</p>
            <p className="text-sm text-gray-500">{t("settings.account.info.two.description")}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "profile" });

  return {
    title: t("settings.account.title"),
    description: t("settings.account.sub-title"),
  };
}
