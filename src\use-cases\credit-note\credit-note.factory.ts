import { Injectable } from '@nestjs/common';
import { CreditNote as ChargeBeeCreditNote } from 'chargebee-typescript/lib/resources';
import { LineItem } from 'chargebee-typescript/lib/resources/credit_note_estimate';
import { IDataServices } from '@abstracts';
import { CreditNote } from '@entities';
import {
  CreditNoteStatus,
  CreditNoteType,
  Currencies,
  InvoicePriceType,
} from '@enums';

@Injectable()
export class CreditNoteFactoryService {
  /**
   * Generates a new credit note based on credit note data coming from ChargeBee Webhook Notification
   * @param chargebeeCreditNote Credit note data from ChargeBee
   * @param paymentId ID of the `payment` related to this `refund`
   */
  async generateFromChargebeeEvent(
    chargebeeCreditNote: ChargeBeeCreditNote,
  ): Promise<CreditNote> {
    const creditNote = new CreditNote();
    creditNote.chargebeeId = chargebeeCreditNote.id;
    creditNote.allocations = chargebeeCreditNote.allocations;
    creditNote.total = chargebeeCreditNote.total;
    creditNote.amountAllocated = chargebeeCreditNote.amount_allocated;
    creditNote.amountRefunded = chargebeeCreditNote.amount_refunded;
    creditNote.amountAvailable = chargebeeCreditNote.amount_available;
    creditNote.businessEntityId = chargebeeCreditNote.business_entity_id;
    creditNote.channel = chargebeeCreditNote.channel;
    creditNote.currencyCode =
      Currencies[chargebeeCreditNote.currency_code.toUpperCase()];
    creditNote.customerId = chargebeeCreditNote.customer_id;
    creditNote.customerNotes = chargebeeCreditNote?.['customer_notes'];
    creditNote.exchangeRate = chargebeeCreditNote?.['exchange_rate'];
    creditNote.status =
      CreditNoteStatus[chargebeeCreditNote.status.toUpperCase()];
    creditNote.type = CreditNoteType[chargebeeCreditNote.type.toUpperCase()];
    creditNote.createReason = chargebeeCreditNote.create_reason_code;
    creditNote.priceType =
      InvoicePriceType[chargebeeCreditNote.price_type.toUpperCase()];
    creditNote.billingAddress = chargebeeCreditNote.billing_address;
    creditNote.shippingAddress = chargebeeCreditNote.shipping_address || null;
    creditNote.referenceInvoiceId = chargebeeCreditNote.reference_invoice_id;
    creditNote.subscriptionId = chargebeeCreditNote.subscription_id;
    creditNote.lineItems = chargebeeCreditNote.line_items;
    creditNote.linkedRefunds = chargebeeCreditNote.linked_refunds;
    creditNote.date = chargebeeCreditNote.date;
    const refundedAt = chargebeeCreditNote.refunded_at;
    if (refundedAt) {
      creditNote.refundedAt = chargebeeCreditNote.refunded_at;
    }
    creditNote.generatedAt = chargebeeCreditNote.generated_at;
    return creditNote;
  }

  /**
   * Adds product name and image to line items
   * @param dataServices Data services
   * @param lineItems Line items
   * @returns Line items with product name and image
   */
  async addImageAndDescriptionToLineItems(
    dataServices: IDataServices,
    lineItems: LineItem[],
  ) {
    for (const lineItem of lineItems) {
      const lineItemId = lineItem.entity_id;
      const productInfo = await dataServices.productPlanPrice
        .getRepository()
        .createQueryBuilder('productPlanPrice')
        .leftJoinAndSelect('productPlanPrice.productPlan', 'productPlan')
        .leftJoinAndSelect('productPlan.product', 'product')
        .where('productPlanPrice.chargebeeId = :lineItemId', { lineItemId })
        .getOne();
      if (productInfo) {
        lineItem['name'] = productInfo?.productPlan?.product?.externalName;
        lineItem['image'] = productInfo?.productPlan?.product?.image;
        lineItem.description = productInfo?.productPlan?.product?.description;
      }
    }
    return lineItems;
  }
}
