import { UserWebhookEvent } from '@clerk/backend';
import { CreateBankTransferDTO } from '@dtos';
import {
  AccessTarget,
  CommunityType,
  CustomerAccessType,
  DiscountType,
} from '@enums';
import { ChargeBeeWebhookPayload } from './chargebee.type';
import { CustomerAccessStatus } from '@px-shared-account/hermes';

/**
 * HTTP request methods used by helper method `makeAxiosRequest`
 */
export enum RequestMethods {
  POST = 'post',
  GET = 'get',
  DELETE = 'delete',
  PUT = 'put',
}

/**
 * Update result type for database that also
 * includes the updated item.
 */
export interface UpdateResultWithItemInfo<T> {
  /**
   * Defines if the item was updated or not
   */
  success: boolean;

  /**
   * Updated item
   */
  updatedItem: T;
}

export interface ChargebeeCustomer {
  id: string;
  lastName: string;
  firstName: string;
  email: string;
  phone: string;
  country?: string;
  city?: string;
  address?: string;
  zip?: string;
  wasFoundInChargeBee: boolean;
  businessEntityId?: string;
  session_edec?: string | null;
}

export type CouponApplicabilityInfo = {
  couponApplicable: boolean;
  couponType?: DiscountType;
  couponAmount?: number;
  couponPercentageAmount?: number;
};

export type ExchangeRates = {
  USD: string;
  EUR: string;
  GBP: string;
  CAD: string;
  CHF: string;
};

export type ExchangeRateLastUpdated = {
  date: string;
  year: string;
  time: string;
};

export type SpaceInfo = {
  id: number;
  slug: string;
  name: string;
};

export type CourseInfo = {
  id: string;
  name: string;
};

export type CommunityInfo = {
  community: CommunityType;
  spaces: SpaceInfo[];
};

export type BaseAccess = {
  target: AccessTarget;
  name: string;
  link: string;
};

export type CourseAccess = BaseAccess & {
  id: number;
  slug: string;
  bannerImage: string;
  cardImage: string;
  thumbnail: string;
  status: CustomerAccessStatus;
};

export type CommunityAccess = BaseAccess & {
  accessId: number;
  domain: CommunityType;
};

export type SpaceAccess = BaseAccess & {
  domain: CommunityType;
  spaceId: number;
  slug: string;
};

export type MediaAccess = BaseAccess;

export type AccessDetails =
  | CourseAccess
  | CommunityAccess
  | SpaceAccess
  | MediaAccess;

export type LMSAccesses = {
  products: CourseInfo[];
  recommended: CourseInfo[];
};

export type CommunityAccesses = {
  products: CommunityInfo[];
  recommended: CommunityInfo[];
};

export type PXActionResult = {
  data?: any;
  success: boolean;
  message?: string;
};

export type SuspensionResult = {
  suspended: boolean;
  accessDetails: AccessDetails;
  serviceResponse: PXActionResult;
  customerName: string;
  customerEmail: string;
};

export type WebhookSender = 'chargebee' | 'clerk' | 'bankTransfer';
export type WebhookPayload = {
  id: string;
  chargebee?: ChargeBeeWebhookPayload;
  clerk?: UserWebhookEvent;
  bankTransfer?: CreateBankTransferDTO;
};
export type WebhookOutcome = {
  message: string;
  affectedEntity: string;
  affectedEntityId: string;
};

export type SubscriptionAccesses = {
  [key: string]: {
    offerName: string;
    communityAccesses: CommunityAccess[];
    lmsAccesses: CourseAccess[];
  };
};

export type AccessInfoWithCustomerInfo = {
  id: number;
  status: CustomerAccessStatus;
  type: CustomerAccessType;
  details: AccessDetails;
  email: string;
  customerName: string;
};

// --- User Access Details Endpoint Types ---

/**
 * Represents an LMS Course entry.
 * Corresponds to CourseInfo but aliased for spec clarity.
 */
export type LmsCourse = CourseInfo;

/**
 * Represents LMS entries associated with an offer.
 * Corresponds to LMSAccesses but aliased for spec clarity.
 */
export type LmsEntries = LMSAccesses;

/**
 * Represents the core access flags for a user.
 */
export interface AccessInfo {
  isPXS: boolean;
  hubspotSession: string | null;
  isPXL: boolean;
  isParadox: boolean;
  hasBothAccess: boolean;
  isPOM: boolean;
}

/**
 * Represents core data from a Chargebee Subscription object (as provided by the view).
 */
export interface SubscriptionData {
  id: string;
  status: string;
  cfIsForever?: boolean | undefined;
  nextBillingAt?: number | undefined;
  cancelledAt?: number | undefined;
  cancelScheduleCreatedAt?: number | undefined;
  mrr?: number | undefined;
  metaData?: Record<string, any> | undefined;
  lastPaymentDate?: number | undefined; // Unix timestamp of the last successful payment
}

/**
 * Represents core data from a Chargebee ItemPrice object (Plan/Addon) (as provided by the view).
 */
export interface PlanData {
  id: string;
  externalName?: string | undefined;
  itemId: string; // Chargebee Item ID
  name: string; // Internal PlanPrice Name
}

/**
 * Represents core data from a Chargebee Item object (as provided by the view).
 */
export interface ItemData {
  id: string; // Chargebee Item ID
  cfProductLine?: string | undefined; // Derived from ProductLine.chargebeeId
  name: string; // Derived from ProductPlan.externalName
}

/**
 * Represents community entries associated with an offer.
 * Similar structure to LMSAccesses but for community spaces.
 */
export interface CommunityEntries {
  products?: Array<{
    spaces?: Array<{
      id: number;
      name: string;
      slug: string;
    }>;
    community: string;
  }>;
  recommended?: Array<{
    spaces?: Array<{
      id: number;
      name: string;
      slug: string;
    }>;
    community: string;
  }>;
}

/**
 * Represents the enriched Offer/Purchase details (as provided by the view).
 */
export interface OfferData {
  orderStatus: string;
  bannerImage?: string | null | undefined;
  cardImage?: string | null | undefined;
  chargebeeId?: string | null | undefined; // Offer's own Chargebee ID
  description?: string | null | undefined;
  lmsIds?: LmsEntries | null | undefined;
  name?: string | null | undefined;
  usp?: string | null | undefined;
  communityIds?: CommunityEntries | null | undefined; // Community IDs from the offer
}

/**
 * Represents a single denormalized purchase record for the user.
 */
export interface PurchaseRecord {
  subscription: SubscriptionData;
  plan: PlanData;
  item: ItemData;
  offer: OfferData;
}

/**
 * Represents the complete response structure from the /customers/access-details endpoint.
 */
export interface UserAccessDetailsResponse {
  email: string;
  accessInfo: AccessInfo;
  purchases: PurchaseRecord[];
}

// --- End User Access Details Endpoint Types ---

export interface PlatformTriadStats {
  overview: {
    totalTriads: number;
    totalParticipants: number;
    averageParticipantsPerTriad: number;
    completionRate: number;
  };
  timeSeriesData: {
    triadsCreated: Array<{ date: string; value: number }>;
    participantsJoined: Array<{ date: string; value: number }>;
  };
  engagement: {
    recurringParticipants: number;
    sessionDurationDistribution: Array<{ duration: number; count: number }>;
  };
  organizers: {
    topOrganizers: Array<{
      id: string;
      name: string;
      triadsCount: number;
    }>;
  };
}

/**
 * Represents a file uploaded using Multer.
 */
export interface UploadedMulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

export interface PaymentSourceInfo {
  type: 'card' | 'bank_transfer';
  card?: {
    brand: string;
    maskedNumber: string;
  };
}

export interface SubscriptionInvoiceDetails {
  subscriptionId: number;
  chargebeeId: string;
  startedAt: Date;
  nextBillingAt: Date;
  amountPaid: number;
  totalAmount: number;
  amountRemaining: number;
  paymentSource?: any;
  invoices: {
    id: number;
    date: Date;
    status: string;
  }[];
}