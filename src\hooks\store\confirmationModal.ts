import { create } from "zustand";

type ModalType = "success" | "error" | "warning" | "info";

interface ModalData {
  title: string;
  description: string;
  type?: ModalType;
  confirmText?: string;
  cancelText?: string;
}

interface ConfirmationModalStore {
  isOpen: boolean;
  loading: boolean;
  data: ModalData | null;
  onConfirm?: () => Promise<void> | void;
  onCancel?: () => Promise<void> | void;
  setLoading: (loading: boolean) => void;
  openModal: (
    data: ModalData,
    onConfirm?: () => Promise<void> | void,
    onCancel?: () => Promise<void> | void,
  ) => void;
  closeModal: () => void;
}

export const useConfirmationModal = create<ConfirmationModalStore>((set) => ({
  isOpen: false,
  loading: false,
  data: null,
  onConfirm: undefined,
  onCancel: undefined,
  setLoading: (loading) => set({ loading }),
  openModal: (data, onConfirm, onCancel) =>
    set({
      isOpen: true,
      data,
      onConfirm,
      onCancel,
    }),
  closeModal: () =>
    set({
      isOpen: false,
      data: null,
      onConfirm: undefined,
      onCancel: undefined,
      loading: false,
    }),
}));
