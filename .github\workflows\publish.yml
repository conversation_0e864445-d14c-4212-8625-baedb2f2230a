name: Publish Package to NPM
on:
  workflow_dispatch:
    inputs:
      release-type:
        description: "Release type (patch, minor, major, prepatch, preminor, premajor, prerelease)"
        required: true
        type: choice
        options:
          - patch
          - minor
          - major
          - prepatch
          - preminor
          - premajor
          - prerelease

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          registry-url: "https://registry.npmjs.org"

      - name: Git config
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>

      - name: Install dependencies
        run: npm install

      - name: Version bump
        run: |
          npm --no-git-tag-version version ${{ inputs.release-type }} -m "Release %s"

      - name: Get version
        id: get_version
        run: echo "version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT

      - name: Build
        run: npm run build

      - name: Push changes
        run: |
          git push
          git push --tags

      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
        with:
          tag_name: v${{ steps.get_version.outputs.version }}
          release_name: Release v${{ steps.get_version.outputs.version }}
          draft: false
          prerelease: false

      - name: Publish to NPM
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Commit version bump
        run: |
          git add package.json
          git commit -m "Bump version to v${{ steps.get_version.outputs.version }}"
          git push
