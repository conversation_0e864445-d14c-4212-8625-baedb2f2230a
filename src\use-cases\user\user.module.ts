import { Module } from '@nestjs/common';
import { UserUseCases } from './user.use-cases';
import { DataServicesModule } from '@services/database/';
import { UserFactory } from './user.factory';
import { RoleModule } from '../role';
import { ClerkModule } from '@services/sso';
import { HubspotModule } from '@services/crm';
import { ChargebeeBillingModule } from '@services/billing';
import { GoliathsModule } from '../goliaths';

@Module({
  imports: [
    DataServicesModule,
    RoleModule,
    ClerkModule,
    HubspotModule,
    ChargebeeBillingModule,
    GoliathsModule,
  ],
  providers: [UserUseCases, UserFactory],
  exports: [UserUseCases],
})
export class UserModule {}
