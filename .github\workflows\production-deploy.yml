name: Deploy to Production EC2

on:
  push:
    branches:
      - main

jobs:
  production-deploy:
    name: Deploy to EC2 Production
    runs-on: ubuntu-latest
    outputs:
      duration: ${{ steps.duration.outputs.total_time }}

    steps:
      - name: Start timer
        id: start_time
        run: echo "start=$(date +%s)" >> $GITHUB_ENV

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Load required envs from Infisical into $GITHUB_ENV
        uses: Infisical/secrets-action@v1.0.11
        with:
          client-id: ${{ secrets.INFISICAL_MACHINE_IDENTITY_CLIENT_ID }}
          client-secret: ${{ secrets.INFISICAL_MACHINE_IDENTITY_CLIENT_SECRET }}
          domain: ${{ secrets.INFISICAL_DOMAIN }}
          env-slug: "prod"
          project-slug: "vega-2-dt-t"
          secret-path: "/px_ci"
          export-type: "env"

      - name: Set up Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: 20.8.1

      - name: Install `pnpm`
        run: npm install -g pnpm

      - name: Export `.npmrc` file
        run: echo "//registry.npmjs.org/:_authToken=${{ env.NPM_TOKEN }}" > .npmrc

      - name: Set up Semantic Release
        run: pnpm add semantic-release @semantic-release/commit-analyzer @semantic-release/git @semantic-release/github semantic-release/npm @semantic-release/release-notes-generator @semantic-release/changelog

      - name: Semantic Release
        id: semantic_release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npx semantic-release 2>&1 | tee error.log

      - name: Get current Git tag (release version)
        id: get_version
        run: echo "RELEASE_VERSION=$(git describe --tags --abbrev=0)" >> "$GITHUB_ENV"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        id: docker_build
        uses: docker/build-push-action@v6.16.0
        with:
          context: .
          push: false
          tags: ${{ env.IMAGE_NAME }}-${{ env.RELEASE_VERSION }}
          load: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          secret-files: |
            npmrc=${{ github.workspace }}/.npmrc

      - name: Save Docker image as TAR archive
        id: save_image
        run: docker save -o ${{ env.IMAGE_NAME }}-${{ env.RELEASE_VERSION }}.tar ${{ env.IMAGE_NAME }}-${{ env.RELEASE_VERSION }} 2>&1 | tee -a deployment-errors.log

      - name: Copy Docker image to EC2 instance
        id: scp_image
        uses: appleboy/scp-action@v1.0.0
        with:
          host: ${{ env.EC2_INSTANCE_IP }}
          username: ${{ env.EC2_USERNAME }}
          key: ${{ env.EC2_SSH_KEY }}
          passphrase: ${{ env.EC2_SSH_PASSPHRASE }}
          source: ${{ env.IMAGE_NAME }}-${{ env.RELEASE_VERSION }}.tar
          target: /home/<USER>/docker-repository/
          debug: true

      - name: SSH into EC2 instance and load Docker image
        id: ssh_deploy
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ env.EC2_INSTANCE_IP }}
          username: ${{ env.EC2_USERNAME }}
          key: ${{ env.EC2_SSH_KEY }}
          passphrase: ${{ env.EC2_SSH_PASSPHRASE }}
          script: |
            (
              docker stop ${{ env.CONTAINER_NAME }} || true
              docker rm ${{ env.CONTAINER_NAME }} || true
              # docker rmi -f ${{ env.IMAGE_NAME }}-${{ env.RELEASE_VERSION }} || true
              docker load -i /home/<USER>/docker-repository/${{ env.IMAGE_NAME }}-${{ env.RELEASE_VERSION }}.tar
              docker run --rm ${{ env.IMAGE_NAME }}-${{ env.RELEASE_VERSION }} ls -la /usr/src/app/dist
              docker run -d --name ${{ env.CONTAINER_NAME }} \
                --network ${{ env.DOCKER_NETWORK }} \
                --ip ${{ env.CONTAINER_IP }} \
                -e INFISICAL_MACHINE_IDENTITY_CLIENT_ID=${{ secrets.INFISICAL_MACHINE_IDENTITY_CLIENT_ID }} \
                -e INFISICAL_MACHINE_IDENTITY_CLIENT_SECRET=${{ secrets.INFISICAL_MACHINE_IDENTITY_CLIENT_SECRET }} \
                -e INFISICAL_ENVIRONMENT=${{ env.INFISICAL_ENVIRONMENT }} \
                -e NODE_ENV=${{ env.NODE_ENV }} \
                -e INFISICAL_PROJECT_ID=${{ env.INFISICAL_PROJECT_ID }} \
                -p ${{env.CONTAINER_PORT}}:${{env.CONTAINER_PORT}} ${{ env.IMAGE_NAME }}-${{ env.RELEASE_VERSION }}
              docker update --restart=always ${{ env.CONTAINER_NAME }}
            ) 2>&1 | tee /tmp/ssh_output.log
            
            # Check if there were any errors in the deployment
            if grep -iE "error|fail|fatal" /tmp/ssh_output.log; then
              echo "Errors found in deployment"
              exit 1
            fi
          debug: true

      - name: Capture Docker build errors
        if: failure() && (steps.docker_build.outcome == 'failure' || steps.save_image.outcome == 'failure')
        run: |
          echo "FAILURE_SOURCE=Docker build" >> $GITHUB_ENV
          echo "FAILURE_REASON=Error occurred during Docker image build or save process." >> $GITHUB_ENV

      - name: Capture SCP errors
        if: failure() && steps.scp_image.outcome == 'failure'
        run: |
          echo "FAILURE_SOURCE=SCP transfer" >> $GITHUB_ENV
          echo "FAILURE_REASON=Failed to copy Docker image to EC2 instance." >> $GITHUB_ENV

      - name: Capture SSH deployment errors
        if: failure() && steps.ssh_deploy.outcome == 'failure'
        run: |
          echo "FAILURE_SOURCE=SSH deployment" >> $GITHUB_ENV
          echo "FAILURE_REASON=Failed to deploy Docker container on EC2 instance." >> $GITHUB_ENV

      - name: Extract semantic release error message (if failure)
        if: failure() && steps.semantic_release.outcome == 'failure'
        run: |
          ERROR_LINE=$(tail -n 20 error.log | grep -iE 'error|fail|fatal' | head -n 1)
          if [ -z "$ERROR_LINE" ]; then
            ERROR_LINE="Unknown error occurred during semantic release."
          fi
          ERROR_LINE_TRUNCATED=$(echo "$ERROR_LINE" | cut -c1-100)
          ERROR_LINE_ESCAPED=$(echo "$ERROR_LINE_TRUNCATED" | sed 's/\"/\\\"/g')
          echo "FAILURE_SOURCE=Semantic Release" >> $GITHUB_ENV
          echo "FAILURE_REASON=$ERROR_LINE_ESCAPED" >> $GITHUB_ENV

      - name: Set default error message if no specific error captured
        if: failure() && env.FAILURE_REASON == ''
        run: |
          echo "FAILURE_SOURCE=Unknown step" >> $GITHUB_ENV
          echo "FAILURE_REASON=An unknown error occurred during the deployment process." >> $GITHUB_ENV

      - name: End timer and calculate duration
        id: duration
        run: |
          end=$(date +%s)
          total=$((end - $start))
          mins=$((total / 60))
          secs=$((total % 60))
          echo "total_time=${mins}m ${secs}s" >> "$GITHUB_OUTPUT"
          echo "TOTAL_DURATION=${mins}m ${secs}s" >> $GITHUB_ENV

      - name: Set error block JSON
        if: failure() && env.FAILURE_REASON != ''
        run: |
          ERROR_BLOCK=$(cat << EOF
          ,{
            "type": "section",
            "text": {
              "type": "mrkdwn",
              "text": "*💥 Error in ${{ env.FAILURE_SOURCE }}:*\n\`\`\`${{ env.FAILURE_REASON }}\`\`\`"
            }
          }
          EOF
          )
          echo "ERROR_BLOCK<<EOF" >> $GITHUB_ENV
          echo "$ERROR_BLOCK" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Send Slack notification
        if: always()
        uses: slackapi/slack-github-action@v2.1.0
        with:
          webhook: ${{ env.SLACK_WEBHOOK_URL }}
          webhook-type: incoming-webhook
          payload: |
            {
              "text": "*Deployment to Production*",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "${{ job.status == 'success' && '✅ Deployment Successful!' || '🚨 Deployment Failed!' }}"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Status:* ${{ job.status == 'success' && '🟢 Success' || '🔴 Failure' }}\n*Duration:* ${{ env.TOTAL_DURATION }}\n*Version:* `${{ env.RELEASE_VERSION }}`\n*Commit:* <${{ github.event.pull_request.html_url || github.event.head_commit.url }}|View Commit>"
                  }
                }${{ env.ERROR_BLOCK || '' }}
              ]
            }