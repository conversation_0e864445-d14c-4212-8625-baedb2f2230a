// Type definition for impersonation data
export interface ImpersonationData {
  isImpersonating: boolean;
  impersonatedEmail: string | null;
  originalEmail: string | null;
}

// Single cookie name for all impersonation data
export const IMPERSONATION_COOKIE = "impersonation";

// Cookie expiration time (24 hours in seconds)
const COOKIE_EXPIRATION = 86400;

// Client-side utilities for impersonation
export const clientImpersonation = {
  // Get impersonation data from cookie
  getData: (): ImpersonationData | null => {
    if (typeof document === "undefined") return null;

    try {
      const cookies = document.cookie.split(";");
      const cookieStr = cookies.find((c) => c.trim().startsWith(`${IMPERSONATION_COOKIE}=`));
      if (!cookieStr) return null;

      const cookieValue = cookieStr.split("=")[1];
      if (!cookieValue) return null;

      const data = JSON.parse(decodeURIComponent(cookieValue));
      return data;
    } catch (error) {
      console.error("Error reading impersonation cookie:", error);
      return null;
    }
  },

  // Set impersonation data to cookie
  setData: (data: ImpersonationData): void => {
    if (typeof document === "undefined") return;

    try {
      const encodedValue = encodeURIComponent(JSON.stringify(data));
      const isSecure = window.location.protocol === "https:";
      document.cookie = `${IMPERSONATION_COOKIE}=${encodedValue};path=/;max-age=${COOKIE_EXPIRATION};SameSite=Lax${isSecure ? ";Secure" : ""}`;
    } catch (error) {
      console.error("Error setting impersonation cookie:", error);
    }
  },

  // Start impersonation
  startImpersonation: (impersonatedEmail: string, originalEmail: string): void => {
    clientImpersonation.setData({
      isImpersonating: true,
      impersonatedEmail,
      originalEmail,
    });
  },

  // Clear impersonation
  clearImpersonation: (): void => {
    if (typeof document === "undefined") return;

    try {
      document.cookie = `${IMPERSONATION_COOKIE}=;path=/;max-age=0;SameSite=Lax`;
    } catch (error) {
      console.error("Error clearing impersonation cookie:", error);
    }
  },

  // Check if impersonation is active
  isActive: (): boolean => {
    const data = clientImpersonation.getData();
    return !!data?.isImpersonating;
  },

  // Get impersonated email
  getImpersonatedEmail: (): string | null => {
    const data = clientImpersonation.getData();
    return data?.impersonatedEmail || null;
  },

  // Get original (admin) email
  getOriginalEmail: (): string | null => {
    const data = clientImpersonation.getData();
    return data?.originalEmail || null;
  },
};

// Server-side utilities for impersonation
export function getImpersonationFromRequestCookie(
  cookieHeader: string | null,
): ImpersonationData | null {
  if (!cookieHeader) return null;

  try {
    const cookies = cookieHeader.split(";");
    const cookie = cookies.find((c) => c.trim().startsWith(`${IMPERSONATION_COOKIE}=`));

    if (!cookie) return null;

    const cookieValue = cookie.split("=")[1];
    if (!cookieValue) return null;

    return JSON.parse(decodeURIComponent(cookieValue));
  } catch (error) {
    console.error("Error parsing impersonation cookie:", error);
    return null;
  }
}

// For server components using next/headers
export function getImpersonationFromNextCookie(cookieStore: {
  get: (name: string) => { value: string } | undefined;
}): ImpersonationData | null {
  try {
    const cookie = cookieStore.get(IMPERSONATION_COOKIE);
    if (!cookie?.value) return null;

    return JSON.parse(decodeURIComponent(cookie.value));
  } catch (error) {
    console.error("Error parsing impersonation cookie:", error);
    return null;
  }
}

// Utility to create a response header to set impersonation cookie
export function createImpersonationCookieHeader(data: ImpersonationData | null): string {
  if (!data) {
    return `${IMPERSONATION_COOKIE}=;path=/;max-age=0;SameSite=Lax`;
  }

  const encodedValue = encodeURIComponent(JSON.stringify(data));
  const isSecure = process.env.NODE_ENV === "production";
  return `${IMPERSONATION_COOKIE}=${encodedValue};path=/;max-age=${COOKIE_EXPIRATION};SameSite=Lax${isSecure ? ";Secure" : ""}`;
}
