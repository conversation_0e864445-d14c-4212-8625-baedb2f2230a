"use server";

import { clerkClient } from "@clerk/nextjs/server";

type SignupGoliathReq = {
  phone: string;
  email: string;
  uuid: string;
  hasPom: boolean;
};

/**
 * Creates a new phone number for a user
 * @param phone - The user's phone number
 * @param userId - The user's UUID
 * @returns {Promise<any>} The JSON response from the API
 */
async function createPhoneNumber(phone: string, userId: string) {
  const clerk = await clerkClient();
  return clerk.phoneNumbers.createPhoneNumber({
    userId,
    phoneNumber: phone,
    primary: true,
    verified: true,
  });
}

export async function signupGoliath(req: SignupGoliathReq) {
  const clerk = await clerkClient();

  try {
    const user = await clerk.users.getUser(req.uuid);

    // check if the phone number is already in the user's phone numbers
    if (!user.phoneNumbers.some((phone) => phone.phoneNumber === req.phone)) {
      try {
        if (!user.primaryPhoneNumberId) {
          await createPhoneNumber(req.phone, user.id);
        } else {
          // Delete the phone number
          await clerk.phoneNumbers.deletePhoneNumber(user.primaryPhoneNumberId);

          // Create a new phone number
          await createPhoneNumber(req.phone, user.id);
        }
      } catch (error: any) {
        // we pass for now to bypass the error, we will handle it later
      }
    }

    const response = await fetch("https://share.goliaths.io/aws-api/signup/partner", {
      method: "POST",
      headers: {
        authorization: "Bearer GoliathsFrontKeyAlban",
        "content-type": "application/json",
      },
      body: JSON.stringify({
        email: req.email,
        phone: req.phone.replaceAll("+", ""),
        uid: req.uuid,
        pomCustomer: req.hasPom,
        referral_code: "PARADOX",
      }),
    });

    return response.json();
  } catch (error: any) {
    console.error(error);
    for (const singleError of error.errors) {
      if (singleError.code === "too_many_requests") {
        return {
          error: "too_many_requests",
        };
      } else if (singleError.code === "form_identifier_exists") {
        if (singleError.message?.toLowerCase().includes("phone number")) {
          return {
            error: "phone_number_already_exists",
          };
        } else if (singleError.message?.toLowerCase().includes("email")) {
          return {
            error: "email_already_exists",
          };
        } else {
          return {
            error: singleError.message,
          };
        }
      }
    }
    return { error: "Failed to sign up to Goliaths" };
  }
}
