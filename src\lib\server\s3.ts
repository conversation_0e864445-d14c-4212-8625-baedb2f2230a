"use server";

import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl as s3GetSignedUrl } from "@aws-sdk/s3-request-presigner";

const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

const createCommand = ({
  filename,
  image,
  contentType,
}: {
  filename: string;
  image: string;
  contentType: any;
}) => {
  const command = {
    Bucket: process.env.AWS_BUCKET_NAME as string,
    Key: process.env.AWS_FOLDER_NAME + "/" + filename,
    Body: image,
    ContentType: contentType,
  };
  return command;
};

export const uploadFile = async (command: { filename: string; image: any; contentType: any }) => {
  try {
    const buffer = Buffer.from(command.image.replace(/^data:image\/\w+;base64,/, ""), "base64");
    command.image = buffer;
    const cmd = new PutObjectCommand(createCommand({ ...command }));
    const response = await s3Client.send(cmd);
    console.log("#uploadFile", { response });
    return response;
  } catch (error) {
    console.error("cannot upload file", error);
  }
};

export const uploadToS3 = async (file: File, key: string): Promise<string> => {
  const command = new PutObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME!,
    Key: key,
    Body: file,
    ContentType: file.type,
  });

  try {
    await s3Client.send(command);
    return `${process.env.AWS_CLOUDFRONT_URL}/${key}`;
  } catch (error) {
    console.error("Error uploading to S3:", error);
    throw error;
  }
};

export const getSignedUrl = async (key: string): Promise<string> => {
  const command = new GetObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME!,
    Key: key,
  });

  try {
    const signedUrl: string = await s3GetSignedUrl(s3Client, command, { expiresIn: 3600 });
    return signedUrl;
  } catch (error) {
    console.error("Error getting signed URL:", error);
    throw error;
  }
};
