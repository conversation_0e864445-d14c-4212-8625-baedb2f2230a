import { Injectable } from '@nestjs/common';
import { WhatsNewFactory } from './whats-new.factory';
import {
    IWhatsNewListResponse,
    IWhatsNewVersion,
    IReactionsResponse,
    IToggleReactionResponse,
    IUploadResponse,
    ICreateWhatsNewPayload,
    IUpdateWhatsNewPayload,
    IToggleReactionPayload,
    ALLOWED_EMOJIS,
    IMediaListResponse,
    IMediaDeleteResponse,
    IMediaItem,
} from '@px-shared-account/hermes';
import { IDataServices } from '@abstracts';
import { ConfigService } from '@config';
import { put, list, del } from '@vercel/blob';

@Injectable()
export class WhatsNewUseCases {
    constructor(
        private readonly databaseService: IDataServices,
        private readonly factory: WhatsNewFactory,
        private readonly configService: ConfigService,
    ) { }

    /**
     * List all What's New versions
     * @returns List of all versions ordered by date descending
     */
    async list(): Promise<IWhatsNewListResponse> {
        const versions = await this.databaseService.whatsNewVersion.getAll(
            undefined,
            undefined,
            { date: 'DESC' },
        );

        return {
            versions: versions.items.map(this.factory.toApiVersion),
        };
    }

    /**
     * Get a specific What's New version by ID
     * @param id Version ID
     * @returns The version or null if not found
     */
    async getById(id: number): Promise<IWhatsNewVersion | null> {
        const version = await this.databaseService.whatsNewVersion.getOneBy({
            id,
        });

        if (!version) {
            return null;
        }

        return this.factory.toApiVersion(version);
    }

    /**
     * Create a new What's New version
     * @param payload Version data
     * @returns Created version
     */
    async create(payload: ICreateWhatsNewPayload): Promise<IWhatsNewVersion> {
        const versionEntity = this.factory.generateFromPayload(payload);
        const createdVersion = await this.databaseService.whatsNewVersion.create(versionEntity);

        return this.factory.toApiVersion(createdVersion);
    }

    /**
     * Update an existing What's New version
     * @param id Version ID
     * @param payload Update data
     * @returns Updated version or null if not found
     */
    async update(
        id: number,
        payload: IUpdateWhatsNewPayload,
    ): Promise<IWhatsNewVersion | null> {
        const existingVersion = await this.databaseService.whatsNewVersion.getOneBy({
            id,
        });

        if (!existingVersion) {
            return null;
        }

        const updateData = this.factory.generateUpdateData(payload);
        const result = await this.databaseService.whatsNewVersion.update(
            { id },
            updateData,
        );

        if (result.affected === 0) {
            return null;
        }

        const updatedEntity = await this.databaseService.whatsNewVersion.getOneBy({ id });

        if (!updatedEntity) {
            console.error(`Failed to re-fetch What's New version with id ${id} after update.`);
            return null;
        }

        return this.factory.toApiVersion(updatedEntity);
    }

    /**
     * Delete a What's New version
     * @param id Version ID
     * @returns Success result
     */
    async delete(id: number): Promise<{ success: boolean }> {
        const existingVersion = await this.databaseService.whatsNewVersion.getOneBy({
            id,
        });

        if (!existingVersion) {
            return { success: false };
        }

        await this.databaseService.whatsNewVersion.delete({ id });

        return { success: true };
    }

    /**
     * Get reactions for a specific version
     * @param versionId Version ID
     * @param userId Optional current user ID to check if user has reacted
     * @returns Reactions summary
     */
    async getReactions(versionId: number, userId?: number): Promise<IReactionsResponse> {
        // Get all reactions grouped by emoji
        const reactions = await this.databaseService.whatsNewReaction.getAllBy(
            { versionId },
            null,
            null
        );

        // Group reactions by emoji and count them
        const reactionCounts = reactions.items.reduce((acc, reaction) => {
            if (!acc[reaction.emoji]) {
                acc[reaction.emoji] = { count: 0, userIds: [] };
            }
            acc[reaction.emoji].count++;
            acc[reaction.emoji].userIds.push(reaction.userId);
            return acc;
        }, {} as Record<string, { count: number; userIds: string[] }>);

        // Convert to response format
        const formattedReactions = Object.entries(reactionCounts).map(([emoji, data]) => ({
            emoji,
            count: data.count,
            userReacted: userId ? data.userIds.includes(String(userId)) : false,
        }));

        return { reactions: formattedReactions };
    }

    /**
     * Toggle a reaction for a version
     * @param payload Reaction data
     * @param userId ID of the current user
     * @returns Toggle result
     */
    async toggleReaction(
        payload: IToggleReactionPayload,
        userId: number
    ): Promise<IToggleReactionResponse> {
        const { versionId, emoji } = payload;

        // Validate emoji
        if (!ALLOWED_EMOJIS.includes(emoji as any)) {
            throw new Error('Invalid emoji');
        }

        // Check if version exists
        const version = await this.databaseService.whatsNewVersion.getOneBy({
            id: versionId,
        });

        if (!version) {
            throw new Error('Version not found');
        }

        if (!userId) {
            throw new Error('User ID is required');
        }

        const userIdString = String(userId);

        // Check if reaction already exists
        const existingReaction = await this.databaseService.whatsNewReaction.getOneBy({
            userId: userIdString,
            versionId,
            emoji,
        });

        if (existingReaction) {
            // Remove existing reaction
            await this.databaseService.whatsNewReaction.delete({ id: existingReaction.id });
            return {
                message: 'Reaction removed',
                action: 'removed',
            };
        } else {
            // Add new reaction
            const reactionEntity = this.factory.generateReaction({
                userId: userIdString,
                versionId,
                emoji,
            });
            await this.databaseService.whatsNewReaction.create(reactionEntity);
            return {
                message: 'Reaction added',
                action: 'added',
            };
        }
    }

    /**
     * Upload an image for What's New using Vercel Blob Storage
     * @param file Uploaded file object
     * @returns Upload result with URL
     */
    async uploadImage(file: { originalname: string; mimetype: string; buffer: Buffer }): Promise<IUploadResponse> {
        if (!file) {
            throw new Error('No file provided');
        }

        if (!file.mimetype.startsWith('image/')) {
            throw new Error('File must be an image');
        }

        const blobReadWriteToken = this.configService.appSecrets.VERCEL_BLOB_READ_WRITE_TOKEN;
        if (!blobReadWriteToken) {
            throw new Error('Vercel Blob token is not configured');
        }

        const timestamp = new Date().getTime();
        const filename = `whats-new/${timestamp}-${file.originalname}`;

        try {
            const blob = await put(filename, file.buffer, {
                access: 'public',
                contentType: file.mimetype,
                token: blobReadWriteToken,
            });

            return {
                url: blob.url,
                success: true,
            };
        } catch (error) {
            console.error('Error uploading to Vercel Blob:', error);
            throw new Error('Failed to upload image');
        }
    }

    /**
     * List all media files for What's New
     * @param prefix Optional prefix to filter files (default: 'whats-new/')
     * @param limit Optional limit for number of files to return
     * @returns List of media files with URLs and metadata
     */
    async listMedia(prefix = 'whats-new/', limit = 100): Promise<IMediaListResponse> {
        const blobReadWriteToken = this.configService.appSecrets.VERCEL_BLOB_READ_WRITE_TOKEN;
        if (!blobReadWriteToken) {
            throw new Error('Vercel Blob token is not configured');
        }

        try {
            const { blobs } = await list({
                prefix,
                limit,
                token: blobReadWriteToken,
            });

            const media: IMediaItem[] = blobs.map(blob => ({
                url: blob.url,
                pathname: blob.pathname,
                uploadedAt: new Date(blob.uploadedAt).toISOString(),
            }));

            return {
                media,
                total: media.length,
            };
        } catch (error) {
            console.error('Error listing media from Vercel Blob:', error);
            throw new Error('Failed to list media');
        }
    }

    /**
     * Delete a media file
     * @param pathname The pathname of the file to delete
     * @returns Success status
     */
    async deleteMedia(pathname: string): Promise<IMediaDeleteResponse> {
        const blobReadWriteToken = this.configService.appSecrets.VERCEL_BLOB_READ_WRITE_TOKEN;
        if (!blobReadWriteToken) {
            throw new Error('Vercel Blob token is not configured');
        }

        if (!pathname.startsWith('whats-new/')) {
            throw new Error('Can only delete files in the whats-new directory');
        }

        try {
            await del(pathname, { token: blobReadWriteToken });
            return {
                success: true,
                message: 'Media deleted successfully'
            };
        } catch (error) {
            console.error('Error deleting media from Vercel Blob:', error);
            throw new Error('Failed to delete media');
        }
    }
} 