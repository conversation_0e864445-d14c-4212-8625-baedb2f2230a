import { Module } from '@nestjs/common';
import { ExchangeRateService } from './exchange-rate.use-cases';
import { ExchangeRateFactory } from './exchange-rate.factory';
import { DataServicesModule } from '@services/database';
import {
  CrawlersModule,
  UaeExchangeRatesCrawlerService,
} from '@services/crawlers';

@Module({
  imports: [DataServicesModule, CrawlersModule],
  providers: [
    ExchangeRateService,
    ExchangeRateFactory,
    UaeExchangeRatesCrawlerService,
  ],
  exports: [ExchangeRateService],
})
export class ExchangeRateModule {}
