import { ICourseOffer } from "@px-shared-account/hermes";

/**
 * Interface for list offers response
 */
export interface IListOffersResponse {
  items: ICourseOffer[];
  total: number;
}

/**
 * Core API endpoints for product operations
 * These are the base paths and methods used by both client and server implementations
 */
export const PRODUCT_OFFERS_API_ENDPOINTS = {
  /**
   * Get all offers endpoint
   * @returns Endpoint configuration
   */
  listAllOffers: () => ({
    url: "/product-offers/listAll",
    method: "GET",
  }),
};

/**
 * Type definitions for API responses
 */
export type ProductOffersApiTypes = {
  listAllOffers: IListOffersResponse;
};
