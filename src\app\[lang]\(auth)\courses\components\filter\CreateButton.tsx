"use client";

import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import { CreateCourseModal } from "../modals/create/CreateCourseModal";
import { useRouter } from "next/navigation";

interface CreateButtonProps {
  onOpenModal?: () => void;
}

export const CreateButton = ({ onOpenModal }: CreateButtonProps) => {
  const t = useTranslations();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);

  // Safely get translations with fallbacks
  const getTranslation = (key: string, fallback: string) => {
    try {
      return t(key);
    } catch (error) {
      return fallback;
    }
  };

  const handleComplete = async (data: any) => {
    try {
      // TODO: Handle course creation with the mock service
      router.refresh();
    } catch (error) {
      console.error("Error creating course:", error);
    }
  };

  const handleClick = () => {
    if (onOpenModal) {
      onOpenModal();
    } else {
      setIsOpen(true);
    }
  };

  return (
    <>
      <Button onClick={handleClick} size="sm" className="hidden h-10 rounded-full md:flex">
        <Plus className="mr-2 h-4 w-4" />
        {getTranslation("courses.create.button", "Create Course")}
      </Button>
      {!onOpenModal && (
        <CreateCourseModal isOpen={isOpen} onOpenChange={setIsOpen} onComplete={handleComplete} />
      )}
    </>
  );
};
