import { Injectable } from '@nestjs/common';
import { Find<PERSON>perator, FindOptionsWhere, ILike, UpdateResult } from 'typeorm';
import { ChargebeeBillingService } from '@services/billing';
import { Product, ProductFamily } from '@entities';
import { ProductTable } from '@tables';
import { ProductPlanUseCases } from '../product-plan';
import { HubspotService } from '@services/crm';
import { IDataServices } from '@abstracts';

@Injectable()
export class ProductUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly productPlanUseCases: ProductPlanUseCases,
    private readonly chargebeeService: ChargebeeBillingService,
    private readonly hubspotService: HubspotService,
  ) {}

  /**
   * Creates a product
   * @param productInfo Info of the product
   * @returns Newly created product
   */
  async create(productInfo: Product): Promise<Product> {
    const updateProductResult =
      await this.hubspotService.updateProductCodeProperty(
        productInfo.code,
        'product_code_px_os',
        'product',
      );
    const updateDealListResult =
      await this.hubspotService.updateProductCodeProperty(
        productInfo.code,
        'product_code_list',
        'deal',
      );
    if (updateProductResult && updateDealListResult) {
      return this.dataServices.product.create(productInfo);
    }
    return null;
  }

  /**
   * Returns all product records from database
   * @returns Product records from database
   */
  async getAll(
    limit: number,
    page: number,
  ): Promise<{ items: Product[]; total: number }> {
    return this.dataServices.product.getAll(limit, page);
  }

  /**
   * Searches and returns a product record from database
   * @param lookupOptions Product properties to match against
   * @returns Product record from database OR null
   */
  async getOneBy(lookupOptions: FindOptionsWhere<Product>): Promise<Product> {
    return this.dataServices.product.getOneBy(lookupOptions);
  }

  /**
   * Retrieves all product records matching provided filters
   * @param searchQuery Value to search for in `name` or `code` columns of the `Product`
   * @param line `id` of `Product.productLine` to include in search
   * @param family `id` of `Product.productFamily` to include in search
   * @param status `status` of the `Product` to filter against
   * @param isForever Filters only products that are created for `forever` subscriptions
   * @param limit Number of `Product`s to fetch
   * @param page Page number
   * @param orderBy Order of the results based on `createdAt` date
   */
  async searchAll(
    searchQuery: string,
    line: string,
    family: string,
    status: string,
    isForever: boolean,
    limit = 500,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: Product[]; total: number }> {
    const filters: FindOptionsWhere<Product> = {};

    if (searchQuery) {
      filters.internalName = ILike(`%${searchQuery}%`);
    }

    if (line && !family) {
      const allFamilies =
        await this.dataServices.productLine.getOneWithRelations(
          'productLines',
          'id',
          line,
        );
      filters.productFamily = new FindOperator(
        'in',
        allFamilies.families.map((family) => family.id),
        true,
        true,
      );
    }

    if (family) {
      const productFamily = new ProductFamily();
      productFamily.id = Number(family);
      filters.productFamily = productFamily;
    }

    if (status) {
      filters.status = status;
    }

    if (isForever) {
      filters.isForever = isForever;
    }

    return this.dataServices.product.getAllBy(filters, limit, page, {
      createdAt: orderBy || 'DESC',
    });
  }

  /**
   * Updates a product that matches the provided `id`
   * @param id `id` of the product to update
   * @param updates Updates for the product
   */
  async update(id: number, updates: Partial<Product>): Promise<UpdateResult> {
    if (!updates?.isEvent) {
      updates.eventLocation = null;
      updates.eventYear = null;
    }
    const updateResult = await this.dataServices.product.update(
      {
        id,
      },
      updates,
    );

    if (
      updateResult.affected &&
      (updates.externalName || updates.description)
    ) {
      await this.productPlanUseCases.syncProductNameChanges(
        id,
        updates.externalName,
        updates.description,
      );
    }

    if (updateResult.affected && updates.taxProfile) {
      const productWithPlans = await this.getAllPlansForProduct(id);
      if (productWithPlans?.plans?.length === 0) return updateResult;
      for (const plan of productWithPlans.plans) {
        const planFromDB = await this.dataServices.productPlan.getOneBy({
          id: plan.id,
        });
        await this.chargebeeService.updateItemPrice(
          planFromDB?.prices[0]?.chargebeeId,
          null,
          updates.taxProfile,
        );
      }
    }

    return updateResult;
  }

  /**
   * Gets ids of product whose `ProductPlanPrice`s match provided billing cycles
   * @param billingCycles Array of billing cycles to filter products against
   * @returns Array of product ids
   */
  async getProductIdsByBillingCycles(billingCycles: number[]): Promise<any> {
    const products = await this.dataServices.product
      .getRepository()
      .createQueryBuilder()
      .from(ProductTable, 'product')
      .leftJoin('product.plans', 'plan')
      .leftJoin('plan.prices', 'price')
      .where('price.totalBillingCycles IN (:...billingCycles)', {
        billingCycles,
      })
      .groupBy('product.id')
      .having(
        'COUNT(DISTINCT CASE WHEN price.totalBillingCycles IN (:...billingCycles) THEN price.totalBillingCycles END) = :numBillingCycles',
        {
          billingCycles,
          numBillingCycles: billingCycles.length,
        },
      )
      .select('product.id')
      .getRawMany();

    const productIds = products.map((product: any) => product.product_id);
    return productIds;
  }

  async getAllPlansForProduct(id: number): Promise<Product> {
    return this.dataServices.product.getOneWithRelations('product', 'id', id);
  }
}
