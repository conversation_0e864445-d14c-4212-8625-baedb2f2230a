import { IDataServices } from '@abstracts';
import {
  PXActionResult,
  PXSSession,
  SessionType,
  UpdateResultWithItemInfo,
  IUserStats,
} from '@px-shared-account/hermes';
import { GlobalHelpers } from '@helpers';
import { addDays, addHours, differenceInDays, isAfter } from 'date-fns';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { EntityNotFoundError, LessThan } from 'typeorm';
import { TriadTable, UserTable } from '@services/database/postgres';
import { AccessInfo, PlatformTriadStats } from '@types';
import { UserUseCases } from '../user';
import { TriadFactory } from './triad.factory';
import { CreateTriadDto, UpdateTriadDto } from '@dtos';
import {
  TriadParticipantsFactory,
  TriadParticipantsUseCases,
} from '../triad-participants';

@Injectable()
export class TriadsUseCases {
  private readonly MAX_FUTURE_DAYS = 120;
  private readonly TRIAD_LIMITS = [
    { days: 7, maxTriads: 3 },
    { days: 30, maxTriads: 4 },
    { days: 120, maxTriads: 5 },
  ];
  constructor(
    private readonly dataServices: IDataServices,
    private readonly triadFactory: TriadFactory,
    private readonly userService: UserUseCases,
    private readonly triadParticipantsFactory: TriadParticipantsFactory,
    private readonly triadParticipantsUseCases: TriadParticipantsUseCases,
  ) {}

  /**
   * Creates a new triad after validating the session time and triad limits
   * @param triadInfo - The information of the triad
   * @param userId - The user ID of the organizer
   * @returns The created triad
   * @throws BadRequestException if the session time is not in the future or the triad limits are exceeded
   */
  async create(
    triadInfo: CreateTriadDto,
    userId: number,
  ): Promise<TriadTable> {
    const { sessionTime, maxParticipants } = triadInfo;
    this.validateDateConstraints(sessionTime);
    const organizer = await this.userService.getById(userId);
    if (!organizer) {
      throw new NotFoundException('organizer_not_found');
    }
    const isParadoxUser = GlobalHelpers.isParadoxUser(organizer.email);

    if (maxParticipants > 2 && !isParadoxUser) {
      const limitValidation = await this.validateTriadLimits(
        organizer.id,
        sessionTime,
      );
      if (!limitValidation.isValid) {
        throw new BadRequestException({
          message: 'triad_limit_exceeded',
          ...limitValidation.error,
        });
      }
    }

    const triad = this.triadFactory.generate(triadInfo, organizer);
    return this.dataServices.triad.create(triad);
  }

  /**
   * Updates a triad
   * @param triadId - The ID of the triad
   * @param triadUpdates - The updates to the triad
   * @returns The updated triad
   * @throws BadRequestException if the triad update fails
   */
  async updateTriad(
    triadId: number,
    triadUpdates: UpdateTriadDto,
  ): Promise<UpdateResultWithItemInfo<TriadTable>> {
    try {
      return this.dataServices.triad.updateAndReturnItem(
        'id',
        triadId.toString(),
        triadUpdates,
      );
    } catch (err) {
      throw new BadRequestException({
        code: 'triad_update_failed',
        message: err.message,
      });
    }
  }

  /**
   * Get a triad by ID
   * @param id - The ID of the triad
   * @returns {Promise<TriadTable>} - The triad
   * @throws NotFoundException if the triad is not found
   */
  async getById(id: number): Promise<TriadTable> {
    return this.dataServices.triad.getOneBy({ id });
  }

  /**
   * Validates the date constraints for a triad
   * @param sessionTime - The time of the session
   */
  validateDateConstraints(sessionTime: Date): void {
    this.validateSessionTimeIsInFuture(sessionTime);
    this.validateFutureSessionMaxDays(sessionTime);
  }

  /**
   * Validates that the session time is in the future
   * @param sessionTime - The time of the session
   */
  validateSessionTimeIsInFuture(sessionTime: Date): void {
    const sessionDate = new Date(sessionTime);
    const now = new Date();
    if (!isAfter(sessionDate, now)) {
      console.log('sessionTime', sessionTime);
      console.log('now', now);
      throw new BadRequestException({ code: 'date_in_past', sessionTime });
    }
  }

  /**
   * Validates that the session time is not too far in the future
   * @param sessionTime - The time of the session
   */
  validateFutureSessionMaxDays(sessionTime: Date): void {
    console.log('sessionTime', sessionTime);
    console.log('this.MAX_FUTURE_DAYS', this.MAX_FUTURE_DAYS);
    const sessionDate = new Date(sessionTime);
    const now = new Date();
    const maxFutureDate = addDays(now, this.MAX_FUTURE_DAYS);
    console.log('maxFutureDate', maxFutureDate);
    console.log('isAfter', isAfter(sessionDate, maxFutureDate));
    if (isAfter(sessionDate, maxFutureDate)) {
      console.log('isAfter', isAfter(sessionDate, maxFutureDate));
      throw new BadRequestException({
        code: 'session_too_far_in_future',
        sessionTime,
      });
    }
  }

  /**
   * Validates the triad limits for a user
   * @param userId - The ID of the user
   * @param sessionDate - The date of the session
   * @returns {Promise<any>} - The validation result
   */
  private async validateTriadLimits(
    userId: number,
    sessionDate: Date,
  ): Promise<any> {
    const now = new Date();
    const daysUntilSession = differenceInDays(sessionDate, now);

    const { organizedTriads, participatingTriads } =
      await this.getAllFutureTriadsForUser(userId);

    const bonusAllowance = participatingTriads.length;

    const nextWeekLimit = this.TRIAD_LIMITS[0];
    const organizedTriadsInNextWeek = organizedTriads.filter(
      (triad) => differenceInDays(triad.sessionTime, now) <= nextWeekLimit.days,
    );

    if (
      daysUntilSession <= nextWeekLimit.days &&
      organizedTriadsInNextWeek.length >=
      nextWeekLimit.maxTriads + bonusAllowance
    ) {
      return {
        isValid: false,
        error: {
          code: 'too_many_open_triads',
          violatedLimit: {
            days: nextWeekLimit.days,
            maxTriads: nextWeekLimit.maxTriads + bonusAllowance,
            currentTriads: organizedTriadsInNextWeek.length,
          },
        },
      };
    }

    if (daysUntilSession > nextWeekLimit.days) {
      let applicableLimit = this.TRIAD_LIMITS[2];
      if (daysUntilSession <= this.TRIAD_LIMITS[1].days) {
        applicableLimit = this.TRIAD_LIMITS[1];
      }

      const weekNumber =
        Math.floor((daysUntilSession - nextWeekLimit.days - 1) / 7) + 1;

      const weekStartDay = nextWeekLimit.days + (weekNumber - 1) * 7 + 1;
      const weekEndDay = Math.min(weekStartDay + 6, applicableLimit.days);

      const triadsInThisWeek = organizedTriads.filter((triad) => {
        const triadDays = differenceInDays(triad.sessionTime, now);
        return triadDays >= weekStartDay && triadDays <= weekEndDay;
      });

      if (
        triadsInThisWeek.length >=
        applicableLimit.maxTriads + bonusAllowance
      ) {
        return {
          isValid: false,
          error: {
            code: 'too_many_open_triads',
            violatedLimit: {
              weekNumber,
              weekStartDay,
              weekEndDay,
              maxTriads: applicableLimit.maxTriads + bonusAllowance,
              currentTriads: triadsInThisWeek.length,
              days: applicableLimit.days,
            },
          },
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Get all future triads for a user, both organized and participating
   * @param userId - The ID of the user
   * @returns {Promise<{organizedTriads: TriadTable[], participatingTriads: TriadTable[]}>}
   */
  async getAllFutureTriadsForUser(userId: number): Promise<{
    organizedTriads: TriadTable[];
    participatingTriads: TriadTable[];
  }> {
    const now = new Date();
    const allUserFutureTriads = await this.dataServices.triad
      .getRepository()
      .createQueryBuilder('triad')
      .leftJoinAndSelect('triad.participants', 'participants')
      .where(
        `(triad.organizerId = :userId OR participants.userId = :userId)
        AND triad.sessionTime > :now
        AND triad.maxParticipants > 2`,
        { userId, now },
      )
      .getMany();

    const organizedTriads = [];
    const participatingTriads = [];

    for (const triad of allUserFutureTriads) {
      if (triad.organizer?.id === userId) {
        if (triad.participants?.length < triad.maxParticipants - 1) {
          organizedTriads.push(triad);
        }
      } else {
        participatingTriads.push(triad);
      }
    }

    return { organizedTriads, participatingTriads };
  }

  /**
   * List all triads
   * @param search - An optional search query
   * @param limit - The number of items to return
   * @param page - The page number
   * @param sortBy - Field to sort by
   * @param sortOrder - Sort direction
   * @returns {Promise<{items: TriadTable[], total: number}>}
   */
  async listAll(
    search?: string,
    limit = 500,
    page = 1,
    sortBy = 'sessionTime',
    sortOrder: 'ASC' | 'DESC' = 'ASC',
  ): Promise<{ items: TriadTable[]; total: number }> {
    const validSortFields = ['sessionTime', 'maxParticipants'];
    if (!validSortFields.includes(sortBy)) {
      throw new BadRequestException('invalid_sort_by');
    }

    const queryBuilder = this.dataServices.triad
      .getRepository()
      .createQueryBuilder('triad')
      .leftJoinAndSelect('triad.participants', 'participants')
      .leftJoin('triad.organizer', 'organizer')
      .leftJoin('participants.user', 'user')
      .addSelect([
        'user.id',
        'user.ssoId',
        'user.firstName',
        'user.lastName',
        'organizer.id',
        'organizer.ssoId',
        'organizer.firstName',
        'organizer.lastName',
      ])
      .where(
        "triad.sessionTime + INTERVAL '1 hour' * (triad.duration + 1) > NOW()",
      )
      .orderBy(`triad.${sortBy}`, sortOrder)
      .take(limit)
      .skip((page - 1) * limit);

    if (search) {
      const searchPattern = `%${search}%`;
      queryBuilder.andWhere(
        `(triad.title ILIKE :search OR 
        organizer.firstName ILIKE :search OR 
        organizer.lastName ILIKE :search OR 
        user.firstName ILIKE :search OR 
        user.lastName ILIKE :search)`,
        { search: searchPattern },
      );
    }

    const [items, total] = await queryBuilder.getManyAndCount();
    return { items, total };
  }

  /**
   * Get triads accessible to current user
   * @param userEmail - ID of the user
   * @returns Promise of filtered triads
   */
  async getAccessibleTriads(userEmail: string): Promise<TriadTable[]> {
    const accessDetails = await this.userService.getUserAccessDetails(
      userEmail,
    );

    if (!accessDetails) {
      throw new ForbiddenException('forbidden_access_details');
    }

    const { accessInfo, email } = accessDetails;

    const triads = await this.dataServices.triad
      .getRepository()
      .createQueryBuilder('triad')
      .leftJoinAndSelect('triad.participants', 'participants')
      .orderBy('triad.sessionTime', 'ASC')
      .getMany();

    const accessibleTriads = triads.filter((triad) => {
      const isFuture = this.isTriadInFuture(triad.sessionTime.toISOString());
      if (!isFuture) return false;

      return (
        triad.organizer?.email === userEmail ||
        this.canAccessTriad(
          email,
          accessInfo,
          triad.sessions as PXSSession[],
          triad.sessionType as SessionType,
        )
      );
    });

    return accessibleTriads;
  }

  /**
   * Joins a triad
   * @param triadId - The ID of the triad
   * @param userId - The ID of the user
   * @returns {Promise<PXActionResult>} - The result of the join
   * @throws NotFoundException if the triad is not found
   * @throws BadRequestException if the user cannot join the triad
   */
  async joinTriad(triadId: number, userId: number): Promise<PXActionResult> {
    const participant = await this.userService.getById(userId);
    if (!participant) {
      throw new NotFoundException('user_not_found');
    }
    const triadToJoin = await this.dataServices.triad
      .getRepository()
      .createQueryBuilder('triad')
      .leftJoinAndSelect('triad.participants', 'participants')
      .leftJoinAndSelect('triad.organizer', 'organizer')
      .where('triad.id = :triadId', { triadId })
      .getOne();

    if (!triadToJoin) {
      throw new NotFoundException('triad_not_found');
    }

    this.validateCanJoinTriad(triadToJoin, participant);

    const newParticipant = this.triadParticipantsFactory.generate(
      participant.id,
      triadToJoin.id,
    );
    await this.triadParticipantsUseCases.create(newParticipant);
    return { success: true, message: 'Successfully joined triad' };
  }

  /**
   * Leaves a triad by removing the participant from the triad
   * and deleting the `TriadParticipant` record
   * @param triadId - The ID of the triad
   * @param userId - The ID of the user
   * @returns {Promise<PXActionResult>} - The result of the leave
   * @throws NotFoundException if the triad is not found
   * @throws BadRequestException if the user is not a participant
   */
  async leaveTriad(triadId: number, userId: number): Promise<PXActionResult> {
    const participant = await this.userService.getById(userId);
    const triadToLeave = await this.dataServices.triad.getOneBy({
      id: triadId,
    });
    if (!triadToLeave) {
      throw new NotFoundException('triad_not_found');
    }
    console.log('triadToLeave', triadToLeave);
    const triadParticipants = triadToLeave.participants;
    const participantIndex = triadParticipants.findIndex(
      (p) => p.user.id === participant.id,
    );
    if (participantIndex === -1) {
      console.log('not_participant');
      throw new BadRequestException('not_participant');
    }
    triadParticipants.splice(participantIndex, 1);
    await this.triadParticipantsUseCases.delete(participant.id, triadId);
    return { success: true, message: 'Successfully left triad' };
  }

  /**
   * Deletes a triad by ID. A triad can only be deleted by the organizer
   * of the triad
   * @param id - The ID of the triad
   * @param userId - The ID of the user
   * @returns {Promise<PXActionResult>} - The result of the delete
   * @throws NotFoundException if the triad is not found
   */
  async deleteById(id: number, userId: number): Promise<PXActionResult> {
    const result = await this.dataServices.triad.delete({
      id,
      organizer: { id: userId },
    });

    if (result.affected === 0) {
      const triad = await this.dataServices.triad.getOneBy({ id });
      if (!triad) {
        throw new NotFoundException('triad_not_found');
      }
      throw new BadRequestException('not_organizer');
    }

    return { success: true, message: 'Successfully deleted triad' };
  }

  /**
   * Get one upcoming triad for a user
   * @param userId - The ID of the user
   * @returns {Promise<TriadTable>} - The upcoming triads
   */
  async getOneUpcomingTriadForUser(userId: number): Promise<TriadTable> {
    const user = await this.userService.getById(userId);
    if (!user) {
      throw new NotFoundException('user_not_found');
    }
    try {
      const now = new Date();
      const triads = await this.dataServices.triad
        .getRepository()
        .createQueryBuilder('triad')
        .leftJoinAndSelect('triad.participants', 'participants')
        .where('triad.organizerId = :userId', { userId: user.id })
        .orWhere('participants.userId = :userId', { userId: user.id })
        .andWhere('triad.sessionTime > :now', { now })
        .orderBy('triad.sessionTime', 'ASC')
        .getOneOrFail();
      return triads;
    } catch (err) {
      if (err instanceof EntityNotFoundError) {
        return null;
      }
      throw new NotFoundException('failed_to_get_upcoming_triads');
    }
  }

  /**
   * Checks if a user can join a triad
   * @param triad - The triad to check
   * @param participant - The participant to check
   * @returns {boolean} - True if the user can join the triad, throws an error otherwise
   * @throws BadRequestException if the user cannot join the triad
   */
  validateCanJoinTriad(triad: TriadTable, participant: UserTable): void {
    if (triad?.participants?.length >= triad.maxParticipants) {
      throw new BadRequestException('triad_full');
    }

    if (triad.organizer?.id === participant?.id) {
      throw new BadRequestException('cannot_join_own_triad');
    }

    const isParticipant = triad.participants?.some(
      (participant) => participant?.user?.id === participant?.id,
    );

    if (isParticipant) {
      throw new BadRequestException('already_joined');
    }
  }

  /**
   * Checks if a user can access a triad
   * All paradox users can access all triads
   * PXL users can access PXL triads
   * PXS users can access PXS triads if they have a valid session
   * @param userEmail - The email of the user
   * @param accessInfo - The access information of the user
   * @param triadSessions - The sessions of the triad
   * @param triadSessionType - The type of the triad
   * @returns {boolean} - True if the user can access the triad, false otherwise
   */
  canAccessTriad(
    userEmail: string,
    accessInfo: AccessInfo,
    triadSessions: PXSSession[] | null | undefined,
    triadSessionType: SessionType,
  ): boolean {
    if (accessInfo.isParadox || GlobalHelpers.isParadoxUser(userEmail)) {
      return true;
    }

    if (triadSessionType === SessionType.PXL) {
      if (!accessInfo.isPXL) {
        return false;
      }
      return true;
    }

    if (!accessInfo.hubspotSession) {
      return false;
    }

    if (!triadSessions || triadSessions.length === 0) {
      return true;
    }

    const normalizedUserSession = accessInfo.hubspotSession
      ? this.triadFactory.normalizeHubspotSession(accessInfo.hubspotSession)
      : undefined;

    return normalizedUserSession
      ? triadSessions.some(
        (session) =>
          session === normalizedUserSession ||
          session === accessInfo.hubspotSession,
      )
      : false;
  }

  /**
   * Checks if a triad is in the future
   * @param sessionTime - The session time of the triad
   * @returns {boolean} - True if the triad is in the future, false otherwise
   */
  isTriadInFuture(sessionTime: string): boolean {
    const now = new Date();
    const triadDateTime = addHours(new Date(sessionTime), 6);
    return triadDateTime > now;
  }

  /**
   * Get statistics for triads organized by a user
   * @param userId - The ID of the organizer
   * @returns {Promise<IUserStats>}
   */
  async getTriadStatistics(
    userId: number,
  ): Promise<IUserStats> {
    const user = await this.userService.getById(userId);
    if (!user) {
      throw new NotFoundException('user_not_found');
    }
    // Count total triads organized by the user
    const totalTriadsOrganized = await this.dataServices.triad
      .getRepository()
      .createQueryBuilder('triad')
      .where('triad.organizerId = :userId', { userId: user.id })
      .getCount();

    // Count total participants in user's triads
    const totalParticipants = await this.dataServices.triadParticipant
      .getRepository()
      .createQueryBuilder('triadParticipant')
      .leftJoin('triadParticipant.triad', 'triad')
      .where('triad.organizerId = :userId', { userId: user.id })
      .getCount();

    return { totalTriadsOrganized, totalParticipants } as IUserStats;
  }

  /**
   * Get overall statistics for all triads in the database
   * @param requesterEmail - The email of the requester
   * @param start - Start date for time series in ISO string format (optional)
   * @param end - End date for time series in ISO string format (optional)
   */
  async getPlatformStatistics(
    requesterEmail: string,
    start?: string,
    end?: string,
  ): Promise<PlatformTriadStats> {
    if (!GlobalHelpers.isParadoxUser(requesterEmail)) {
      throw new ForbiddenException('not_authorized');
    }

    // Overview
    const triadRepo = this.dataServices.triad.getRepository();
    const triadParticipantRepo =
      this.dataServices.triadParticipant.getRepository();
    const now = new Date();
    const startDate = start
      ? new Date(start)
      : new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const endDate = end ? new Date(end) : now;

    // Overview statistics
    const totalTriads = await triadRepo.count();
    const totalParticipants = await triadParticipantRepo.count();
    const completedTriads = await triadRepo.count({
      where: { sessionTime: LessThan(now) },
    });
    const overview = {
      totalTriads,
      totalParticipants,
      averageParticipantsPerTriad: totalTriads
        ? totalParticipants / totalTriads
        : 0,
      completionRate: totalTriads ? completedTriads / totalTriads : 0,
    };

    // Time series data (group by date)
    const triadsCreated = await triadRepo
      .createQueryBuilder('triad')
      .select('DATE(triad.createdAt)', 'date')
      .addSelect('COUNT(triad.id)', 'value')
      .where('triad.createdAt >= :startDate AND triad.createdAt <= :endDate', {
        startDate,
        endDate,
      })
      .groupBy('date')
      .orderBy('date', 'ASC')
      .getRawMany();

    const participantsJoined = await triadParticipantRepo
      .createQueryBuilder('tp')
      .select('DATE(tp.joinedAt)', 'date')
      .addSelect('COUNT(tp.id)', 'value')
      .where('tp.joinedAt >= :startDate AND tp.joinedAt <= :endDate', {
        startDate,
        endDate,
      })
      .groupBy('date')
      .orderBy('date', 'ASC')
      .getRawMany();

    const timeSeriesData = {
      triadsCreated,
      participantsJoined,
    };

    // Engagement metrics
    const recurringParticipants = await triadParticipantRepo
      .createQueryBuilder('triadParticipant')
      .select('triadParticipant.userId', 'userId')
      .addSelect('COUNT(triadParticipant.id)', 'count')
      .groupBy('triadParticipant.userId')
      .having('COUNT(triadParticipant.id) > 1')
      .getRawMany();

    const sessionDistribution = await triadRepo
      .createQueryBuilder('triad')
      .select('triad.duration', 'duration')
      .addSelect('COUNT(triad.id)', 'count')
      .groupBy('triad.duration')
      .getRawMany();

    const engagement = {
      recurringParticipants: recurringParticipants.length,
      sessionDurationDistribution: sessionDistribution.map((s) => ({
        duration: s.duration,
        count: Number(s.count),
      })),
    };

    // Organizer statistics
    const subQuery = triadRepo
      .createQueryBuilder('triad')
      .select('user.id', 'id')
      .addSelect('user.firstName', 'firstName')
      .addSelect('user.lastName', 'lastName')
      .addSelect('COUNT(triad.id)', 'triadsCount')
      .leftJoin('triad.organizer', 'user')
      .groupBy('user.id');

    const topOrganizers = await subQuery
      .orderBy('"triadsCount"', 'DESC')
      .getRawMany();

    const organizers = {
      topOrganizers: topOrganizers.map((o) => ({
        id: o.id,
        name: [o.firstName, o.lastName].filter(Boolean).join(' ') || 'Unknown',
        triadsCount: Number(o.triadsCount),
      })),
    };

    return {
      overview,
      timeSeriesData,
      engagement,
      organizers,
    };
  }
}
