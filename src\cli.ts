import { NestFactory } from '@nestjs/core';
import { IDataServices } from '@abstracts';
import { AppModule } from './app.module';

async function bootstrap() {
  console.log(`🚀 Setting up application context`);
  const application = await NestFactory.createApplicationContext(AppModule, {
    logger: false,
  });
  const action = process.argv[2];
  try {
    switch (action) {
      case 'clean-staging':
        console.log(`⚙️ Now executing command: \x1b[33m${action}\x1b[0m`);
        const dataServices = application.get(IDataServices);
        const result = await dataServices.resetDatabase();
        if (result) {
          console.log(`🧹 Successfully cleaned staging database`);
        }
        process.exit(0);
      default:
        console.log('Command not found');
        process.exit(0);
    }
  } catch (error) {
    throw new Error(
      `💣 \x1b[42m\x1b[37m Error cleaning database: ${error}\x1b[0m`,
    );
  }
}

bootstrap().catch(() => process.exit(1));
