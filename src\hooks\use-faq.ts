"use client";

import { useUser } from "@clerk/nextjs";
import { useEffect, useState } from "react";

import { useCustomerAccessDetails } from "@/services/customers";

export const useFaq = () => {
  const { user } = useUser();
  const actualEmail = user?.primaryEmailAddress?.emailAddress || "";
  const { data: currentUserAccessData } = useCustomerAccessDetails(actualEmail);

  const access = {
    isPXL: currentUserAccessData?.accessInfo?.isPXL || false,
    isPXS: currentUserAccessData?.accessInfo?.isPXS || false,
    isParadox: currentUserAccessData?.accessInfo?.isParadox || false,
    hasBothAccess: currentUserAccessData?.accessInfo?.hasBothAccess || false,
  };

  return { access };
};

export const useFaqIframe = () => {
  const [iframeHeight, setIframeHeight] = useState("65vh");

  useEffect(() => {
    const handleResize = () => {
      // Calculate height based on viewport
      const viewportHeight = window.innerHeight;
      const minHeight = Math.max(400, viewportHeight * 0.65); // At least 400px or 65% of viewport

      setIframeHeight(`${minHeight}px`);
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return { iframeHeight };
};
