import { useMediaQuery } from "react-responsive";
import tailwindConfig from "../../../tailwind.config"; // Your tailwind config

const breakpoints = tailwindConfig.theme.screens;

type BreakpointKey = keyof typeof breakpoints;

/**
 * @description Hook to get the breakpoint value of a given key from tailwind.config.ts.
 * @param breakpointKey - The breakpoint key to get the value of
 * @returns An boolean value that is true if the breakpoint is active
 */
export function useBreakpoint<K extends BreakpointKey>(breakpointKey: K) {
  const bool = useMediaQuery({
    query: `(min-width: ${breakpoints[breakpointKey]})`,
  });
  return bool;
}
