import fs from "fs-extra";
import path from "path";

const isVercel = process.env.VERCEL === "1";
const isMacOS = process.platform === "darwin";

if (!isVercel) {
  try {
    console.log("🧪 Installing hermes locally");

    let pathname = isMacOS ? "../hermes/src" : "../hermes";
    const hermesSourcePath = path.resolve(pathname);
    const hermesDestPath = path.resolve("node_modules/@px-shared-account/hermes");

    // Check if hermes source exists
    if (!fs.existsSync(hermesSourcePath)) {
      console.error("❌ Could not find hermes source at:", hermesSourcePath);
      console.log(
        "💡 Make sure you have the hermes repository cloned at the same level as this project",
      );
      process.exit(1);
    }

    // Ensure destination directory exists
    fs.ensureDirSync(hermesDestPath);

    // Copy the src folder to the hermes package
    if (isMacOS) {
      fs.copySync(hermesSourcePath, path.join(hermesDestPath, "src"), { overwrite: true });
    } else {
      fs.copySync(path.join(hermesSourcePath, "src"), path.join(hermesDestPath, "src"), {
        overwrite: true,
      });
      fs.copySync(path.join(hermesSourcePath, "dist"), path.join(hermesDestPath, "dist"), {
        overwrite: true,
      });
    }
    console.log("✅ Successfully copied hermes source files");
  } catch (error) {
    console.error("❌ Error during hermes installation:", error.message);
    process.exit(1);
  }
}
