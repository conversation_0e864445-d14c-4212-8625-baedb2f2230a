import { getTranslations } from "next-intl/server";

import { PageSectionBase } from "@/components/base/page-section";
import { getCustomerAccessDetails } from "@/services/customers/server";
import { EventCard, HeroCard, FeatureCard } from "@/components/cards";
import { ToolsSection } from "@/components/sections";
import { CourseCatalogSection } from "@/components/sections";

export default async function Page() {
  const t = await getTranslations("dashboard");

  const customerAccessDetails = await getCustomerAccessDetails();
  // need to find a way to get the user's first name dynamically
  // for now, using an empty string as a placeholder
  const firstname = "";

  const eventDetailsList = [
    t("eventsSection.sampleCard.details.improveConfidence"),
    t("eventsSection.sampleCard.details.noFearOfJudgment"),
    t("eventsSection.sampleCard.details.focusOnStrengths"),
    t("eventsSection.sampleCard.details.organizeBetter"),
    t("eventsSection.sampleCard.details.manageTimeEffectively"),
    t("eventsSection.sampleCard.details.unleashPotential"),
  ];

  const heroButtons = [];
  if (customerAccessDetails?.accessInfo?.isPXS || customerAccessDetails?.accessInfo?.isPXL) {
    heroButtons.push({
      text: t("heroCard.actionButtonLabel"),
      variant: "primary" as const,
      ariaLabel: t("heroCard.actionButtonLabel"),
      href: "https://www.paradox.io/",
    });
  }

  return (
    <>
      {/* Hero Card Section - Now acts as page header background */}
      <HeroCard
        imageUrl="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
        title={t("heroCard.title", { firstname })}
        subtitle={t("heroCard.subtitle")}
        buttons={heroButtons}
        className="-mt-14 md:-mt-16"
      />
      {/* Main page content with consistent padding and max-width */}
      <div className="max-w-screen-3xl mx-auto flex w-full flex-col gap-8 p-8">
        {/* Purchases Section - Now uses ProductsSection component */}
        <CourseCatalogSection />

        <ToolsSection customerAccessDetails={customerAccessDetails} />
        {/* Upcoming Events Section */}
        <PageSectionBase
          contentClassName="flex flex-col lg:flex-row gap-4"
          title={t("eventsSection.title")}
          description={t("eventsSection.subtitle")}
        >
          {/* <EventCard
            imageUrl="https://images.unsplash.com/photo-1505373877841-8d25f7d46678?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1280&q=80"
            title="EPR 2025"
            description={t("eventsSection.sampleCard.description")}
            detailsList={eventDetailsList}
            actionButtonLabel={t("eventsSection.sampleCard.actionButtonLabel")}
          /> */}
          <EventCard
            imageUrl="https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?q=80&w=1280&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            title="EPR 2026"
            description={t("eventsSection.sampleCard.description")}
            detailsList={eventDetailsList}
            actionButtonLabel={t("eventsSection.sampleCard.actionButtonLabel")}
            className="w-full lg:w-[50%]"
            href="https://epr.paradox.io/event?utm_source=hubspot&utm_campaign=SP-EPR_2023-SALES_DPT&utm_medium=email#pricing"
          />
        </PageSectionBase>
        {/* Wall of Dreams Section */}
        <PageSectionBase title="" className="!gap-0 !space-y-0">
          <FeatureCard
            imageUrl={
              "https://images.unsplash.com/photo-1713947505684-25b9bf8d544f?q=80&w=2664&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            }
            title={t("wallOfDreams.title").toUpperCase()}
            subtitle={t("wallOfDreams.subtitle")}
            buttons={[
              {
                text: t("wallOfDreams.buttonWriteStory"),
                ariaLabel: t("wallOfDreams.buttonWriteStory"),
                href: "https://ask.paradox.io/paradox-heros",
              },
              {
                text: t("wallOfDreams.buttonDiscoverStories"),
                variant: "secondary",
                ariaLabel: t("wallOfDreams.buttonDiscoverStories"),
                href: "https://www.paradox.io/fr/etudes-de-cas",
              },
            ]}
          />
        </PageSectionBase>
      </div>
    </>
  );
}
