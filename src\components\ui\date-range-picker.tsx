"use client";

import * as React from "react";
import { addDays, format } from "date-fns";
import { Calendar as CalendarIcon, ChevronsUpDown } from "lucide-react";
import { DateRange } from "react-day-picker";
import { useTranslations } from "next-intl";

import { cn } from "@/lib/utils";
import { Button } from "@/components/base/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { motion } from "framer-motion";

export function DatePickerWithRange({
  className,
  value,
  onChange,
  disabled,
  hasError,
  placeholder,
  label,
}: React.HTMLAttributes<HTMLDivElement> & {
  value?: DateRange;
  onChange?: (date: DateRange | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  hasError?: boolean;
  label?: string;
}) {
  const t = useTranslations();
  const [date, setDate] = React.useState<DateRange | undefined>(value);
  const [open, setOpen] = React.useState(false);

  // Sync internal state with external value
  React.useEffect(() => {
    setDate(value);
  }, [value]);

  const handleSelect = (newDate: DateRange | undefined) => {
    setDate(newDate);
    onChange?.(newDate);
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-full justify-between",
              disabled && "cursor-not-allowed opacity-50",
              hasError && "border-destructive text-destructive",
            )}
          >
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "dd/MM/yyyy")} → {format(date.to, "dd/MM/yyyy")}
                </>
              ) : (
                format(date.from, "dd/MM/yyyy")
              )
            ) : (
              label || t("date_range_picker.select_date")
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="bg-background pointer-events-auto min-w-sm rounded-2xl border-none p-0 text-white"
          style={{ width: "var(--radix-popover-trigger-width)" }}
          align="start"
        >
          {date?.from && date?.to && (
            <div className="flex items-center justify-between gap-2 px-3 pt-2">
              <Button
                variant={"outline"}
                disabled
                className="rounded-full border-white bg-transparent text-white !opacity-100"
              >
                <span>{format(date.from, "dd/MM/yyyy")}</span>
              </Button>
              <span className="text-white">→</span>
              <Button
                variant={"outline"}
                disabled
                className="rounded-full border-white bg-transparent text-white !opacity-100"
              >
                <span>{format(date.to, "dd/MM/yyyy")}</span>
              </Button>
            </div>
          )}
          <div className="bg-background rounded-2xl p-1">
            <Calendar
              autoFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={handleSelect}
              numberOfMonths={1}
              className="border-none"
              disabled={{ before: new Date() }}
              footer={
                <div className="mt-4 flex items-center justify-between gap-2">
                  <Button
                    variant="default"
                    className="!w-3/4"
                    onClick={() => {
                      handleSelect(date);
                      setOpen(false);
                    }}
                  >
                    {t("date_range_picker.apply")}
                  </Button>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      variant="link"
                      className="flex-1 text-white underline hover:text-gray-300"
                      onClick={() => {
                        handleSelect(undefined);
                        setOpen(false);
                      }}
                    >
                      <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.1 }}
                      >
                        {t("date_range_picker.clear")}
                      </motion.span>
                    </Button>
                  </motion.div>
                </div>
              }
            />
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
