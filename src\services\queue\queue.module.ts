import { Module } from '@nestjs/common';
import { QueueModule as PgBossQueueModule } from '@nestjs-enhanced/pg-boss';
import { ConfigService } from '@config';

@Module({
    imports: [
        PgBossQueueModule.registerAsync({
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => {
                if (!configService.dbSecrets) {
                    throw new Error('DB secrets not found');
                }

                const {
                    HOST: host,
                    PORT: port,
                    USERNAME: username,
                    PASSWORD: password,
                    DATABASE: database,
                } = configService.dbSecrets;

                // Construct the database connection string for pg-boss
                const connectionString = `postgres://${username}:${password}@${host}:${port}/${database}`;

                return {
                    // pg-boss specific options
                    connectionString: connectionString,
                    ssl: {
                        rejectUnauthorized: false,
                    },
                    retryLimit: 3,
                    retryDelay: 60000,
                    retryBackoff: true,
                    deleteAfterDays: 7, // Clean up completed jobs after 7 days
                    archiveCompletedAfterSeconds: 86400, // Archive completed jobs after 24 hours
                };
            },
        }),
    ],
    exports: [PgBossQueueModule],
})
export class QueueModule { }
