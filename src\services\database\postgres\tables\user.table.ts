import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from '@entities';
import { RoleTable } from './role.table';
import { UserProfileTable } from './user-profile.table';
import { CourseTable } from './course.table';
import { CohortTable } from './cohort.table';
import { UserType, IUserChargebeeIds } from '@px-shared-account/hermes';
import { TriadTable } from './triad.table';
import { TriadParticipantTable } from './triad-participant.table';

@Entity({
  name: 'users',
})
export class UserTable implements UserEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column('varchar')
  firstName: string;

  @Column('varchar')
  lastName: string;

  @Index('idx_users_email')
  @Column('varchar', { unique: true })
  email: string;

  @Column('varchar', { nullable: true })
  ssoId?: string;

  @ManyToOne(() => RoleTable, (role) => role.members)
  role: RoleTable;

  @OneToOne(() => UserProfileTable, (profile) => profile.id)
  @JoinColumn({ name: 'profileId' })
  profile: UserProfileTable;

  @ManyToMany(() => CourseTable, (course) => course.managers)
  coursesManaged: CourseTable[];

  @ManyToMany(() => TriadTable, (triad) => triad.organizer)
  triadsManaged: TriadTable[];

  @ManyToMany(() => TriadParticipantTable, (participant) => participant.user)
  triadParticipations: TriadParticipantTable[];

  @ManyToMany(() => CohortTable, (cohort) => cohort.students)
  cohorts: CohortTable[];

  @Column('jsonb', { nullable: true })
  metaData?: Record<string, any>;

  @Column('enum', { enum: UserType, default: UserType.EXTERNAL })
  type: UserType;

  @Column('jsonb', { nullable: true })
  chargebeeIds?: IUserChargebeeIds;

  @Column('varchar', { nullable: true })
  lmsId?: string;

  @Column('varchar', { nullable: true })
  communityId?: string;

  @Column('varchar', { nullable: true })
  crmId?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}
