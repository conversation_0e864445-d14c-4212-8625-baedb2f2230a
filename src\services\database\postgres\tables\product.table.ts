import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CommunityInfo, CourseInfo } from '@types';
import { FiscalEntity } from '@enums';
import { Product } from '@entities';
import { ProductFamilyTable } from './product-family.table';
import { ProductOfferTable } from './product-offers.table';
import { ProductPlanTable } from './product-plan.table';

@Entity({
  name: 'products',
})
export class ProductTable implements Product {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 255 })
  internalName: string;

  @Column({ type: 'varchar', length: 255 })
  externalName: string;

  @Column({ type: 'varchar', length: 500 })
  description: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  code: string;

  @Column({ type: 'enum', enum: FiscalEntity, nullable: true })
  fiscalEntity?: FiscalEntity;

  @ManyToOne(() => ProductFamilyTable, (productFamily) => productFamily.id)
  productFamily: ProductFamilyTable;

  @OneToMany(() => ProductPlanTable, (productPlan) => productPlan.product)
  plans: ProductPlanTable[];

  @Column({ type: 'varchar', length: 100, nullable: true })
  image?: string;

  @Column({ type: 'varchar', length: 10 })
  status: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  taxProfile?: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  isEvent?: boolean;

  @Column({ type: 'boolean', default: false, nullable: true })
  isForever?: boolean;

  @Column({ type: 'varchar', length: 10, nullable: true })
  eventYear?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  eventLocation?: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  withProductDelivery?: boolean;

  @Column({ type: 'jsonb', nullable: true })
  lmsIds?: CourseInfo[];

  @Column({ type: 'jsonb', default: [], nullable: true })
  communityIds?: CommunityInfo[];

  @ManyToMany(() => ProductOfferTable, (offer) => offer.products)
  offers?: ProductOfferTable[];

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
