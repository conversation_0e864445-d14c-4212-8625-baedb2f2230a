import {
  LinkedInvoice,
  LinkedCreditNote,
  LinkedRefund,
} from 'chargebee-typescript/lib/resources/transaction';
import {
  Currencies,
  PaymentMethod,
  TransactionStatus,
  TransactionType,
} from '@enums';

export class Transaction {
  id: number;
  chargebeeId: string;
  status: TransactionStatus;
  date: number;
  type: TransactionType;
  currency: Currencies;
  amount: number;
  amountUnused: number;
  subscriptionId?: string;
  customerId: string;
  customerEmail: string;
  customerName: string;
  exchangeRate?: number;
  referenceNumber?: string;
  linkedInvoices: LinkedInvoice[];
  linkedCreditNotes: LinkedCreditNote[];
  linkedRefunds: LinkedRefund[];
  paymentMethod: PaymentMethod;
  maskedCardNumber?: string;
  paymentMethodDetails?: any;
  gateway: string;
  idAtGateway?: string;
  businessEntityId: string;
  errorCode?: string;
  errorText?: string;
  createdAt?: Date;
}
