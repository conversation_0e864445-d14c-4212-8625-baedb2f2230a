import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Webhook } from '@entities';
import { WebhookStatus } from '@enums';
import { WebhookOutcome, WebhookSender } from '@types';

@Entity({ name: 'webhooks' })
export class WebhookTable implements Webhook {
  @PrimaryGeneratedColumn('increment')
  id: string;

  @Column({ type: 'varchar' })
  sender: WebhookSender;

  @Column({ type: 'varchar', unique: true })
  senderWebhookId: string;

  @Column({ type: 'bigint' })
  occurredAt: number;

  @Column({ type: 'enum', enum: WebhookStatus })
  status: WebhookStatus;

  @Column({ type: 'jsonb', nullable: true })
  outcome: WebhookOutcome;

  @CreateDateColumn({ type: 'timestamp', default: () => 'now()' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deletedAt: Date;
}
