import { ColumnDef } from "@tanstack/react-table";

export interface Column<T> {
  id: string;
  header: string;
  accessorKey: keyof T;
  cell?: (value: any, row: T) => React.ReactNode;
  enableSorting?: boolean;
  sortKey?: string;
}

export interface TableState {
  pageIndex: number;
  pageSize: number;
  searchQuery: string;
  sortBy?: { id: string; desc: boolean };
}

export type SelectedRows = string[] | "all";

export interface TableProps<T> {
  /** React node containing bulk actions to be displayed when rows are selected */
  bulkActions?: React.ReactNode;
  /** Array of column definitions for the table */
  columns: ColumnDef<T>[];
  /** Array of data items to be displayed in the table */
  data: T[];
  /** Enable row selection functionality */
  enableSelection?: boolean;
  /** Enable search functionality */
  enableSearch?: boolean;
  /** Enable pagination functionality */
  enablePagination?: boolean;
  /** Enable sorting functionality */
  enableSorting?: boolean;
  /** Use client-side pagination instead of server-side pagination */
  useClientPagination?: boolean;
  /** React node containing filter components */
  filters?: React.ReactNode;
  /** React node containing selection actions to be displayed below the header */
  selectionActions?: React.ReactNode;
  /** Indicates if the table is in a loading state */
  isLoading?: boolean;
  /** Message to display when no results are found */
  noResults?: string;
  /** Number of items to display per page */
  pageSize?: number;
  /** Currently selected row IDs or "all" to select all rows */
  selectedRows?: SelectedRows;
  /** Table title to be displayed */
  title?: string | React.ReactNode;
  /** Total number of rows in the dataset (including non-visible rows) */
  totalRows: number;
  /** Function to get unique identifier for each row */
  getRowId: (row: T) => string;
  /** Callback fired when table state changes (pagination, sorting, search) */
  onStateChange: (state: TableState) => void;
  /** Callback fired when row selection changes */
  onSelectionChange?: (selectedRows: SelectedRows) => void;
  minSearchLength?: number;
}
