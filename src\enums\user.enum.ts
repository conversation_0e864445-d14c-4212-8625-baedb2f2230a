export enum RoleGroup {
  ADMIN = "ADMIN",
  PROGRAM_MANAGER = "PROGRAM_MANAGER",
  STUDENT = "STUDENT",
  EXTERNAL = "EXTERNAL",
}

export enum Roles {
  ADMIN = "ADMIN",
  FACILITATOR = "FACILITATOR",
  MENTOR = "MENTOR",
  MASTER_COACH = "MASTER_COACH",
  PROGRAM_MANAGER = "PROGRAM_MANAGER",
  STUDENT = "STUDENT",
}

export enum Gender {
  MALE = "MALE",
  FEMALE = "FEMALE",
}

export enum UserType {
  INTERNAL = "internal",
  EXTERNAL = "external",
  BETA_TESTER = "beta_tester",
}
