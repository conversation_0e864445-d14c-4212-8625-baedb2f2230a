import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { WhatsNewVersionEntity, WhatsNewReactionEntity } from '@entities';
import { IThumbnailImage } from '@px-shared-account/hermes';

@Entity({
    name: 'whats_new_versions',
})
export class WhatsNewVersionTable implements WhatsNewVersionEntity {
    @PrimaryGeneratedColumn('increment')
    id: number;

    @Index('idx_whats_new_version_code')
    @Column('varchar', { unique: true })
    versionCode: string;

    @Index('idx_whats_new_date')
    @Column('timestamp')
    date: Date;

    @Column('varchar')
    titleEn: string;

    @Column('varchar')
    titleFr: string;

    @Column('text', { default: '' })
    descriptionEn: string;

    @Column('text', { default: '' })
    descriptionFr: string;

    @Column('jsonb', { default: [] })
    addedEn: string[];

    @Column('jsonb', { default: [] })
    addedFr: string[];

    @Column('jsonb', { default: [] })
    changedEn: string[];

    @Column('jsonb', { default: [] })
    changedFr: string[];

    @Column('jsonb', { default: [] })
    fixedEn: string[];

    @Column('jsonb', { default: [] })
    fixedFr: string[];

    @Column('jsonb', { default: [] })
    removedEn: string[];

    @Column('jsonb', { default: [] })
    removedFr: string[];

    @Column('jsonb', { default: [] })
    thumbnails: IThumbnailImage[];

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @DeleteDateColumn()
    deletedAt: Date;
}

@Entity({
    name: 'whats_new_reactions',
})
export class WhatsNewReactionTable implements WhatsNewReactionEntity {
    @PrimaryGeneratedColumn('increment')
    id: number;

    @Column('varchar')
    emoji: string;

    @Index('idx_whats_new_reactions_user_id')
    @Column('varchar')
    userId: string;

    @Index('idx_whats_new_reactions_version_id')
    @Column('integer')
    versionId: number;

    @ManyToOne(() => WhatsNewVersionTable, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'versionId' })
    version: WhatsNewVersionTable;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @DeleteDateColumn()
    deletedAt: Date;
} 