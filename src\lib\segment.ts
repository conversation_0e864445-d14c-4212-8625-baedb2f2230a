import {
  AnalyticsBrowser,
  SegmentEvent,
  UserTraits,
  Options,
  EventProperties,
} from "@segment/analytics-next";

export const analytics = process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY
  ? AnalyticsBrowser.load({
      writeKey: process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY,
    })
  : null;

const isEnabled = !!process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY;

export const trackEvent = async (
  event: string,
  properties?: SegmentEvent["properties"],
  options?: SegmentEvent["options"],
) => {
  if (!analytics || !isEnabled) return;

  analytics.track(event, properties, options);
};

export const identifyUser = async (
  userId: string,
  traits?: UserTraits,
  options?: Record<string, unknown>,
  callback?: () => void,
) => {
  if (!analytics || !isEnabled) return;

  analytics.identify(userId, traits, options, callback);
};

export const trackPage = async (
  category?: string | object | null | undefined,
  name?: string | object,
  properties?: Options | EventProperties | Record<string, any>,
  options?: Options,
  callback?: () => void,
) => {
  if (!analytics || !isEnabled) return;

  analytics.page(category, name, properties, options, callback);
};

export const resetAnalytics = async () => {
  if (!analytics || !isEnabled) return;

  analytics.reset();
};
