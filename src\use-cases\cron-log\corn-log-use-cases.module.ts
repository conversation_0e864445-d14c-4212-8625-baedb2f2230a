import { Modu<PERSON> } from '@nestjs/common';
import { SlackModule } from '@services/notification';
import { DataServicesModule } from '@services/database';
import { CronLogUseCases } from './cron-log-use-cases.service';
import { SubscriptionUseCasesModule } from '../subscription';
import { CronLogFactory } from './cron-log.factory';
import { ProductDeliveryModule } from '../product-delivery';
import { ExchangeRateModule } from '../exchange-rate';
import { WebhooksUseCasesModule } from '../webhooks';

@Module({
  imports: [
    DataServicesModule,
    SubscriptionUseCasesModule,
    ProductDeliveryModule,
    ExchangeRateModule,
    SlackModule,
    WebhooksUseCasesModule,
  ],
  providers: [CronLogUseCases, CronLogFactory],
  exports: [CronLogFactory, CronLogUseCases],
})
export class CronLogUseCasesModule {}
