"use client";

import { useTranslations } from "next-intl";

import { But<PERSON> } from "@/components/base/button";
import useCalendarStore from "@/hooks/store/calendar";

export default function ChangeLayout() {
  const t = useTranslations("calendar");
  const { setLayout } = useCalendarStore();

  return (
    <div className="flex w-60 flex-row items-center justify-between gap-2">
      <Button variant="secondary" onClick={() => setLayout("day")}>
        {t("day")}
      </Button>
      <Button variant="secondary" onClick={() => setLayout("week")}>
        {t("week")}
      </Button>
      <Button variant="secondary" onClick={() => setLayout("month")}>
        {t("month")}
      </Button>
    </div>
  );
}
