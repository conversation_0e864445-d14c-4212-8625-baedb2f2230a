import { useState } from "react";

interface Program {
  id: string;
  name: string;
  description: string;
  rating: number;
  image: string;
  isActive: boolean;
}

// Initial mock data
const initialPrograms = [
  {
    id: "1",
    name: "Executive Leadership Coaching",
    description:
      "Master the art of leadership with our comprehensive program designed for aspiring and current executives. Develop strategic thinking, emotional intelligence, and decision-making skills.",
    rating: 9.2,
    image:
      "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=800&auto=format&fit=crop",
    isActive: true,
  },
  {
    id: "2",
    name: "Life Coach Certification",
    description:
      "Transform lives and start your coaching career. Learn proven methodologies, coaching frameworks, and client relationship management techniques.",
    rating: 9.4,
    image:
      "https://images.unsplash.com/photo-1552664730-d307ca884978?q=80&w=800&auto=format&fit=crop",
    isActive: true,
  },
  {
    id: "3",
    name: "Business Growth Accelerator",
    description:
      "Scale your business with confidence. Our program combines strategic planning, marketing optimization, and leadership development for entrepreneurs.",
    rating: 9.5,
    image:
      "https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=800&auto=format&fit=crop",
    isActive: false,
  },
  {
    id: "4",
    name: "Mindfulness & Wellness Coaching",
    description:
      "Help others achieve balance and well-being. Learn to coach mindfulness practices, stress management, and holistic wellness approaches.",
    rating: 9.1,
    image:
      "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?q=80&w=800&auto=format&fit=crop",
    isActive: true,
  },
  {
    id: "5",
    name: "Career Transition Mastery",
    description:
      "Guide clients through successful career changes. Develop expertise in career assessment, planning, and transition coaching strategies.",
    rating: 8.9,
    image:
      "https://images.unsplash.com/photo-1520333789090-1afc82db536a?q=80&w=800&auto=format&fit=crop",
    isActive: false,
  },
  {
    id: "6",
    name: "High Performance Coaching",
    description:
      "Learn to coach elite performers and athletes. Focus on peak performance, mental toughness, and goal achievement methodologies.",
    rating: 9.6,
    image:
      "https://images.unsplash.com/photo-1551632436-cbf8dd35adfa?q=80&w=800&auto=format&fit=crop",
    isActive: true,
  },
  {
    id: "7",
    name: "Relationship Coaching",
    description:
      "Help couples and individuals build stronger relationships. Master communication coaching, conflict resolution, and relationship dynamics.",
    rating: 8.8,
    image:
      "https://images.unsplash.com/photo-1529156069898-49953e39b3ac?q=80&w=800&auto=format&fit=crop",
    isActive: true,
  },
  {
    id: "8",
    name: "Digital Business Coaching",
    description:
      "Guide entrepreneurs in the digital age. Learn to coach online business strategy, digital marketing, and remote team management.",
    rating: 9.3,
    image:
      "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=800&auto=format&fit=crop",
    isActive: true,
  },
  {
    id: "9",
    name: "Financial Freedom Coaching",
    description:
      "Help clients achieve financial independence. Master coaching techniques for budgeting, investing, and wealth mindset development.",
    rating: 9.0,
    image:
      "https://images.unsplash.com/photo-1579532537598-459ecdaf39cc?q=80&w=800&auto=format&fit=crop",
    isActive: false,
  },
  {
    id: "10",
    name: "Personal Brand Development",
    description:
      "Guide professionals in building authentic personal brands. Focus on identity development, social presence, and thought leadership.",
    rating: 9.2,
    image:
      "https://images.unsplash.com/photo-1493612276216-ee3925520721?q=80&w=800&auto=format&fit=crop",
    isActive: true,
  },
];

export function usePrograms() {
  const [programs, setPrograms] = useState<Program[]>(initialPrograms);

  const createProgram = async (data: Partial<Program>) => {
    const newProgram: Program = {
      id: Math.random().toString(36).substring(7),
      name: data.name || "",
      description: data.description || "",
      rating: 0,
      image:
        data.image ||
        "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=800&auto=format&fit=crop",
      isActive: data.isActive || false,
    };

    setPrograms((prev) => [newProgram, ...prev]);
    return newProgram;
  };

  const deleteProgram = (id: string) => {
    setPrograms((prev) => prev.filter((program) => program.id !== id));
  };

  const duplicateProgram = (id: string) => {
    const programToDuplicate = programs.find((program) => program.id === id);
    if (!programToDuplicate) return;

    const newProgram = {
      ...programToDuplicate,
      id: Math.random().toString(36).substring(7),
      name: `${programToDuplicate.name} (Copy)`,
    };

    setPrograms((prev) => {
      const index = prev.findIndex((p) => p.id === id);
      const newPrograms = [...prev];
      newPrograms.splice(index + 1, 0, newProgram);
      return newPrograms;
    });
  };

  return {
    programs,
    createProgram,
    deleteProgram,
    duplicateProgram,
  };
}
