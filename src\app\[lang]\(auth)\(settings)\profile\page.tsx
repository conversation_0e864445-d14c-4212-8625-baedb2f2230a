import { CloudAlert, CreditCardIcon, Shield } from "lucide-react";
import { getTranslations } from "next-intl/server";
import { getCustomerAccessDetails } from "@/services/customers/server";
import LogoutButton from "@/components/base/(auth)/LogoutButton";
import UpdateAvatar from "@/components/base/profile/UpdateAvatar";
import SettingCard from "@/components/base/profile/SettingCard";

export default async function Page() {
  const t = await getTranslations("profile");
  const user = await getCustomerAccessDetails();

  const settings = [
    {
      title: t("settings.payments.title"),
      subTitle: t("settings.payments.sub-title"),
      href: "/payments",
      icon: <CreditCardIcon />,
    },
    {
      title: t("settings.account.title"),
      subTitle: t("settings.account.sub-title"),
      href: "/account",
      icon: <Shield />,
    },
    {
      title: t("settings.whats-new.title"),
      subTitle: t("settings.whats-new.sub-title"),
      href: "/whats-new",
      icon: <CloudAlert />,
    },
  ];

  return (
    <div className="flex flex-col gap-4 px-4 pt-6">
      <h1 className="text-center text-3xl font-bold">{t("title")}</h1>
      <UpdateAvatar />
      <div className="grid grid-cols-1 px-4 md:grid-cols-2 md:gap-4 lg:grid-cols-3">
        {settings.map((setting) => (
          <SettingCard
            key={setting.title}
            title={setting.title}
            subTitle={setting.subTitle}
            icon={setting.icon}
            href={setting.href}
          />
        ))}
      </div>
      <div className="mt-8 flex justify-center text-gray-400">{user?.email}</div>
      <div className="flex justify-center">
        <LogoutButton />
      </div>
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "profile" });
  return {
    title: t("title"),
    description: t("sub-title"),
  };
}
