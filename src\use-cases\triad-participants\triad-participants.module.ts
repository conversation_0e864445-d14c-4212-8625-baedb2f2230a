import { DataServicesModule } from '@services/database';
import { Module } from '@nestjs/common';
import { TriadParticipantsUseCases } from './triad-participants.use-cases';
import { TriadParticipantsFactory } from './triad-participants.factory';
import { UserModule } from '../user';
@Module({
  imports: [DataServicesModule, UserModule],
  providers: [TriadParticipantsUseCases, TriadParticipantsFactory],
  exports: [TriadParticipantsUseCases, TriadParticipantsFactory],
})
export class TriadParticipantsModule {}
