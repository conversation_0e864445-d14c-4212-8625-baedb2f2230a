import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * This migration updates the user_access_purchases_view to use the new users table
 * instead of the customers table. The whole migration is the same as the previous one,
 * but with the new users table and with comments updated to reflect the new table names.
 */
export class createUserAccessPurchasesView1747045317807
  implements MigrationInterface
{
  name = 'create-user-access-purchases-view1747045317807';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
-- Drop existing views if they exist
DROP VIEW IF EXISTS user_access_purchases_view;
DROP MATERIALIZED VIEW IF EXISTS user_access_purchases_view;
DROP INDEX IF EXISTS idx_user_access_purchases_email;

/*
 * USER ACCESS PURCHASES VIEW
 * 
 * PURPOSE:
 * This view provides a real-time, queryable representation of user access information
 * by combining subscription data with product and offer details, as well as direct access
 * grants from the customerAccesses table. It's used primarily by the getUserAccessDetails 
 * endpoint to determine user access rights and product entitlements.
 *
 * REAL-TIME ACCESS:
 * As a standard view (not materialized), this view always returns current data reflecting
 * the latest changes in subscriptions, products, and offers. This ensures clients can see
 * their purchases immediately without waiting for view refresh operations.
 * 
 * KEY COMPONENTS:
 * 1. User Identification: Email address used as the primary lookup key
 * 2. Subscription Data: Status, payment details, metadata, etc.
 * 3. Product Details: Information from associated products through subscription items
 * 4. Offer Details: Information from product offers linked to subscriptions
 * 5. Customer Accesses: Direct access grants from the customerAccesses table
 * 6. Access Flags: has_pxl and has_pxs determine user access levels based on product lines
 * 7. Content Access: LMS course IDs and community IDs determine what content user can access
 * 
 * DEPENDENCIES:
 * - subscriptions table (primary data source)
 * - productOffers table (offer details)
 * - products, productPlans, productPlanPrices tables (product details)
 * - productFamilies and productLines tables (for product line determination)
 * - transactions table (for payment data)
 * - users and customerAccesses tables (for direct access grants)
 * 
 * USAGE:
 * Most commonly queried by: SELECT * FROM user_access_purchases_view WHERE "userEmail" = ?
 * 
 * PERFORMANCE NOTES:
 * - As a standard view, each query computes results in real-time
 * - Consider adding appropriate indexes on the underlying tables for better performance
 * - For very high query volumes, consider query caching at the application level
 */
CREATE VIEW user_access_purchases_view AS
WITH user_data AS (
    -- Get user ID for later joining with customerAccesses
    SELECT 
        id AS user_id,
        email AS user_email
    FROM 
        users
    WHERE 
        "deletedAt" IS NULL
),
customer_accesses AS (
    -- Aggregate all valid access grants by user and type
    SELECT 
        u.user_email,
        ca.type,
        jsonb_agg(ca.details) AS access_details,
        bool_or(ca.status = 'granted') AS has_valid_access
    FROM 
        "customerAccesses" ca
    JOIN 
        user_data u ON ca."customerId" = u.user_id
    WHERE 
        ca."deletedAt" IS NULL
        AND ca.status = 'granted'
        AND (ca."expiresAt" IS NULL OR ca."expiresAt" > NOW())
    GROUP BY 
        u.user_email, ca.type
),
subscription_products AS (
    SELECT 
        -- USER IDENTIFICATION
        s."customerEmail" AS "userEmail",               -- Primary lookup key
        
        -- SUBSCRIPTION CORE DATA
        s."chargebeeId" AS "subscriptionId",            -- Unique subscription identifier
        s.status AS "subscriptionStatus",               -- Current subscription status
        (s."customFields"->>'cf_is_forever')::boolean AS "subscriptionCfIsForever",  -- Whether subscription never expires
        s."nextBillingAt" AS "subscriptionNextBillingAt",         -- Next billing timestamp
        s."cancelledAt" AS "subscriptionCancelledAt",              -- When subscription was cancelled
        s."cancelScheduleCreatedAt" AS "subscriptionCancelScheduleCreatedAt",  -- When cancel was scheduled
        s.mrr AS "subscriptionMrr",                     -- Monthly recurring revenue
        s.metadata AS "subscriptionMetaData",          -- Additional subscription metadata (includes includesRecommended flag)
        s."amountPaid" AS "subscriptionAmountPaid",    -- Total amount paid for subscription
        s."startedAt" AS "subscriptionStartedAt",      -- When subscription started
        
        -- OFFER DISPLAY DATA
        po."chargebeeId" AS "offerChargebeeId",         -- Offer identifier
        po."bannerImage" AS "offerBannerImage",         -- Banner image URL for UI
        po."cardImage" AS "offerCardImage",             -- Card image URL for UI
        po.description AS "offerDescription",           -- Offer description text
        po.name AS "offerName",                         -- Offer name for display
        po.usp AS "offerUsp",                           -- Unique selling proposition text
        s."orderStatus"::text AS "offerOrderStatus",    -- Status used for displaying UI elements
        
        -- LMS COURSE ACCESS - Prioritize customer direct access, then offer data, then product data
        -- Used to determine which courses user can access in the LMS
        CASE
            -- First priority: Direct LMS access from customerAccesses
            WHEN ca_lms.has_valid_access IS TRUE THEN 
                jsonb_build_object(
                    'products', (
                        SELECT jsonb_agg(detail->'courseId')
                        FROM jsonb_array_elements(ca_lms.access_details) AS detail
                        WHERE detail ? 'courseId'
                    ),
                    'recommended', '[]'::jsonb
                )
            
            -- Second priority: Offer LMS IDs 
            WHEN po."lmsIds" IS NOT NULL THEN 
                po."lmsIds"
                
            -- Third priority: Product LMS IDs
            WHEN p.id IS NOT NULL AND p."lmsIds" IS NOT NULL AND jsonb_array_length(p."lmsIds") > 0 THEN 
                jsonb_build_object(
                    'products', p."lmsIds",            -- Put product lmsIds into products array
                    'recommended', '[]'::jsonb         -- Empty recommended array for product sources
                )
                
            -- No LMS access
            ELSE NULL
        END AS "offerLmsIds",
        
        -- COMMUNITY ACCESS - Prioritize user direct access, then combine offer and product community data
        -- Used to determine which community spaces user can access
        CASE
            -- First priority: Direct community access from customerAccesses
            WHEN ca_community.has_valid_access IS TRUE THEN 
                jsonb_build_object(
                    'products', (
                        SELECT jsonb_agg(detail->'spaceId')
                        FROM jsonb_array_elements(ca_community.access_details) AS detail
                        WHERE detail ? 'spaceId'
                    ),
                    'recommended', '[]'::jsonb
                )
                
            -- When both offer and product have communities data
            WHEN po."communityIds" IS NOT NULL AND p.id IS NOT NULL AND p."communityIds" IS NOT NULL
            THEN 
                -- Handle offer with standard products/recommended structure and product with array structure
                jsonb_build_object(
                    'products', 
                    (
                        SELECT jsonb_agg(community)
                        FROM (
                            -- Get communities from offer's products array
                            SELECT community
                            FROM jsonb_array_elements(COALESCE(po."communityIds"->'products', '[]'::jsonb)) AS community
                            
                            UNION
                            
                            -- Get communities directly from product's communityIds array
                            SELECT community
                            FROM jsonb_array_elements(p."communityIds") AS community
                        ) AS combined_communities
                    ),
                    'recommended', COALESCE(po."communityIds"->'recommended', '[]'::jsonb)
                )
            
            -- When only offer has communities data
            WHEN po."communityIds" IS NOT NULL
            THEN po."communityIds"
            
            -- When only product has communities data
            WHEN p.id IS NOT NULL AND p."communityIds" IS NOT NULL
            THEN jsonb_build_object(
                'products', p."communityIds",
                'recommended', '[]'::jsonb
            )
            
            -- Neither has communities data
            ELSE '{"products":[],"recommended":[]}'::jsonb
        END AS "offerCommunityIds",
        
        -- PRODUCT LINE INFORMATION - Used to identify product category
        -- Tries multiple sources to determine the product line
        CASE
            WHEN pl.name IS NOT NULL THEN pl.name
            WHEN s."customFields"->>'cf_product_line' IS NOT NULL THEN s."customFields"->>'cf_product_line'
            ELSE NULL
        END AS "cfProductLine",
        
        -- PXL ACCESS FLAG - Determines if user has PXL access
        -- This directly impacts the isPXL flag in the user's accessInfo
        CASE 
            WHEN EXISTS (
                -- Check if any subscription item is linked to a product with PXL product line
                SELECT 1 
                FROM jsonb_array_elements(s."subscriptionItems") as si
                JOIN "productPlanPrices" pp ON si->>'item_price_id' = pp."chargebeeId"
                JOIN "productPlans" plan ON pp."productPlanId" = plan.id
                JOIN products p ON plan."productId" = p.id
                JOIN "productFamilies" pf ON p."productFamilyId" = pf.id
                JOIN "productLines" pl ON pf."productLineId" = pl.id
                WHERE pl.name = 'PXL'
            ) OR s."customFields"->>'cf_product_line' = 'PXL'
            THEN true
            ELSE false
        END AS "hasPxl",
        
        -- PXS ACCESS FLAG - Determines if user has PXS access
        -- This directly impacts the isPXS flag in the user's accessInfo
        CASE 
            WHEN EXISTS (
                -- Check if any subscription item is linked to a product with PXS product line
                SELECT 1 
                FROM jsonb_array_elements(s."subscriptionItems") as si
                JOIN "productPlanPrices" pp ON si->>'item_price_id' = pp."chargebeeId"
                JOIN "productPlans" plan ON pp."productPlanId" = plan.id
                JOIN products p ON plan."productId" = p.id
                JOIN "productFamilies" pf ON p."productFamilyId" = pf.id
                JOIN "productLines" pl ON pf."productLineId" = pl.id
                WHERE pl.name = 'PXS'
            ) OR s."customFields"->>'cf_product_line' = 'PXS'
            THEN true
            ELSE false
        END AS "hasPxs",
        
        -- PAYMENT INFORMATION
        t.date AS "lastPaymentDate"                    -- Last successful payment date
        
    FROM
        subscriptions s
    
    -- OFFER DATA JOIN
    -- Get product offer information linked through custom field
    LEFT JOIN "productOffers" po ON po."chargebeeId" = s."customFields"->>'cf_os_offer_id'
    
    -- PRODUCT DATA JOIN (LATERAL)
    -- Get first product associated with this subscription through subscription items
    LEFT JOIN LATERAL (
        SELECT 
            p.id, p."lmsIds", p."communityIds", pf.id AS family_id, pl.name AS line_name
        FROM 
            jsonb_array_elements(s."subscriptionItems") AS si             -- Unnest subscription items array
            JOIN "productPlanPrices" pp ON si->>'item_price_id' = pp."chargebeeId"  -- Link to price
            JOIN "productPlans" plan ON pp."productPlanId" = plan.id       -- Link to plan
            JOIN products p ON plan."productId" = p.id                     -- Link to product
            LEFT JOIN "productFamilies" pf ON p."productFamilyId" = pf.id  -- Link to product family
            LEFT JOIN "productLines" pl ON pf."productLineId" = pl.id      -- Link to product line
        WHERE 
            p.id IS NOT NULL
        LIMIT 1                                        -- Only get first matched product
    ) AS p(id, "lmsIds", "communityIds", family_id, line_name) ON true
    
    -- PRODUCT LINE INFO
    -- Direct join to product lines table using the line name from product
    LEFT JOIN "productLines" pl ON pl.name = p.line_name
    
    -- PAYMENT DATA (LATERAL)
    -- Get the most recent successful payment for this subscription
    LEFT JOIN LATERAL (
        SELECT t.date 
        FROM transactions t 
        WHERE t."subscriptionId" = s."chargebeeId"     -- Match to subscription
        AND t.type = 'payment'                         -- Only payment transactions
        AND t.status = 'success'                       -- Only successful transactions
        ORDER BY t.date DESC                           -- Get most recent
        LIMIT 1
    ) t ON true
    
    -- CUSTOMER ACCESS DATA - LMS
    -- Get direct access grants for LMS courses
    LEFT JOIN customer_accesses ca_lms 
    ON s."customerEmail" = ca_lms.user_email 
    AND ca_lms.type = 'lms'
    
    -- CUSTOMER ACCESS DATA - COMMUNITY
    -- Get direct access grants for community spaces
    LEFT JOIN customer_accesses ca_community 
    ON s."customerEmail" = ca_community.user_email 
    AND ca_community.type = 'community'
    
    WHERE
        s."deletedAt" IS NULL                          -- Only active (non-deleted) subscriptions
)
SELECT * FROM subscription_products;
        `);

    await queryRunner.query(`
-- Create an index on the email column for faster lookups
-- Note: For standard views, we need to use indexes on underlying tables instead
-- Ensure user_email (customerEmail in the subscriptions table) is indexed
CREATE INDEX IF NOT EXISTS idx_subscriptions_customer_email ON subscriptions ("customerEmail");
-- Add index for user email for our new joins
CREATE INDEX IF NOT EXISTS idx_users_email ON users ("email");
-- Add index for customer accesses by user ID
CREATE INDEX IF NOT EXISTS idx_customer_accesses_user_id ON "customerAccesses" ("customerId");
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_subscriptions_customer_email;`,
    );
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_email;`);
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_customer_accesses_user_id;`,
    );
    await queryRunner.query(`DROP VIEW IF EXISTS user_access_purchases_view;`);
  }
}
