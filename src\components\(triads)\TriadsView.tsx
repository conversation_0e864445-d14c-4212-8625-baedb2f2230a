"use client";

import { useState, useEffect } from "react";
import { TriadsList } from "./TriadsList";
import { TriadsTable } from "./TriadsTable";
import { CreateTriadDialog } from "./CreateTriadDialog";
import { FilterDialog } from "./FilterDialog";
import { SearchAccordion } from "./SearchAccordion";
import { TriadViewSkeleton } from "./shared/TriadSkeletons";
import { TriadMobileHeader, TriadDesktopHeader } from "./shared/TriadHeader";
import { useTranslations } from "next-intl";
import { useAuth } from "@clerk/nextjs";
import { motion, AnimatePresence } from "framer-motion";
import { TestimonialForm } from "./TestimonialForm";
import { useCustomerAccessDetails } from "@/services/customers";
import { useBreakpoints } from "@/hooks/breakpoints";
import { useTriadList } from "@/services/triad";
import { useTriadFiltering } from "@/hooks/useTriadFiltering";
import {
  containerVariants,
  searchAccordionVariants,
  contentSwitchVariants,
} from "@/constants/triadAnimations";

type TriadsViewProps = {
  showSessionTypeFilter: boolean;
};

export function TriadsView({ showSessionTypeFilter }: TriadsViewProps) {
  const t = useTranslations("triad");
  const { userId } = useAuth();

  // Use the updated hook that automatically handles impersonation
  const { data: accessData, isLoading: isAccessLoading, email } = useCustomerAccessDetails();

  const [mounted, setMounted] = useState(false);
  const [createOpen, setCreateOpen] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [isReloading, setIsReloading] = useState(false);
  const [showTestimonial, setShowTestimonial] = useState(true);

  const { triads, fetchTriads, isLoading } = useTriadList();
  const size = useBreakpoints();
  const isMobile = size === "xs" || size === "sm" || size === "md";
  const isParadox = accessData?.accessInfo.isParadox;

  // Use custom filtering hook
  const {
    searchQuery,
    setSearchQuery,
    isFilterReady,
    localizedFilterSteps,
    filterValues,
    stepsState,
    handleFilterChange,
    isStepEnabled,
    clearAll,
    filteredTriads,
  } = useTriadFiltering({
    triads: triads || [],
    showSessionTypeFilter,
    userId: userId || undefined,
    t,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleReload = async () => {
    setIsReloading(true);
    try {
      await fetchTriads();
    } finally {
      // Wait for 1 second before setting isReloading to false
      setTimeout(() => {
        setIsReloading(false);
      }, 1000);
    }
  };

  const handleSearchToggle = () => {
    setSearchOpen(!searchOpen);
    setFilterOpen(false);
  };

  const handleCreateOpen = () => {
    setCreateOpen(true);
    setSearchOpen(false);
  };

  // Check if user has already submitted a high-rated testimonial
  useEffect(() => {
    const checkTestimonial = async () => {
      try {
        // Testimonial check logic commented out
        // const response = await fetch("/api/testimonials?tool=TRIADS");
        // const { testimonial } = await response.json();
        // if (testimonial && testimonial.rating > 3) {
        //   setShowTestimonial(false);
        // }
      } catch (error) {
        // console.error("Failed to check testimonial:", error);
      }
    };

    // checkTestimonial();
  }, []);

  // Don't render until mounted
  if (!mounted) {
    return null;
  }

  if (isAccessLoading || !accessData || !userId || !email) {
    return <TriadViewSkeleton isMobile={isMobile} showSessionType={showSessionTypeFilter} />;
  }

  const headerProps = {
    triadsCount: filteredTriads?.length || 0,
    isReloading,
    isParadox: !!isParadox,
    searchOpen,
    searchQuery,
    isFilterReady,
    filterSteps: localizedFilterSteps,
    filterValues,
    stepsState,
    onReload: handleReload,
    onSearchToggle: handleSearchToggle,
    onSearchChange: setSearchQuery,
    onFilterChange: handleFilterChange,
    onCreateOpen: handleCreateOpen,
    isStepEnabled,
    clearAll,
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-4"
    >
      {isMobile ? <TriadMobileHeader {...headerProps} /> : <TriadDesktopHeader {...headerProps} />}

      <AnimatePresence mode="wait">
        {isMobile && (
          <motion.div
            variants={searchAccordionVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ duration: 0.2 }}
          >
            <SearchAccordion
              open={searchOpen}
              onOpenChange={setSearchOpen}
              value={searchQuery}
              onChange={setSearchQuery}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence mode="wait">
        <motion.div
          key={isMobile ? "list" : "table"}
          variants={contentSwitchVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          transition={{ duration: 0.2 }}
        >
          {isMobile ? (
            <TriadsList
              triads={filteredTriads}
              triadsReady={!isAccessLoading && !isLoading}
              accessInfo={accessData.accessInfo}
              userEmail={email}
              userId={userId}
            />
          ) : (
            <TriadsTable
              accessInfo={accessData.accessInfo}
              triads={filteredTriads}
              triadsReady={!isAccessLoading && !isLoading}
            />
          )}
        </motion.div>
      </AnimatePresence>

      <CreateTriadDialog open={createOpen} onOpenChange={setCreateOpen} />
      <FilterDialog open={filterOpen} onOpenChange={setFilterOpen} />
      {showTestimonial && (
        <TestimonialForm
          afterSubmit={() => {
            setShowTestimonial(false);
          }}
        />
      )}
    </motion.div>
  );
}
