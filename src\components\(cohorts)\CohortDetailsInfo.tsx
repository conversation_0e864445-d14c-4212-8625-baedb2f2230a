"use client";

import { useTranslations } from "next-intl";
import Score from "../../app/[lang]/(auth)/user-management/users/components/Score";

const KeyValue = ({ label, value }: { label: string; value: string | React.ReactNode }) => {
  return (
    <div className="flex flex-col">
      <div className="text-sm font-bold text-[#6e6e6e]">{label}</div>
      <div className="text-sm">{value}</div>
    </div>
  );
};

export default function CohortDetailsInfo() {
  const t = useTranslations("cohorts.details-page");

  return (
    <div className="w-full">
      <div className="pb-4 text-2xl font-bold">{t("info.details-title")}</div>
      <div className="rounded-2xl bg-[#171717] px-8 pt-8">
        <div className="flex flex-row gap-16 pb-8">
          <div className="flex flex-col">
            <KeyValue label={t("info.start")} value="TO DO" />
          </div>
          <div className="flex flex-col">
            <KeyValue label={t("info.end")} value="TO DO" />
          </div>
          <div className="flex flex-col">
            <KeyValue label={t("info.duration")} value="TO DO" />
          </div>
          <div className="flex flex-col">
            <KeyValue label={t("info.status")} value="TO DO" />
          </div>
          <div className="flex flex-col">
            <KeyValue label={t("info.seats")} value="TO DO" />
          </div>
        </div>
        <div className="flex flex-row pb-8">
          <div className="flex flex-col">
            <KeyValue
              label={t("info.score")}
              value={
                <div className="pt-2">
                  <Score score={0} />
                </div>
              }
            />
          </div>
        </div>
        <div className="flex flex-row pb-8">
          <div className="flex flex-col">
            <KeyValue label={t("info.description")} value="TO DO" />
          </div>
        </div>
      </div>
    </div>
  );
}
