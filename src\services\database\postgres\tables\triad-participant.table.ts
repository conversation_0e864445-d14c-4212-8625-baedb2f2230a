import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';
import { TriadParticipantEntity } from '@entities';
import { UserTable } from './user.table';
import { TriadTable } from './triad.table';

@Entity('triadParticipants')
@Unique(['user', 'triad'])
export class TriadParticipantTable implements TriadParticipantEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Index('idx_triadParticipants_userId')
  @ManyToOne(() => UserTable, (user) => user.triadParticipations)
  @JoinColumn({ name: 'userId' })
  user: UserTable;

  @ManyToOne(() => TriadTable, (triad) => triad.participants, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'triadId' })
  triad: TriadTable;

  @Column({ type: 'timestamp' })
  joinedAt: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt: Date;
}
