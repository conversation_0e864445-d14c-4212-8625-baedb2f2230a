import { z } from "zod";

export const PaginationParamsSchema = z.object({
  limit: z.coerce.number().min(1).max(100).optional(),
  page: z.coerce.number().min(1).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});
export type IPaginationParams = z.infer<typeof PaginationParamsSchema>;

export interface UpdateResultWithItemInfo<T> {
  success: boolean;
  updatedItem: T;
}

export const PXActionResultSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  error: z.string().optional(),
});
export type PXActionResult = z.infer<typeof PXActionResultSchema>;
