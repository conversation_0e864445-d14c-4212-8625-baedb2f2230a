import { Dialog<PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { useFormContext } from "react-hook-form";
import { StepConfig } from "../types";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ModalHeaderProps {
  title: string;
  currentStep: number;
  totalSteps: number;
  onClose: () => void;
  steps: StepConfig[];
}

export const ModalHeader = ({
  title,
  currentStep,
  totalSteps,
  onClose,
  steps,
}: ModalHeaderProps) => {
  const {
    formState: { errors },
    getValues,
  } = useFormContext();

  // Check if a step has errors
  const hasStepErrors = (stepIndex: number) => {
    const stepFields = steps[stepIndex].fields.map((f) => f.id);
    return Object.keys(errors).some((errorKey) => stepFields.includes(errorKey));
  };

  // Check if a step is validated (all required fields are filled and no errors)
  const isStepValidated = (stepIndex: number) => {
    const step = steps[stepIndex];
    const values = getValues();

    // Check if all required fields are filled and have no errors
    return step.fields.every((field) => {
      if (!field.required) return true;
      const value = values[field.id];
      return value !== undefined && value !== "" && !errors[field.id];
    });
  };

  return (
    <DialogHeader className="space-y-4">
      <div className="flex items-center justify-between">
        <DialogTitle>{title}</DialogTitle>
        <Button type="button" variant="ghost" size="icon" className="h-8 w-8" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>
      <div className="flex justify-center space-x-2">
        {Array.from({ length: totalSteps }).map((_, index) => {
          const isActive = index === currentStep;
          const hasError = hasStepErrors(index);
          const isValidated = isStepValidated(index);
          const isFuture = index > currentStep;

          // If there is only one step, don't show the step indicator
          if (totalSteps === 1) {
            return null;
          }

          return (
            <div
              key={index}
              className={`h-2 w-12 rounded-full transition-colors ${
                isActive
                  ? "bg-primary"
                  : hasError
                    ? "bg-destructive"
                    : isValidated
                      ? "bg-emerald-500"
                      : isFuture
                        ? "bg-zinc-300"
                        : "bg-muted"
              }`}
            />
          );
        })}
      </div>
    </DialogHeader>
  );
};
