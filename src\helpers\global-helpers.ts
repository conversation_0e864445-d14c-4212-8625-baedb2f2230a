import axios, {
  AxiosBasicCredentials,
  AxiosRequestConfig,
  isAxiosError,
} from 'axios';
import _ from 'lodash';
import { Injectable } from '@nestjs/common';
import { PXActionResult, RequestMethods } from '@types';

@Injectable()
export class GlobalHelpers {
  private static readonly pxDomains = [
    'paradoxgroup.com',
    'paradox-ext.com',
    'paradoxinstitute.com',
    'paradoxfoundation.com',
    'paradox.io',
    'paradox.media',
    'davidlaroche.fr',
    'larocheproduction.com',
    'davidlarocheworld.com',
  ];

  /**
   * Makes an HTTP request using Axios library
   */
  static async makeAxiosRequest(
    url: string,
    method: RequestMethods,
    auth?: AxiosBasicCredentials,
    headers?: Record<string, string>,
    data?: Record<string, any> | string,
  ): Promise<any> {
    const axiosRequestConfig: AxiosRequestConfig = {
      method,
      url,
      headers,
      data,
      auth,
    };
    try {
      const { data: responseData } = await axios(axiosRequestConfig);
      return responseData;
    } catch (err) {
      if (isAxiosError(err)) throw err;
      throw new Error(String(err));
    }
  }

  /**
   * Constructs an error response object from an Axios error
   * @param err Error object
   * @returns `PXActionResult`
   */
  static constructErrorResponse(err: any): PXActionResult {
    if (isAxiosError(err)) {
      const message =
        err?.response?.data?.error ||
        err?.response?.data?.message ||
        err?.message;
      return {
        success: false,
        message,
      };
    }
    return { success: false, message: err?.message };
  }

  static getRandomString(): string {
    return (
      (Math.floor(Date.now() / 1000) % 1000000) +
      Math.floor(Math.random() * 1000000)
    )
      .toString(36)
      .toUpperCase();
  }

  /**
   * Finds difference between two objects and returns the keys that changed
   * @param object1 First object to be tested
   * @param object2 Second object to be tested
   * @param prefix Optional and can be an empty string, used to keep track of deeply nested keys
   * @returns An array of keys (strings) which are the properties that changed
   */
  static findDifference(
    object1: any,
    object2: any,
    prefix = '',
    changedKeys: string[] = [],
  ): string[] {
    const keys = _.union(_.keys(object1), _.keys(object2));

    for (const key of keys) {
      const fullKey = prefix ? `${prefix}.${key}` : key;

      if (!_.has(object1, key)) {
        changedKeys.push(fullKey);
      } else if (!_.has(object2, key)) {
        changedKeys.push(fullKey);
      } else if (!_.isEqual(object1[key], object2[key])) {
        if (_.isObject(object1[key]) && _.isObject(object2[key])) {
          changedKeys = changedKeys.concat(
            this.findDifference(object1[key], object2[key], fullKey),
          );
        } else {
          changedKeys.push(fullKey);
        }
      }
    }

    return changedKeys;
  }

  /**
   * Checks if the provided email is a Paradox email
   * @param email Email address to check
   * @returns A boolean indicating whether the email is a Paradox email
   */
  static isParadoxUser(email: string): boolean {
    return GlobalHelpers.pxDomains.includes(email?.split('@')?.[1]);
  }

  /**
   * Converts a number of cents to a decimal string
   * @param cents Number of cents to convert
   * @returns A string representing the number of cents in decimal format
   */
  static centsToDecimal(cents: number): string {
    const str = cents.toString();
    const len = str.length;
  
    if (len <= 2) {
      // Pad with leading zeros if less than 3 digits
      return '0.' + str.padStart(2, '0');
    }
  
    // Insert decimal point 2 digits from the right
    return str.slice(0, -2) + '.' + str.slice(-2);
  }
}
