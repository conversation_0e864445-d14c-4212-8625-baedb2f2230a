import { ProductPlan } from '.';
import { FiscalEntity } from '@enums';

export class ProductPlanPrice {
  id: number;
  internalName: string;
  externalName: string;
  description: string;
  status: string;
  pricingModel: string;
  amountPerBillingCycle: number;
  amount: number;
  period: number;
  periodUnit: string;
  currencyCode: string;
  totalBillingCycles?: number;
  productPlan: ProductPlan;
  chargebeeId: string;
  fiscalEntity?: FiscalEntity;
  createdAt?: Date;
}
