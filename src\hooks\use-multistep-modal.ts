"use client";

import { Step } from "@/components/base/multistep-modal";
import { useState, useCallback } from "react";

interface UseMultiStepModalProps<T> {
  steps: Step<T>[];
  initialData: T;
  onComplete: (data: T) => void;
}

export function useMultiStepModal<T>({
  steps,
  initialData,
  onComplete,
}: UseMultiStepModalProps<T>) {
  const [currentStep, setCurrentStep] = useState(0);
  const [modalData, setModalData] = useState<T>(initialData);
  const [isLoading, setIsLoading] = useState(false);

  const updateData = useCallback((newData: Partial<T>) => {
    setModalData((prev) => ({ ...prev, ...newData }));
  }, []);

  const nextStep = useCallback(async () => {
    if (currentStep < steps.length - 1) {
      const currentStepConfig = steps[currentStep];

      if (currentStepConfig.onNext) {
        setIsLoading(true);
        try {
          const canProceed = await currentStepConfig.onNext(modalData);
          if (canProceed === false) {
            return;
          }
        } catch (error) {
          console.error("Error in step validation:", error);
          return;
        } finally {
          setIsLoading(false);
        }
      }

      setCurrentStep((prev) => prev + 1);
    } else {
      onComplete(modalData);
    }
  }, [currentStep, steps, modalData, onComplete]);

  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  const resetModal = useCallback(() => {
    setCurrentStep(0);
    setModalData(initialData);
    setIsLoading(false);
  }, [initialData]);

  return {
    currentStep,
    modalData,
    isLoading,
    updateData,
    nextStep,
    prevStep,
    resetModal,
  };
}
