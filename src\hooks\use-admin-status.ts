"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { checkCurrentUserAdminStatus } from "@/app/actions/auth-actions";

/**
 * Hook to check if the current user is a Paradox admin
 * Uses the server-side checkCurrentUserAdminStatus function
 *
 * @returns Object with isAdmin boolean and loading state
 */
export function useAdminStatus() {
  const { user, isLoaded } = useUser();
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    let isMounted = true;

    async function checkAdminStatus() {
      if (!isLoaded || !user) {
        if (isMounted) {
          setIsAdmin(false);
          setIsLoading(false);
        }
        return;
      }

      try {
        setIsLoading(true);
        const adminStatus = await checkCurrentUserAdminStatus();

        if (isMounted) {
          setIsAdmin(adminStatus);
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Error checking admin status:", error);
        if (isMounted) {
          setIsAdmin(false);
          setIsLoading(false);
        }
      }
    }

    checkAdminStatus();

    return () => {
      isMounted = false;
    };
  }, [user, isLoaded]);

  return { isAdmin, isLoading };
}
