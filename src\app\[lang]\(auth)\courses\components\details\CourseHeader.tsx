"use client";

import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { IBaseCourse } from "@px-shared-account/hermes";
import { StatusBadge } from "@/components/base/courses/status-badge";
import { usePublishCourse, useArchiveCourse, useDuplicateCourse } from "@/services/course";
import { useRouter } from "next/navigation";
import { EditCourseModal } from "../modals/edit/EditCourseModal";
import { useState } from "react";
import { Archive, ArrowUpRight, Copy, Pencil } from "lucide-react";
import { useBreakpoint } from "@/lib/hooks/tailwind-breakpoints";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

interface CourseHeaderProps {
  course: IBaseCourse;
}

export function CourseHeader({ course }: CourseHeaderProps) {
  const t = useTranslations("courses");
  const tStatus = useTranslations("courses");
  const router = useRouter();
  const { publishCourse, isLoading: isPublishing } = usePublishCourse(course.id);
  const { archiveCourse, isLoading: isArchiving } = useArchiveCourse(course.id);
  const { duplicateCourse, isLoading: isDuplicating } = useDuplicateCourse(course.id);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const isDesktop = useBreakpoint("lg");

  const handlePublish = async () => {
    try {
      await publishCourse();
      // Refresh the page to show updated status
      router.refresh();
    } catch (error) {
      // Error is handled by the hook
      console.error("Failed to publish course:", error);
    }
  };

  const handleArchive = async () => {
    try {
      await archiveCourse();
      // Refresh the page to show updated status
      router.refresh();
    } catch (error) {
      // Error is handled by the hook
      console.error("Failed to archive course:", error);
    }
  };

  const handleDuplicate = async () => {
    try {
      await duplicateCourse();
      // Refresh the page to show the updated list
      router.push("/courses");
    } catch (error) {
      // Error is handled by the hook
      console.error("Failed to duplicate course:", error);
    }
  };

  const handleEditComplete = async () => {
    setIsEditModalOpen(false);
    router.refresh();
  };

  return (
    <>
      <div className="space-y-4">
        <BreadcrumbSettings active={`/courses/${course.id}`} />

        {/* Header content */}
        <div className="flex justify-between gap-4">
          <div className="flex flex-col items-start gap-2 md:flex-row md:items-center">
            <h1 className="text-3xl font-semibold tracking-tight">{course.name}</h1>
            <StatusBadge status={tStatus(`status.${course.status?.toLowerCase() || "draft"}`)} />
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            {course.status === "PUBLISHED" ? (
              <Button
                variant="secondary"
                size={isDesktop ? "sm" : "icon"}
                className="rounded-full p-5"
                onClick={handleArchive}
                disabled={isArchiving}
              >
                <Archive className="h-4 w-4" />
                <span className="hidden lg:block">{t("actions.archive")}</span>
              </Button>
            ) : (
              <Button
                variant="secondary"
                size={isDesktop ? "sm" : "icon"}
                className="rounded-full p-5"
                onClick={handlePublish}
                disabled={isPublishing}
              >
                <ArrowUpRight className="h-4 w-4" />
                <span className="hidden lg:block">{t("actions.publish.label")}</span>
              </Button>
            )}
            <Button
              variant="secondary"
              size={isDesktop ? "sm" : "icon"}
              className="rounded-full p-5"
              onClick={handleDuplicate}
              disabled={isDuplicating}
            >
              <Copy className="h-4 w-4" />
              <span className="hidden lg:block">{t("actions.duplicate")}</span>
            </Button>
            <Button
              variant="secondary"
              size={isDesktop ? "sm" : "icon"}
              className="rounded-full p-5"
              onClick={() => setIsEditModalOpen(true)}
            >
              <Pencil className="h-4 w-4" />
              <span className="hidden lg:block">{t("actions.edit")}</span>
            </Button>
          </div>
        </div>
      </div>

      <EditCourseModal
        course={course}
        isOpen={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        onComplete={handleEditComplete}
      />
    </>
  );
}
