import {
  CohortStatus,
  ICohortBase,
  ICreateCohort,
  IListCohortsResponse,
  IUpdateCohort,
  PXActionResult,
  UpdateResultWithItemInfo,
  IPaginationParams,
} from "@px-shared-account/hermes";
import { ICohortWithStudents } from "@/types/cohort";

/**
 * Type for cohort list parameters
 */
export type ListCohortsParams = IPaginationParams & {
  search?: string;
  status?: CohortStatus;
  courseId?: number;
};

/**
 * Core API endpoints for cohort operations
 * These are the base paths and methods used by both client and server implementations
 */
export const COHORT_API_ENDPOINTS = {
  /**
   * List cohorts endpoint
   * @param params Query parameters for filtering and pagination
   * @returns Formatted URL with query parameters
   */
  list: (params: ListCohortsParams = {}) => {
    const { search, status, courseId, page = 1, limit = 10 } = params;
    const queryParams = new URLSearchParams();

    if (search) queryParams.append("search", search);
    if (status) queryParams.append("status", status);
    queryParams.append("page", String(page));
    queryParams.append("limit", String(limit));

    // Use different endpoint when listing cohorts for a specific course
    if (courseId) {
      queryParams.append("courseId", courseId.toString());
      return {
        url: `/cohort?${queryParams.toString()}`,
        method: "GET",
      };
    }

    return {
      url: `/cohort/list?${queryParams.toString()}`,
      method: "GET",
    };
  },

  /**
   * Get cohort by ID endpoint
   * @param id Cohort ID
   * @returns Endpoint configuration
   */
  getById: (id: number) => ({
    url: `/cohort/${id}`,
    method: "GET",
  }),

  /**
   * Create cohort endpoint
   * @returns Endpoint configuration
   */
  create: () => ({
    url: "/cohort",
    method: "POST",
  }),

  /**
   * Update cohort endpoint
   * @param id Cohort ID
   * @returns Endpoint configuration
   */
  update: (id: number) => ({
    url: `/cohort/${id}`,
    method: "PATCH",
  }),

  /**
   * Add students to cohort endpoint
   * @param id Cohort ID
   * @returns Endpoint configuration
   */
  addStudents: (id: number) => ({
    url: `/cohort/${id}/add-students`,
    method: "POST",
  }),

  /**
   * Remove students from cohort endpoint
   * @param id Cohort ID
   * @returns Endpoint configuration
   */
  removeStudents: (id: number) => ({
    url: `/cohort/${id}/remove-students`,
    method: "POST",
  }),
};

/**
 * Type definitions for API responses
 */
export type CohortApiTypes = {
  list: IListCohortsResponse;
  getById: ICohortWithStudents & { id: string; startDate: string; endDate: string };
  create: ICohortBase;
  update: UpdateResultWithItemInfo<ICohortBase>;
  addStudents: PXActionResult;
  removeStudents: PXActionResult;
};

/**
 * Type definitions for API request payloads
 */
export type CohortApiPayloads = {
  create: ICreateCohort;
  update: IUpdateCohort;
  addStudents: { studentIds: string[] };
  removeStudents: { studentIds: string[] };
};
