"use client";

import { motion, useAnimation } from "framer-motion";
import { ArrowRightIcon } from "lucide-react";
import Link from "next/link";

type SettingCardProps = {
  href: string;
  icon: React.ReactNode;
  subTitle: string;
  title: string;
};

export default function SettingCard({ title, subTitle, icon, href }: SettingCardProps) {
  const iconControls = useAnimation();

  return (
    <Link href={href}>
      <div className="bg-background-secondary hover:bg-secondary overflow-hidden rounded-lg transition duration-100">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onHoverStart={() => {
            iconControls.start({
              rotate: [0, -10, 10, -10, 0],
              scale: 1.1,
              transition: {
                duration: 0.5,
                times: [0, 0.2, 0.4, 0.6, 1],
              },
            });
          }}
          onHoverEnd={() => {
            iconControls.start({
              rotate: 0,
              scale: 1,
              transition: { duration: 0.2 },
            });
          }}
          transition={{ duration: 0.2 }}
          className="flex cursor-pointer flex-col gap-4 rounded-lg p-4"
        >
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <motion.div animate={iconControls}>{icon}</motion.div>
              <h2 className="text-lg font-bold">{title}</h2>
            </div>
            <ArrowRightIcon className="h-4 w-4 md:hidden" />
          </div>
          <p className="hidden text-sm text-gray-500 md:block">{subTitle}</p>
        </motion.div>
      </div>
    </Link>
  );
}
