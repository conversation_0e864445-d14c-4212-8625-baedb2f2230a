import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, ILike, UpdateResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { ProductLine, ProductFamily } from '@entities';
import { HubspotService } from '@services/crm';
import { IDataServices } from '@abstracts';
import { ProductFamilyTable } from '@services/database/postgres';

@Injectable()
export class ProductFamilyUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly hubspotService: HubspotService,
  ) {}

  /**
   * Creates a product family
   * @param familyInfo Info of the product family
   */
  async create(familyInfo: ProductFamily): Promise<ProductFamily> {
    await this.hubspotService.createProductFamily(
      familyInfo.name,
      familyInfo.description,
    );
    return this.dataServices.productFamily.create(familyInfo);
  }

  /**
   * Returns all product family records from database
   * @returns Product family records from database
   */
  async getAll(
    limit: number,
    page: number,
  ): Promise<{ items: ProductFamily[]; total: number }> {
    return this.dataServices.productFamily.getAll(limit, page);
  }

  /**
   * Updates a product family
   * @param id ID of the family to update
   * @param updates Updates for the product family
   */
  async updateById(
    id: number,
    updates: QueryDeepPartialEntity<ProductFamily>,
  ): Promise<UpdateResult> {
    return this.dataServices.productFamily.update(
      {
        id,
      },
      updates,
    );
  }

  /**
   * Retrieves a product family matching provided filters
   * @param filter Properties of family to search for
   */
  async searchOne(filter: Partial<ProductFamilyTable>): Promise<ProductFamily> {
    return this.dataServices.productFamily.getOneBy(filter);
  }

  /**
   * Retrieves a product family matching provided `id``
   * @param id `id` of family to fetch
   */
  async getOne(id: number): Promise<ProductFamily> {
    return this.dataServices.productFamily.getOneWithRelations(
      'productFamilies',
      'id',
      id,
    );
  }

  /**
   * Retrieves all product families matching provided `name`, `status` or `line`
   * @param name `name` of the `ProductFamily` to search for
   * @param status `status` of `ProductFamily` to search for
   * @param line `line` of `ProductFamily` to search for
   */
  async searchByNameStatusOrLine(
    name: string,
    status: string,
    line: number,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: ProductFamily[]; total: number }> {
    const filters: FindOptionsWhere<ProductFamily> = {};

    if (status) {
      filters.status = status;
    }

    if (name) {
      filters.name = ILike(`%${name}%`);
    }

    if (line) {
      const productLine = new ProductLine();
      productLine.id = line;
      filters.productLine = productLine;
    }

    return this.dataServices.productFamily.getAllBy(filters, limit, page, {
      createdAt: orderBy || 'DESC',
    });
  }
}
