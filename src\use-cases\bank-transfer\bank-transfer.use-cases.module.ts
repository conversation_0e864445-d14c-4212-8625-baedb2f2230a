import { Module } from '@nestjs/common';
import { DataServicesModule } from '@services/database';
import { BankTransferUseCases, BankTransferFactory } from '.';
import { ChargebeeBillingModule } from '@services/billing';

@Module({
  imports: [DataServicesModule, ChargebeeBillingModule],
  providers: [BankTransferFactory, BankTransferUseCases],
  exports: [BankTransferFactory, BankTransferUseCases],
})
export class BankTransferModule {}
