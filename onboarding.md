# Hermes - Shared TypeScript Types Library

Welcome to the Hermes project! This documentation will help you understand the purpose, architecture, and capabilities of this shared types library.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Getting Started](#getting-started)
3. [Project Architecture](#project-architecture)
4. [Core Components](#core-components)
5. [Development Workflow](#development-workflow)
6. [Publishing Process](#publishing-process)
7. [Best Practices](#best-practices)

## Project Overview

Hermes is a shared TypeScript types library for the Paradox multi-project system. It provides a centralized repository of type definitions, interfaces, enums, and constants that are used across multiple applications within the Paradox ecosystem.

The primary purpose of this library is to:
- Ensure type consistency across different applications
- Reduce code duplication
- Provide a single source of truth for data models
- Simplify API contract validation using Zod schemas

The library is published as an npm package under the name `@px-shared-account/hermes` and can be imported into any TypeScript project that needs to use these shared types.

## Getting Started

### Prerequisites

- Node.js (v14.17 or higher)
- npm or yarn

### Installation

For local development, you can reference this package directly from the file system in your `package.json`:

```json
{
  "dependencies": {
    "@px-shared-account/hermes": "^2.0.0"
  },
  "dev-dependencies": {
    "@px-shared-account/hermes": "file:../path/to/shared-types"
  }
}
```

For production, install from npm:

```bash
npm install @px-shared-account/hermes
```

### Basic Usage

```typescript
import { User, UserRole, PaginationParams } from "@px-shared-account/hermes";

// Use the types in your code
const user: User = {
  id: "1",
  email: "<EMAIL>",
  username: "user1",
  role: UserRole.USER,
  createdAt: new Date(),
  updatedAt: new Date(),
};
```

## Project Architecture

The Hermes library follows a modular architecture organized by domain entities. The main structure is:

```
hermes/
├── .github/
│   └── workflows/
│       └── publish.yml      # GitHub Actions workflow for publishing
├── src/
│   ├── constants/           # Shared constants (e.g., permissions)
│   ├── enums/               # Enumeration types
│   ├── interfaces/          # Type definitions and interfaces
│   └── index.ts             # Main export file
├── package.json             # Project configuration
├── tsconfig.json            # TypeScript configuration
└── README.md                # Project documentation
```

The library uses a barrel pattern for exports, where each directory has an `index.ts` file that re-exports all the types from that directory. This makes imports cleaner for consumers of the library.

## Core Components

### Interfaces

The interfaces directory contains type definitions for the main domain entities:

#### User

The `user.interface.ts` file defines types related to user management:
- `UserBaseSchema`: Base schema for user data
- `CreateUserSchema`: Schema for creating a new user
- `UpdateUserSchema`: Schema for updating an existing user
- `GetUserByIdSchema`: Schema for retrieving a user by ID
- `FilterUserSchema`: Schema for filtering users
- `SearchUserSchema`: Schema for searching users
- `ListUsersResponseSchema`: Schema for paginated user list responses

#### Role

The `role.interface.ts` file defines types related to role management:
- `RoleBaseSchema`: Base schema for role data
- `CreateRoleSchema`: Schema for creating a new role
- `UpdateRoleSchema`: Schema for updating an existing role
- `GetRoleByIdSchema`: Schema for retrieving a role by ID
- `FilterRoleSchema`: Schema for filtering roles
- `ListRoleSchema`: Schema for role list items
- `ListRolesResponseSchema`: Schema for paginated role list responses
- `BulkUpdateRoleSchema`: Schema for bulk updating roles

#### Course

The `course.interface.ts` file defines types related to course management:
- `CourseBaseSchema`: Base schema for course data
- `CourseOfferSchema`: Schema for course offers
- `CreateUpdateCourseSchema`: Schema for creating/updating courses
- `GetCourseByIdSchema`: Schema for retrieving a course by ID
- `ListCourseSchema`: Schema for listing courses
- `ListCoursesResponseSchema`: Schema for paginated course list responses
- `CourseStudentSchema`: Schema for students in a course
- `GetStudentsForCourseSchema`: Schema for retrieving students for a course
- `GetStudentsForCourseResponseSchema`: Schema for paginated student list responses

#### Cohort

The `cohort.interface.ts` file defines types related to cohort management:
- `CohortBaseSchema`: Base schema for cohort data
- `CreateCohortSchema`: Schema for creating a new cohort
- `UpdateCohortSchema`: Schema for updating an existing cohort
- `GetCohortByIdSchema`: Schema for retrieving a cohort by ID
- `ListCohortsSchema`: Schema for listing cohorts
- `ListCohortsResponseSchema`: Schema for paginated cohort list responses
- `UpdateCohortStudentsSchema`: Schema for updating students in a cohort

#### Common

The `common.interface.ts` file defines shared utility types:
- `PaginationParamsSchema`: Schema for pagination parameters
- `UpdateResultWithItemInfo`: Interface for update operation results
- `PXActionResultSchema`: Schema for action results

### Enums

The enums directory contains enumeration types used across the application:

#### User Enums

The `user.enum.ts` file defines user-related enumerations:
- `RoleGroup`: Enum for role groups (ADMIN, PROGRAM_MANAGER, STUDENT, EXTERNAL)
- `Roles`: Enum for specific roles (ADMIN, FACILITATOR, MENTOR, etc.)
- `Gender`: Enum for gender (MALE, FEMALE)

#### Course Enums

The `course.enum.ts` file defines course-related enumerations:
- `CourseStatus`: Enum for course status (DRAFT, PUBLISHED, ARCHIVED)
- `CourseOfferStatus`: Enum for course offer status (ACTIVE, DISABLED)

#### Cohort Enums

The `cohort.enum.ts` file defines cohort-related enumerations:
- `CohortStatus`: Enum for cohort status (ACTIVE, PAST, FUTURE)

### Constants

The constants directory contains shared constant values:

#### Permissions

The `permissions.ts` file defines permission constants for different entities:
- `UserPermissions`: Permissions for user management
- `RolePermissions`: Permissions for role management
- `CoursePermissions`: Permissions for course management
- `CohortPermissions`: Permissions for cohort management

## Development Workflow

1. **Setup**: Clone the repository and install dependencies
   ```bash
   git clone <repository-url>
   cd hermes
   npm install
   ```

2. **Development**: Make changes to the types in the `src` directory

3. **Building**: Build the project to compile TypeScript to JavaScript
   ```bash
   npm run build
   ```

4. **Testing**: Test your changes by importing the library into a consumer project
   ```bash
   # In the consumer project
   npm install file:/path/to/hermes
   ```

5. **Committing**: Commit your changes and push to the repository
   ```bash
   git add .
   git commit -m "Description of changes"
   git push
   ```

## Publishing Process

The library is published to npm using a GitHub Actions workflow:

1. Go to the GitHub Actions tab in the repository
2. Select the "Publish Package to NPM" workflow
3. Click "Run workflow"
4. Select the release type:
   - `patch`: Bug fixes (0.0.x)
   - `minor`: New features (0.x.0)
   - `major`: Breaking changes (x.0.0)
   - `prepatch`, `preminor`, `premajor`: Pre-release versions
   - `prerelease`: Increment pre-release version
5. Click "Run workflow"

The action will automatically:
- Bump the version according to the release type
- Create a git tag
- Create a GitHub release
- Publish to npm

## Best Practices

### Type Design

1. **Use Zod for validation**: All interfaces should have corresponding Zod schemas for runtime validation.

2. **Keep types focused**: Each type should represent a single concept and have a clear purpose.

3. **Use descriptive names**: Type names should clearly indicate what they represent.

4. **Document complex types**: Add comments to explain complex types or validation rules.

5. **Maintain backward compatibility**: Avoid breaking changes to existing types when possible.

### Schema Design

1. **Base schemas and extensions**: Create base schemas and extend them for specific use cases.

2. **Partial schemas for updates**: Use `partial()` for update operations.

3. **Validation rules**: Include appropriate validation rules (min/max length, patterns, etc.).

4. **Consistent naming**: Follow a consistent naming convention for schemas.

### Development Guidelines

1. **Keep the library focused**: Only include types that are shared across multiple applications.

2. **Minimize dependencies**: Avoid adding runtime dependencies beyond what's necessary.

3. **Test changes**: Test changes in a consumer application before publishing.

4. **Semantic versioning**: Follow semantic versioning rules when publishing updates.

5. **Documentation**: Keep documentation up-to-date with changes to the library.

---

This documentation provides an overview of the Hermes shared types library. If you have any questions or need further assistance, please reach out to the team.
