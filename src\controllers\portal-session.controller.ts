import { Body, Controller, Post } from '@nestjs/common';
import { PortalSessionUseCase } from '@useCases';

@Controller('portal-session')
export class PortalSessionController {
  constructor(private readonly portalSessionUseCases: PortalSessionUseCase) {}

  @Post('/')
  async createPortalSession(@Body() body: { customerId: string }) {
    return this.portalSessionUseCases.createPortalSession(body.customerId);
  }
}
