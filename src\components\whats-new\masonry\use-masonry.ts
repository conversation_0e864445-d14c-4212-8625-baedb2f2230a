"use client";

import { createContext, useContext, useRef, useState, useEffect } from "react";

// Create context for lightbox functionality
interface MasonryContextType {
  openLightbox: (src: string, alt: string) => void;
}

export const MasonryContext = createContext<MasonryContextType | null>(null);

// Custom hook to use masonry context
export const useMasonryContext = () => {
  const context = useContext(MasonryContext);
  if (!context) {
    throw new Error("useMasonryContext must be used within a MasonryGallery");
  }
  return context;
};

// Custom hook for masonry layout
export function useMasonry() {
  const masonryContainer = useRef<HTMLDivElement | null>(null);
  const [items, setItems] = useState<ChildNode[]>([]);
  const [imagesLoaded, setImagesLoaded] = useState(false);

  // Set up image loading tracking
  useEffect(() => {
    let imageCount = 0;
    let loadedCount = 0;

    const onAllImagesLoaded = () => {
      setImagesLoaded(true);
    };

    const onImageLoad = () => {
      loadedCount++;
      if (loadedCount === imageCount && imageCount > 0) {
        onAllImagesLoaded();
      }
    };

    // Find all images inside the container
    if (masonryContainer.current) {
      const images = masonryContainer.current.querySelectorAll("img");
      imageCount = images.length;

      if (imageCount === 0) {
        setImagesLoaded(true);
        return;
      }

      images.forEach((img) => {
        if (img.complete) {
          onImageLoad();
        } else {
          img.addEventListener("load", onImageLoad);
          img.addEventListener("error", onImageLoad); // Count errors as loaded
        }
      });
    }

    // Store the ref's current value
    const container = masonryContainer.current;

    return () => {
      // Use the stored value in the cleanup function
      if (container) {
        const images = container.querySelectorAll("img");
        images.forEach((img) => {
          img.removeEventListener("load", onImageLoad);
          img.removeEventListener("error", onImageLoad);
        });
      }
    };
  }, []); // Removed masonryContainer.current from dependencies

  useEffect(() => {
    if (masonryContainer.current) {
      const masonryItems = Array.from(masonryContainer.current.children);
      setItems(masonryItems);
    }
  }, [imagesLoaded]); // Removed masonryContainer.current from dependencies

  useEffect(() => {
    const handleMasonry = () => {
      if (!items || items.length < 1) return;

      let gapSize = 0;
      if (masonryContainer.current) {
        // Get computed gap size from CSS
        const style = window.getComputedStyle(masonryContainer.current);
        gapSize = parseInt(
          style.getPropertyValue("gap") || style.getPropertyValue("grid-gap") || "0",
        );

        // Reset all margins first
        items.forEach((el) => {
          if (el instanceof HTMLElement) {
            el.style.marginTop = "0";
          }
        });

        // Apply masonry layout
        items.forEach((el) => {
          if (!(el instanceof HTMLElement)) return;

          // Find the element above in the same column
          const previous = el.previousElementSibling;
          if (!previous) return;

          // Calculate column positions
          const elRect = el.getBoundingClientRect();
          const prevRect = previous.getBoundingClientRect();

          // If in the same column (X positions match), adjust the margin
          if (Math.abs(elRect.left - prevRect.left) < 5) {
            const newMarginTop = prevRect.bottom + gapSize - elRect.top;
            if (newMarginTop > 0) {
              el.style.marginTop = `${newMarginTop}px`;
            }
          }
        });
      }
    };

    if (imagesLoaded) {
      // Allow time for layout to settle
      setTimeout(handleMasonry, 100);
    }

    // Handle window resize events
    const debouncedHandleMasonry = debounce(handleMasonry, 200);
    window.addEventListener("resize", debouncedHandleMasonry);

    return () => {
      window.removeEventListener("resize", debouncedHandleMasonry);
    };
  }, [items, imagesLoaded]);

  // Simple debounce function
  function debounce(fn: (...args: any[]) => void, delay: number) {
    let timeout: NodeJS.Timeout;
    return function (...args: any[]) {
      clearTimeout(timeout);
      timeout = setTimeout(() => fn(...args), delay);
    };
  }

  return masonryContainer;
}
