"use client";

import { UseFormReturn } from "react-hook-form";
import { allTimezones, useTimezoneSelect } from "react-timezone-select";

import { FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { CreateNewSlotFormType } from "../types";

type EventDetailsSectionProps = {
  t: (key: string) => string;
  form: UseFormReturn<CreateNewSlotFormType>;
};

const eventTypes = [
  "mentoring",
  "masterclass",
  "supervision",
  "guided-triad",
  "customized-slot",
] as const;

const durationOptions = [
  { value: 30, label: "30 minutes" },
  { value: 60, label: "1 hour" },
  { value: 90, label: "1 hour 30 minutes" },
  { value: 120, label: "2 hours" },
  { value: 150, label: "2 hours 30 minutes" },
  { value: 180, label: "3 hours" },
  { value: 210, label: "3 hours 30 minutes" },
  { value: 240, label: "4 hours" },
];

const recurrenceOptions = ["one-time", "weekly", "monthly", "yearly"];

export default function EventDetailsSection({ form, t }: EventDetailsSectionProps) {
  const { options: timezoneOptions } = useTimezoneSelect({
    labelStyle: "original",
    timezones: allTimezones,
  });

  return (
    <div className="mb-5">
      <div className="text-xl font-semibold">{t("what-is-this-event")}</div>
      {/* Type of events */}
      <FormField
        control={form.control}
        name="type"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("type-of-event")}</FormLabel>
            <Select value={field.value} onValueChange={field.onChange}>
              <SelectTrigger className="w-1/2">
                <SelectValue placeholder={t("type-of-event")} />
              </SelectTrigger>
              <SelectContent>
                {eventTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {t(`event-types.${type}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormItem>
        )}
      />
      <div className="grid gap-4 md:grid-cols-2">
        {/* Date and time */}
        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("date-and-time")}</FormLabel>
              <Input
                title="date-and-time"
                name="date-and-time"
                type="datetime-local"
                value={field.value}
                onChange={field.onChange}
              />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="tz"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("time-zone")}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("time-zone")} />
                </SelectTrigger>
                <SelectContent>
                  {timezoneOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
      </div>
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
        {/* Duration */}
        <FormField
          control={form.control}
          name="duration"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("duration")}</FormLabel>
              <Select value={field.value.toString()} onValueChange={field.onChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("duration")} />
                </SelectTrigger>
                <SelectContent>
                  {durationOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        {/* Recurrence */}
        <FormField
          control={form.control}
          name="recurrence"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("recurrence")}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("recurrence")} />
                </SelectTrigger>
                <SelectContent>
                  {recurrenceOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        {/* Replay */}
        <FormField
          control={form.control}
          name="replay"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("replay")}</FormLabel>
              <div className="flex items-center gap-2">
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </div>
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
