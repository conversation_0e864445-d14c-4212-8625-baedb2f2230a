import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { SubscriptionItem } from 'chargebee-typescript/lib/resources/subscription';
import { Customer as ChargebeeCustomer } from 'chargebee-typescript/lib/resources';
import { ReadStream } from 'typeorm/platform/PlatformTools';
import { Response } from 'express';
import {
  InvoiceLineItemPX,
  EditSubscriptionLineItem,
  ProductPlanPrice,
  Subscription,
} from '@entities';
import { SubscriptionFactoryService } from '.';
import { InvoiceUseCases } from '../invoice';
import {
  ProductOfferFactoryService,
  ProductOfferUseCases,
} from '../product-offers';
import {
  ChargeBeeWebhookPayload,
  CronLogResult,
  PaymentSourceInfo,
  PXActionResult,
  RequestMethods,
  SubscriptionDownsellParams,
  SubscriptionInvoiceDetails,
} from '@types';
import {
  ExportSubscriptionsDTO,
  GetSubscriptionsStatusDTO,
  UpdateSubscriptionDTO,
} from '@dtos';
import { ChargebeeBillingService } from '@services/billing';
import {
  ChargeBeeDurationPeriodUnit,
  CronLogRefPrefix,
  CronLogStatus,
  CronLogType,
  SubscriptionOrderStatus,
  SubscriptionStatus,
} from '@enums';
import { ProductDeliveryService } from '../product-delivery';
import { HubspotService } from '@services/crm';
import { IDataServices } from '@abstracts';
import { GlobalHelpers } from '@helpers';
import { ConfigService } from '@config';
import { ProductOfferTable } from '@tables';
import { SlackService } from '@services/notification';
import { UserUseCases } from '../user';
import { GoliathService } from '../goliaths';

@Injectable()
export class SubscriptionUseCases {
  private readonly EXPORT_FIELDS: string;
  private readonly SLACK_ERRORS_CHANNEL: string;
  private readonly PRODUCT_DELIVERY_ENABLED: boolean;
  private readonly MAKE_SUBSCRIPTION_NOTIFICATION_URL: string;
  private readonly ENVIRONMENT: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly dataServices: IDataServices,
    private readonly factory: SubscriptionFactoryService,
    private readonly chargebeeService: ChargebeeBillingService,
    private readonly hubspotService: HubspotService,
    private readonly invoiceUseCases: InvoiceUseCases,
    private readonly productOfferFactoryService: ProductOfferFactoryService,
    private readonly productOfferUseCases: ProductOfferUseCases,
    private readonly productDeliveryService: ProductDeliveryService,
    private readonly slackService: SlackService,
    private readonly userService: UserUseCases,
    private readonly goliathService: GoliathService,
  ) {
    this.ENVIRONMENT = this.configService.appSecrets.NODE_ENV;
    this.EXPORT_FIELDS =
      this.configService.appSecrets.SUBSCRIPTION_EXPORT_FIELDS;
    this.SLACK_ERRORS_CHANNEL =
      this.configService.notificationSecrets.SLACK_ERRORS_CHANNEL;
    this.PRODUCT_DELIVERY_ENABLED =
      this.configService.appSecrets.PRODUCT_DELIVERY_ENABLED;
    this.MAKE_SUBSCRIPTION_NOTIFICATION_URL =
      this.configService.appSecrets.MAKE_SUBSCRIPTION_NOTIFICATION_URL;
  }

  /**
   * Finds a subscription or creates a new one if it doesn't exist
   * @param subscription Subscription entity
   * @returns Subscription item record from database
   */
  async findOrCreate(subscription: Subscription): Promise<Subscription> {
    const existingSubscription = await this.dataServices.subscription.getOneBy({
      chargebeeId: subscription.chargebeeId,
    });

    if (existingSubscription) {
      return existingSubscription;
    }

    return this.dataServices.subscription.create(subscription);
  }

  /**
   * Notifies Make about a new subscription
   * @param subscription Subscription entity
   */
  async sendSubscriptionToMake(subscription: Subscription): Promise<void> {
    const requestBody = {
      subscriptionId: subscription.id,
      subscriptionChargebeeId: subscription.chargebeeId,
      offerId: subscription?.customFields?.cf_os_offer_id,
      customerEmail: subscription.customerEmail,
      amountPaid: subscription.amountPaid,
      metaData: subscription.metadata,
    };
    await GlobalHelpers.makeAxiosRequest(
      this.MAKE_SUBSCRIPTION_NOTIFICATION_URL,
      RequestMethods.POST,
      null,
      {
        'Content-Type': 'application/json',
      },
      requestBody,
    );
  }

  /**
   * Creates a `Subscription` from a Chargebee event.
   *
   * @param chargebeeEventContent - The content of the Chargebee webhook payload.
   * @param isCron - Whether the event a re-processed from a cron job
   * @returns A Promise that resolves to the created Subscription.
   */
  async createFromChargebeeEvent(
    chargebeeEventContent: ChargeBeeWebhookPayload['content'],
    isCron?: boolean,
  ): Promise<PXActionResult> {
    try {
      const chargebeeSubscription = chargebeeEventContent.subscription;
      let existingSubscription: Subscription;

      if (isCron) {
        existingSubscription = await this.dataServices.subscription.getOneBy({
          chargebeeId: chargebeeSubscription.id,
        });
      }

      if (existingSubscription?.crmId) {
        return this.retryFailedDeliveryForSubscription(existingSubscription);
      }
      let linkedCustomer = chargebeeEventContent.customer;
      const subscription = await this.factory.generateFromChargebeeEvent(
        chargebeeSubscription,
      );

      if (!linkedCustomer) {
        linkedCustomer = await this.chargebeeService.getCustomerInfo(
          subscription.customerId,
        );
      }
      subscription.customerEmail = linkedCustomer.email?.toLowerCase()?.trim();
      subscription.customerName = `${linkedCustomer.first_name} ${linkedCustomer.last_name}`;

      if (chargebeeSubscription?.auto_collection) {
        subscription.autoCollection = chargebeeSubscription.auto_collection;
      } else if (linkedCustomer?.auto_collection) {
        subscription.autoCollection = linkedCustomer.auto_collection;
      }

      const createdSubscription = await this.findOrCreate(subscription);

      await this.goliathService.issueAndPush(linkedCustomer.email);

      if (this.ENVIRONMENT === 'production') {
        await this.sendSubscriptionToMake(createdSubscription);
      }
      await this.syncWithHubspot(createdSubscription, linkedCustomer);
      const linkedOffer = createdSubscription?.customFields?.['cf_os_offer_id'];
      if (linkedOffer) {
        await this.initiateProductDelivery(
          createdSubscription,
          linkedOffer,
          linkedCustomer,
        );
      } else {
        await this.slackService.sendMessage(
          `No cf_os_offer_id found for subscription with ID: ${subscription.id} - No Access granted`,
          this.SLACK_ERRORS_CHANNEL,
        );
      }
      return {
        data: { id: createdSubscription?.chargebeeId },
        success: !!createdSubscription?.id,
        message: !!createdSubscription?.id
          ? 'Subscription created successfully'
          : 'No Subscription created',
      };
    } catch (err) {
      return {
        data: { id: chargebeeEventContent?.subscription?.id },
        success: false,
        message: `Error occurred while creating subscription: ${err}`,
      };
    }
  }

  /**
   * Retries the product delivery for a failed subscription event
   * @param subscription The subscription entity
   * @returns A promise that resolves to the result of the retry
   */
  async retryFailedDeliveryForSubscription(
    subscription: Subscription,
  ): Promise<PXActionResult> {
    try {
      const linkedOfferId = subscription?.customFields?.['cf_os_offer_id'];
      const linkedCustomer = {
        email: subscription.customerEmail,
        first_name: subscription.customerName.split(' ')[0],
        last_name: subscription.customerName.split(' ')[1],
        id: subscription.customerId,
      } as ChargebeeCustomer;

      let result: PXActionResult;
      if (linkedOfferId) {
        result = await this.initiateProductDelivery(
          subscription,
          linkedOfferId,
          linkedCustomer,
        );
      }
      if (result?.success) {
        return {
          success: true,
          message:
            'Delivery initiated successfully for failed subscription creation event',
          data: {
            affectedEntity: 'Subscription',
            affectedEntityId: subscription?.chargebeeId,
          },
        };
      }
      return result;
    } catch (error) {
      return {
        success: false,
        message: `Error occurred while retrying failed delivery for subscription: ${error}`,
      };
    }
  }

  /**
   * Syncs a subscription with HubSpot by creating/updating a deal
   * @param subscription Subscription entity
   * @param customer Chargebee customer entity
   */
  async syncWithHubspot(
    subscription: Subscription,
    customer: ChargebeeCustomer,
  ): Promise<PXActionResult> {
    const isExistingDeal = !!subscription.crmId;
    try {
      const syncedDeal = await this.hubspotService.createDealOrUpdateExisting(
        subscription,
        customer,
        isExistingDeal,
      );

      await this.syncLineItemsWithHubspot(
        subscription.attachedPrices,
        subscription.subscriptionItems,
        syncedDeal?.id,
        subscription.isForever,
        subscription.billingPeriodUnit,
      );

      if (syncedDeal?.id) {
        const crmUpdate = { crmId: syncedDeal.id };
        await Promise.all([
          this.chargebeeService.updateSubscription(
            subscription.chargebeeId,
            crmUpdate,
          ),
          this.update(subscription.id, crmUpdate),
        ]);
      }
      return {
        success: true,
        message: 'Hubspot sync successful',
        data: {
          id: subscription.id,
          syncedDeal: syncedDeal?.id,
        },
      };
    } catch (error) {
      await this.logFailedHubspotSync(subscription, customer, error);
      return {
        success: false,
        message: `Error occurred while syncing subscription with HubSpot: ${error}`,
      };
    }
  }

  /**
   * Reprocesses a failed HubSpot sync for a subscription
   * @param subscription Subscription entity
   * @param linkedCustomer Chargebee customer object
   * @returns A promise that resolves to the result of the reprocessing
   */
  async reProcessFailedHubspotSync(
    subscription: Subscription,
    linkedCustomer: ChargebeeCustomer,
  ): Promise<CronLogResult> {
    const syncResult = await this.syncWithHubspot(subscription, linkedCustomer);
    return {
      success: syncResult.success,
      message: syncResult.message,
      input: {
        subscription,
        linkedCustomer,
      },
      output: {
        syncResult,
      },
    };
  }

  /**
   * Logs a failed HubSpot sync for a subscription
   * @param subscription Subscription entity
   * @param customer Chargebee customer object
   * @param error Error object
   * @returns A promise that resolves when the failed HubSpot sync is logged
   */
  async logFailedHubspotSync(
    subscription: Subscription,
    customer?: ChargebeeCustomer,
    error?: any,
  ): Promise<any> {
    this.dataServices.cronLog.create({
      id: `${CronLogRefPrefix.HS_SYNC}${subscription.id}`,
      type: CronLogType.FAILED_HUBSPOT_SYNC,
      status: CronLogStatus.FAILED,
      slackNotified: false,
      startTime: new Date(),
      endTime: new Date(),
      retryCount: 0,
      error: error,
      result: {
        input: {
          subscription,
          customer,
        },
        success: false,
        message: `Error occurred while syncing subscription with HubSpot: ${error}`,
      },
    });
  }

  /**
   * Initiates product delivery for a subscription
   * @param subscription Subscription entity
   * @param linkedOfferChargebeeId Chargebee offer ID
   * @param customerInfo Chargebee customer object
   * @returns A promise that resolves to the result of the product delivery initiation
   */
  async initiateProductDelivery(
    subscription: Subscription,
    linkedOfferChargebeeId: string,
    customerInfo: ChargebeeCustomer,
  ): Promise<PXActionResult> {
    try {
      if (!this.PRODUCT_DELIVERY_ENABLED) return;
      const linkedOffer = await this.productOfferUseCases.getOneBy({
        chargebeeId: linkedOfferChargebeeId,
      });

      if (!linkedOffer) {
        await this.slackService.sendMessage(
          `No offer found (offerId: ${linkedOfferChargebeeId}) for subscription with ID: ${subscription.id}. No product delivery initiated`,
          this.SLACK_ERRORS_CHANNEL,
        );
        return {
          success: false,
          message: `No offer found (offerId: ${linkedOfferChargebeeId}) for subscription with ID: ${subscription.id}. No product delivery initiated`,
        };
      }

      if (!linkedOffer.withProductDelivery) {
        await this.userService.createOrInvite(
          customerInfo?.email,
          customerInfo?.first_name,
          customerInfo?.last_name,
          subscription.businessEntityId,
          customerInfo?.id,
        );
        return {
          success: true,
          message: `No product delivery enabled for offer with ID: ${linkedOfferChargebeeId}. User created or invited without product delivery`,
          data: {
            subscription,
            linkedOfferChargebeeId,
            customerInfo,
          },
        };
      }

      const lmsIds = [...linkedOffer.lmsIds.products];
      const communityIds = [...linkedOffer.communityIds.products];

      if (this.subscriptionHasRecommendedProducts(subscription.metadata)) {
        lmsIds.push(...linkedOffer.lmsIds.recommended);
        communityIds.push(...linkedOffer.communityIds.recommended);
      }

      return this.productDeliveryService.initiateProductDelivery(
        subscription.id,
        linkedOffer.id,
        linkedOffer.name,
        subscription.customerEmail,
        customerInfo.first_name,
        customerInfo.last_name,
        subscription.businessEntityId,
        subscription.customerId,
        lmsIds,
        communityIds,
      );
    } catch (error) {
      return {
        success: false,
        message: `Error occurred while initiating product delivery: ${error?.message}`,
        data: {
          subscription,
          linkedOfferChargebeeId,
          customerInfo,
          error,
        },
      };
    }
  }

  /**
   * Checks if the subscription has recommended products
   * @param metaData Chargebee subscription metadata
   * @returns A boolean indicating whether the subscription has recommended products
   */
  subscriptionHasRecommendedProducts(metaData: any): boolean {
    return metaData?.includesRecommended === 'true';
  }

  /**
   *
   * @param attachedPrices Product Prices attached to subscription
   * @param subscriptionItems Subscription items
   * @param dealId Hubspot `id` of the deal linked to this subscription
   * @param isForever Whether the subscription is forever
   * @param billingPeriodUnit Chargebee billing period unit
   * @returns A promise that resolves to product codes
   */
  async syncLineItemsWithHubspot(
    attachedPrices: ProductPlanPrice[],
    subscriptionItems: SubscriptionItem[],
    dealId: string,
    isForever: boolean,
    billingPeriodUnit: ChargeBeeDurationPeriodUnit,
  ): Promise<PXActionResult> {
    if (!attachedPrices || attachedPrices?.length === 0) {
      return {
        success: false,
        message: 'No attached prices found for subscription',
      };
    }

    try {
      const pricesWithPlans = await this.factory.getAttachedPlans(
        attachedPrices,
      );
      await this.hubspotService.createLineItems(
        pricesWithPlans,
        dealId,
        subscriptionItems,
        isForever,
        billingPeriodUnit,
      );

      const plansWithProducts =
        await this.dataServices.productPlan.getAllWithMatchingIds(
          pricesWithPlans.map((price) => price.productPlan.id),
          'products',
          'product',
        );
      const productCodes = plansWithProducts.map((plan) => plan?.product?.code);
      await this.hubspotService.updateDealProductCodeList(dealId, productCodes);
    } catch (error) {
      return {
        success: false,
        message: error?.message,
      };
    }
  }

  /**
   * Updates a `Subscription` from a Chargebee event.
   *
   * @param chargebeeEventContent - The content of the Chargebee webhook payload.
   * @returns A Promise that resolves to the updated Subscription.
   */
  async updateFromChargebeeEvent(
    chargebeeEventContent: ChargeBeeWebhookPayload['content'],
  ): Promise<PXActionResult> {
    try {
      const chargebeeSubscription = chargebeeEventContent.subscription;
      const subscriptionFromDB = await this.dataServices.subscription.getOneBy({
        chargebeeId: chargebeeSubscription.id,
      });
      const subscriptionUpdates = await this.factory.getUpdatedFields(
        chargebeeSubscription,
        subscriptionFromDB,
      );
      const updatedLineItems = this.factory.getUpdatedLineItems(
        subscriptionFromDB.subscriptionItems,
        chargebeeSubscription.subscription_items,
      );

      if (updatedLineItems) {
        subscriptionUpdates.subscriptionItems = updatedLineItems;
      }

      if (
        updatedLineItems &&
        chargebeeSubscription?.meta_data?.billingCyclesChanged
      ) {
        subscriptionUpdates.attachedPrices =
          await this.factory.getAttachedPlanPrices(updatedLineItems);
      }

      await Promise.all([
        this.update(subscriptionFromDB.id, subscriptionUpdates),
        this.hubspotService.updateDealProperties(
          subscriptionFromDB.crmId,
          subscriptionUpdates,
        ),
      ]);
      return {
        data: { id: chargebeeSubscription.id },
        success: true,
        message: 'Subscription updated successfully',
      };
    } catch (err) {
      return {
        data: { id: chargebeeEventContent?.subscription?.id },
        success: false,
        message: `Error occurred while updating subscription: ${err}`,
      };
    }
  }

  /**
   * Recalculates `amountRefunded` for a subscription after a refund
   * @param refundedAmount The amount refunded
   * @param subscriptionId The Chargebee ID of the subscription
   * @returns A promise that resolves when `amountRefunded` are recalculated & updated
   */
  async recalculateAmountsAfterRefunded(
    subscriptionId: string,
    refundedAmount: number,
  ): Promise<any> {
    const subscription = await this.getOne(subscriptionId);
    if (!subscription) {
      throw new HttpException(
        `Subscription not found with ID: ${subscriptionId}`,
        HttpStatus.NOT_FOUND,
      );
    }
    const { amountRefunded, amountPaid } = subscription;
    const newAmountRefunded = amountRefunded + refundedAmount;
    const newAmountPaid = amountPaid - refundedAmount;
    return this.update(subscription.id, {
      amountRefunded: newAmountRefunded,
      amountPaid: newAmountPaid,
    });
  }

  /**
   * Marks a subscription as refunded (cancelled) & also cancels it at Chargebee
   *
   * @param chargebeeId - The Chargebee `id` the subscription
   * @returns A Promise that resolves when subscription is updated both in DB & Chargebee
   */
  async markRefundedAndCancel(chargebeeId: string): Promise<any> {
    const updateResult =
      await this.dataServices.subscription.updateAndReturnItem(
        'chargebeeId',
        chargebeeId,
        {
          status: SubscriptionStatus.CANCELLED,
          orderStatus: SubscriptionOrderStatus.REFUNDED,
        },
      );
    if (updateResult?.success) {
      await this.chargebeeService.cancelSubscription(chargebeeId);
    }
  }

  /**
   * Handles the `subscription_cancelled` event from Chargebee
   * If the subscription status in DB is `refunded` it ignores the event as `refunded`
   * means the subscription is already cancelled and this event could be the result of
   * marking this subscription as `refunded` by manual action. In such a case, the event
   * must be ignored because this function sets the `orderStatus` to `stopped` and if a
   * subscription is already in `refunded` state we don't want the `orderStatus` to change
   * @param chargebeeEventContent - The content of the Chargebee webhook payload.
   * @returns A promise which resolves after the subscription's status is updated
   */
  async cancelFromChargebeeEvent(
    chargebeeEventContent: ChargeBeeWebhookPayload['content'],
  ): Promise<PXActionResult> {
    try {
      const chargebeeSubscription = chargebeeEventContent.subscription;
      const chargebeeId = chargebeeSubscription.id;
      const subscriptionFromDB = await this.dataServices.subscription.getOneBy({
        chargebeeId,
      });

      if (
        subscriptionFromDB?.orderStatus === SubscriptionOrderStatus.REFUNDED
      ) {
        console.log(
          `Subscription cancellation event will be ignored as this subscription is already in Refunded state`,
        );
        await this.hubspotService.updateDealProperties(
          subscriptionFromDB?.crmId,
          {
            orderStatus: SubscriptionOrderStatus.REFUNDED,
          },
        );
        return {
          data: { id: chargebeeId },
          success: true,
          message: `Subscription already in Refunded state, event ignored. Hubspot deal updated.`,
        };
      }

      const updatedFields = await this.factory.getUpdatedFields(
        chargebeeSubscription,
        subscriptionFromDB,
      );

      updatedFields.orderStatus =
        updatedFields?.orderStatus || subscriptionFromDB?.orderStatus;

      if (updatedFields?.orderStatus === SubscriptionOrderStatus.STOPPED) {
        await this.productDeliveryService.initiateSuspension(
          subscriptionFromDB?.id,
        );
      }

      await this.update(subscriptionFromDB.id, updatedFields);
      await this.hubspotService.updateDealProperties(
        subscriptionFromDB?.crmId,
        {
          orderStatus: updatedFields?.orderStatus,
        },
      );
      return {
        data: { id: chargebeeId },
        success: true,
        message: `Subscription updated successfully. New orderStatus = ${updatedFields?.orderStatus}`,
      };
    } catch (err) {
      return {
        data: { id: chargebeeEventContent?.subscription?.id },
        success: false,
        message: `Error occurred while cancelling subscription: ${err}`,
      };
    }
  }

  /**
   * Updates a `Subscription` with the specified `chargebeeId`.
   *
   * @param id - The `id` of the `Subscription` to update.
   * @param updates - The updates to apply to the `Subscription`.
   * @returns A promise that resolves to the result of the update operation.
   */
  async update(
    id: number,
    updates: Partial<Subscription>,
  ): Promise<Subscription> {
    return this.dataServices.subscription
      .getRepository()
      .save({ id, ...updates });
  }

  /**
   * Searches and returns a `Subscription` record from database
   * @param id `id` of the `Subscription` to search for
   * @returns `Subscription` record from database OR null
   */
  async getOne(id: string): Promise<Subscription> {
    return this.dataServices.subscription.getOneBy({ chargebeeId: id });
  }

  /**
   * Searches for subscriptions based on the provided criteria.
   *
   * @param searchQuery - The search query to filter subscriptions by customer email.
   * @param product - The ID of the product to filter subscriptions by.
   * @param offer - The ID of the offer to filter subscriptions by.
   * @param orderStatus - The `orderStatus` of the subscriptions to filter by.
   * @param isForever - Whether to fetch forever subscriptions or normal ones
   * @param limit - The maximum number of subscriptions to retrieve.
   * @param page - The page number of the results.
   * @param orderBy - The order in which the subscriptions should be sorted.
   *                  Valid values are 'DESC' (descending) and 'ASC' (ascending).
   * @returns A promise that resolves to an object containing the retrieved subscriptions and the total count.
   */
  async searchAll(
    searchQuery?: string,
    product?: number,
    offer?: number,
    orderStatus?: SubscriptionOrderStatus,
    isForever?: boolean,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: Subscription[]; total: number }> {
    console.log('Getting all');
    const queryBuilder = this.dataServices.subscription
      .getRepository()
      .createQueryBuilder('subscription')
      .leftJoin('subscription.attachedPrices', 'attachedPrices')
      .leftJoin('attachedPrices.productPlan', 'productPlan')
      .leftJoin('productPlan.offers', 'offers')
      .leftJoin('productPlan.product', 'product');

    if (searchQuery) {
      queryBuilder.where('subscription.customerEmail ILIKE :searchQuery', {
        searchQuery: `%${searchQuery}%`,
      });
      queryBuilder.orWhere('subscription.customerName ILIKE :searchQuery', {
        searchQuery: `%${searchQuery}%`,
      });
    }

    if (product) {
      queryBuilder.andWhere('product.id = :product', { product });
    }

    if (offer) {
      queryBuilder.andWhere('offers.id = :offer', { offer });
    }

    if (orderStatus) {
      queryBuilder.andWhere('subscription.orderStatus = :orderStatus', {
        orderStatus,
      });
    }

    if (isForever !== undefined) {
      queryBuilder.andWhere('subscription.isForever = :isForever', {
        isForever,
      });
    }

    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.orderBy('subscription.createdAt', orderBy || 'DESC');

    const [items, total] = await queryBuilder.getManyAndCount();
    return {
      items,
      total,
    };
  }

  /**
   * Marks a subscription as overdue and also updates the associated deal by marking it overdue.
   * @param subscriptionId ID of the subscription to update
   * @param invoiceId ID of the overdue invoice linked to this subscription
   * @returns A promise which resolves when the the subscription & it's deal are updated, or
   * rejects if any of these operations fail
   */
  async markOverDueAndUpdateDeal(
    subscriptionId: string,
    invoiceId: string,
  ): Promise<CronLogResult> {
    const { updatedItem, success } =
      await this.dataServices.subscription.updateAndReturnItem(
        'chargebeeId',
        subscriptionId,
        {
          orderStatus: SubscriptionOrderStatus.OVERDUE,
        },
      );

    if (!success) {
      return {
        success: true,
        message: `Overdue subscription was not updated in PX OS, perhaps it was already marked overdue? ID: ${subscriptionId}`,
        output: { subscriptionUpdated: false, subscriptionId, hsResult: null },
      };
    }
    if (!updatedItem?.crmId) {
      return {
        success: false,
        message: `No crmId found on overdue subscription: ${subscriptionId}`,
        output: { subscriptionUpdated: true, subscriptionId, hsResult: null },
      };
    } else {
      const hsResult = await this.hubspotService.updateDealProperties(
        updatedItem?.crmId,
        {
          orderStatus: SubscriptionOrderStatus.OVERDUE,
        },
      );
      await this.invoiceUseCases.update(invoiceId, {
        overdueCheckPassed: hsResult?.success,
      });
      return {
        success: hsResult?.success,
        message: `Overdue subscription & deal successfully updated: ${subscriptionId}`,
        output: { hsResult, subscriptionUpdated: true, subscriptionId },
      };
    }
  }

  /**
   * Marks the subscriptions and related HS deal overdue if the provided invoice
   * is overdue and has a subscription linked to it.
   * @param chargebeeInvoice Chargebee `id` of the overdue invoice linked to a subscription
   * @returns A promise which resolves to `OverdueSubscriptionUpdateResult` upon completion
   * OR to `{success: boolean, alreadyProcessed: boolean}` if invoice is already processed.
   */
  async processOverdueSubscription(
    chargebeeInvoice: any,
  ): Promise<CronLogResult> {
    const invoiceId = chargebeeInvoice?.id;
    const invoiceIsProcessed = await this.dataServices.invoice.getOneBy({
      chargebeeId: invoiceId,
      overdueCheckPassed: true,
    });
    if (invoiceIsProcessed) {
      return {
        success: true,
        message: `Subscription is already updated for this chargebee invoice: ${invoiceId}`,
        output: {
          subscriptionUpdated: false,
          hsResult: null,
          alreadyProcessed: false,
          subscriptionId: invoiceIsProcessed?.subscriptionId,
        },
      };
    }
    const relatedSubscriptionId = chargebeeInvoice?.subscription_id;
    return this.markOverDueAndUpdateDeal(relatedSubscriptionId, invoiceId);
  }

  /**
   * Updates the line items' amount and billing cycles for a subscription. Also updates the
   * billing cycle for the whole subscription.
   * @param id Chargebee `id` of the subscription to update items for
   * @param updates Updates for the subscription, including line items & updated billing cycles
   * @returns A promise that resolves with
   */
  async updateBillingCyclesAndAmount(
    id: string,
    updates: UpdateSubscriptionDTO,
  ): Promise<PXActionResult> {
    try {
      const subscription = await this.dataServices.subscription.getOneBy({
        chargebeeId: id,
      });
      if (id.includes('PX_MIG')) {
        return this.chargebeeService.updateSubscriptionLineItems(id, updates);
      }
      const [prices] = await this.dataServices.productPlanPrice
        .getRepository()
        .createQueryBuilder('productPlanPrice')
        .leftJoinAndSelect('productPlanPrice.productPlan', 'productPlan')
        .where('productPlanPrice.chargebeeId IN (:...ids)', {
          ids: updates.updatedItems.map((x) => x.id),
        })
        .getManyAndCount();

      if (subscription?.crmId) {
        await this.hubspotService.updateLineItemsForDownsell(
          subscription,
          updates,
          prices,
        );
      }

      await this.chargebeeService.updateSubscriptionLineItems(id, updates);
      return { success: true, message: 'Subscription updated successfully' };
    } catch (err) {
      return {
        success: false,
        message: `Error occurred while updating subscription: ${err}`,
      };
    }
  }

  /**
   * Get the attached products for a subscription
   * @param subscriptionId Chargebee `id` of the subscription to get products for
   * @returns A promise that resolves with the attached products
   */
  async getAttachedProducts(subscriptionId: string): Promise<any> {
    try {
      const subscription = await this.dataServices.subscription.getOneBy({
        chargebeeId: subscriptionId,
      });
      if (!subscription) {
        throw new HttpException(
          `Subscription not found with ID: ${subscriptionId}`,
          HttpStatus.NOT_FOUND,
        );
      }
      const invoices = await this.dataServices.invoice.getAllBy(
        {
          subscriptionId,
        },
        100,
        1,
      );
      const subscriptionItems = subscription.subscriptionItems;
      if (subscriptionId.includes('PX_MIG')) {
        return subscriptionItems;
      }
      if (invoices?.total === 0 || invoices?.items?.length === 0) {
        throw new HttpException(
          `No invoice found for subscription with ID: ${subscriptionId}`,
          HttpStatus.NOT_FOUND,
        );
      }
      const invoiceOne = invoices.items[invoices.items.length - 1];
      const lineItems: InvoiceLineItemPX[] =
        invoiceOne.lineItems as InvoiceLineItemPX[];
      const addOnItemPriceIds = lineItems
        .filter((item) => item.entity_type === 'addon_item_price')
        .map((item) => item.entity_id);
      if (!lineItems || !addOnItemPriceIds) {
        throw new HttpException(
          `No line items found for subscription with ID: ${subscriptionId}`,
          HttpStatus.NOT_FOUND,
        );
      }
      const addOnItemPrices: ProductPlanPrice[] = await Promise.all(
        addOnItemPriceIds.map(async (i) => {
          return await this.dataServices.productPlanPrice.getOneBy({
            chargebeeId: i,
          });
        }),
      );
      const lineItemsWithPrices: EditSubscriptionLineItem[] = lineItems.map(
        (item) => {
          const lineItemWithPrice = item as EditSubscriptionLineItem;
          lineItemWithPrice.price = addOnItemPrices.find(
            (p) => p.chargebeeId === item.entity_id,
          );
          const subscriptionItem = subscriptionItems.find(
            (si) => si.item_price_id === item.entity_id,
          );
          lineItemWithPrice.billingCyclesRemaining =
            subscriptionItem?.billing_cycles;
          lineItemWithPrice.amountPerBillingCycle = subscriptionItem?.amount;
          return lineItemWithPrice;
        },
      );
      return {
        existingBillingCycles: lineItemsWithPrices[0].price?.totalBillingCycles,
        products: lineItemsWithPrices,
      };
    } catch (error) {
      throw new HttpException(
        `Something went wrong while getting products for subscription: ${subscriptionId}`,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Get the offer details for a subscription
   * @param subscriptionId Chargebee `id` of the subscription to get offer for
   * @returns A promise that resolves with the offer details
   */
  async getOfferDetails(subscriptionId: string): Promise<any> {
    try {
      const subscription = await this.dataServices.subscription.getOneBy({
        chargebeeId: subscriptionId,
      });
      if (!subscription) {
        throw new HttpException(
          `Subscription not found with ID: ${subscriptionId}`,
          HttpStatus.NOT_FOUND,
        );
      }
      const chargebeePlan = subscription?.subscriptionItems.filter(
        (x) => x.item_type === 'plan',
      )[0];
      const planPrice = await this.chargebeeService.getPlanPrice(
        chargebeePlan.item_price_id,
      );
      const itemPriceInChargebee = planPrice?.item_price;
      const subscriptionOffer = await this.dataServices.productOffer.getOneBy({
        chargebeeId: itemPriceInChargebee?.item_id,
      });
      if (!subscriptionOffer) {
        throw new HttpException(
          `No offer found for subscription with ID: ${subscriptionId}`,
          HttpStatus.NOT_FOUND,
        );
      }
      const bc =
        this.productOfferFactoryService.getOfferBillingCycles(
          subscriptionOffer,
        );
      return {
        offerName: subscriptionOffer?.name,
        billingCycles: bc,
      };
    } catch (error) {
      throw new HttpException(
        `Something went wrong while getting offer for subscription: ${subscriptionId}`,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Downsell a subscription to another offer
   * @param subscriptionId Chargebee `id` of the subscription to downsell
   * @param updates New subscription details to create a new subscription
   * @returns A promise that resolves with the new subscription details
   */
  async downsell(
    subscriptionId: string,
    updates: SubscriptionDownsellParams,
  ): Promise<any> {
    try {
      const subscription = await this.dataServices.subscription.getOneBy({
        chargebeeId: subscriptionId,
      });
      const newSubscriptionId = `PX-SUB-${Math.floor(Date.now() / 1000)}`;
      await this.chargebeeService.updateSubscriptionMetadata(subscriptionId, {
        ...subscription.metadata,
        cancelled_for_downsell: true,
        new_subscription_id: newSubscriptionId,
      });
      await this.chargebeeService.cancelSubscriptionForItems(
        subscriptionId,
        'Order Change',
      );
      updates.auto_collection = subscription.autoCollection as 'on' | 'off';
      updates.id = newSubscriptionId;
      updates.business_entity_id = subscription.businessEntityId;
      const newSubscription = await this.chargebeeService.createSubscription(
        subscription.customerId,
        updates,
      );
      return newSubscription;
    } catch (error) {
      throw new HttpException(
        `Something went wrong while downselling subscription: ${subscriptionId}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /*
   * Retrieves the details of orders for the given subscriptions.
   *
   * @param body - The DTO containing the subscription IDs.
   * @returns A promise that resolves to an object containing the retrieved items and the total count.
   * @throws {HttpException} If an error occurs while retrieving the subscriptions status and order amount.
   */
  async getOrdersDetails(body: GetSubscriptionsStatusDTO): Promise<any> {
    try {
      const queryBuilder = this.dataServices.subscription
        .getRepository()
        .createQueryBuilder('subscription')
        .select([
          'subscription.chargebeeId',
          'subscription.orderStatus',
          'subscription.totalOrderAmount',
        ])
        .where('subscription.chargebeeId IN (:...chargebeeIds)', {
          chargebeeIds: body.subscriptionIds,
        })
        .take(body.subscriptionIds.length)
        .leftJoin(
          'productOffers',
          'offer',
          'offer.chargebeeId = subscription."customFields"->>\'cf_os_offer_id\'',
        )
        .addSelect([
          'offer."chargebeeId"',
          'offer."name"',
          'offer."cardImage"',
          'offer."bannerImage"',
          'offer."description"',
          'offer."usp"',
          'offer."lmsIds"',
        ])
        .orderBy('subscription.createdAt', 'DESC');

      const subscriptionsWithOfferInfo = await queryBuilder.getRawMany();
      return subscriptionsWithOfferInfo.map((record) => ({
        subscription: {
          chargebeeId: record.subscription_chargebeeId,
          orderStatus: record.subscription_orderStatus,
          totalOrderAmount: record.subscription_totalOrderAmount,
        },
        offer: {
          chargebeeId: record.chargebeeId,
          name: record.name,
          cardImage: record.cardImage,
          bannerImage: record.bannerImage,
          description: record.description,
          usp: record.usp,
          lmsIds: record.lmsIds,
        },
      }));
    } catch (error) {
      console.error(error);
      throw new HttpException(
        `Something went wrong while getting subscriptions status and order amount`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Retrieves the order details by the Chargebee ID.
   *
   * @param chargebeeId - The Chargebee ID of the subscription.
   * @returns A promise that resolves to an object containing the order details.
   * @throws {HttpException} If an error occurs while retrieving the details.
   */
  async getOrderDetailsByChargebeeId(chargebeeId: string): Promise<any> {
    try {
      const queryBuilder = this.dataServices.subscription
        .getRepository()
        .createQueryBuilder('subscription')
        .select([
          'subscription.chargebeeId',
          'subscription.orderStatus',
          'subscription.totalOrderAmount',
        ])
        .where('subscription.chargebeeId = :chargebeeId', {
          chargebeeId,
        })
        .take(1)
        .orderBy('subscription.createdAt', 'DESC');
      const item = await queryBuilder.getOneOrFail();
      return item ?? {};
    } catch (error) {
      throw new HttpException(
        `Something went wrong while getDetailsByChargebeeId`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Retrieves all subscriptions for a user
   * @param userEmail The email of the user to retrieve subscriptions for
   * @returns A promise that resolves to an object containing the subscriptions and the total count
   */
  async getAllForUser(
    userEmail: string,
  ): Promise<{ items: Subscription[]; total: number }> {
    const queryBuilder = this.dataServices.subscription
      .getRepository()
      .createQueryBuilder('subscription')
      .where('subscription.customerEmail = :userEmail', {
        userEmail,
      })
      .select([
        'subscription.id as id',
        'subscription.chargebeeId as chargebeeId',
        'subscription.orderStatus as "orderStatus"',
        'TO_TIMESTAMP(subscription.startedAt) as "startedAt"',
      ])
      .leftJoin(
        'productOffers',
        'offer',
        'offer.chargebeeId = subscription."customFields"->>\'cf_os_offer_id\'',
      )
      .addSelect(['offer."name"', 'offer."image"'])
      .orderBy('subscription.startedAt', 'DESC');
    const items = await queryBuilder.getRawMany();
    return {
      items,
      total: items.length,
    };
  }

  /**
   * Retrieves the details of subscriptions with their invoices
   * @param subscriptionId The Chargebee ID of the subscription to retrieve details for
   * @param email The email of the user to retrieve subscriptions for
   * @returns A promise that resolves to an object containing the subscription details and the invoices
   */
  private async fetchSubscriptionWithInvoiceRows(
    subscriptionId: string,
    email: string,
  ): Promise<any[]> {
    return this.dataServices.subscription
      .getRepository()
      .createQueryBuilder('subscription')
      .select([
        'subscription.id AS "subscriptionId"',
        'subscription.chargebeeId AS "subscriptionChargebeeId"',
        'TO_TIMESTAMP(subscription.startedAt) AS "startedAt"',
        'TO_TIMESTAMP(subscription.nextBillingAt) AS "nextBillingAt"',
        'subscription.amountPaid AS "amountPaid"',
        'subscription.totalOrderAmount AS "totalOrderAmount"',
        'subscription.amountRemaining AS "amountRemaining"',
        'subscription.paymentSourceId AS "paymentSourceId"',
      ])
      .leftJoin(
        'invoices',
        'invoice',
        'invoice.subscriptionId = subscription.chargebeeId',
      )
      .addSelect([
        'invoice.id AS "invoiceId"',
        'invoice.chargebeeId AS "invoiceChargebeeId"',
        'TO_TIMESTAMP(invoice.date) AS "invoiceDate"',
        'invoice.status AS "invoiceStatus"',
        'invoice.amountPaid AS "invoiceAmountPaid"',
        'invoice.total AS "invoiceTotal"',
      ])
      .where('subscription.customerEmail = :email', { email })
      .andWhere('subscription.chargebeeId = :subscriptionId', {
        subscriptionId,
      })
      .orderBy('invoice.createdAt', 'DESC')
      .getRawMany();
  }

  /**
   * Retrieves the details of subscriptions with their invoices
   * @param subscriptionId The Chargebee ID of the subscription to retrieve details for
   * @param email The email of the user to retrieve subscriptions for
   * @returns A promise that resolves to an object containing the subscription details and the invoices OR null if no subscription is found
   */
  async getDetailsWithInvoices(
    subscriptionId: string,
    email: string,
  ): Promise<SubscriptionInvoiceDetails | null> {
    const rows = await this.fetchSubscriptionWithInvoiceRows(
      subscriptionId,
      email,
    );
    if (!rows.length) return null;

    const [firstRow] = rows;

    const paymentSource = await this.getPaymentSourceInfo(
      firstRow.subscriptionChargebeeId,
    );

    const invoices = await Promise.all(
      rows
        .filter((row) => row.invoiceId)
        .map(async (row) => ({
          id: row.invoiceId,
          date: row.invoiceDate,
          status: row.invoiceStatus,
          amountPaid: GlobalHelpers.centsToDecimal(row.invoiceAmountPaid),
          total: GlobalHelpers.centsToDecimal(row.invoiceTotal),
          downloadLink: await this.chargebeeService.getInvoicePDFLink(
            row.invoiceChargebeeId,
          ),
        })),
    );

    return {
      subscriptionId: firstRow.subscriptionId,
      chargebeeId: firstRow.subscriptionChargebeeId,
      startedAt: firstRow.startedAt,
      nextBillingAt: firstRow.nextBillingAt,
      amountPaid: firstRow.amountPaid,
      totalAmount: firstRow.totalOrderAmount,
      amountRemaining: firstRow.amountRemaining,
      paymentSource,
      invoices,
    };
  }

  /**
   * Retrieves the payment source information for a subscription
   * @param chargebeeId Chargebee `id` of the subscription to retrieve the payment source for
   * @returns A promise that resolves to the payment source information
   */
  async getPaymentSourceInfo(
    chargebeeId: string,
  ): Promise<PaymentSourceInfo | null> {
    const paymentSource =
      await this.chargebeeService.getPaymentSourceForSubscription(chargebeeId);

    if (!paymentSource) return null;
    if (paymentSource === 'bank_transfer') {
      return {
        type: 'bank_transfer',
      };
    }
    return {
      type: 'card',
      card: {
        brand: paymentSource.card_type,
        maskedNumber: paymentSource.masked_number,
      },
    };
  }

  /**
   * Checks if the subscription is eligible for suspension
   * @param subscription `Subscription` entity
   * @returns A promise that resolves to a boolean indicating
   * whether the subscription is eligible for suspension
   */
  async canBeSuspended(subscription: Subscription): Promise<boolean> {
    const offerId = subscription?.customFields?.cf_os_offer_id;
    const offer = await this.productOfferUseCases.getOneBy({
      chargebeeId: offerId,
    });
    if (offer?.suspendable) {
      const delayPeriod = offer.delayPeriod;
      const today = new Date();
      const todayWithDelayDaysAdded = new Date(
        today.getTime() + delayPeriod * 24 * 60 * 60 * 1000,
      );
      if (!(todayWithDelayDaysAdded >= today)) return false;
    }
    return true;
  }

  /**
   * Exports all the subscriptions currently we have in DB in a CSV file
   * @param responseHandler Response object to attach the generated export file to
   * @returns A promise which resolves to response object with attached CSV file
   */
  async export(
    responseHandler: Response,
    range: ExportSubscriptionsDTO,
  ): Promise<any> {
    const queryStream: ReadStream = await this.dataServices.subscription
      .getRepository()
      .createQueryBuilder('subscription')
      .where('subscription.startedAt BETWEEN :startDate AND :endDate', {
        startDate: range.startDate,
        endDate: range.endDate,
      })
      .leftJoin('subscription.attachedPrices', 'attachedPrices')
      .leftJoin('attachedPrices.productPlan', 'productPlan')
      .leftJoinAndSelect('productPlan.product', 'product')
      .leftJoin('product.productFamily', 'family')
      .leftJoin('family.productLine', 'line')
      .innerJoin(
        ProductOfferTable,
        'offer',
        'offer.chargebeeId = subscription."customFields"->>\'cf_os_offer_id\'',
      )
      .select(this.EXPORT_FIELDS.split(','))
      .orderBy('subscription.startedAt', 'DESC')
      .stream();

    responseHandler.setHeader('Content-Type', 'text/csv');
    const exportFileName = `PX_OS_Subscriptions_${new Date().toISOString()}.csv`;
    responseHandler.setHeader(
      'Content-Disposition',
      `attachment; filename="${exportFileName}"`,
    );

    const headers = this.EXPORT_FIELDS.match(/AS\s+(\w+)/gi).map(
      (alias) => alias.split(' ')[1],
    );
    responseHandler.write(headers.join(',') + '\n');
    const addedSubscriptions: string[] = [];
    queryStream.on('data', (row) => {
      const processedRow = Object.entries(row).map(([, value]) => {
        return typeof value === 'string'
          ? `"${value.replace(/"/g, '""')}"`
          : value;
      });

      const subscriptionId = processedRow[0];
      if (addedSubscriptions.includes(subscriptionId)) return;

      const csvRow = `${processedRow.join(',')}\n`;
      responseHandler.write(csvRow);
      addedSubscriptions.push(subscriptionId);
    });

    queryStream.on('end', () => {
      responseHandler.end();
    });

    queryStream.on('error', (err) => {
      console.error(err);
      responseHandler.status(500).send('Internal Server Error');
    });

    return responseHandler;
  }

  /**
   * Retrieves the subscription invoices, credit notes and transactions by the Chargebee ID.
   * @param chargebeeId - The Chargebee ID of the subscription.
   * @returns A promise that resolves to an object containing the subscription details.
   * @throws {HttpException} If an error occurs while retrieving the details.
   */
  async getHistoricalData(chargebeeId: string): Promise<any> {
    try {
      const subscription = await this.dataServices.subscription.getOneBy({
        chargebeeId,
      });
      if (!subscription) {
        throw new HttpException(
          `No subscription found with Chargebee ID: ${chargebeeId}`,
          HttpStatus.NOT_FOUND,
        );
      }
      const invoices = await this.dataServices.invoice.getAllBy(
        {
          subscriptionId: subscription.chargebeeId,
        },
        100,
        1,
      );
      const creditNotes = await this.dataServices.creditNote.getAllBy(
        {
          subscriptionId: subscription.chargebeeId,
        },
        100,
        1,
      );
      const transactions = await this.dataServices.transaction.getAllBy(
        {
          subscriptionId: subscription.chargebeeId,
        },
        100,
        1,
      );
      return {
        invoices: invoices.items,
        creditNotes: creditNotes.items,
        transactions: transactions.items,
      };
    } catch (error) {
      throw new HttpException(
        `Something went wrong while getting subscription details`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Retrieves the subscription Line items (extrapolated from invoices) by
   *  the Chargebee ID.
   * @param subscriptionId - The Chargebee ID of the subscription.
   * @returns A promise that resolves to an object containing the subscription line items.
   * @throws {HttpException} If an error occurs while retrieving the details.
   */
  async getSubscriptionLineItems(subscriptionId: string): Promise<any> {
    try {
      const subscription = await this.dataServices.subscription.getOneBy({
        chargebeeId: subscriptionId,
      });
      if (!subscription) {
        throw new HttpException(
          `Subscription not found with ID: ${subscriptionId}`,
          HttpStatus.NOT_FOUND,
        );
      }
      const subscriptionItems = subscription.subscriptionItems;
      const invoices = await this.dataServices.invoice.getAllBy(
        {
          subscriptionId,
        },
        100,
        1,
      );
      let invoiceLineItems: InvoiceLineItemPX[];
      let billingCycles =
        invoices.total + (subscriptionItems[0].billing_cycles || 0);
      if (invoices.items.length === 0) {
        if (
          subscription.status !== SubscriptionStatus.CANCELLED &&
          subscription.status !== SubscriptionStatus.NON_RENEWING
        ) {
          const subscriptionEstimate =
            await this.chargebeeService.getFutureInvoiceEstimate(
              subscription.chargebeeId,
            );
          invoiceLineItems = subscriptionEstimate.invoice_estimate.line_items;
        }
      } else {
        const lineItemsToCheck = invoices.items[invoices.items.length - 1]
          .lineItems as InvoiceLineItemPX[];
        const filteredSubscriptionItems = subscription.subscriptionItems.filter(
          (item) =>
            (item.item_type === 'addon' ||
              (subscriptionId.includes('PX_MIG') &&
                item.item_type === 'plan')) &&
            item.amount !== 0 &&
            item.unit_price !== 0,
        );
        if (
          this.isInvoiceOutOfDate(filteredSubscriptionItems, lineItemsToCheck)
        ) {
          if (
            subscription.status !== SubscriptionStatus.CANCELLED &&
            subscription.status !== SubscriptionStatus.NON_RENEWING
          ) {
            const subscriptionEstimate =
              await this.chargebeeService.getFutureInvoiceEstimate(
                subscription.chargebeeId,
              );
            invoiceLineItems = subscriptionEstimate.invoice_estimate.line_items;
            billingCycles = subscriptionItems[0].billing_cycles;
          }
        } else {
          invoiceLineItems = lineItemsToCheck;
        }
      }
      return { lineItems: invoiceLineItems, billingCycles };
    } catch (error) {
      throw new HttpException(
        `Something went wrong while getting products for subscription: ${subscriptionId}`,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Checks if the invoice line items are out of date compared to the subscription line items.
   * @param subscriptionLineItems - The subscription line items.
   * @param invoiceLineItems - The invoice line items.
   * @returns A boolean indicating if the invoice line items are out of date.
   */
  isInvoiceOutOfDate(
    subscriptionLineItems: SubscriptionItem[],
    invoiceLineItems: InvoiceLineItemPX[],
  ): boolean {
    if (subscriptionLineItems.length === 0) {
      return false;
    }
    for (const subscriptionItem of subscriptionLineItems) {
      const invoiceItem = invoiceLineItems.find(
        (item) =>
          item.entity_id === subscriptionItem.item_price_id &&
          item.amount === subscriptionItem.amount &&
          item.unit_amount === subscriptionItem.unit_price,
      );
      if (!invoiceItem) {
        return true;
      }
    }
    return false;
  }

  /**
   * Resyncs subscription info with Chargebee
   * @param chargebeeId Chargebee ID of the subscription to resync
   * @returns A promise that resolves to the re-synced subscription info
   */
  async resyncWithChargebee(chargebeeId: string): Promise<any> {
    const chargebeeSubscription = await this.chargebeeService.getSubscription(
      chargebeeId,
    );
    if (!chargebeeSubscription) {
      throw new HttpException(
        `Subscription not found in Chargebee with ID: ${chargebeeId}`,
        HttpStatus.NOT_FOUND,
      );
    }
    const currentVersion = await this.getOne(chargebeeId);
    const updatedVersion = await this.factory.getUpdatedFields(
      chargebeeSubscription,
      currentVersion,
    );
    const result = await this.dataServices.subscription.updateAndReturnItem(
      'id',
      currentVersion.id.toString(),
      {
        ...updatedVersion,
      },
    );
    return result?.success ? result?.updatedItem : currentVersion;
  }

  /**
   * Retrieves the webhook events by the Chargebee ID.
   * @param chargebeeId - The Chargebee ID of the subscription.
   * @returns A promise that resolves to an object containing the webhook events.
   */
  async getWebhookEvents(chargebeeId: string): Promise<any> {
    return this.dataServices.webhook
      .getRepository()
      .createQueryBuilder('webhook')
      .where('webhook.outcome ::jsonb @> :outcome', {
        outcome: {
          affectedEntity: 'subscription',
          affectedEntityId: chargebeeId,
        },
      })
      .orderBy('webhook.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Imports the subscription data from a json file
   * @param body The json data to import
   * @returns A promise that resolves to the imported subscription data
   */
  async importSubscriptionData(body: { jsonData: string }) {
    const offset = 100000;
    const jsonData = JSON.parse(body.jsonData);
    const newSubId = `PX_DBG_${jsonData.subscription.chargebeeId}`;
    // this should be decimal (no float)
    const now = Math.floor(new Date().getTime() / 1000);
    // Create a new subscription
    await this.dataServices.subscription.create({
      ...jsonData.subscription,
      chargebeeId: newSubId,
      id: jsonData.subscription.id + offset,
      createdAt: now,
      updatedAt: now,
      metadata: {
        ...jsonData.subscription.metadata,
        import_for_testing: 'true',
        originalId: jsonData.subscription.id,
        originalChargebeeId: jsonData.subscription.chargebeeId,
      },
    });
    // Create a new invoices for the subscription (if any)
    if (jsonData.history.invoices) {
      for (const invoice of jsonData.history.invoices) {
        await this.dataServices.invoice.create({
          ...invoice,
          id: invoice.id + offset,
          subscriptionId: newSubId,
          chargebeeId: `PX_DBG_${invoice.chargebeeId}`,
        });
      }
    }
    if (jsonData.history.creditNotes) {
      for (const creditNote of jsonData.history.creditNotes) {
        await this.dataServices.creditNote.create({
          ...creditNote,
          id: creditNote.id + offset,
          subscriptionId: newSubId,
          chargebeeId: `PX_DBG_${creditNote.chargebeeId}`,
        });
      }
    }
    if (jsonData.history.transactions) {
      for (const transaction of jsonData.history.transactions) {
        await this.dataServices.transaction.create({
          ...transaction,
          id: transaction.id + offset,
          subscriptionId: newSubId,
          chargebeeId: `PX_DBG_${transaction.chargebeeId}`,
        });
      }
    }
    return { success: true };
  }
}
