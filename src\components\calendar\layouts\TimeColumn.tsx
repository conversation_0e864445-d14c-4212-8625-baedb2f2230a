"use client";

export default function TimeColumn() {
  return (
    <div
      data-group="time-column"
      className="mt-[-0.125rem] flex h-auto min-w-10 max-w-10 flex-none flex-col items-start"
    >
      <div className="relative ml-auto">
        <div className="h-[60px] pr-2 text-right text-[0.75rem] text-gray-500" />
        {Array.from(Array(16).keys()).map((index) => {
          return (
            <div
              className="relative h-[60px] pr-2 text-right text-xs text-gray-500"
              key={`time-${index}`}
            >
              <span>{index + 8}</span>
            </div>
          );
        })}
        <div className="h-12 pr-2 text-right text-sm text-gray-500" />
      </div>
    </div>
  );
}
