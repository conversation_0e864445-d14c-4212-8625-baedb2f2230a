{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2021",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "paths": {
      "@abstracts": ["src/core/abstracts"],
      "@auth": ["src/auth"],
      "@config": ["src/config"],
      "@controllers": ["src/controllers"],
      "@dtos": ["src/core/dtos"],
      "@entities": ["src/core/entities"],
      "@enums": ["src/core/enums"],
      "@helpers": ["src/helpers"],
      "@services/*": ["src/services/*"],
      "@tables": ["src/services/database/postgres/tables"],
      "@types": ["src/core/types"],
      "@useCases": ["src/use-cases"],
      "@errors": ["src/common/exceptions/errors"],
      "@filters": ["src/common/exceptions/filters"],
      "@interceptors": ["src/common/interceptors"],
    }
  }
}
