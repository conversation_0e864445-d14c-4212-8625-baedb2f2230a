import { CronLogType } from '@enums';

export type CronLogResult = {
  success: boolean;
  message: string;
  input?: any;
  output?: any;
};

export type OverdueSubscriptionUpdateResult = {
  success: boolean;
  message: string;
  subscriptionUpdated: boolean;
  hsResult: any;
  alreadyProcessed?: boolean;
  subscriptionId: string;
};

export type ExhaustedCronJob = {
  type: CronLogType;
  logId: string;
  result: CronLogResult;
};
