"use client";

import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface SessionBadgeProps {
  session: string;
}

export function SessionBadge({ session }: SessionBadgeProps) {
  const t = useTranslations("triad");

  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full px-3 text-sm font-medium",
        "bg-blue-500/10 text-blue-500",
      )}
    >
      {t("session_badge", { session })}
    </span>
  );
}
