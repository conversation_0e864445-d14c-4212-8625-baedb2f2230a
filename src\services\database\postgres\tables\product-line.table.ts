import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProductFamilyTable } from './product-family.table';
import { ProductLine } from '@entities';

@Entity({
  name: 'productLines',
})
export class ProductLineTable implements ProductLine {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'varchar',
    unique: true,
  })
  name: string;

  @Column({
    type: 'varchar',
  })
  description: string;

  @OneToMany(
    () => ProductFamilyTable,
    (productFamily) => productFamily.productLine,
    {
      eager: true,
    },
  )
  families: ProductFamilyTable[];

  @Column({
    type: 'varchar',
  })
  status: string;

  @Column({
    type: 'varchar',
    length: 100,
  })
  chargebeeId: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deletedAt?: Date;
}
