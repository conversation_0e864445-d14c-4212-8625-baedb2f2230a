"use client";

import { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { motion, AnimatePresence } from "framer-motion";
import {
  IWhatsNewVersion,
  getLocalizedContent,
  ICreateWhatsNewPayload,
  IUpdateWhatsNewPayload,
} from "@px-shared-account/hermes";
import { Plus, ImageIcon } from "lucide-react";
import { Button } from "@/components/base/button";
import { useCustomerAccessDetails } from "@/services/customers";
import { useSession } from "@clerk/nextjs";
import {
  useWhatsNewVersions,
  useCreateWhatsNewVersion,
  useUpdateWhatsNewVersion,
  useDeleteWhatsNewVersion,
} from "@/services/whats-new/client";
import AdminForm from "./admin-form";
import ChangelogEntry from "./changelog-entry";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

export default function WhatsNewPage() {
  const t = useTranslations("whats-new");
  const locale = useLocale();
  const [showAdminForm, setShowAdminForm] = useState(false);
  const [editingVersion, setEditingVersion] = useState<IWhatsNewVersion | null>(null);

  const pathname = usePathname();

  // Fetch user access details
  const { session } = useSession();
  const userEmail = session?.user?.primaryEmailAddress?.emailAddress;
  const { data: accessData, isLoading: isAccessLoading } = useCustomerAccessDetails(userEmail);

  // Determine if user is admin (Paradox email)
  const isAdmin = accessData?.accessInfo.isParadox ?? false;

  // Use the client hooks
  const { data, error: fetchError, isLoading, mutate: refetchVersions } = useWhatsNewVersions();
  const { create, error: createError } = useCreateWhatsNewVersion();
  const { update, error: updateError } = useUpdateWhatsNewVersion();
  const { deleteVersion, error: deleteError } = useDeleteWhatsNewVersion();

  // Combine all errors
  const error = fetchError || createError || updateError || deleteError;

  const handleCreate = async (data: ICreateWhatsNewPayload) => {
    try {
      await create(data);
      await refetchVersions();
      setShowAdminForm(false);
    } catch (err) {
      // Error already handled by the hook
    }
  };

  const handleUpdate = async (data: IUpdateWhatsNewPayload) => {
    try {
      await update(data);
      await refetchVersions();
      setEditingVersion(null);
      setShowAdminForm(false);
    } catch (err) {
      // Error already handled by the hook
    }
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm(t("admin.confirm-delete"))) return;

    try {
      await deleteVersion(id);
      await refetchVersions();
    } catch (err) {
      // Error already handled by the hook
    }
  };

  const handleEdit = (versionId: number) => {
    const versionToEdit = data?.versions.find((v) => v.id === versionId) || null;
    setEditingVersion(versionToEdit);
    setShowAdminForm(true);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto max-w-4xl px-4 py-16">
        <div className="animate-pulse space-y-12">
          <div className="h-10 w-1/4 rounded bg-[#333]" />
          <div className="h-[200px] w-full rounded bg-[#333]" />
          <div className="h-[200px] w-full rounded bg-[#333]" />
        </div>
      </div>
    );
  }

  const versions = data?.versions || [];
  const localizedVersions = versions.map((version) =>
    getLocalizedContent(version, (locale || "en") as "en" | "fr"),
  );

  // Build the media management URL
  const mediaPageUrl = `/${locale}/whats-new/media`;

  return (
    <>
      <div className="px-4">
        <BreadcrumbSettings active="/whats-new" />
      </div>
      <div className="container mx-auto max-w-4xl px-4 py-16">
        <div className="mb-8 flex items-center gap-4">
          <h1 className={"font-anton text-4xl font-bold"}>{t("title")}</h1>
          {isAdmin && (
            <div className="flex gap-2">
              {!showAdminForm && !editingVersion && (
                <Button size="sm" onClick={() => setShowAdminForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  {t("admin.add-version")}
                </Button>
              )}
              <Button size="sm" variant="outline" asChild>
                <Link href={mediaPageUrl}>
                  <ImageIcon className="mr-2 h-4 w-4" />
                  {t("admin.media-management")}
                </Link>
              </Button>
            </div>
          )}
        </div>

        {error && (
          <div className="mb-6 rounded-lg border border-red-500 bg-red-900/50 p-4">
            <p className="text-red-500">{error.message}</p>
          </div>
        )}

        {isAdmin && showAdminForm && (
          <AdminForm
            onSubmit={{ create: handleCreate, update: handleUpdate }}
            onCancel={() => {
              setShowAdminForm(false);
              setEditingVersion(null);
            }}
            initialData={editingVersion || undefined}
            isEditing={!!editingVersion}
          />
        )}

        <div className="space-y-12">
          <AnimatePresence mode="popLayout">
            {localizedVersions.map((version) => (
              <motion.div key={version.id}>
                <ChangelogEntry
                  version={version}
                  isAdmin={isAdmin}
                  onEdit={() => handleEdit(version.id)}
                  onDelete={() => handleDelete(version.id)}
                />
              </motion.div>
            ))}
          </AnimatePresence>

          {localizedVersions.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-muted-foreground py-12 text-center"
            >
              {t("no-entries")}
            </motion.div>
          )}
        </div>
      </div>
    </>
  );
}
