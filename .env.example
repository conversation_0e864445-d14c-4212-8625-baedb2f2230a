# NodeJS Setup
NODE_ENV=local
PX_OS_URL=http://127.0.0.1:3001

# Database
DB_HOST=paradox-api-staging.cfgimfe2w1ow.eu-west-3.rds.amazonaws.com
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=
DB_DATABASE_NAME=paradox-api

# Sentry
SENTRY_DSN=
TRACE_SAMPLE_RATE=
PROFILE_SAMPLE_RATE=
SENTRY_AUTH_TOKEN=

# API key (should be used by the client: finance-dashboard)
API_KEY=

# JWT
JWT_SECRET_KEY=
JWT_REFRESH_KEY=
TOKEN_EXPIRY=3600

# Stripe
STRIPE_ACC_ID_PG=
STRIPE_API_SK_PG=
STRIPE_ACC_ID_WLD=
STRIPE_API_SK_WLD=
STRIPE_ACC_ID_PI=
STRIPE_API_SK_PI=
STRIPE_ACC_ID_PM=
STRIPE_API_SK_PM=

# Chargebee
CHARGEBEE_API_KEY=
CHARGEBEE_SITE=
CHARGEBEE_GATEWAY_ACCOUNT_ID_PG=
CHARGEBEE_GATEWAY_ACCOUNT_ID_PI=
CHARGEBEE_API_URL=

# Redis
REDIS_CONNECTION_STRING=

# Clerk 
CLERK_PEM_PUBLIC_KEY=
