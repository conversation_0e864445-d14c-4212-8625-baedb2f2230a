"use client";

import { format } from "date-fns";

import useCalendarStore, { CalendarEvent } from "@/hooks/store/calendar";
import getCalendarEvent from "@/lib/calendar/getCalendarEvents";
import { cn } from "@/lib/utils";

type CalendarLayoutMonthProps = {
  weeks: any;
};

export default function CalendarLayoutMonth({ weeks }: CalendarLayoutMonthProps) {
  const { events } = useCalendarStore();

  const maxHeight = (weeks: any[]) => {
    const size = weeks.length;
    return {
      height: size === 5 ? "calc((100% / 5) - 21.2px)" : "calc((100% / 6) - 17.5px)",
    };
  };

  const getEventData = (day: Date) => {
    const { dayEvents, eventsByHour } = getCalendarEvent(events, day);

    const markers = eventsByHour.map((evHour: any) => {
      return dayEvents
        .filter((event: CalendarEvent) => new Date(event.begin).getHours() === evHour.hour)
        .map((event: CalendarEvent, index: number) => (
          <div
            key={`event-${event.id}`}
            className="z-30 mb-2 min-h-[23px] cursor-pointer overflow-hidden rounded rounded-tr border bg-[#E1E1E1] px-[3px] py-[1px] hover:z-40"
          >
            {event.title}
          </div>
        ));
    });
    return markers;
  };

  return (
    <>
      <div data-group="layout-month" className="grid grid-cols-7">
        {/* Weekday Headers */}
        {weeks[0].map((weekDay: Date, index: number) => (
          <div
            key={`calendar-column-header-label-${index}`}
            className={cn("border-b border-gray-300 p-2 text-center text-gray-600", {
              "bg-gray-200": index === 5 || index === 6,
            })}
          >
            <span className="capitalize">{format(weekDay, "eee")}</span>
          </div>
        ))}
      </div>

      {/* Calendar Weeks */}
      {weeks.map((week: any, weekIndex: number) => (
        <div
          key={`calendar-main-line-${weekIndex}`}
          className="grid grid-cols-7"
          style={maxHeight(weeks)}
        >
          {week.map((day: Date, dayIndex: number) => {
            const isToday = format(day, "ddMMyyyy") === format(new Date(), "ddMMyyyy");
            const eventsOfDay = getEventData(day);

            return (
              <div
                key={`calendar-main-line-${weekIndex}-column-${dayIndex}`}
                className={cn("relative border-b border-r border-gray-300 p-2")}
              >
                <div className="text-center">
                  <span
                    className={cn(
                      "inline-block h-6 w-6 rounded-full text-center text-white",
                      isToday ? "bg-burgundy" : "",
                    )}
                  >
                    {day.getDate()}
                  </span>
                  {day.getDate() === 1 && (
                    <span className="ml-1 text-sm">{format(new Date(day), "MMM")}</span>
                  )}
                </div>

                {/* Events of the Day */}
                {eventsOfDay && eventsOfDay.length > 0 && (
                  <div className="mt-2 flex flex-col space-y-1 overflow-hidden" data-date={day}>
                    {eventsOfDay}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      ))}
    </>
  );
}
