import { Module } from '@nestjs/common';
import {
  ChargebeeBillingModule,
  ChargebeeBillingService,
} from '@services/billing';
import { ProductPlanFactory, ProductPlanUseCases } from './';
import {
  ProductPlanPriceUseCasesModule,
  ProductPlanPriceFactory,
} from '../product-plan-price';
import { DataServicesModule } from '@services/database';
import { HubspotModule } from '@services/crm';

@Module({
  imports: [
    DataServicesModule,
    ChargebeeBillingModule,
    ProductPlanPriceUseCasesModule,
    HubspotModule,
  ],
  providers: [
    ProductPlanUseCases,
    ProductPlanFactory,
    ChargebeeBillingService,
    ProductPlanPriceFactory,
  ],
  exports: [ProductPlanUseCases, ProductPlanFactory, ChargebeeBillingService],
})
export class ProductPlanUseCasesModule {}
