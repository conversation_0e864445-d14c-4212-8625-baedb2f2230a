"use client";

import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useCourseDetails } from "@/services/course";
import { Skeleton } from "@/components/ui/skeleton";
import Image from "next/image";

interface CourseInformationProps {
  id: number;
}

export function CourseInformation({ id }: CourseInformationProps) {
  const t = useTranslations("courses.details");

  const { data: course, isLoading } = useCourseDetails(id);

  if (isLoading) {
    return <Skeleton className="h-[200px] w-full" />;
  }

  if (!course) {
    return (
      <div className="flex h-[200px] items-center justify-center">
        <p className="text-muted-foreground">{t("not_found")}</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-stretch gap-8 md:flex-row">
      {/* Information Card */}
      <div className="w-full">
        <h2 className="mb-4 text-lg font-medium">{t("general_info")}</h2>
        <Card className="h-full rounded-3xl bg-secondary">
          <CardContent className="space-y-4 p-6">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">{t("name")}</h3>
              <p className="text-lg">{course.name}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">{t("description")}</h3>
              <p className="text-sm text-muted-foreground">{course.description}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Visuals Card */}
      <div className="w-full">
        <h2 className="mb-4 text-lg font-medium">{t("visuals")}</h2>
        <Card className="h-full rounded-3xl bg-secondary">
          <CardContent className="flex flex-col gap-6 p-6 md:flex-row">
            {/* Card Image */}
            <div className="flex-1">
              <h3 className="mb-4 text-sm font-medium text-muted-foreground">{t("card")}</h3>
              <div className="relative aspect-[9/16] max-h-full max-w-full overflow-hidden rounded-3xl">
                {course.cardImage ? (
                  <Image
                    src={course.cardImage}
                    alt={`${course.name} card`}
                    fill
                    className="object-cover"
                    priority={false}
                  />
                ) : (
                  <div className="flex h-full items-center justify-center bg-muted">
                    <p className="text-sm text-muted-foreground">{t("no_card")}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Right Column: Banner and Thumbnail */}
            <div className="flex flex-col gap-6 md:w-1/2">
              {/* Banner Image */}
              <div>
                <h3 className="mb-4 text-sm font-medium text-muted-foreground">{t("banner")}</h3>
                <div className="relative aspect-[2/1] overflow-hidden rounded-3xl">
                  {course.bannerImage ? (
                    <Image
                      src={course.bannerImage}
                      alt={`${course.name} banner`}
                      fill
                      className="object-cover"
                      priority={false}
                    />
                  ) : (
                    <div className="flex h-full items-center justify-center bg-muted">
                      <p className="text-sm text-muted-foreground">{t("no_banner")}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Thumbnail Image */}
              <div>
                <h3 className="mb-4 text-sm font-medium text-muted-foreground">{t("thumbnail")}</h3>
                <div className="relative aspect-square max-w-[50%] overflow-hidden rounded-3xl">
                  {course.thumbnail ? (
                    <Image
                      src={course.thumbnail}
                      alt={`${course.name} thumbnail`}
                      fill
                      className="object-cover"
                      priority={false}
                    />
                  ) : (
                    <div className="flex h-full items-center justify-center bg-muted">
                      <p className="text-sm text-muted-foreground">{t("no_thumbnail")}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
