import { Injectable, Logger } from '@nestjs/common';
import { createClient, RedisClientType } from 'redis';
import { ConfigService } from '@config';

@Injectable()
export class RedisClientsService {
  private readonly logger = new Logger(RedisClientsService.name);
  private readonly pxApiRedisClient: RedisClientType;
  private readonly vegaRedisClient: RedisClientType;
  private pxApiConnecting: Promise<any> | null = null;
  private vegaConnecting: Promise<any> | null = null;

  constructor(configService: ConfigService) {
    const cacheSecrets = configService.cacheSecrets;
    const pxApiRedisUrl = cacheSecrets.PX_API_REDIS_CONNECTION_STRING;
    this.pxApiRedisClient = createClient({
      url: pxApiRedisUrl,
      socket: { tls: true },
    });
    this.pxApiRedisClient.on('error', (err) => {
      this.logger.error('PX API Redis Client Error:', err);
    });
    const vegaRedisUrl = cacheSecrets.VEGA_REDIS_CONNECTION_STRING;
    this.vegaRedisClient = createClient({ url: vegaRedisUrl });
    this.vegaRedisClient.on('error', (err) => {
      this.logger.error('Vega Redis Client Error:', err);
    });
  }

  /**
   * Get the PX API Redis client
   * @returns The PX API Redis client
   */
  async getPxApiRedisClient(): Promise<RedisClientType> {
    if (!this.pxApiRedisClient.isOpen) {
      if (!this.pxApiConnecting) {
        this.logger.log('connecting to px api redis client');
        this.pxApiConnecting = this.pxApiRedisClient.connect().finally(() => {
          this.pxApiConnecting = null;
          this.logger.log('px api redis client connected');
        });
      }
      await this.pxApiConnecting;
    }
    return this.pxApiRedisClient;
  }

  /**
   * Get the Vega Redis client
   * @returns The Vega Redis client
   */
  async getVegaRedisClient(): Promise<RedisClientType> {
    if (!this.vegaRedisClient.isOpen) {
      if (!this.vegaConnecting) {
        this.logger.log('connecting to vega redis client');
        this.vegaConnecting = this.vegaRedisClient.connect().finally(() => {
          this.logger.log('inside finally vega redis client connected');
          this.vegaConnecting = null;
        });
      }
      await this.vegaConnecting;
    }
    return this.vegaRedisClient;
  }
}
