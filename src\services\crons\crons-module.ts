import { Modu<PERSON> } from '@nestjs/common';
import { ChargebeeBillingModule } from '@services/billing';
import { DataServicesModule } from '@services/database';
import {
  CronLogUseCasesModule,
  CustomerAccessModule,
  UserModule,
  SubscriptionUseCasesModule,
  WebhooksUseCasesModule,
} from '@useCases';
import { CronsService } from './crons-service';
import { SlackModule } from '@services/notification';

@Module({
  imports: [
    ChargebeeBillingModule,
    DataServicesModule,
    CronLogUseCasesModule,
    SubscriptionUseCasesModule,
    CustomerAccessModule,
    UserModule,
    SlackModule,
    WebhooksUseCasesModule,
  ],
  providers: [CronsService],
})
export class CronsModule {}
