import { Module } from '@nestjs/common';
import { CourseUseCases } from './course.use-cases';
import { DataServicesModule } from '@services/database';
import { CourseFactory } from './course.factory';
import { ClerkModule } from '@services/sso';
import { RedisClientsModule } from '@services/event-publishers';
import { CohortModule } from '../cohort';
import { ProductOfferUseCasesModule } from '../product-offers';
import {
  RedisPublisher,
  RedisClientsService,
} from '@services/event-publishers';
@Module({
  imports: [
    DataServicesModule,
    ClerkModule,
    CohortModule,
    ProductOfferUseCasesModule,
    RedisClientsModule,
  ],
  providers: [
    CourseUseCases,
    CourseFactory,
    {
      provide: RedisPublisher,
      useFactory: async (redisClientsService: RedisClientsService) => {
        const redisClient = await redisClientsService.getVegaRedisClient();
        return new RedisPublisher(redisClient);
      },
      inject: [RedisClientsService],
    },
  ],
  exports: [CourseUseCases],
})
export class CourseModule {}
