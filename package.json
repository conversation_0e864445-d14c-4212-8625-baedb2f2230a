{"name": "paradox-api", "version": "1.7.0-beta.4", "description": "", "private": true, "scripts": {"build": "nest build -p tsconfig.build.json", "doc:gen": "pnpm dlx @compodoc/compodoc -p tsconfig.json -s", "doc:run": "live-server documentation", "docker:build": "docker build -t paradox-api .", "docker:run": "docker-compose up --build", "docker:stop": "docker-compose down", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node --max-old-space-size=4096 dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typeorm": "ts-node -r tsconfig-paths/register -P tsconfig.json ./node_modules/typeorm/cli", "migration:run": "infisical run --env=$NODE_ENV --path=/px_db -- pnpm run typeorm migration:run -d ./src/services/database/migration.service.ts", "migration:generate": "infisical run --env=$NODE_ENV --path=/px_db -- pnpm run typeorm -d ./src/services/database/migration.service.ts migration:generate ./src/services/database/migrations/$migration && eslint ./src/services/database/migrations/*.ts --fix", "migration:revert": "infisical run --env=$NODE_ENV --path=/px_db -- pnpm run typeorm -d ./src/services/database/migration.service.ts migration:revert", "test": "jest --setupFiles dotenv/config", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "cli": "node -r ts-node/register -r tsconfig-paths/register ./src/cli.ts"}, "dependencies": {"@clerk/backend": "^1.13.4", "@hubspot/api-client": "^11.1.0", "@infisical/sdk": "^3.0.91", "@nestjs-enhanced/pg-boss": "^1.2.5", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.2", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.2", "@nestjs/swagger": "^7.1.2", "@nestjs/typeorm": "^10.0.0", "@px-shared-account/hermes": "^1.3.1", "@sentry/nestjs": "^9.6.0", "@sentry/node": "^9.15.0", "@slack/web-api": "^7.1.0", "@vercel/blob": "^1.1.1", "axios": "^1.9.0", "cache-manager": "^6.4.3", "chargebee-typescript": "^2.33.0", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "customerio-node": "^4.1.1", "date-fns": "^4.1.0", "dotenv": "^16.3.1", "express": "^4.18.2", "lodash": "^4.17.21", "moment": "^2.29.4", "nestjs-pino": "^3.3.0", "nestjs-zod": "^4.3.1", "node-cache": "^5.1.2", "passport": "^0.6.0", "passport-headerapikey": "^1.2.2", "pg": "^8.11.2", "pg-boss": "^10.3.2", "pg-query-stream": "^4.6.0", "pino-http": "^8.3.3", "pino-pretty": "^10.2.0", "redis": "^4.6.13", "redis-om": "^0.4.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "stripe": "^12.14.0", "svix": "^1.37.0", "typeorm": "^0.3.17", "zod": "^3.24.3"}, "devDependencies": {"@compodoc/compodoc": "^1.1.21", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.3", "@semantic-release/npm": "github:semantic-release/npm", "@semantic-release/release-notes-generator": "^14.0.3", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.4", "@types/node": "^20.3.1", "@types/passport": "^1.0.12", "@types/passport-http": "^0.3.9", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^9.1.7", "jest": "^29.5.0", "live-server": "^1.2.2", "prettier": "^2.8.8", "semantic-release": "^24.2.5", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}