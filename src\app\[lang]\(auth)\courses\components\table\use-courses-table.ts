import { useCallback, useMemo } from "react";
import { CourseStatus, IBaseCourse } from "@px-shared-account/hermes";
import { useQueryParams } from "@/hooks/use-query-params";

type TableState = {
  page: number;
  search: string;
  status?: CourseStatus;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
};

const itemsPerPage = 10;

export function useCoursesTable() {
  const { params, updateParams } = useQueryParams();

  // Convert URL params to table state
  const tableState = useMemo<TableState>(() => {
    const state = {
      page: parseInt(params.page as string) || 1,
      search: (params.search as string) || "",
      status: params.status as CourseStatus | undefined,
      sortBy: (params.sortBy as string) || "createdAt",
      sortOrder: (params.sortOrder as "asc" | "desc") || "desc",
    };
    return state;
  }, [params]);

  // Memoize state change handler
  const handleStateChange = useCallback(
    (state: any) => {
      const newPage = state.pageIndex + 1;
      const newSearch = state.searchQuery || "";

      // Handle sorting
      let newSortBy = tableState.sortBy;
      let newSortOrder = tableState.sortOrder;

      if (state.sorting && state.sorting.length > 0) {
        newSortBy = state.sorting[0].id;
        newSortOrder = state.sorting[0].desc ? "desc" : "asc";
      }

      // Get all current filter-related params
      const filterParams = Object.fromEntries(
        Object.entries(params).filter(
          ([key]) => !["page", "search", "sortBy", "sortOrder"].includes(key),
        ),
      );

      const updates = {
        ...filterParams, // Keep all filter params
        ...(newSearch !== tableState.search ? { search: newSearch } : {}),
        ...(newPage !== tableState.page ? { page: newPage.toString() } : {}),
        ...(newSortBy !== tableState.sortBy ? { sortBy: newSortBy } : {}),
        ...(newSortOrder !== tableState.sortOrder ? { sortOrder: newSortOrder } : {}),
      };

      // Only update if there are changes
      if (Object.keys(updates).length > 0) {
        updateParams(updates);
      }
    },
    [
      params,
      tableState.page,
      tableState.search,
      tableState.sortBy,
      tableState.sortOrder,
      updateParams,
    ],
  );

  // Memoize table configuration
  const tableConfig = useMemo(
    () => ({
      pageSize: itemsPerPage,
      onStateChange: handleStateChange,
      initialState: {
        pageIndex: tableState.page - 1,
        globalFilter: tableState.search,
        sorting: [
          {
            id: tableState.sortBy || "createdAt",
            desc: tableState.sortOrder === "desc",
          },
        ],
      },
    }),
    [
      handleStateChange,
      tableState.page,
      tableState.search,
      tableState.sortBy,
      tableState.sortOrder,
    ],
  );

  const getRowId = useCallback((row: IBaseCourse) => row.id.toString(), []);

  return {
    tableConfig,
    getRowId,
    itemsPerPage,
    tableState,
  };
}
