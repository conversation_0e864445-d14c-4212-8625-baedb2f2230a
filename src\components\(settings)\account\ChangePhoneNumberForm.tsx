"use client";

import { useUser } from "@clerk/nextjs";
import { useTranslations } from "next-intl";
import { useState } from "react";
import PhoneInput from "react-phone-number-input";
import { toast } from "sonner";
import "react-phone-number-input/style.css";

import { Button } from "@/components/base/button";
import { useCreatePhoneNumber, useUnlinkPhoneNumber } from "@/services/user/client";

type ChangePhoneNumberFormProps = {
  currentPhoneNumber: string | null;
  onCancel?: () => void;
};

export default function ChangePhoneNumberForm({
  currentPhoneNumber,
  onCancel,
}: ChangePhoneNumberFormProps) {
  const { user } = useUser();
  const t = useTranslations("profile.settings.account.form.phone");
  const { createPhoneNumber } = useCreatePhoneNumber(user?.id ?? "");
  const { unlinkPhoneNumber } = useUnlinkPhoneNumber(user?.id ?? "");
  const [phoneNumber, setPhoneNumber] = useState(currentPhoneNumber ?? "");

  const handleUnlinkPhoneNumber = async () => {
    if (!user?.primaryPhoneNumberId) return;

    await unlinkPhoneNumber({ phoneNumberId: user.primaryPhoneNumberId });
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleCreatePhoneNumber = async () => {
    if (!user?.id) return;

    await createPhoneNumber({ phoneNumber });
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  return (
    <div>
      {currentPhoneNumber ? (
        <div className="flex flex-col gap-2">
          <div className="text-[#8c8c8c]">{currentPhoneNumber}</div>
          <div className="flex flex-row gap-2">
            <Button variant="secondary" onClick={onCancel}>
              {t("cancel")}
            </Button>
            <Button onClick={handleUnlinkPhoneNumber}>{t("unlink")}</Button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col gap-2">
          <PhoneInput
            international
            countryCallingCodeEditable={false}
            defaultCountry="FR"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e || "")}
            className="mt-1 block w-full rounded-md border border-gray-700 bg-gray-800 px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:outline-none"
            numberInputProps={{
              className: "bg-transparent",
            }}
          />
          <div className="flex flex-row gap-2">
            <Button variant="secondary" onClick={onCancel}>
              {t("cancel")}
            </Button>
            <Button onClick={handleCreatePhoneNumber}>{t("save")}</Button>
          </div>
        </div>
      )}
    </div>
  );
}
