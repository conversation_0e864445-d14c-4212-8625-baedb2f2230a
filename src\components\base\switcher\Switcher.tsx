"use client";

import * as React from "react";
import * as SwitchPrimitive from "@radix-ui/react-switch";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useSwitcher } from "./useSwitcher";
import { SwitcherProps } from "./types";

const MotionThumb = motion.create(SwitchPrimitive.Thumb);

export const Switcher = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitive.Root>,
  SwitcherProps
>(
  (
    {
      className,
      defaultChecked,
      checked,
      onCheckedChange,
      disabled,
      activeIcon: ActiveIcon,
      inactiveIcon: InactiveIcon,
      label,
      description,
      ...props
    },
    ref,
  ) => {
    const { checked: isChecked, onCheckedChange: handleChange } = useSwitcher({
      defaultChecked,
      checked,
      onCheckedChange,
    });

    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <SwitchPrimitive.Root
          ref={ref}
          checked={isChecked}
          onCheckedChange={handleChange}
          disabled={disabled}
          className={
            "peer relative inline-flex h-full w-full shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent bg-zinc-100 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50"
          }
          {...props}
        >
          <MotionThumb
            className={cn(
              "pointer-events-none absolute aspect-square h-full rounded-full bg-background text-muted-foreground ring-0 drop-shadow-[0px_2px_1px_rgba(0,0,0,0.2)]",
              {
                "flex items-center justify-center": ActiveIcon || InactiveIcon,
              },
            )}
            animate={{
              left: isChecked ? "auto" : "0",
              right: isChecked ? "0" : "auto",
            }}
            transition={{
              type: "spring",
              stiffness: 700,
              damping: 30,
              mass: 0.5,
            }}
          >
            {(ActiveIcon || InactiveIcon) && (
              <div className="relative size-1/2">
                <AnimatePresence mode="wait" initial={false}>
                  {isChecked && ActiveIcon ? (
                    <motion.div
                      key="active"
                      initial={{ opacity: 0, scale: 0.7 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.7 }}
                      transition={{ duration: 0.08 }}
                      className="absolute inset-0 flex items-center justify-center"
                    >
                      <ActiveIcon className="h-full w-full" />
                    </motion.div>
                  ) : InactiveIcon ? (
                    <motion.div
                      key="inactive"
                      initial={{ opacity: 0, scale: 0.7 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.7 }}
                      transition={{ duration: 0.08 }}
                      className="absolute inset-0 flex items-center justify-center"
                    >
                      <InactiveIcon className="h-full w-full" />
                    </motion.div>
                  ) : null}
                </AnimatePresence>
              </div>
            )}
          </MotionThumb>
        </SwitchPrimitive.Root>
        {(label || description) && (
          <div className="flex flex-col">
            {label && (
              <span className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                {label}
              </span>
            )}
            {description && <span className="text-sm text-muted-foreground">{description}</span>}
          </div>
        )}
      </div>
    );
  },
);

Switcher.displayName = "Switcher";
