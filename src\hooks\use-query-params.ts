import { useCallback, useMemo } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

type QueryParams = Record<string, string | string[]>;

export const useQueryParams = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const params = useMemo(() => {
    const params: QueryParams = {};
    searchParams.forEach((value, key) => {
      if (params[key]) {
        params[key] = Array.isArray(params[key])
          ? [...(params[key] as string[]), value]
          : [params[key] as string, value];
      } else {
        params[key] = value;
      }
    });
    return params;
  }, [searchParams]);

  const updateParams = useCallback(
    (newParams: QueryParams) => {
      const urlParams = new URLSearchParams(searchParams.toString());
      const newSearchParams = new URLSearchParams(searchParams.toString());

      // merge newParams with urlParams
      Object.entries(newParams).forEach(([key, value]) => {
        if (value) {
          urlParams.set(key, value as string);
        }
      });

      // Update or remove params
      urlParams.forEach((value, key) => {
        const newParamsValue = newParams[key];
        if (newParamsValue === undefined || newParamsValue === "") {
          newSearchParams.delete(key);
        } else if (Array.isArray(newParamsValue)) {
          newSearchParams.delete(key); // Clear existing values
        } else {
          newSearchParams.set(key, newParamsValue);
        }
      });

      const newUrl = `${pathname}?${newSearchParams.toString()}`;
      router.replace(newUrl, { scroll: false });
    },
    [pathname, router, searchParams],
  );

  const clearParams = useCallback(() => {
    router.replace(pathname, { scroll: false });
  }, [pathname, router]);

  return { params, updateParams, clearParams };
};
