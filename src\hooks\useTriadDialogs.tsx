import { useState, useCallback } from "react";
import { JoinTriadConfirmDialog } from "@/components/(triads)/JoinTriadConfirmDialog";
import { LeaveTriadConfirmDialog } from "@/components/(triads)/LeaveTriadConfirmDialog";
import { DeleteTriadConfirmDialog } from "@/components/(triads)/DeleteTriadConfirmDialog";
import { ITriadBase } from "@px-shared-account/hermes";

interface UseTriadDialogsProps {
  upcomingTriad?: Pick<ITriadBase, "sessionTime">;
  locale: string;
  onJoinConfirm: () => void;
  onLeaveConfirm: () => void;
  onDeleteConfirm: () => void;
  onCancel: () => void;
  isLoading?: (triadId: number) => boolean;
  selectedTriadId?: number;
}

export function useTriadDialogs({
  upcomingTriad,
  locale,
  onJoinConfirm,
  onLeaveConfirm,
  onDeleteConfirm,
  onCancel,
  isLoading,
  selectedTriadId = 0,
}: UseTriadDialogsProps) {
  const [joinConfirmOpen, setJoinConfirmOpen] = useState(false);
  const [leaveConfirmOpen, setLeaveConfirmOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [selectedTriadParticipants, setSelectedTriadParticipants] = useState<number>(0);

  const openJoinDialog = useCallback(() => {
    setJoinConfirmOpen(true);
  }, []);

  const openLeaveDialog = useCallback(() => {
    setLeaveConfirmOpen(true);
  }, []);

  const openDeleteDialog = useCallback((participantCount: number = 0) => {
    setSelectedTriadParticipants(participantCount);
    setDeleteConfirmOpen(true);
  }, []);

  const closeAllDialogs = useCallback(() => {
    setJoinConfirmOpen(false);
    setLeaveConfirmOpen(false);
    setDeleteConfirmOpen(false);
    onCancel();
  }, [onCancel]);

  const handleJoinConfirm = useCallback(() => {
    onJoinConfirm();
    setJoinConfirmOpen(false);
  }, [onJoinConfirm]);

  const handleLeaveConfirm = useCallback(() => {
    onLeaveConfirm();
    setLeaveConfirmOpen(false);
  }, [onLeaveConfirm]);

  const handleDeleteConfirm = useCallback(() => {
    onDeleteConfirm();
    setDeleteConfirmOpen(false);
  }, [onDeleteConfirm]);

  const dialogs = (
    <>
      <JoinTriadConfirmDialog
        open={joinConfirmOpen}
        onOpenChange={setJoinConfirmOpen}
        onConfirm={handleJoinConfirm}
        onCancel={closeAllDialogs}
        existingTriadDate={upcomingTriad?.sessionTime}
        loading={selectedTriadId ? isLoading?.(selectedTriadId) || false : false}
        locale={locale}
      />

      <LeaveTriadConfirmDialog
        open={leaveConfirmOpen}
        onOpenChange={setLeaveConfirmOpen}
        onConfirm={handleLeaveConfirm}
        onCancel={closeAllDialogs}
        loading={selectedTriadId ? isLoading?.(selectedTriadId) || false : false}
      />

      <DeleteTriadConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        onConfirm={handleDeleteConfirm}
        onCancel={closeAllDialogs}
        participantCount={selectedTriadParticipants}
        loading={selectedTriadId ? isLoading?.(selectedTriadId) || false : false}
      />
    </>
  );

  return {
    // State
    joinConfirmOpen,
    leaveConfirmOpen,
    deleteConfirmOpen,
    selectedTriadParticipants,

    // Actions
    openJoinDialog,
    openLeaveDialog,
    openDeleteDialog,
    closeAllDialogs,

    // Dialog components
    dialogs,
  };
}
