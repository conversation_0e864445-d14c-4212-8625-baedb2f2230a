import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { RoleGroup } from '@px-shared-account/hermes';
import { RoleEntity } from '@entities';
import { UserTable } from './user.table';

@Entity({
  name: 'roles',
})
export class RoleTable implements RoleEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'enum',
    enum: RoleGroup,
    default: RoleGroup.STUDENT,
  })
  group: RoleGroup;

  @Column({
    type: 'varchar',
    unique: true,
  })
  name: string;

  @Column({
    type: 'boolean',
    default: false,
  })
  isDefault: boolean;

  @Column('text', { array: true, default: [] })
  permissions: string[];

  @OneToMany(() => UserTable, (user) => user.role)
  members: UserTable[];

  @CreateDateColumn({ default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}
