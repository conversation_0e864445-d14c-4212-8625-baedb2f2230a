import {
  Controller,
  Get,
  Param,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { InvoiceUseCases } from '@useCases';
import { ListInvoicesDTO } from '@dtos';

@ApiTags('invoices')
@Controller('invoices')
export class InvoicesController {
  constructor(private readonly invoiceUseCases: InvoiceUseCases) {}

  @ApiOperation({
    summary: 'List invoices',
  })
  @Get('/')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  )
  async list(@Query() query: ListInvoicesDTO) {
    return this.invoiceUseCases.searchAll(
      query.query,
      query.fiscalEntity,
      query.customerId,
      query.subscriptionId,
      query.status,
      query.paidAtStart,
      query.paidAtEnd,
      query.limit,
      query.page,
      query.orderBy,
    );
  }

  @ApiOperation({
    summary:
      'Search and get all invoices for a search query searching email, name or invoice amount',
  })
  @Get('/matchBankTransfer')
  async searchForBankTransferMatching(@Query('query') query: string) {
    return this.invoiceUseCases.getInvoicesForMatchingBankTransfers(query);
  }

  @ApiOperation({
    summary: 'Get an invoice',
  })
  @Get('/:id')
  async getOne(@Param('id') id: string) {
    return this.invoiceUseCases.getOne(id);
  }
}
