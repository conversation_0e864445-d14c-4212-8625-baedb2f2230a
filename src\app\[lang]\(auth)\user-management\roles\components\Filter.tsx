"use client";

import { useTranslations } from "next-intl";
import { FilterBar } from "@/components/base/filter-bar";
import { FilterStep } from "@/components/base/filter-bar/types";
import { RoleGroup } from "@px-shared-account/hermes";
import { PlusIcon } from "lucide-react";
import { Button } from "@/components/base/button";

interface FilterProps {
  onCreateClick: () => void;
  onChange?: (values: Record<string, string>) => void;
  value?: Record<string, string>;
}

const Filter = ({ onCreateClick, onChange, value }: FilterProps) => {
  const t = useTranslations("gestion.users.roles");

  const filterConfig: { groups: FilterStep[] } = {
    groups: [
      {
        id: "group",
        label: t("filter-by-group"),
        type: "filter",
        options: Object.values(RoleGroup).map((group) => ({
          label: group,
          value: group,
        })),
      },
    ],
  };

  return (
    <div className="flex items-center justify-end gap-4">
      <FilterBar
        steps={filterConfig.groups}
        forceModal={true}
        triggerText={t("filters")}
        onChange={onChange}
        value={value}
      />
      <Button onClick={onCreateClick} className="rounded-full">
        <PlusIcon className="mr-2 h-4 w-4" />
        <span>{t("create")}</span>
      </Button>
    </div>
  );
};

export default Filter;
