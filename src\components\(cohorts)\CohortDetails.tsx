"use client";

import { useTranslations } from "next-intl";
import { useState, memo } from "react";

import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BreadcrumbNavigation } from "./BreadcrumbNavigation";
import CohortDetailsContent from "./CohortDetailsContent";
import CohortDetailsInfo from "./CohortDetailsInfo";
import CohortStudentTable from "./CohortStudentTable";

type CohortDetailsProps = {
  id: string;
};

type Tab = "informations" | "students" | "offers";

const CohortInformations = memo(() => {
  return (
    <div className="flex flex-row gap-4">
      <CohortDetailsInfo />
      <CohortDetailsContent />
    </div>
  );
});
CohortInformations.displayName = "CohortInformations";

const CohortStudents = memo(() => {
  return <CohortStudentTable />;
});
CohortStudents.displayName = "CohortStudents";

const CohortOffers = memo(() => {
  return <div>CohortOffers</div>;
});
CohortOffers.displayName = "CohortOffers";

// Map of tab components for cleaner rendering
const TAB_COMPONENTS: Record<Tab, React.ReactNode> = {
  informations: <CohortInformations />,
  students: <CohortStudents />,
  offers: <CohortOffers />,
};

export default function CohortDetails({ id }: CohortDetailsProps) {
  const t = useTranslations("cohorts.details-page");
  const [activeTab, setActiveTab] = useState<Tab>("informations");

  const TABS: Array<{ value: Tab; label: string }> = [
    { value: "informations", label: t("tabs.informations") },
    { value: "students", label: t("tabs.students") },
    { value: "offers", label: t("tabs.offers") },
  ];

  return (
    <div className="flex flex-col gap-4">
      <BreadcrumbNavigation id={id} t={t} />
      <h1 className="text-2xl font-bold">
        {t("title")} {id}
      </h1>
      <Tabs
        defaultValue={activeTab}
        onValueChange={(value) => setActiveTab(value as Tab)}
        aria-label="Cohort details tabs"
      >
        <TabsList>
          {TABS.map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {TABS.map((tab) => (
          <TabsContent key={tab.value} value={tab.value}>
            {TAB_COMPONENTS[tab.value]}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
