import { Injectable } from '@nestjs/common';
import { ProductPlan, ProductPlanPrice } from '@entities';
import { CreateProductPlanPriceDTO } from '@dtos';

@Injectable()
export class ProductPlanPriceFactory {
  generate(
    planPriceInfo: CreateProductPlanPriceDTO,
    linkedPlanId: number,
    internalName: string,
  ): ProductPlanPrice {
    const planPrice = new ProductPlanPrice();
    planPrice.internalName = internalName;
    planPrice.externalName = planPriceInfo.externalName;
    planPrice.description = planPriceInfo.description;
    planPrice.amount = planPriceInfo.amount;
    planPrice.status = 'active';
    planPrice.amountPerBillingCycle = Number(
      Number.parseFloat(planPriceInfo.amountPerBillingCycle.toString()).toFixed(
        2,
      ),
    );
    planPrice.totalBillingCycles = planPriceInfo.totalBillingCycles || 0;
    planPrice.currencyCode = planPriceInfo.currencyCode;
    planPrice.periodUnit = planPriceInfo.billingCycle;
    planPrice.period = 1;
    planPrice.pricingModel = 'per_unit';
    planPrice.chargebeeId = crypto.randomUUID();
    const relatedPlan = new ProductPlan();
    relatedPlan.id = linkedPlanId;
    planPrice.productPlan = relatedPlan;
    return planPrice;
  }
}
