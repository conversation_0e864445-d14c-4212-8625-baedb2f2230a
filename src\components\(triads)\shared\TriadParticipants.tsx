import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { getAllParticipants } from "@/utils/triadHelpers";
import { ITriadBase } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";

interface TriadParticipantsProps {
  triad: ITriadBase;
  variant?: "table" | "list";
  className?: string;
}

export function TriadParticipants({ triad, variant = "table", className }: TriadParticipantsProps) {
  const t = useTranslations("triad");
  const allParticipants = getAllParticipants(triad);

  if (allParticipants.length === 0) {
    return (
      <span className={`text-gray-400 ${variant === "list" ? "text-sm" : ""} ${className}`}>
        {variant === "list" ? t("list.no_participants") : t("table.no_participants")}
      </span>
    );
  }

  const firstParticipant = allParticipants[0];
  const remainingParticipants = allParticipants.slice(1);

  const buttonBaseClasses =
    variant === "list"
      ? "h-auto p-0 text-sm text-gray-400 hover:text-gray-300"
      : "h-auto p-0 text-white hover:text-gray-300";

  const ParticipantButton = ({
    participant,
    asChild = false,
  }: {
    participant: typeof firstParticipant;
    asChild?: boolean;
  }) => (
    <Button variant="link" className={buttonBaseClasses} asChild={asChild && !!participant?.email}>
      {participant?.email && asChild ? (
        <a href={`mailto:${participant?.email}`} target="_blank" rel="noopener noreferrer">
          {participant?.name}
        </a>
      ) : (
        <span>{participant?.name}</span>
      )}
    </Button>
  );

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <ParticipantButton participant={firstParticipant} asChild={variant === "table"} />

      {remainingParticipants.length > 0 && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              className="h-auto rounded-full bg-white/10 px-2 py-0.5 text-xs font-medium text-white hover:bg-white/20"
            >
              +{remainingParticipants.length}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className={`w-auto border-[#2C2C2C] p-2 ${
              variant === "list" ? "bg-background" : "bg-background-tertiary"
            }`}
          >
            <div className="flex flex-col gap-1">
              {remainingParticipants.map((participant) => (
                <ParticipantButton key={participant?.id} participant={participant} asChild={true} />
              ))}
            </div>
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}
