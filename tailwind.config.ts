import type { Config } from "tailwindcss";

export default {
  darkMode: "class",
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    screens: {
      xs: "320px",
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
      "3xl": "1920px",
      "4xl": "2560px",
    },
    extend: {
      fontFamily: {
        sans: ["var(--font-clash)", "sans-serif"],
        anton: ["var(--font-anton)", "sans-serif"],
      },
      colors: {
        burgundy: {
          DEFAULT: "hsl(var(--burgundy))",
          foreground: "hsl(var(--burgundy-foreground))",
        },
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        "background-secondary": "hsl(var(--background-secondary))",
        "background-secondary-foreground": "hsl(var(--background-secondary-foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      fontSize: {
        "5xl": [
          "3.5rem", // 56px
          // { lineHeight: "120%" } // 67.2px (67px rounded)
        ],
        "4xl": [
          "3rem", // 48px
          // { lineHeight: "100%" } // 48px
        ],
        "3xl": [
          "2.25rem", // 36px
          // { lineHeight: "100%" } // 36px
        ],
        "2xl": [
          "1.75rem", // 28px
          // { lineHeight: "100%" } // 28px
        ],
        xl: [
          "1.5rem", // 24px
          // { lineHeight: "120%" } // 28.8px (29px rounded)
        ],
        lg: [
          "1.25rem", // 20px
          // { lineHeight: "120%" } // 24px
        ],
        base: [
          "1.125rem", // 18px
          // { lineHeight: "120%" } // 22px
        ],
        sm: [
          "1rem", // 16px
          // { lineHeight: "120%" }, // 18px
        ],
        xs: [
          "0.875rem", // 14px
          // { lineHeight: "130%" } // 16px
        ],
        "2xs": [
          "0.75rem", // 12px
          // { lineHeight: "130%" } // 12px
        ],
      },
      borderRadius: {
        lg: "var(--radius-card)",
        md: "calc(var(--radius-card) - 2px)",
        sm: "calc(var(--radius-card) - 4px)",
        pill: "var(--radius)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [],
} satisfies Config;
