"use client";

import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Search,
  ChevronsUpDown,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { ChangeEvent, useEffect, useState, useCallback, useMemo } from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { TableProps, SelectedRows } from "@/types/table";
import { Input } from "../base/input";

// Helper function to check if columns include an actions column
const hasActionColumn = (columns: any[]) => {
  return columns.some((col) => col.id === "actions");
};

export function DataTable<T>({
  bulkActions = null,
  columns,
  data = [],
  totalRows,
  isLoading,
  enableSelection,
  enableSearch,
  enablePagination = true,
  enableSorting = false,
  useClientPagination = false,
  filters = null,
  selectionActions = null,
  noResults = "",
  pageSize = 10,
  selectedRows = [],
  title = "",
  onStateChange,
  onSelectionChange,
  getRowId,
  minSearchLength = 3,
}: TableProps<T>) {
  const t = useTranslations("table");
  const [inputSearch, setInputSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [sorting, setSorting] = useState<SortingState>([]);

  // For client-side pagination, filter data based on search input
  const filteredData = useMemo(() => {
    if (!useClientPagination || !enableSearch || inputSearch.trim().length < minSearchLength) {
      return data;
    }

    // Simple client-side search implementation
    const searchTerm = inputSearch.trim().toLowerCase();
    return data.filter((item) => {
      // Search through all string properties of the item
      return Object.entries(item as Record<string, any>).some(([_, value]) => {
        if (typeof value === "string") {
          return value.toLowerCase().includes(searchTerm);
        }
        return false;
      });
    });
  }, [data, useClientPagination, enableSearch, inputSearch, minSearchLength]);

  // Calculate total rows and pages based on pagination mode
  const effectiveTotalRows = useClientPagination ? filteredData.length : totalRows;
  const totalPages = Math.ceil(effectiveTotalRows / pageSize);

  // For client-side pagination, get the current page data
  const paginatedData = useMemo(() => {
    if (!useClientPagination || !enablePagination) {
      return filteredData;
    }

    const startIndex = currentPage * pageSize;
    const endIndex = Math.min(startIndex + pageSize, filteredData.length);
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, useClientPagination, enablePagination, currentPage, pageSize]);

  // Use the appropriate data source based on pagination mode
  const tableData = useClientPagination ? paginatedData : data;

  const table = useReactTable<T>({
    columns: columns,
    data: tableData,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getRowId: getRowId,
    manualPagination: !useClientPagination, // Use TanStack's pagination when client-side
    enableSorting,
    state: {
      sorting,
      pagination: useClientPagination
        ? {
            pageIndex: currentPage,
            pageSize,
          }
        : undefined,
    },
    onSortingChange: setSorting,
    pageCount: useClientPagination ? totalPages : undefined,
  });

  const { getHeaderGroups, getRowModel } = table;

  const handlePageChange = useCallback(
    (newPage: number) => {
      setCurrentPage(newPage);

      // Only notify parent component of page change if not using client-side pagination
      if (!useClientPagination) {
        onStateChange({
          pageIndex: newPage,
          pageSize,
          searchQuery: inputSearch.trim(),
        });
      }
    },
    [onStateChange, pageSize, inputSearch, useClientPagination],
  );

  const handleSearch = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setInputSearch(e.target.value);
  }, []);

  const handleSelectAllPage = useCallback(
    (checked: boolean) => {
      if (onSelectionChange) {
        if (selectedRows === "all") {
          // If all rows are already selected, deselect all
          onSelectionChange([]);
          return;
        }

        const currentPageIds = data.map(getRowId);

        if (checked) {
          // Add current page IDs to existing selection
          const existingIds = Array.isArray(selectedRows) ? selectedRows : [];
          const newSelection = [...new Set([...existingIds, ...currentPageIds])];
          onSelectionChange(newSelection);
        } else {
          // Remove current page IDs from existing selection
          const existingIds = Array.isArray(selectedRows) ? selectedRows : [];
          const newSelection = existingIds.filter((id) => !currentPageIds.includes(id));
          onSelectionChange(newSelection);
        }
      }
    },
    [data, getRowId, onSelectionChange, selectedRows],
  );

  const handleSelectAll = useCallback(
    (term: "all" | []) => {
      if (onSelectionChange) {
        onSelectionChange(term === "all" ? "all" : []);
      }
    },
    [onSelectionChange],
  );

  const handleSelectRow = useCallback(
    (checked: boolean, rowId: string) => {
      if (onSelectionChange) {
        let newSelection: SelectedRows;
        if (checked) {
          newSelection = [...selectedRows, rowId];
        } else {
          if (selectedRows === "all") {
            newSelection = [];
          } else {
            newSelection = selectedRows.filter((id) => id !== rowId);
          }
        }

        onSelectionChange(newSelection);
      }
    },
    [selectedRows, onSelectionChange],
  );

  // Calculate if all rows on the current page are selected
  const currentPageIds = data.map(getRowId);
  const isAllCurrentPageSelected =
    data.length > 0 &&
    (selectedRows === "all" ||
      (Array.isArray(selectedRows) && currentPageIds.every((id) => selectedRows.includes(id))));

  // Calculate if some (but not all) rows on the current page are selected
  const isSomeCurrentPageSelected =
    Array.isArray(selectedRows) &&
    currentPageIds.some((id) => selectedRows.includes(id)) &&
    !isAllCurrentPageSelected;

  // Optimize search debounce
  useEffect(() => {
    if (inputSearch.trim().length < minSearchLength && inputSearch.trim().length !== 0) {
      return;
    }

    const delayInputTimeoutId = setTimeout(() => {
      // Reset to first page when searching
      setCurrentPage(0);

      // Only notify parent component if not using client-side pagination
      if (!useClientPagination) {
        onStateChange({
          pageIndex: 0,
          pageSize,
          searchQuery: inputSearch.trim(),
        });
      }
    }, 500);

    return () => clearTimeout(delayInputTimeoutId);
    // If anyone reads this, try to add onStateChange to the dependency array,
    // you'll see a funny side effect bug. I spent hours debugging this.
    // AI and the compiler/linter does not help as it pushes you to add it to the array. Thanks React.
  }, [inputSearch, pageSize, minSearchLength, useClientPagination]);

  return (
    <div className="my-2 space-y-4">
      <div className="flex w-full flex-col justify-between gap-4 md:flex-row md:items-center">
        {typeof title === "string" ? <div className="text-2xl font-bold">{title}</div> : title}
        <div className="flex items-center justify-end gap-4">
          {/* Search */}
          {enableSearch && (
            <div className="flex w-full items-center justify-between">
              <Input
                type="search"
                placeholder={t("search")}
                className="w-full rounded-full border-white md:max-w-sm"
                onChange={handleSearch}
                value={inputSearch}
                prefixIcon={<Search className="h-4 w-4" />}
              />
            </div>
          )}
          {/* Filters */}
          {filters}
        </div>
      </div>
      <div>
        {/* Selection actions below the header */}
        {selectionActions}

        {/* Default selected rows actions (only show if selectionActions is not provided) */}
        {!selectionActions && Array.isArray(selectedRows) && selectedRows.length > 0 && (
          <div className="flex w-full items-center justify-start gap-4">
            <span className="text-muted-foreground text-sm">
              {selectedRows.length}
              &nbsp;{t("rows-selected")}
            </span>
            <Button variant="outline" size="sm" onClick={() => handleSelectAll("all")}>
              {t("select-all")}
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleSelectAll([])}>
              {t("deselect-all")}
            </Button>
            {bulkActions}
          </div>
        )}
        {!selectionActions && selectedRows === "all" && (
          <div className="flex w-full items-center justify-start gap-4">
            <span className="text-muted-foreground text-sm">
              {totalRows} {t("rows-selected")}
            </span>
            <Button variant="outline" size="sm" onClick={() => handleSelectAll([])}>
              {t("deselect-all")}
            </Button>
            {bulkActions}
          </div>
        )}
      </div>

      <div className="!rounded-2xl border">
        <Table className="">
          <TableHeader>
            {getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {enableSelection && (
                  <TableHead className="flex items-center justify-center !p-4">
                    <Checkbox
                      checked={
                        isAllCurrentPageSelected
                          ? true
                          : isSomeCurrentPageSelected
                            ? "indeterminate"
                            : false
                      }
                      onCheckedChange={handleSelectAllPage}
                    />
                  </TableHead>
                )}
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className={`h-12 text-xs font-medium tracking-wide text-white ${
                      header.column.getCanSort() ? "cursor-pointer select-none" : ""
                    }`}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    <div className="flex items-center gap-2">
                      <span>{flexRender(header.column.columnDef.header, header.getContext())}</span>
                      {header.column.getCanSort() && (
                        <div className="flex w-4 items-center">
                          {header.column.getIsSorted() === "asc" ? (
                            <ChevronUp className="text-primary h-4 w-4 transition-all duration-200 hover:scale-125" />
                          ) : header.column.getIsSorted() === "desc" ? (
                            <ChevronDown className="text-primary h-4 w-4 transition-all duration-200 hover:scale-125" />
                          ) : (
                            <ChevronsUpDown className="text-muted-foreground hover:text-primary h-4 w-4 transition-all duration-200 hover:scale-110" />
                          )}
                        </div>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={enableSelection ? columns.length + 1 : columns.length}
                  className="h-24 text-center"
                >
                  {t("loading")}
                </TableCell>
              </TableRow>
            ) : data && data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={enableSelection ? columns.length + 1 : columns.length}
                  className="h-24 text-center font-bold"
                >
                  {noResults || t("no-results")}
                </TableCell>
              </TableRow>
            ) : (
              !isLoading &&
              getRowModel()?.rows.map((row) => (
                <TableRow
                  key={row.id}
                  className={hasActionColumn(columns) ? "hover:bg-muted/50 cursor-pointer" : ""}
                  onClick={(e) => {
                    // Don't trigger row click if clicking on interactive elements
                    if (
                      e.target instanceof HTMLElement &&
                      (e.target.closest("button") ||
                        e.target.closest("a") ||
                        e.target.closest('[role="checkbox"]') ||
                        e.target.closest("input"))
                    ) {
                      return;
                    }

                    // Find the action button in the row and click it
                    // Check if this row has an actions column
                    const hasActionsColumn = row
                      .getVisibleCells()
                      .some((cell) => cell.column.id === "actions");

                    if (hasActionsColumn) {
                      // Find the action button in the DOM
                      const rowElement = e.currentTarget;

                      // Try to find the action button - look for buttons in the actions cell
                      const actionCell = rowElement.querySelector(
                        `td:nth-child(${enableSelection ? row.getVisibleCells().length + 1 : row.getVisibleCells().length})`,
                      );
                      if (actionCell) {
                        const actionButton = actionCell.querySelector("button");
                        if (actionButton) {
                          actionButton.click();
                          return;
                        }
                      }

                      // Fallback: try to find any button in the row
                      const anyButton = rowElement.querySelector("button");
                      if (anyButton) {
                        anyButton.click();
                      }
                    }
                  }}
                >
                  {enableSelection && (
                    <TableCell className="w-12">
                      <Checkbox
                        checked={selectedRows === "all" ? true : selectedRows.includes(row.id)}
                        onCheckedChange={(checked) => handleSelectRow(checked as boolean, row.id)}
                      />
                    </TableCell>
                  )}
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {enablePagination && (
        <div className="flex items-center justify-between px-2">
          <div className="text-muted-foreground flex-1 text-sm">
            {enableSelection &&
              (selectedRows === "all" ||
                (Array.isArray(selectedRows) && selectedRows.length > 0)) && (
                <span>
                  {selectedRows === "all" ? totalRows : selectedRows.length} {t("of")} {totalRows}{" "}
                  {t("rows-selected")}
                </span>
              )}
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              {t("page")} {currentPage + 1} / {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => handlePageChange(0)}
                disabled={currentPage === 0}
              >
                <span className="sr-only">{t("goto-first")}</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 0}
              >
                <span className="sr-only">{t("goto-prev")}</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages - 1}
              >
                <span className="sr-only">{t("goto-next")}</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => handlePageChange(totalPages - 1)}
                disabled={currentPage === totalPages - 1}
              >
                <span className="sr-only">{t("goto-last")}</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
