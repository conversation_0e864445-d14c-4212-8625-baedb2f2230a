export interface ScheduleHttpRequestParams {
  url: string;
  at: string;
  method: string;
  headers: Record<string, string>;
  body?: any;
  query?: Record<string, string>;
}

export interface ScheduledJobResponse {
  jobId: string;
  scheduledAt: Date;
  url: string;
  method: string;
}

export interface HttpRequestJob {
  id: string;
  url: string;
  method: string;
  headers: Record<string, string>;
  body?: any;
  query?: Record<string, string>;
  scheduledAt: Date;
}
