import { SignUp as SignUp<PERSON>lerk } from "@clerk/nextjs";
import { getTranslations } from "next-intl/server";

export default function SignInPage() {
  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <SignUpClerk />
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "navigation" });
  return {
    title: t("sign-up"),
  };
}
