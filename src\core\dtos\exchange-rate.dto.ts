import { ExchangeRateCurrencies } from '@enums';
import {
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateExchangeRateDTO {
  @IsEnum(ExchangeRateCurrencies)
  @IsNotEmpty()
  from: ExchangeRateCurrencies;

  @IsString()
  @IsNotEmpty()
  rate: string;

  @IsDateString({ strict: true })
  @IsNotEmpty()
  date?: string;

  @IsObject()
  @IsOptional()
  metaData?: any;
}

export class GetExchangeRateDTO {
  @IsEnum(ExchangeRateCurrencies)
  @IsNotEmpty()
  from: ExchangeRateCurrencies;

  @IsDateString({ strict: true })
  @IsNotEmpty()
  date: string;
}

export class ListExchangeRateDTO {
  @IsDateString({ strict: true })
  @IsNotEmpty()
  date: string;

  @IsInt()
  @IsOptional()
  limit?: number;

  @IsInt()
  @IsOptional()
  page?: number;
}
