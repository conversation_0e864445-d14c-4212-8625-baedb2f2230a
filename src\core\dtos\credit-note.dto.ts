import { IsEnum, IsInt, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { FiscalEntity, CreditNoteStatus } from '@enums';

export class ListCreditNotesDTO {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsEnum(FiscalEntity)
  fiscalEntity?: FiscalEntity;

  @IsOptional()
  @IsString()
  customerId?: string;

  @IsOptional()
  @IsString()
  subscriptionId?: string;

  @IsOptional()
  @IsEnum(CreditNoteStatus)
  status?: CreditNoteStatus;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  limit?: number;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  page?: number;

  @IsOptional()
  @IsEnum(['DESC', 'ASC'])
  orderBy?: 'DESC' | 'ASC';
}
