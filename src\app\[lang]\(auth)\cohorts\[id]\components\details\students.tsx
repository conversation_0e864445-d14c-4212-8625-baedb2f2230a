"use client";

import { useTranslations } from "next-intl";
import { ColumnDef } from "@tanstack/react-table";
import { useState, useMemo, useCallback } from "react";
import { ArrowRight, MoreHorizontal, Trash2 } from "lucide-react";
import { IUserBase } from "@px-shared-account/hermes";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/table";
import { useTable } from "@/hooks/use-table";
import { TableProps } from "@/types/table";
import UserInfo from "@/app/[lang]/(auth)/user-management/users/components/UserInfo";
import { useCohortDetails } from "@/services/cohort";
import { CohortStudentsSelectionActions } from "./CohortStudentsSelectionActions";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { RemoveStudentsConfirmation } from "../modals/remove-students/RemoveStudentsConfirmation";

interface CohortStudentsProps {
  id: number;
}

export function CohortStudents({ id }: CohortStudentsProps) {
  const t = useTranslations("cohorts");
  const [tableState, setTableState] = useState({
    pageIndex: 0,
    pageSize: 10,
    searchQuery: "",
  });
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
  const [studentToRemove, setStudentToRemove] = useState<IUserBase | null>(null);

  const { data: cohort, isLoading, mutate: refreshCohort } = useCohortDetails(id);
  const allStudents = cohort?.students || [];

  const handleRemoveStudent = (student: IUserBase) => {
    setStudentToRemove(student);
    setIsRemoveModalOpen(true);
  };

  const handleStudentsRemoved = useCallback(() => {
    refreshCohort();
  }, [refreshCohort]);

  const columns = useMemo(
    () =>
      [
        {
          id: "user",
          header: t("students.name"),
          cell: ({ row }) => <UserInfo row={row} />,
        },
        {
          id: "createdAt",
          header: t("students.joined_at"),
          accessorKey: "createdAt",
          cell: ({ row }) => <span>{format(row.original.createdAt, "MMM dd, yyyy")}</span>,
        },
        {
          id: "actions",
          header: t("students.actions"),
          cell: ({ row }) => {
            const student = row.original;
            return (
              <div className="flex items-center justify-start gap-2">
                {/* <Button
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    // TODO: Implement view student details
                  }}
                >
                  <span className="sr-only">
                    {t("students.view_details", {
                      name: `${student.firstName} ${student.lastName}`,
                    })}
                  </span>
                  <ArrowRight className="h-4 w-4" />
                </Button> */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">
                        {t("students.open_menu", {
                          name: `${student.firstName || ""} ${student.lastName || ""}`,
                        })}
                      </span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => handleRemoveStudent(student)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t("students.remove")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            );
          },
        },
      ] as ColumnDef<IUserBase>[],
    [t],
  );

  const handleTableStateChange = useCallback((newState: typeof tableState) => {
    setTableState(newState);
  }, []);

  const { selectedRows, handleRowSelection } = useTable({
    initialState: tableState,
    onStateChange: handleTableStateChange,
  });

  // Filter students based on search query
  const filteredStudents = useMemo(() => {
    if (!tableState.searchQuery) {
      return allStudents;
    }

    const searchLower = tableState.searchQuery.toLowerCase();
    return allStudents.filter((student) => {
      const fullName = `${student.firstName || ""} ${student.lastName || ""}`.toLowerCase();
      const email = student.email.toLowerCase();
      return fullName.includes(searchLower) || email.includes(searchLower);
    });
  }, [allStudents, tableState.searchQuery]);

  // Calculate paginated students based on current table state
  const paginatedStudents = useMemo(() => {
    const startIndex = tableState.pageIndex * tableState.pageSize;
    const endIndex = startIndex + tableState.pageSize;
    return filteredStudents.slice(startIndex, endIndex);
  }, [filteredStudents, tableState.pageIndex, tableState.pageSize]);

  // Handle select all students (including those not in current pagination)
  const handleSelectAll = useCallback(() => {
    if (allStudents.length > 0) {
      handleRowSelection("all");
    }
  }, [allStudents, handleRowSelection]);

  // Handle clearing the selection
  const handleClearSelection = useCallback(() => {
    handleRowSelection([]);
  }, [handleRowSelection]);

  // Get selected students objects from their IDs
  const selectedStudents = useMemo(() => {
    if (selectedRows === "all") {
      return allStudents;
    }
    return allStudents.filter((student: IUserBase) => selectedRows.includes(student.id.toString()));
  }, [allStudents, selectedRows]);

  const dataTableProps: TableProps<IUserBase> = {
    columns,
    data: paginatedStudents,
    isLoading,
    selectedRows,
    enableSearch: true,
    enableSelection: true, // Always enable selection
    enablePagination: true,
    pageSize: tableState.pageSize,
    totalRows: filteredStudents.length,
    noResults: t("students.no_results"),
    onStateChange: handleTableStateChange,
    onSelectionChange: handleRowSelection,
    getRowId: (row: IUserBase) => row.id.toString(),
    minSearchLength: 1,
    title: `${t("students.title")} (${filteredStudents.length})`,
  };

  // Add the selection actions component to be displayed below the header
  const selectionActions =
    selectedRows === "all" || selectedRows.length > 0 ? (
      <CohortStudentsSelectionActions
        cohortId={id}
        selectedStudents={selectedStudents}
        onStudentsRemoved={handleStudentsRemoved}
        onStudentsMoved={handleStudentsRemoved}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
        isAllSelected={selectedRows === "all"}
        totalStudents={allStudents.length}
      />
    ) : null;

  // Update the dataTableProps to include the selectionActions
  const updatedDataTableProps = {
    ...dataTableProps,
    selectionActions,
  };

  return (
    <div className="space-y-4">
      <DataTable {...updatedDataTableProps} />

      {studentToRemove && (
        <RemoveStudentsConfirmation
          cohortId={id}
          students={[studentToRemove]}
          isOpen={isRemoveModalOpen}
          onOpenChange={setIsRemoveModalOpen}
          onComplete={handleStudentsRemoved}
        />
      )}
    </div>
  );
}
