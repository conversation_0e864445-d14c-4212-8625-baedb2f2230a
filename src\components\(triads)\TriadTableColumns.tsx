import { useMemo } from "react";
import { ColumnDef, Column, Row } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ArrowUpDown } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";
import { ITriadBase, SessionType } from "@px-shared-account/hermes";
import { AccessInfo } from "@/services/customers/core";
import { AvailabilityBadge } from "./AvailabilityBadge";
import { SessionBadge } from "./SessionBadge";
import { SessionTypeBadge } from "./SessionTypeBadge";
import { TriadParticipants } from "./shared/TriadParticipants";
import { TriadAccess } from "./shared/TriadAccess";
import { TriadActionButton } from "./shared/TriadActionButton";
import { formatTriadDate, getAvailableSpots } from "@/utils/triadHelpers";

interface UseTriadTableColumnsProps {
  accessInfo: AccessInfo;
  userId?: string;
  isTriadLoading: (triadId: number) => boolean;
  onJoinAttempt: (triadId: number) => void;
  onLeaveAttempt: (triadId: number) => void;
  onDeleteAttempt: (triadId: number, participantCount: number) => void;
}

export function useTriadTableColumns({
  accessInfo,
  userId,
  isTriadLoading,
  onJoinAttempt,
  onLeaveAttempt,
  onDeleteAttempt,
}: UseTriadTableColumnsProps) {
  const t = useTranslations("triad");
  const locale = useLocale();

  const columns = useMemo<ColumnDef<ITriadBase>[]>(
    () =>
      [
        {
          accessorKey: "title",
          header: ({ column }: { column: Column<ITriadBase> }) => (
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="h-auto w-full justify-start p-0 hover:bg-transparent hover:text-gray-400"
            >
              {t("table.title")}
              <ArrowUpDown className="ml-2 h-4 w-4" />
            </Button>
          ),
          cell: ({ row }: { row: Row<ITriadBase> }) => {
            const triad = row.original;
            return triad.title || triad.organizer?.firstName || triad.organizer?.lastName;
          },
        },
        {
          id: "availability",
          accessorFn: (triad: ITriadBase) => getAvailableSpots(triad),
          header: ({ column }: { column: Column<ITriadBase> }) => (
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="h-auto w-full justify-start p-0 hover:bg-transparent hover:text-gray-400"
            >
              {t("table.availability")}
              <ArrowUpDown className="ml-2 h-4 w-4" />
            </Button>
          ),
          cell: ({ row }: { row: Row<ITriadBase> }) => {
            const triad = row.original;
            const availableSpots = getAvailableSpots(triad);
            return (
              <AvailabilityBadge availableSpots={availableSpots} maxSpots={triad.maxParticipants} />
            );
          },
          sortingFn: (rowA: Row<ITriadBase>, rowB: Row<ITriadBase>) => {
            // we use Infinity to considre the "Complet" triad as the last in sorting
            const a = getAvailableSpots(rowA.original);
            const b = getAvailableSpots(rowB.original);
            const aVal = a <= 0 ? Infinity : a;
            const bVal = b <= 0 ? Infinity : b;
            return aVal - bVal;
          },
        },
        {
          id: "datetime",
          accessorKey: "sessionTime",
          header: ({ column }: { column: Column<ITriadBase> }) => (
            <div className="flex flex-col items-start">
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                className="h-auto w-full justify-start p-0 hover:bg-transparent hover:text-gray-400"
              >
                {t("table.session_time")}
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </Button>
              <span className="text-xs text-gray-400">{t("time_picker.local_time")}</span>
            </div>
          ),
          cell: ({ row }: { row: Row<ITriadBase> }) => {
            const date = new Date(row.getValue("datetime"));
            return (
              <div className="flex items-center justify-center">
                {formatTriadDate(date, locale)}
              </div>
            );
          },
        },
        {
          accessorKey: "duration",
          header: ({ column }: { column: Column<ITriadBase> }) => (
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="h-auto w-full justify-start p-0 hover:bg-transparent hover:text-gray-400"
            >
              {t("table.duration")}
              <ArrowUpDown className="ml-2 h-4 w-4" />
            </Button>
          ),
          cell: ({ row }: { row: Row<ITriadBase> }) =>
            t("table.duration_value", { hours: row.original.duration }),
        },
        // Conditionally include session type column
        accessInfo.hasBothAccess
          ? {
              accessorKey: "sessionType",
              header: ({ column }: { column: Column<ITriadBase> }) => (
                <Button
                  variant="ghost"
                  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                  className="h-auto w-full justify-start p-0 hover:bg-transparent hover:text-gray-400"
                >
                  {t("table.session_type")}
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              ),
              cell: ({ row }: { row: Row<ITriadBase> }) => {
                const sessionType = row.getValue("sessionType") as SessionType;
                return <SessionTypeBadge type={sessionType} />;
              },
            }
          : null,
        // Conditionally include sessions column
        accessInfo.isPXS || accessInfo.hasBothAccess
          ? {
              accessorKey: "sessions",
              header: ({ column }: { column: Column<ITriadBase> }) => {
                return (
                  <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                    className="h-auto w-full justify-start p-0 hover:bg-transparent hover:text-gray-400"
                  >
                    {t("table.sessions")}
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                );
              },
              cell: ({ row }: { row: Row<ITriadBase> }) => {
                const sessions = row.getValue("sessions") as string[];
                if (!sessions || sessions.length === 0) {
                  return (
                    <span className="text-muted-foreground block w-full text-center text-sm">
                      {t("table.no_session_restriction")}
                    </span>
                  );
                }

                return (
                  <div className="flex items-center justify-center gap-1">
                    <SessionBadge session={sessions[0]} />
                    {sessions.length > 1 && (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="ghost"
                            className="h-auto rounded-full bg-white/10 px-2 py-0.5 text-xs font-medium text-white hover:bg-white/20"
                          >
                            +{sessions.length - 1}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="bg-background-tertiary w-auto border-[#2C2C2C] p-2">
                          <div className="flex flex-col gap-1">
                            {sessions.slice(1).map((session) => (
                              <SessionBadge key={session} session={session} />
                            ))}
                          </div>
                        </PopoverContent>
                      </Popover>
                    )}
                  </div>
                );
              },
            }
          : null,
        {
          id: "participants",
          header: t("table.participants"),
          cell: ({ row }: { row: Row<ITriadBase> }) => {
            return <TriadParticipants triad={row.original} variant="table" />;
          },
        },
        {
          id: "access",
          header: t("table.access"),
          cell: ({ row }: { row: Row<ITriadBase> }) => {
            return <TriadAccess triad={row.original} userId={userId} variant="table" />;
          },
        },
        {
          id: "actions",
          header: "",
          enableSorting: false,
          cell: ({ row }: { row: Row<ITriadBase> }) => {
            const triad = row.original;

            return (
              <TriadActionButton
                triad={triad}
                userId={userId}
                isLoading={isTriadLoading(triad.id)}
                onJoin={onJoinAttempt}
                onLeave={onLeaveAttempt}
                onDelete={onDeleteAttempt}
                variant="table"
              />
            );
          },
        },
      ].filter(Boolean) as ColumnDef<ITriadBase>[],
    [
      t,
      locale,
      accessInfo.isPXS,
      accessInfo.hasBothAccess,
      userId,
      isTriadLoading,
      onJoinAttempt,
      onLeaveAttempt,
      onDeleteAttempt,
    ],
  );

  return columns;
}
