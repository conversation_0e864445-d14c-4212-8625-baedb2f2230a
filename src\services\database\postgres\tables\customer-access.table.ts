import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Index,
} from 'typeorm';
import { CustomerAccessType } from '@enums';
import { AccessDetails } from '@types';
import { CustomerAccess } from '@entities';
import { CustomerAccessStatus } from '@px-shared-account/hermes';

@Entity({ name: 'customerAccesses' })
export class CustomerAccessTable implements CustomerAccess {
  @PrimaryGeneratedColumn()
  id: number;

  @Index('idx_customer_accesses_customer_id')
  @Column({ type: 'int' })
  customerId: number;

  @Column({ type: 'int', nullable: true })
  subscriptionId?: number;

  @Column({ type: 'int', nullable: true })
  offerId?: number;

  @Column({ type: 'enum', enum: CustomerAccessType })
  type: CustomerAccessType;

  @Column({ type: 'enum', enum: CustomerAccessStatus })
  status: CustomerAccessStatus;

  @Column({ type: 'jsonb' })
  details: AccessDetails;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt?: Date;

  @Column({ type: 'jsonb', nullable: true })
  metaData?: any;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp', nullable: true })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;
}
