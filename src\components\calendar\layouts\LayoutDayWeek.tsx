"use client";

import CalendarBoard from "../Board";
import CalendarHeader from "../Header";
import TimeColumn from "./TimeColumn";

type CalendarLayoutDayWeekProps = {
  selectedWeekIndex: number;
  selectedWeek: Date[];
};

export default function CalendarLayoutDayWeek({
  selectedWeekIndex,
  selectedWeek,
}: CalendarLayoutDayWeekProps) {
  return (
    <div className="flex h-full flex-col overflow-y-hidden rounded-lg pt-1">
      <CalendarHeader selectedWeekIndex={selectedWeekIndex} selectedWeek={selectedWeek} />
      <div
        data-group="layout-dayweek"
        className="flex h-[calc(100%-150px)] flex-row items-stretch justify-center overflow-y-scroll"
      >
        <TimeColumn />
        <div data-group="board-container" className="flex max-w-full flex-1 items-start">
          <div className="relative flex w-full flex-1 items-start">
            <CalendarBoard selectedWeekIndex={selectedWeekIndex} selectedWeek={selectedWeek} />
          </div>
        </div>
      </div>
    </div>
  );
}
