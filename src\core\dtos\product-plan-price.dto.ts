import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsInt,
  IsString,
  IsNotEmpty,
  IsOptional,
  ValidateIf,
} from 'class-validator';
import { ProductCatalogEntityStatus } from '@enums';

export class CreateProductPlanPriceDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  internalName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  externalName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  amountPerBillingCycle: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  amount: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  currencyCode: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  billingCycle: string;

  @ApiProperty()
  @ValidateIf(
    (planPriceInfo: CreateProductPlanPriceDTO) =>
      !planPriceInfo?.billingCycle?.includes('forever'),
  )
  @IsNotEmpty()
  @IsInt()
  totalBillingCycles?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  productPlanId: number;
}

export class UpdateProductPlanPriceDTO extends PartialType(
  CreateProductPlanPriceDTO,
) {
  @ApiProperty()
  id: number;

  @ApiPropertyOptional()
  @ApiProperty({ enum: ProductCatalogEntityStatus })
  @IsOptional()
  status?: ProductCatalogEntityStatus;
}
