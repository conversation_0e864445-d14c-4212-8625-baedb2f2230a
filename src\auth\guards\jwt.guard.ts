import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { ConfigService } from '@config';

@Injectable()
export class JwtGuard implements CanActivate {
  private readonly customerPortalPK: string;
  private readonly pxOSPK: string;

  constructor(private jwtService: JwtService, configService: ConfigService) {
    this.customerPortalPK = configService.authSecrets.CLERK_PEM_PUBLIC_KEY;
    this.pxOSPK = configService.authSecrets.CLERK_PEM_PUBLIC_KEY_OS;
  }
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const { token } = this.extractTokenFromHeader(req);

    if (!token) throw new UnauthorizedException();

    try {
      const clientName = this.getClientName(req);

      if (clientName === 'customer-portal' || clientName === 'px-app') {
        const payload = await this.jwtService.verifyAsync(token, {
          publicKey: this.customerPortalPK,
        });
        req['user'] = payload;
      } else {
        const payload = await this.jwtService.verifyAsync(token, {
          publicKey: this.pxOSPK,
        });
        req['user'] = payload;
      }
    } catch (error) {
      console.error('cannot verify token', error);
      throw new UnauthorizedException();
    }

    return true;
  }

  private extractTokenFromHeader(request: Request) {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? { token } : undefined;
  }

  private getClientName(request: Request) {
    return request?.headers?.['client-name'];
  }
}
