import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Permissions } from '@px-shared-account/hermes';
import {
  CreateCohortDto,
  GetCohortByIdDto,
  ListCohortsDTO,
  ListCohortsForCourseDto,
  UpdateCohortDto,
  UpdateCohortStudentsDto,
} from '@dtos';
import { CohortUseCases } from '@useCases';
import { RequirePermissions } from 'src/auth/decorators';
import { JwtPermissionsGuard } from '@auth';

@Controller('cohort')
@UseGuards(JwtPermissionsGuard)
export class CohortController {
  constructor(private readonly cohortUseCases: CohortUseCases) { }

  @Post('/')
  @RequirePermissions(Permissions.Cohort.CREATE)
  async createCohort(@Body() payload: CreateCohortDto) {
    return this.cohortUseCases.create(payload);
  }

  @Patch('/:id')
  @RequirePermissions(Permissions.Cohort.UPDATE)
  async updateCohort(
    @Param() params: GetCohortByIdDto,
    @Body() payload: UpdateCohortDto,
  ) {
    return this.cohortUseCases.update(params.id, payload);
  }

  @Get('/list')
  @RequirePermissions(Permissions.Cohort.READ)
  async listUsers(@Query() query: ListCohortsDTO) {
    return this.cohortUseCases.search(
      query.search,
      query.courseId,
      query.status,
      query.limit,
      query.page,
    );
  }

  @Get('/')
  @RequirePermissions(Permissions.Cohort.READ)
  async listCohortsForCourse(@Query() payload: ListCohortsForCourseDto) {
    return this.cohortUseCases.getAllForCourse(
      payload.courseId,
      payload.limit,
      payload.page,
    );
  }

  @Get('/:id')
  @RequirePermissions(Permissions.Cohort.READ)
  async getCohort(@Param() params: GetCohortByIdDto) {
    return this.cohortUseCases.getById(params.id);
  }

  @Post('/:id/add-students')
  @RequirePermissions(Permissions.Cohort.UPDATE)
  async addStudentsToCohort(
    @Param() params: GetCohortByIdDto,
    @Body() payload: UpdateCohortStudentsDto,
  ) {
    return this.cohortUseCases.addStudents(params.id, payload.studentIds);
  }

  @Post('/:id/remove-students')
  @RequirePermissions(Permissions.Cohort.UPDATE)
  async removeStudentsFromCohort(
    @Param() params: GetCohortByIdDto,
    @Body() payload: UpdateCohortStudentsDto,
  ) {
    return this.cohortUseCases.removeStudents(params.id, payload.studentIds);
  }
}
