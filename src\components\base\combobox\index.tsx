"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

/**
 * Represents a single option in the combobox dropdown
 */
export interface ComboboxOption {
  /** Unique value for the option */
  value: string;
  /** Display text for the option */
  label: string;
  /** Optional custom render function */
  render?: () => React.ReactNode;
}

/**
 * Props for the Combobox component
 */
interface ComboboxProps {
  /** Optional ID for the combobox */
  id?: string;
  /** Currently selected value */
  value?: string | string[];
  /** Callback fired when selection changes */
  onChange?: (value: string | string[]) => void;
  /** Available options to display */
  options: ComboboxOption[];
  /** Label text for the combobox */
  label?: string;
  /** Placeholder text when no value is selected */
  placeholder?: string;
  /** Whether the combobox is disabled */
  disabled?: boolean;
  /** Error message to display */
  error?: string | null;
  /** Whether the combobox is in a loading state */
  loading?: boolean;
  /** Additional class name for the root element */
  className?: string;
  /** Additional class name for the trigger button */
  triggerClassName?: string;
  /** Additional class name for the dropdown content */
  contentClassName?: string;
  /** Alignment of the dropdown relative to the trigger */
  align?: "start" | "center" | "end";
  /** Offset of the dropdown from the trigger */
  sideOffset?: number;
  /** Controls the open state of the dropdown */
  open?: boolean;
  /** Callback fired when open state changes */
  onOpenChange?: (open: boolean) => void;
  /** Whether the combobox supports multiple selection */
  multiple?: boolean;
}

/**
 * A searchable dropdown component with support for loading and error states
 *
 * @component
 * @example
 * ```tsx
 * <Combobox
 *   label="Select an option"
 *   options={[
 *     { value: "1", label: "Option 1" },
 *     { value: "2", label: "Option 2" }
 *   ]}
 *   onChange={(value) => console.log(value)}
 * />
 * ```
 *
 * @remarks
 * Features:
 * - Searchable options with keyboard navigation
 * - Loading and error states
 * - Custom positioning and alignment
 * - Accessible keyboard interactions
 */
export function Combobox({
  id,
  value,
  onChange,
  options,
  label,
  placeholder,
  disabled = false,
  error = null,
  loading = false,
  className,
  triggerClassName,
  contentClassName,
  align = "start",
  sideOffset = 4,
  open,
  onOpenChange,
  multiple = false,
}: ComboboxProps) {
  // Multi-select helpers
  const isMulti = multiple && Array.isArray(value);
  const selectedMultiOptions = isMulti
    ? options.filter((option) => value.includes(option.value))
    : [];
  const selectedSingleOption = !isMulti
    ? options.find((option) => option.value === value)
    : undefined;

  // Text to display in the trigger button
  const displayValue =
    isMulti && value.length > 0
      ? selectedMultiOptions.map((o) => o.label).join(", ")
      : selectedSingleOption?.label || (typeof value === "string" ? value : "");
  const hasError = error !== null;

  return (
    <Popover open={open} onOpenChange={onOpenChange} modal={true}>
      <PopoverTrigger asChild>
        <Button
          id={id}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            disabled && "cursor-not-allowed opacity-50",
            hasError && "border-destructive text-destructive",
            triggerClassName,
          )}
          disabled={disabled}
        >
          <span className="truncate">
            {isMulti && value.length > 0
              ? selectedMultiOptions.map((o) => o.label).join(", ")
              : value
                ? displayValue
                : hasError
                  ? error
                  : label}
          </span>
          {loading ? (
            <Loader2 className="ml-2 h-4 w-4 animate-spin" />
          ) : (
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className={cn("pointer-events-auto p-0", contentClassName)}
        align={align}
        sideOffset={sideOffset}
      >
        <Command shouldFilter={true}>
          <CommandInput placeholder={placeholder || `Rechercher un ${label?.toLowerCase()}...`} />
          <CommandList>
            <CommandEmpty>Pas de résultat</CommandEmpty>
            <CommandGroup>
              {options.map((option) => {
                const checked = isMulti ? value.includes(option.value) : value === option.value;
                return (
                  <CommandItem
                    className="cursor-pointer"
                    key={option.value}
                    value={option.value}
                    onSelect={(currentValue) => {
                      if (isMulti) {
                        let newValue = [...value];
                        if (newValue.includes(currentValue)) {
                          newValue = newValue.filter((v) => v !== currentValue);
                        } else {
                          newValue.push(currentValue);
                        }
                        onChange?.(newValue);
                      } else {
                        onChange?.(currentValue === value ? "" : currentValue);
                        onOpenChange?.(false);
                      }
                    }}
                  >
                    <Check className={cn("mr-2 h-4 w-4", checked ? "opacity-100" : "opacity-0")} />
                    {option.render ? option.render() : option.label}
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
