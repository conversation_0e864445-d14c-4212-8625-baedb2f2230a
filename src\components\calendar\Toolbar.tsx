"use client";

import { useShowMonthsAndYears } from "@/hooks/calendar";
import ChangeLayout from "./layouts/ChangeLayout";
import { useTranslations } from "next-intl";
import { Button } from "../base/button";

type CalendarToolbarProps = {
  goToToday: () => void;
  next: () => void;
  previous: () => void;
};

export default function CalendarToolbar({ goToToday, next, previous }: CalendarToolbarProps) {
  const t = useTranslations("calendar");
  const { showMonthsAndYears, showMonthsAndYear, showMonthAndYear } = useShowMonthsAndYears();

  return (
    <div className="flex h-20 flex-row items-center text-black">
      <div className="flex w-48 flex-row items-center justify-between">
        <Button variant="secondary" onClick={goToToday}>
          {t("today")}
        </Button>
        <div className="flex w-[3.75rem] flex-row items-center justify-between gap-1">
          <div
            className="flex size-7 cursor-pointer items-center justify-center bg-[#7D8DA61A]"
            onClick={previous}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="9" height="15" fill="none">
              <path
                stroke="#848585"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M7.33301 1.16663 1.49967 7.58329 7.33301 14"
              />
            </svg>
          </div>
          <div
            className="flex size-7 cursor-pointer items-center justify-center bg-[#7D8DA61A]"
            onClick={next}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="9" height="15" fill="none">
              <path
                stroke="#848585"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m1.66699 13.8334 5.83333-6.41669-5.83333-6.41667"
              />
            </svg>
          </div>
        </div>
      </div>
      <div className="ml-2 flex-grow capitalize text-white">
        {showMonthsAndYears || showMonthsAndYear || showMonthAndYear}
      </div>
      <ChangeLayout />
    </div>
  );
}
