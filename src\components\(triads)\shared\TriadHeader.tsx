import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search, Filter, RefreshCw, BarChart3 } from "lucide-react";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { anton } from "@/lib/fonts/anton";
import Link from "next/link";
import { FilterBar } from "@/components/base/filter-bar-new";
import { FilterBarModal } from "@/components/base/filter-bar-new/FilterBarModal";
import { Step } from "@/components/base/filter-bar-new/types";
import { AnimatedNumber } from "./AnimatedNumber";
import { containerVariants, itemVariants } from "@/constants/triadAnimations";

interface TriadHeaderProps {
  triadsCount: number;
  isReloading: boolean;
  isParadox: boolean;
  searchOpen: boolean;
  searchQuery: string;
  isFilterReady: boolean;
  filterSteps: Step[];
  filterValues: Record<string, any>;
  stepsState: Record<string, any>;
  onReload: () => void;
  onSearchToggle: () => void;
  onSearchChange: (value: string) => void;
  onFilterChange: (id: string, value: any) => void;
  onCreateOpen: () => void;
  isStepEnabled: (stepId: string) => boolean;
  clearAll: () => void;
}

export function TriadMobileHeader({
  triadsCount,
  isReloading,
  isParadox,
  isFilterReady,
  filterSteps,
  filterValues,
  stepsState,
  onReload,
  onSearchToggle,
  onFilterChange,
  onCreateOpen,
  isStepEnabled,
  clearAll,
}: TriadHeaderProps) {
  const t = useTranslations("triad");

  return (
    <motion.div
      className="flex items-center justify-between"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.h2
        className={cn("text-3xl tracking-tight text-white", anton.className)}
        variants={itemVariants}
      >
        <span className="inline-flex items-baseline">
          <AnimatedNumber number={triadsCount} />
          &nbsp;
          {t("view.count")}
        </span>
      </motion.h2>
      <motion.div className="flex items-center gap-2" variants={itemVariants}>
        <Button
          size="icon"
          variant="outline"
          onClick={onReload}
          disabled={isReloading}
          className="h-10 w-10 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black active:bg-white active:text-black"
        >
          <RefreshCw className={cn("h-5 w-5", isReloading && "animate-spin")} />
        </Button>

        {isParadox && (
          <Link href="/practice/stats" prefetch={true}>
            <Button
              size="icon"
              variant="outline"
              className="h-10 w-10 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black active:bg-white active:text-black"
            >
              <BarChart3 className="h-5 w-5" />
            </Button>
          </Link>
        )}

        <Button
          size="icon"
          variant="outline"
          onClick={onSearchToggle}
          className="h-10 w-10 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black active:bg-white active:text-black"
        >
          <Search className="h-5 w-5" />
        </Button>

        {isFilterReady && (
          <FilterBarModal
            triggerEl={(count = 0) => (
              <Button
                size="icon"
                variant="outline"
                className="relative h-10 w-10 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black active:bg-white active:text-black"
              >
                <Filter className="h-5 w-5" />
                {count > 0 && (
                  <span className="bg-primary absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center rounded-full text-[10px] text-white">
                    {count}
                  </span>
                )}
              </Button>
            )}
            steps={filterSteps}
            values={filterValues}
            stepsState={stepsState}
            handleChange={onFilterChange}
            isStepEnabled={isStepEnabled}
            clearAll={clearAll}
          />
        )}

        <Button
          size="icon"
          onClick={onCreateOpen}
          className="bg-primary aspect-square size-10 rounded-full text-white hover:bg-white hover:text-black active:bg-white active:text-black"
        >
          <Plus className="h-5 w-5" />
        </Button>
      </motion.div>
    </motion.div>
  );
}

export function TriadDesktopHeader({
  triadsCount,
  isReloading,
  isParadox,
  searchQuery,
  isFilterReady,
  filterSteps,
  filterValues,
  onReload,
  onSearchChange,
  onFilterChange,
  onCreateOpen,
}: TriadHeaderProps) {
  const t = useTranslations("triad");

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex items-start justify-between">
        <motion.div variants={itemVariants}>
          <motion.h2
            className={cn("text-3xl tracking-tight text-white", anton.className)}
            variants={itemVariants}
          >
            <span className="inline-flex items-baseline">
              <AnimatedNumber number={triadsCount} />
              &nbsp;
              {t("view.count")}
            </span>
          </motion.h2>
          <motion.p className="text-base text-gray-400" variants={itemVariants}>
            {t("view.description")}
          </motion.p>
        </motion.div>
        <div className="flex flex-col items-end gap-4 lg:flex-row lg:flex-row-reverse">
          <Button onClick={onCreateOpen} className="h-12 px-4 py-2 font-medium text-white">
            <Plus className="mr-2 h-5 w-5 text-white" />
            {t("view.create")}
          </Button>
          <div className="flex items-stretch gap-4">
            <Button
              size="icon"
              variant="outline"
              onClick={onReload}
              disabled={isReloading}
              className="size-12 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black active:bg-white active:text-black"
            >
              <RefreshCw className={cn("h-5 w-5", isReloading && "animate-spin")} />
            </Button>

            {isParadox && (
              <Link href="/practice/stats" prefetch={true}>
                <Button
                  size="icon"
                  variant="outline"
                  className="size-12 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black active:bg-white active:text-black"
                  title={t("stats.title")}
                >
                  <BarChart3 className="h-5 w-5" />
                </Button>
              </Link>
            )}

            <div className="relative flex-1">
              <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                type="search"
                placeholder={t("view.search")}
                className="h-12 min-w-[300px] rounded-full border-white bg-transparent pl-10"
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>
      {isFilterReady ? (
        <motion.div variants={itemVariants}>
          <FilterBar
            steps={filterSteps}
            value={filterValues}
            onChange={onFilterChange}
            instanceId="triads-filter"
          />
        </motion.div>
      ) : (
        <div />
      )}
    </motion.div>
  );
}
