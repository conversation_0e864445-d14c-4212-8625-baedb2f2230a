"use client";

/**
 * A cell component for displaying a badge with variant styling
 * Used for status indicators or labels in the DataTable
 * Badges are centered and have a minimum width for consistent sizing
 */

import { Badge } from "@/components/ui/badge";
import { type BadgeVariant } from "@/types/data-table";

interface BadgeCellProps {
  /** The text or numeric value to display in the badge */
  value: string | number;
  /** Visual style variant for the badge */
  variant?: BadgeVariant;
}

export function BadgeCell({ value, variant = "default" }: BadgeCellProps) {
  return (
    <div className="flex justify-center">
      <Badge variant={variant} className="min-w-[80px] justify-center">
        {value}
      </Badge>
    </div>
  );
}
