import { createZodDto } from 'nestjs-zod';
import {
  CreateCohortSchema,
  GetCohortByIdSchema,
  ListCohortsSchema,
  ListCohortsForCourseSchema,
  ListCohortsResponseSchema,
  PaginationParamsSchema,
  UpdateCohortSchema,
  UpdateCohortStudentsSchema,
} from '@px-shared-account/hermes';

export class CreateCohortDto extends createZodDto(CreateCohortSchema) {}
export class UpdateCohortDto extends createZodDto(UpdateCohortSchema) {}
export class GetCohortByIdDto extends createZodDto(GetCohortByIdSchema) {}
export class UpdateCohortStudentsDto extends createZodDto(
  UpdateCohortStudentsSchema,
) {}
export class ListCohortsDTO extends createZodDto(
  ListCohortsSchema.merge(PaginationParamsSchema),
) {}
export class ListCohortsForCourseDto extends createZodDto(
  ListCohortsForCourseSchema.merge(PaginationParamsSchema),
) {}
export class ListCohortsResponseDto extends createZodDto(
  ListCohortsResponseSchema,
) {}
