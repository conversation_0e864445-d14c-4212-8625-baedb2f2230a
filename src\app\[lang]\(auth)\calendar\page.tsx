import { useTranslations } from "next-intl";
import { getTranslations } from "next-intl/server";

import PageHeader from "./components/page-header";
import { CalendarFilters } from "./components/calendar-filters";
import Calendar from "@/components/calendar/Calendar";

export default function Page() {
  const t = useTranslations("calendar");

  return (
    <div className="space-y-4">
      <PageHeader />
      <p className="text-muted-foreground">{t("sub-title")}</p>
      <CalendarFilters className="mt-6" />
      <Calendar />
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "calendar" });

  return {
    title: t("title"),
    description: t("sub-title"),
  };
}
