import { MigrationInterface, QueryRunner } from 'typeorm';

export class CheckoutTestimonyFieldsAdd1732693604611
  implements MigrationInterface
{
  name = 'CheckoutTestimonyFieldsAdd1732693604611';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "checkoutPages" ADD "template" character varying(5) DEFAULT 'A'`,
    );
    await queryRunner.query(
      `ALTER TABLE "checkoutPages" ADD "cancellationPolicy" character varying(800)`,
    );
    await queryRunner.query(
      `ALTER TABLE "checkoutPages" ADD "testimony" jsonb DEFAULT '{}'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "checkoutPages" DROP COLUMN "testimony"`,
    );
    await queryRunner.query(
      `ALTER TABLE "checkoutPages" DROP COLUMN "cancellationPolicy"`,
    );
    await queryRunner.query(
      `ALTER TABLE "checkoutPages" DROP COLUMN "template"`,
    );
  }
}
