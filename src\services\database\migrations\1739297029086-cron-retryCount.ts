import { MigrationInterface, QueryRunner } from 'typeorm';

export class CronRetryCount1739297029086 implements MigrationInterface {
  name = 'CronRetryCount1739297029086';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ADD "retryCount" integer DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "cronLogs" DROP COLUMN "retryCount"`);
  }
}
