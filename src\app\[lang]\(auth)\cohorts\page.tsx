import { getTranslations } from "next-intl/server";

import CohortsTable from "@/components/(cohorts)/CohortsTable";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

export default function CohortsPage() {
  return (
    <div className="space-y-4">
      <BreadcrumbSettings active="/cohorts" />
      <CohortsTable />
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "navigation.cohorts" });

  return {
    title: t("title"),
  };
}
