import { RoleGroup, UserType } from "../enums";
import { z } from "zod";
import { RoleBaseSchema } from "./role.interface";

export const UserChargebeeIdsSchema = z.object({
  PG: z.string().optional(),
  PP: z.string().optional(),
  PI: z.string().optional(),
});
export type IUserChargebeeIds = z.infer<typeof UserChargebeeIdsSchema>;

export const UserBaseSchema = z.object({
  id: z.number().positive(),
  firstName: z.string().min(1).nonempty(),
  lastName: z.string().min(1).nonempty(),
  email: z.string().email().nonempty(),
  ssoId: z.string().optional(),
  crmId: z.string().optional(),
  chargebeeIds: UserChargebeeIdsSchema.optional(),
  type: z.nativeEnum(UserType).optional(),
  role: RoleBaseSchema,
  metaData: z.record(z.string(), z.any()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});
export type IUserBase = z.infer<typeof UserBaseSchema>;

export const CreateUserSchema = UserBaseSchema.partial({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export type ICreateUser = z.infer<typeof CreateUserSchema>;

export const UpdateUserSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  email: z.string().email().optional(),
  role: z.string().optional(),
  ssoId: z.string().optional(),
});
export type IUpdateUser = z.infer<typeof UpdateUserSchema>;

export const GetUserByIdSchema = z.object({
  id: z.number().positive(),
});
export type IGetUserById = z.infer<typeof GetUserByIdSchema>;

export const FilterUserSchema = z.object({
  role: z.nativeEnum(RoleGroup).optional(),
});
export type IFilterUser = z.infer<typeof FilterUserSchema>;

export const SearchUserSchema = z.object({
  search: z.string().optional(),
  role: z.number().positive().optional(),
});
export type ISearchUser = z.infer<typeof SearchUserSchema>;

export const ListUsersResponseSchema = z.object({
  data: z.array(
    UserBaseSchema.extend({
      id: z.number().positive(),
      role: RoleBaseSchema.extend({
        id: z.number().positive(),
      }),
      createdAt: z.date(),
      updatedAt: z.date(),
    })
  ),
  page: z.number(),
  total: z.number(),
});
export type IListUsersResponse = z.infer<typeof ListUsersResponseSchema>;
