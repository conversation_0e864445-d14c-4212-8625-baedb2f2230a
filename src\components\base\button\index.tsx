"use client";

import { memo } from "react";

import { Button as Shadcn<PERSON><PERSON><PERSON> } from "@/components/ui/button";
import type { ButtonProps as ShadcnButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export interface ButtonProps extends ShadcnButtonProps {
  fullWidth?: boolean;
}

export const Button = memo(
  ({ className, variant = "default", fullWidth, ...props }: ButtonProps) => {
    // Memoize the className computation
    const buttonClassName = cn(
      // Default styles for black buttons
      variant === "default" && "bg-primary text-primary-foreground hover:bg-primary/90",
      // Full width option
      fullWidth && "w-full",
      // Allow className override
      className,
    );

    return <ShadcnButton className={buttonClassName} variant={variant} {...props} />;
  },
);

export default Button;

Button.displayName = "Button";
