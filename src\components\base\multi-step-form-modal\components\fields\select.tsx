import { useFormContext } from "react-hook-form";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from "next-intl";
import { SelectField as SelectFieldType } from "../../types";

interface SelectFieldProps {
  field: SelectFieldType;
}

export const SelectField = ({ field }: SelectFieldProps) => {
  const {
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();
  const value = watch(field.id);
  const error = errors[field.id];
  const t = useTranslations("validation");

  const renderError = () => {
    if (!error) return null;

    if (error.type === "custom" && error.message) {
      return error.message.toString();
    }

    switch (error.type) {
      case "required":
        return t("required");
      default:
        return error.message?.toString() || t("invalid");
    }
  };

  return (
    <div className="w-full space-y-2">
      <Label htmlFor={field.id} className={error ? "text-destructive" : ""}>
        {field.label}
        {field.required && <span className="text-destructive">*</span>}
      </Label>
      {field.description && <p className="text-xs text-muted-foreground">{field.description}</p>}
      <Select
        value={value?.toString() || ""}
        onValueChange={(val) => {
          setValue(field.id, val, { shouldValidate: true });
          field.onChange?.(val);
        }}
        disabled={field.disabled}
      >
        <SelectTrigger className={error ? "border-destructive" : ""}>
          <SelectValue placeholder={field.placeholder} />
        </SelectTrigger>
        <SelectContent>
          {field.options?.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-sm font-medium text-destructive">{renderError()}</p>}
    </div>
  );
};
