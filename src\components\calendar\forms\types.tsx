import { z } from "zod";

const eventTypes = [
  "mentoring",
  "masterclass",
  "supervision",
  "guided-triad",
  "customized-slot",
] as const;

export const CreateNewSlotFormSchema = z.object({
  // Part 1 - Event Details
  type: z.enum(eventTypes),
  date: z.string().min(1),
  tz: z.string(),
  duration: z.string(),
  recurrence: z.string(),
  replay: z.boolean().default(false),
  // Part 2 - Speakers
  mainSpeaker: z.string(),
  otherSpeakers: z.array(z.string()),
  technicalSupport: z.string(),
  // Part 3 - Audience
  cohort: z.string(),
  participantMin: z.number().positive(),
  participantMax: z.number().positive(),
  program: z.string(),
  course: z.string(),
  module: z.string(),
  // Part 4 - Comment
  comment: z.string().optional(),
});

export type CreateNewSlotFormType = z.infer<typeof CreateNewSlotFormSchema>;
