"use client";

import * as React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { AccordionSelectField, FieldConfig } from "../../types";
import { FieldComponent } from "./field-component";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";

interface AccordionSelectProps {
  field: AccordionSelectField;
  className?: string;
}

export function AccordionSelect({ field, className }: AccordionSelectProps) {
  const {
    formState: { errors },
    register,
  } = useFormContext();

  const t = useTranslations("components.multi-step-modal");

  // Keep track of which section is open
  const [openSection, setOpenSection] = React.useState<string | undefined>(field.options[0]?.value);

  // Register each nested field
  React.useEffect(() => {
    field.options.forEach((section) => {
      section.fields?.forEach((nestedField) => {
        register(nestedField.id);
      });
    });
  }, [register, field.options]);

  // Simple error check for a section
  const getSectionErrorCount = (sectionFields: FieldConfig[] = []) => {
    return sectionFields.filter((field) => errors[field.id]).length;
  };

  // Simple check for required fields in a section
  const getSectionRequiredCount = (sectionFields: FieldConfig[] = []) => {
    return sectionFields.filter((field) => field.required).length;
  };

  // Auto-expand section with errors
  React.useEffect(() => {
    const sectionWithError = field.options.find(
      (section) => getSectionErrorCount(section.fields) > 0,
    );
    if (sectionWithError) {
      setOpenSection(sectionWithError.value);
    }
  }, [field.options, errors]);

  return (
    <div className={cn("space-y-2", className)}>
      <Accordion type="single" collapsible value={openSection} onValueChange={setOpenSection}>
        {field.options.map((section) => {
          const errorCount = getSectionErrorCount(section.fields);
          const requiredCount = getSectionRequiredCount(section.fields);

          return (
            <AccordionItem key={section.value} value={section.value}>
              <AccordionTrigger
                className={cn("text-sm font-medium", errorCount > 0 && "text-destructive")}
              >
                <span className="flex items-center gap-1">
                  {section.label}
                  {requiredCount > 0 && (
                    <span className="text-destructive" title={t("required_fields_indicator")}>
                      *
                    </span>
                  )}
                </span>
                {errorCount > 0 && (
                  <span className="ml-2 text-xs text-destructive">
                    {t("section_error_count", {
                      count: errorCount,
                      s: errorCount > 1 ? "s" : "",
                    })}
                  </span>
                )}
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pb-4 pt-2">
                {section.fields?.map((nestedField) => (
                  <FieldComponent key={nestedField.id} field={nestedField} />
                ))}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </div>
  );
}
