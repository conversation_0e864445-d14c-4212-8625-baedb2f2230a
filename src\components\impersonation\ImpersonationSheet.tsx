"use client";

import React, { useState, useCallback } from "react";
import { useUser } from "@clerk/nextjs";
import { She<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON>ooter, SheetClose, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useImpersonationStore } from "@/hooks/store/impersonation";
import { UserCog, UserIcon, AlertTriangle, Check, X } from "lucide-react";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { useImpersonatedEmail } from "@/hooks/use-impersonated-email";
import { useCustomerAccessDetails } from "@/services/customers/client";
import { useRouter } from "next/navigation";

export default function ImpersonationSheet() {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const { user } = useUser();
  const [email, setEmail] = useState("");
  const t = useTranslations("impersonation");

  // Get the actual email of the logged-in user
  const actualEmail = user?.primaryEmailAddress?.emailAddress || "";

  const {
    isImpersonating,
    impersonatedEmail,
    originalEmail,
    setImpersonation,
    clearImpersonation,
  } = useImpersonationStore();

  // Get effective email for status panel & API call
  const currentEmail = useImpersonatedEmail();

  // Fetch user access details using the customer access hook
  const {
    data: userAccessData,
    error: userAccessError,
    isLoading: userAccessLoading,
    refreshData,
  } = useCustomerAccessDetails(currentEmail);

  // Get current user's admin status
  const { data: currentUserAccessData, isLoading: currentUserLoading } =
    useCustomerAccessDetails(actualEmail);

  // Check if the current user is an admin
  const isAdmin = currentUserAccessData?.accessInfo?.isParadox ?? false;

  // Extract access info, default to safe values if loading/error
  const accessInfo = userAccessData?.accessInfo;
  const isPXS = accessInfo?.isPXS ?? false;
  const session = accessInfo?.hubspotSession;
  const isPXL = accessInfo?.isPXL ?? false;
  const isPOM = accessInfo?.isPOM ?? false;
  const isParadox = accessInfo?.isParadox ?? false;

  const handleStartImpersonation = useCallback(() => {
    if (!email.trim()) {
      toast.error(t("errors.empty_email"));
      return;
    }

    if (!email.includes("@")) {
      toast.error(t("errors.invalid_email"));
      return;
    }

    // Check if the target email belongs to a Paradox admin
    const targetEmail = email.trim().toLowerCase();

    // Save the email that we're impersonating
    const impersonatedEmailValue = targetEmail;

    // Update the impersonation store
    setImpersonation(impersonatedEmailValue, actualEmail);

    toast.success(t("started", { email: impersonatedEmailValue }));

    // Close the sheet
    setIsOpen(false);

    // Short timeout to ensure state propagates before updating
    setTimeout(() => {
      // Force a refresh of the user data
      refreshData();

      // Refresh the router without a full page reload
      router.refresh();
    }, 200); // Slightly longer timeout for starting
  }, [email, t, actualEmail, setImpersonation, refreshData, router, setIsOpen]);

  const handleStopImpersonation = useCallback(() => {
    // Save reference to original email before clearing
    const originalUserEmail = originalEmail;

    // Update the impersonation store
    clearImpersonation();

    toast.success(t("stopped"));

    // Store the original email in localStorage just as a backup
    if (originalUserEmail) {
      localStorage.setItem("last_admin_email", originalUserEmail);
    }

    // Close the sheet
    setIsOpen(false);

    // Short timeout to ensure state propagates before updating
    setTimeout(() => {
      // First refresh the client data (SWR cache)
      refreshData();

      // Then refresh the server components
      router.refresh();
    }, 300);
  }, [clearImpersonation, originalEmail, t, setIsOpen, refreshData, router]);

  // Only show for Paradox admins
  if (!user || !actualEmail || currentUserLoading || !isAdmin) {
    return null;
  }

  // Status panel to show PXS, PXL, POM status
  const renderStatusPanel = () => {
    const isLoading = userAccessLoading;

    return (
      <div className="mb-6 rounded-lg border border-[#444444] bg-[#333333]/30 p-5">
        <h3 className="mb-4 text-base font-medium text-white">{t("status.title")}</h3>

        <div className="space-y-3 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-gray-300">{t("status.email")}</span>
            <span className="font-medium text-white">{currentEmail}</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-gray-300">{t("status.pxs_access")}</span>
            {isLoading ? (
              <span className="text-gray-400">{t("status.loading")}</span>
            ) : (
              <span className={cn("flex items-center", isPXS ? "text-green-400" : "text-red-400")}>
                {isPXS ? <Check className="mr-1.5 h-4 w-4" /> : <X className="mr-1.5 h-4 w-4" />}
                {isPXS ? t("status.yes") : t("status.no")}
              </span>
            )}
          </div>

          {isPXS && session && (
            <div className="flex items-center justify-between">
              <span className="text-gray-300">{t("status.pxs_session")}</span>
              <span className="font-medium text-white">{session}</span>
            </div>
          )}

          <div className="flex items-center justify-between">
            <span className="text-gray-300">{t("status.pxl_access")}</span>
            {isLoading ? (
              <span className="text-gray-400">{t("status.loading")}</span>
            ) : (
              <span className={cn("flex items-center", isPXL ? "text-green-400" : "text-red-400")}>
                {isPXL ? <Check className="mr-1.5 h-4 w-4" /> : <X className="mr-1.5 h-4 w-4" />}
                {isPXL ? t("status.yes") : t("status.no")}
              </span>
            )}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-gray-300">{t("status.pom_access")}</span>
            {isLoading ? (
              <span className="text-gray-400">{t("status.loading")}</span>
            ) : (
              <span className={cn("flex items-center", isPOM ? "text-green-400" : "text-red-400")}>
                {isPOM ? <Check className="mr-1.5 h-4 w-4" /> : <X className="mr-1.5 h-4 w-4" />}
                {isPOM ? t("status.yes") : t("status.no")}
              </span>
            )}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-gray-300">{t("status.is_paradox")}</span>
            {isLoading ? (
              <span className="text-gray-400">{t("status.loading")}</span>
            ) : (
              <span
                className={cn("flex items-center", isParadox ? "text-green-400" : "text-red-400")}
              >
                {isParadox ? (
                  <Check className="mr-1.5 h-4 w-4" />
                ) : (
                  <X className="mr-1.5 h-4 w-4" />
                )}
                {isParadox ? t("status.yes") : t("status.no")}
              </span>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Floating Action Button */}
      <div className="fixed right-4 bottom-36 z-50 transition-all md:bottom-20 lg:bottom-6">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button
              variant="destructive"
              size={null}
              className={cn(
                "flex !h-14 !w-14 items-center justify-center rounded-full shadow-lg",
                isImpersonating ? "bg-[#BC3823]" : "bg-burgundy",
                "relative",
              )}
            >
              {isImpersonating && (
                <span className="absolute h-full w-full animate-ping rounded-full bg-[#BC3823] opacity-75"></span>
              )}
              {isImpersonating ? (
                <UserCog className="h-6 w-6 text-white" />
              ) : (
                <UserIcon className="h-6 w-6 text-white" />
              )}
            </Button>
          </SheetTrigger>
          <SheetContent
            side="right"
            className="max-w-unset bg-background w-full overflow-y-auto border-[#414141] p-6 !pt-20 text-white shadow-xl"
          >
            {isImpersonating && (
              <div className="mb-7 flex items-start gap-3 rounded-lg border border-[#BC3823] bg-[#BC3823]/10 p-4">
                <AlertTriangle className="mt-0.5 h-5 w-5 flex-shrink-0 text-[#BC3823]" />
                <div>
                  <p className="font-medium text-[#BC3823]">{t("indicator.active")}</p>
                  <p className="mt-1 text-sm text-gray-300">
                    {t("indicator.message", {
                      originalEmail,
                      impersonatedEmail,
                    })}
                  </p>
                </div>
              </div>
            )}

            {isImpersonating && renderStatusPanel()}

            <div className="space-y-7 py-2">
              {isImpersonating ? (
                <div className="space-y-6">
                  <div className="flex flex-col space-y-2.5">
                    <Label className="text-sm font-medium text-white">
                      {t("currently_impersonating")}
                    </Label>
                    <Input
                      value={impersonatedEmail || ""}
                      disabled
                      className="rounded-lg border-[#414141] bg-[#333333]/80 py-2.5 text-gray-300"
                    />
                  </div>

                  <div className="flex flex-col space-y-2.5">
                    <Label className="text-sm font-medium text-white">{t("your_email")}</Label>
                    <Input
                      value={originalEmail || ""}
                      disabled
                      className="rounded-lg border-[#414141] bg-[#333333]/80 py-2.5 text-gray-300"
                    />
                  </div>

                  <Button
                    variant="destructive"
                    onClick={handleStopImpersonation}
                    className="bg-burgundy hover:bg-burgundy/90 mt-4 w-full rounded-full py-2.5 text-white"
                  >
                    {t("stop")}
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="flex flex-col space-y-2.5">
                    <Label htmlFor="email" className="text-sm font-medium text-white">
                      {t("user_email_label")}
                    </Label>
                    <Input
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value.trim().toLowerCase())}
                      placeholder={t("user_email_placeholder")}
                      className="rounded-lg border-[#414141] bg-[#333333]/80 py-2.5 text-white placeholder:text-gray-500"
                    />
                  </div>

                  <Button
                    onClick={handleStartImpersonation}
                    className="w-full rounded-full bg-white py-2.5 text-black hover:bg-gray-200"
                  >
                    {t("start")}
                  </Button>
                </div>
              )}
            </div>

            <SheetFooter className="mt-8 border-t border-[#414141] pt-5">
              <SheetClose asChild>
                <Button
                  variant="outline"
                  className="w-full rounded-full border-white bg-transparent py-2.5 text-white hover:bg-white/10"
                >
                  {t("close")}
                </Button>
              </SheetClose>
            </SheetFooter>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
}
