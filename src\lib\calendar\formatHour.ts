"use client";

export default function formatHours(startDate: string, endDate: string) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  const startHour = start.getHours();
  const startMinutes = start.getMinutes();
  const endHour = end.getHours();
  const endMinutes = end.getMinutes();

  return {
    start: `${startHour}:${startMinutes}`,
    end: `${endHour}:${endMinutes}`,
  };
}
