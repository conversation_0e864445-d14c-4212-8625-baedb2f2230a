/**
 * Core type definitions for the DataTable system
 */

import { LucideIcon } from "lucide-react";

/**
 * Available column types for rendering different kinds of data
 * - text: Simple text display
 * - number: Numeric values with optional formatting
 * - chip: Pill-shaped indicator with optional icon
 * - badge: Status indicator with variants
 * - thumbnails: Stack of image thumbnails
 * - thumb-text: Combination of thumbnail and text
 */
export type ColumnType = "text" | "number" | "chip" | "badge" | "thumbnails" | "thumb-text";

/**
 * Visual variants for chip elements
 * - default: Primary color with subtle background
 * - success: Green theme for positive states
 * - warning: Yellow theme for cautionary states
 * - danger: Red theme for error states
 */
export type ChipVariant = "default" | "success" | "warning" | "danger";

/**
 * Visual variants for badge elements
 * - default: Primary theme
 * - secondary: Secondary theme
 * - destructive: Error/danger theme
 * - outline: Bordered style
 */
export type BadgeVariant = "default" | "secondary" | "destructive" | "outline";

/**
 * Badge shape options
 * - rounded: Standard rounded corners
 * - squared: No rounded corners
 * - pill: Fully rounded ends
 */
export type BadgeShape = "rounded" | "squared" | "pill";

/**
 * Badge style variations
 * - outlined: Border only
 * - filled: Solid background
 * - ghost: Semi-transparent background
 */
export type BadgeStyle = "outlined" | "filled" | "ghost";

/**
 * Configuration options for badge appearance
 */
export interface BadgeConfig {
  /** Shape of the badge */
  shape?: BadgeShape;
  /** Style variant of the badge */
  style?: BadgeStyle;
  /** Color value (supports Tailwind classes, hex values, and CSS variables) */
  color?: string;
  /** Predefined variant */
  variant?: BadgeVariant;
}

/**
 * Column definition for the DataTable
 * Defines how a column should be rendered and behave
 */
export interface ColumnDef<T> {
  /** Unique identifier for the column */
  id: string;
  /** Display text for the column header */
  header: string;
  /** Key to access the data in each row */
  accessorKey: keyof T;
  /** Type of content this column displays */
  type: ColumnType;
  /** Whether this column can be sorted */
  sortable?: boolean;
  /** Optional CSS class name for styling */
  className?: string;
  /** Additional configuration options */
  meta?: {
    /** Text alignment within cells */
    align?: "left" | "center" | "right";
    /** Icon to display (for chip type) */
    icon?: LucideIcon;
    /** Maximum number of thumbnails to display */
    maxThumbnails?: number;
    /** Visual variant for chips or badges */
    variant?: ChipVariant | BadgeVariant;
    /** Column width */
    width?: string;
    /** Key to access image URL for thumb-text type */
    imageKey?: keyof T;
    /** Badge specific configuration */
    badge?: BadgeConfig;
  };
}

/**
 * Props for the DataTable component
 */
export interface DataTableProps<T extends { id: string }> {
  /** Array of data to display */
  data: T[];
  /** Column definitions */
  columns: ColumnDef<T>[];
  /** Enable row selection */
  selectable?: boolean;
  /** Singular label for the data type (e.g., "student") */
  dataLabel?: string;
  /** Plural label for the data type (e.g., "students") */
  dataLabelPlural?: string;
  /** Callback when rows are selected */
  onRowSelect?: (selectedRows: T[]) => void;
  /** Callback when a row action is triggered */
  onRowAction?: (row: T) => void;
}
