"use client";

import { IListRolesResponse, IRoleBase, IUserBase } from "@px-shared-account/hermes";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { useMemo, useCallback, useState } from "react";

import { DataTable } from "@/components/table";
import { useTable } from "@/hooks/use-table";
import { useCanAccess } from "@/lib/permissions/client";
import { Permissions } from "@px-shared-account/hermes";
import { useRoleList } from "@/services/role";
import { useBulkUpdateUsers, useUserList } from "@/services/user";
import { useToast } from "@/hooks/use-toast";
import { useUsersTable } from "./use-users-table";
import Filter from "./Filter";
import UserInfo from "./UserInfo";
import Score from "./Score";
import SelectRole from "./SelectRole";
import BulkUpdateRoleModal from "./BulkUpdateRoleModal";
import { TableProps } from "@/types/table";
import Button from "@/components/base/button";

const createColumns = (
  t: (key: string) => string,
  roles?: IListRolesResponse,
  isLoadingRoles?: boolean,
): ColumnDef<IUserBase>[] => [
  {
    header: t("name"),
    cell: ({ row }) => (
      <Link href={`/user-management/users/${row.original.id}`}>
        <UserInfo row={row} />
      </Link>
    ),
  },
  {
    header: t("score"),
    cell: ({ row }) => <Score score={Math.floor(Math.random() * 10)} />,
  },
  {
    header: t("information.role"),
    accessorKey: "role",
    cell: ({ row }) => (
      <SelectRole
        roles={roles?.data as IRoleBase[]}
        isLoading={isLoadingRoles ?? false}
        userData={row.original as IUserBase}
      />
    ),
  },
  {
    header: t("created-at"),
    accessorKey: "createdAt",
    cell: ({ row }) => <span>{format(row.original.createdAt, "MMM dd, yyyy")}</span>,
  },
  {
    header: t("updated-at"),
    accessorKey: "updatedAt",
    cell: ({ row }) => <span>{format(row.original.updatedAt, "MMM dd, yyyy")}</span>,
  },
  {
    header: t("actions-header"),
    enableHiding: true,
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-2">
          <Link href={`/user-management/users/${row.original.id}`}>
            <ArrowRight className="h-4 w-4 cursor-pointer" />
          </Link>
        </div>
      );
    },
  },
];

export default function UsersTable() {
  const t = useTranslations("gestion.users");
  const { toast } = useToast();
  const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = useState(false);

  // Use the custom hook for table state and configuration
  const { tableState, tableConfig, getRowId, itemsPerPage, handleFilterChange } = useUsersTable();

  // Fetch roles for filters and dropdowns
  const { data: roles, isLoading: isLoadingRoles } = useRoleList();

  // Bulk update functionality
  const { bulkUpdate, isLoading: isUpdating } = useBulkUpdateUsers();

  // Create params for user list query
  const userListParams = useMemo(() => {
    return {
      page: tableState.page,
      limit: itemsPerPage,
      ...(tableState.search ? { search: tableState.search } : {}),
      ...(tableState.role !== undefined ? { role: tableState.role } : {}), // Convert number to string for API
      ...(tableState.sortBy ? { sortBy: tableState.sortBy } : {}),
      ...(tableState.sortOrder ? { sortOrder: tableState.sortOrder } : {}),
    };
  }, [tableState, itemsPerPage]);

  // Fetch users based on params
  const {
    data: users,
    isLoading: isLoadingUsers,
    error: usersError,
    mutate,
  } = useUserList(userListParams);

  // Access check
  const { hasAccess } = useCanAccess(Permissions.User.READ);

  // Handle filter changes from FilterBar
  const handleFilterValueChange = useCallback(
    (values: Record<string, string>) => {
      // Convert the filter values to our state format
      const filters: Partial<typeof tableState> = {};

      if (values.role) {
        filters.role = parseInt(values.role, 10); // Convert to number
      }

      handleFilterChange(filters);
    },
    [handleFilterChange],
  );

  // Table row selection
  const { selectedRows, handleRowSelection, resetSelection } = useTable(tableConfig);

  // Handle bulk update
  const handleBulkUpdate = useCallback(() => {
    setIsBulkUpdateModalOpen(true);
  }, []);

  // Process bulk update
  const onBulkUpdate = useCallback(
    async (payload: { targetRole: string }) => {
      const targetRole = roles?.data.find((role) => role.name === payload.targetRole);
      if (!targetRole) return;
      try {
        const filters = {
          search: tableState.search,
          ...(tableState.role !== undefined ? { role: tableState.role } : {}),
        };

        await bulkUpdate({
          targetRole: targetRole.id,
          users: selectedRows === "all" ? "all" : selectedRows.map(Number),
          filters,
        });

        setIsBulkUpdateModalOpen(false);
        await mutate();
        resetSelection();
        toast({
          title: t("bulk-update-role-success"),
          description: t("bulk-update-role-success-description"),
        });
      } catch (error) {
        console.error("UsersTable-onBulkUpdate", error);
        toast({
          title: t("bulk-update-role-error"),
          description: t("bulk-update-role-error-description"),
          variant: "destructive",
        });
      }
    },
    [
      selectedRows,
      tableState.search,
      tableState.role,
      roles?.data,
      bulkUpdate,
      mutate,
      resetSelection,
      toast,
      t,
    ],
  );

  // Create columns with translations
  const columns = useMemo(
    () => createColumns(t, roles, isLoadingRoles),
    [t, roles, isLoadingRoles],
  );

  // Get table data
  const data = useMemo(() => users?.data, [users]);

  // Get current filter values for FilterBar
  const currentFilterValues = useMemo(() => {
    return {
      role: tableState.role !== undefined ? String(tableState.role) : "",
    };
  }, [tableState.role]);

  // Memoize all table-related properties
  const tableProps: TableProps<IUserBase> = useMemo(
    () => ({
      columns,
      data: data as IUserBase[],
      totalRows: users?.total ?? 0,
      isLoading: isLoadingUsers,
      enableSelection: true,
      enableSearch: true,
      enablePagination: (users?.total ?? 0) > itemsPerPage,
      pageSize: itemsPerPage,
      selectedRows: selectedRows,
      title: `${t("title")} (${users?.total ?? 0})`,
      getRowId,
      onStateChange: tableConfig.onStateChange,
      onSelectionChange: handleRowSelection,
      noResults: t("errors.no-results"),
      bulkActions: selectedRows.length > 0 && (
        <Button variant="outline" size="sm" onClick={handleBulkUpdate}>
          {t("bulk-update-role")}
        </Button>
      ),
      filters: <Filter onChange={handleFilterValueChange} value={currentFilterValues} />,
    }),
    [
      columns,
      data,
      users?.total,
      isLoadingUsers,
      itemsPerPage,
      selectedRows,
      t,
      getRowId,
      tableConfig.onStateChange,
      handleRowSelection,
      handleBulkUpdate,
      handleFilterValueChange,
      currentFilterValues,
    ],
  );

  // Error handling section after all hooks are defined
  if (!isLoadingUsers && !hasAccess) {
    return <div>{t("errors.no-access")}</div>;
  }

  if (!isLoadingUsers && usersError) {
    return <div>{t("errors.loading-failed")}</div>;
  }

  return (
    <>
      <DataTable {...tableProps} />
      <BulkUpdateRoleModal
        isOpen={isBulkUpdateModalOpen}
        roles={roles?.data ?? []}
        onClose={() => setIsBulkUpdateModalOpen(false)}
        onUpdate={onBulkUpdate}
      />
    </>
  );
}
