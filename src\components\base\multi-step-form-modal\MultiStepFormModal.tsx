import { <PERSON><PERSON><PERSON>ider } from "react-hook-form";
import { Dialog } from "@/components/ui/dialog";
import { <PERSON><PERSON>Header } from "./components/ModalHeader";
import { ModalContent } from "./components/ModalContent";
import { ModalFooter } from "./components/ModalFooter";
import { ModalConfig, ModalEvents } from "./types";
import { useMultiStepModal } from "./hooks/useMultiStepModal";
import { useEffect, useCallback, useState, useRef, useMemo } from "react";
import { DialogContent } from "./components/CustomDialogContent";
import { ConfirmDialog } from "./components/ConfirmDialog";
import { useTranslations } from "next-intl";

/** Props for the MultiStepFormModal component */
interface MultiStepFormModalProps {
  /** Configuration object defining the modal's structure and behavior */
  config: ModalConfig;
  /** Optional event handlers for modal lifecycle events */
  events?: ModalEvents;
  /** Optional controlled open state */
  isOpen?: boolean;
  /** Optional callback when open state changes */
  onOpenChange?: (open: boolean) => void;
}

/**
 * A flexible multi-step modal component that supports form validation, navigation,
 * and complex field types. It can be used either controlled or uncontrolled.
 *
 * @example
 * ```tsx
 * <MultiStepModal
 *   config={{
 *     key: "example-modal",
 *     title: "Example Modal",
 *     steps: [
 *       {
 *         id: "step1",
 *         title: "Step 1",
 *         fields: [
 *           {
 *             id: "name",
 *             type: "text",
 *             label: "Name",
 *             required: true
 *           }
 *         ]
 *       }
 *     ]
 *   }}
 *   events={{
 *     onComplete: async (data) => {
 *       console.log("Form submitted:", data);
 *     }
 *   }}
 * />
 * ```
 */
export const MultiStepFormModal = ({
  config,
  events,
  isOpen: controlledIsOpen = false,
  onOpenChange,
}: MultiStepFormModalProps) => {
  const renderCount = useRef(0);
  const lastUpdate = useRef<{ type: string; timestamp: number } | null>(null);
  const lastFormState = useRef<any>(null);

  const {
    form,
    currentStep,
    totalSteps,
    closeModal,
    nextStep,
    previousStep,
    openModal,
    loading,
    setLoading,
    resetForm,
    isDirty,
  } = useMultiStepModal(config, events);

  const t = useTranslations();

  // Memoize dialog states to prevent unnecessary re-renders
  const [dialogState, setDialogState] = useState({
    showCloseConfirm: false,
    showResetConfirm: false,
    pendingClose: false,
  });

  // Batch dialog state updates
  const updateDialogState = useCallback((updates: Partial<typeof dialogState>) => {
    setDialogState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Optimized form state change handler with deep comparison
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    const subscription = form.watch((value) => {
      // Deep compare form state to prevent unnecessary updates
      const formStateStr = JSON.stringify(value);
      if (formStateStr === lastFormState.current) {
        return;
      }
      lastFormState.current = formStateStr;

      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {}, 100);
    });
    return () => {
      subscription.unsubscribe();
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [form]);

  // Memoize handlers with stable references
  const handleOpenChange = useCallback(
    (open: boolean, force = false) => {
      if (dialogState.showResetConfirm) return;

      if (!open) {
        if (force || !isDirty) {
          closeModal();
          onOpenChange?.(false);
          return;
        }

        updateDialogState({
          showCloseConfirm: true,
          pendingClose: true,
        });
        return;
      }

      openModal();
      onOpenChange?.(true);
    },
    [dialogState.showResetConfirm, isDirty, closeModal, openModal, onOpenChange, updateDialogState],
  );

  const handleConfirmClose = useCallback(() => {
    if (dialogState.pendingClose) {
      updateDialogState({
        showCloseConfirm: false,
        showResetConfirm: false,
        pendingClose: false,
      });

      closeModal();
      onOpenChange?.(false);
    }
  }, [dialogState.pendingClose, closeModal, onOpenChange, updateDialogState]);

  const handleCancelClose = useCallback(() => {
    updateDialogState({
      showCloseConfirm: false,
      pendingClose: false,
    });
    onOpenChange?.(true);
  }, [onOpenChange, updateDialogState]);

  /**
   * Handles form reset action with dirty state check
   */
  const handleReset = useCallback(() => {
    if (isDirty) {
      updateDialogState({
        showResetConfirm: true,
      });
    } else {
      resetForm();
    }
  }, [isDirty, resetForm]);

  /**
   * Handles confirmation of form reset action
   */
  const handleConfirmReset = useCallback(() => {
    updateDialogState({
      showResetConfirm: false,
    });
    resetForm();
  }, [resetForm]);

  /**
   * Handles form submission for both step completion and final submit
   * @param e - Form submit event
   * @param buttonData - Optional data from the submit button
   */
  const handleSubmit = useCallback(
    async (e: React.FormEvent, buttonData?: Record<string, any>) => {
      e.preventDefault();

      if (loading.isSubmitting || loading.isLoadingStep) {
        return;
      }

      try {
        /**
         * Recursively gets all field IDs from a field configuration array
         * @param fields - Array of field configurations
         * @returns Array of field IDs
         */
        const getAllFieldIds = (fields: (typeof config.steps)[number]["fields"]): string[] => {
          return fields.flatMap((field) => {
            // Handle accordion-select fields
            if (field.type === "accordion-select" && field.options) {
              return field.options.flatMap((option) => {
                // Recursively get nested fields
                if (option.fields) {
                  return getAllFieldIds(option.fields);
                }
                return [];
              });
            } else if (field.type === "layout") {
              return getAllFieldIds(field.meta.layout.fields);
            }
            // Return the field ID for non-nested fields
            return field.id;
          });
        };

        // Validate only the current step's fields
        const currentFields = getAllFieldIds(config.steps[currentStep].fields);
        const isValid = await form.trigger(currentFields, { shouldFocus: true });

        if (!isValid) {
          events?.onValidationError?.(form.formState.errors);
          return;
        }

        const isLastStep = currentStep === totalSteps - 1;
        if (isLastStep) {
          // On last step, validate all fields before submitting
          const isAllValid = await form.trigger(undefined, { shouldFocus: true });
          if (!isAllValid) {
            events?.onValidationError?.(form.formState.errors);
            return;
          }

          setLoading({ isSubmitting: true });
          const formData = form.getValues();
          // Merge button data with form data
          const finalData = buttonData ? { ...formData, ...buttonData } : formData;
          await events?.onComplete?.(finalData);
          handleOpenChange(false, true); // Force close after successful submission
        } else {
          setLoading({ isLoadingStep: true });
          await nextStep();
        }
      } catch (error) {
        console.error("Error submitting form:", error);
      } finally {
        setLoading({ isSubmitting: false, isLoadingStep: false });
      }
    },
    [
      loading.isSubmitting,
      loading.isLoadingStep,
      currentStep,
      totalSteps,
      form,
      events,
      nextStep,
      setLoading,
      handleOpenChange,
      config.steps,
    ],
  );

  // Memoize child components props
  const headerProps = useMemo(
    () => ({
      title: config.title,
      currentStep,
      totalSteps,
      onClose: () => handleOpenChange(false),
      steps: config.steps,
    }),
    [config.title, currentStep, totalSteps, handleOpenChange, config.steps],
  );

  const contentProps = useMemo(
    () => ({
      step: config.steps[currentStep],
      allSteps: config.steps,
      currentStep,
    }),
    [config.steps, currentStep],
  );

  const footerProps = useMemo(
    () => ({
      currentStep,
      totalSteps,
      onPrevious: previousStep,
      onNext: handleSubmit,
      onReset: handleReset,
      loading,
      isDirty,
      submitButtons: config.submitButtons,
    }),
    [
      currentStep,
      totalSteps,
      previousStep,
      handleSubmit,
      handleReset,
      loading,
      isDirty,
      config.submitButtons,
    ],
  );

  return (
    <>
      <Dialog open={controlledIsOpen} onOpenChange={handleOpenChange}>
        <DialogContent
          className="max-w-screen flex max-h-screen w-screen flex-col overflow-hidden p-4 lg:max-h-[80vh] lg:max-w-screen-lg"
          onPointerDownOutside={(e) => e.preventDefault()}
        >
          <FormProvider {...form}>
            <form
              onSubmit={handleSubmit}
              className="flex h-full flex-col overflow-hidden"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleSubmit(e);
                }
              }}
            >
              <ModalHeader {...headerProps} />
              <div className="flex-1 overflow-y-auto p-1">
                <ModalContent {...contentProps} />
              </div>
              <div className="mt-auto p-1">
                <ModalFooter {...footerProps} />
              </div>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>

      <ConfirmDialog
        open={dialogState.showCloseConfirm}
        onOpenChange={(open) => updateDialogState({ showCloseConfirm: open })}
        title={t("modal.confirm.close.title")}
        description={t("modal.confirm.close.description")}
        confirmLabel={t("modal.confirm.close.confirm")}
        cancelLabel={t("modal.confirm.close.cancel")}
        onConfirm={handleConfirmClose}
        onCancel={handleCancelClose}
        variant="destructive"
      />

      <ConfirmDialog
        open={dialogState.showResetConfirm}
        onOpenChange={(open) => updateDialogState({ showResetConfirm: open })}
        title={t("modal.confirm.reset.title")}
        description={t("modal.confirm.reset.description")}
        confirmLabel={t("modal.confirm.reset.confirm")}
        cancelLabel={t("modal.confirm.reset.cancel")}
        onConfirm={handleConfirmReset}
        onCancel={() => updateDialogState({ showResetConfirm: false })}
        variant="destructive"
      />
    </>
  );
};
