import {
  Clerk<PERSON>lient,
  Invitation,
  User,
  UserJ<PERSON><PERSON>,
  create<PERSON>lerkClient,
} from '@clerk/backend';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@config';
import { PXActionResult } from '@types';

@Injectable()
export class ClerkService {
  private readonly redirectUrl: string;
  private readonly client: ClerkClient;
  private readonly SECRET: string;

  constructor(private readonly configService: ConfigService) {
    const authSecrets = this.configService.authSecrets;
    this.SECRET = authSecrets.CLERK_SECRET_KEY;
    this.redirectUrl =
      this.configService.appSecrets.CUSTOMER_PORTAL_SIGNUP_LINK;
    this.client = createClerkClient({ secretKey: this.SECRET });
  }

  /**
   * Invites the provided email address to our SSO platform
   * @param emailAddress Email address of the user to invite
   * @param metaData Any metadata about the invitation/user
   * @param notify Whether an invitation email should be sent to the given email address.
   * @returns
   */
  async inviteUser(
    emailAddress: string,
    metaData?: any,
    notify?: boolean,
  ): Promise<PXActionResult> {
    try {
      const res = await this.client.invitations.createInvitation({
        emailAddress,
        redirectUrl: this.redirectUrl,
        publicMetadata: metaData,
        notify,
      });
      return {
        success: true,
        message: 'Invitation created',
        data: { invitation: res, alreadyInvited: false },
      };
    } catch (error) {
      console.log('Error inviting user', error);
      if (
        error instanceof Error &&
        'clerkError' in error &&
        'errors' in error
      ) {
        const errorCode = error?.errors?.[0]?.code;
        if (errorCode === 'duplicate_record') {
          const invitation = await this.getInvitationByEmail(emailAddress);
          return {
            success: true,
            message: 'Already invited',
            data: {
              invitation,
              alreadyInvited: true,
            },
          };
        }

        if (errorCode === 'form_identifier_exists') {
          const user = await this.getUserDetailsByEmail(emailAddress);
          return {
            success: !!user,
            message: 'User already exists, returning user info from Clerk',
            data: {
              invitation: null,
              alreadyInvited: true,
              existingUserInfo: user,
            },
          };
        }
      }
      return {
        success: false,
        message: error?.message,
        data: { invitation: null, alreadyInvited: null },
      };
    }
  }

  /**
   * Extracts the primary email of the user
   * @param eventData Event data containing user info
   * @returns Email address of the user
   */
  getPrimaryEmail(eventData: UserJSON) {
    const primaryEmailId = eventData.primary_email_address_id;
    return eventData.email_addresses?.filter(
      (email) => email.id === primaryEmailId,
    )[0]?.email_address;
  }

  /**
   * Gets the user details by email
   * @param email Email address of the user
   * @returns A promise that resolves to the user details (if found) or null
   */
  async getUserDetailsByEmail(email: string): Promise<User | null> {
    const userListResponse = await this.client.users.getUserList({
      emailAddress: [email],
    });
    return userListResponse?.data?.[0] || null;
  }

  /**
   * Extracts the role of the user from Clerk's public metadata
   * @param eventData Event data containing user info
   * @returns Role of the user
   */
  getUserRole(eventData: UserJSON): string {
    return eventData.public_metadata?.role as string;
  }

  /**
   * Gets the invitation by email
   * @param email Email address of the user
   * @returns A promise that resolves to the invitation (if found) or null
   */
  async getInvitationByEmail(email: string): Promise<Invitation | null> {
    const invitationListResponse =
      await this.client.invitations.getInvitationList({
        query: email,
      });
    return invitationListResponse?.data?.[0] || null;
  }

  /**
   * Creates a verified phone number for a user
   * @param userId User ID of the user
   * @param phoneNumber Phone number of the user
   * @returns A promise that resolves to the ID of the created phone number
   */
  async createPhoneNumber(userId: string, phoneNumber: string) {
    const response = await this.client.phoneNumbers.createPhoneNumber({
      userId,
      phoneNumber,
      primary: true,
      verified: true,
    });
    return response.id;
  }

  /**
   * Unlinks a phone number from a user
   * @param phoneNumberId ID of the phone number to unlink
   * @returns A promise that resolves to true if the phone number was unlinked successfully
   */
  async unlinkPhoneNumber(phoneNumberId: string) {
    return this.client.phoneNumbers.deletePhoneNumber(phoneNumberId);
  }
}
