"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle2, Info, XCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { useConfirmationModal } from "@/hooks/store/confirmationModal";

const icons = {
  success: CheckCircle2,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
};

const variants = {
  success: "text-green-500",
  error: "text-red-500",
  warning: "text-yellow-500",
  info: "text-blue-500",
};

export function ConfirmationModal() {
  const { isOpen, closeModal, data, onConfirm, onCancel, loading, setLoading } =
    useConfirmationModal();

  const handleConfirm = async () => {
    try {
      setLoading(true);
      console.log("Loading is true");
      await onConfirm?.();
      closeModal();
    } catch (error) {
      console.error("Error in confirmation action:", error);
    } finally {
      setLoading(false);
      console.log("Loading false");
    }
  };

  const handleCancel = async () => {
    try {
      setLoading(true);
      await onCancel?.();
      closeModal();
    } catch (error) {
      console.error("Error in cancel action:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!data) return null;

  const Icon = icons[data.type || "info"];
  const variantClass = variants[data.type || "info"];

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader className="flex flex-row items-center gap-4">
          <Icon className={`h-6 w-6 ${variantClass}`} />
          <div>
            <DialogTitle>{data.title}</DialogTitle>
            <DialogDescription className="mt-2">{data.description}</DialogDescription>
          </div>
        </DialogHeader>
        <DialogFooter className="mt-4 flex flex-row justify-end gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={loading}>
            {data.cancelText || "Cancel"}
          </Button>
          <Button
            variant={data.type === "error" ? "destructive" : "default"}
            onClick={handleConfirm}
            disabled={loading}
          >
            {data.confirmText || "Confirm"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
