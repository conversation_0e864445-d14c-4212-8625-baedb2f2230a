import { FiscalEntity } from '@enums';
import { ProductFamily, ProductOffer, ProductPlan } from '.';
import { CommunityInfo, CourseInfo } from '@types';

export class Product {
  id: number;
  code: string;
  internalName: string;
  externalName: string;
  description: string;
  fiscalEntity?: FiscalEntity;
  productFamily: ProductFamily;
  plans: ProductPlan[];
  offers?: ProductOffer[];
  status: string;
  taxProfile?: string;
  isEvent?: boolean;
  isForever?: boolean;
  eventYear?: string;
  eventLocation?: string;
  image?: string;
  withProductDelivery?: boolean;
  lmsIds?: CourseInfo[];
  communityIds?: CommunityInfo[];
  createdAt?: Date;
}
