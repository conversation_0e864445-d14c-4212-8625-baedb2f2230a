"use client";

import { cn } from "@/lib/utils";

interface CardRowProps {
  field: string;
  value: string | number | null | undefined;
  hasNext?: boolean;
  className?: string;
}

export default function CardRow({ field, value, hasNext = false, className }: CardRowProps) {
  if (!field || value === null || value === undefined || value === "") {
    return null;
  }

  return (
    <div className="flex flex-col gap-4">
      <div className={cn("flex items-center justify-between text-sm", className)}>
        <span className="text-neutral-400">{field}</span>
        <span className="text-right font-medium text-white">{value}</span>
      </div>
      {hasNext && <div className="mb-4 h-px w-full bg-neutral-700" />}
    </div>
  );
}
