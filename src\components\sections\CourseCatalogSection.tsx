"use client";

import { useCallback, useEffect, useMemo, useState } from "react";
import { useCourseCatalog } from "@/services/course";
import { ICourseCatalogItem } from "@px-shared-account/hermes";
import { ProductCard, ProductCardSkeleton } from "@/components/cards/ProductCard";
import { PageSectionBase, PageSectionCarousel } from "../base/page-section";
import { useTranslations } from "next-intl";

export function CourseCatalogSection() {
  const t = useTranslations("dashboard");
  const [cursor, setCursor] = useState<number | undefined>(0);
  const [allCourses, setAllCourses] = useState<ICourseCatalogItem[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { data, isLoading } = useCourseCatalog({ limit: 20, cursor });

  // Accumulate courses data when new data arrives (windowed pagination)
  useEffect(() => {
    if (data?.data) {
      if (cursor === 0) {
        // Initial load - replace all courses
        setAllCourses(data.data);
      } else {
        // Windowed pagination load - append new courses from current window
        setAllCourses((prev) => {
          const existingIds = new Set(prev.map((course) => course.id));
          const newCourses = data.data.filter((course) => !existingIds.has(course.id));
          return [...prev, ...newCourses];
        });
      }
      setIsLoadingMore(false);
    }
  }, [data, cursor]);

  const loadMoreCourses = useCallback(() => {
    if (
      data?.nextCursor !== null &&
      data?.nextCursor !== undefined &&
      !isLoadingMore &&
      !isLoading
    ) {
      setIsLoadingMore(true);
      setCursor(data.nextCursor);
    }
  }, [data, isLoadingMore, isLoading]);

  const { ownedCourses, purchasableCourses } = useMemo(() => {
    const ownedCourses: ICourseCatalogItem[] = [];
    const purchasableCourses: ICourseCatalogItem[] = [];
    for (const course of allCourses) {
      if (course.owned) {
        ownedCourses.push(course);
      } else {
        purchasableCourses.push(course);
      }
    }
    return { ownedCourses, purchasableCourses };
  }, [allCourses]);

  if (isLoading && allCourses.length === 0) {
    return (
      <PageSectionCarousel title={t("courses.title")} description={t("courses.sectionSubtitle")}>
        {Array.from({ length: 12 }).map((_, index) => (
          <ProductCardSkeleton key={index} />
        ))}
      </PageSectionCarousel>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      {ownedCourses.length === 1 ? (
        <PageSectionBase title={t("purchases.title")} description={t("purchases.sectionSubtitle")}>
          <div className="flex justify-center py-4">
            <ProductCard course={ownedCourses[0]} />
          </div>
        </PageSectionBase>
      ) : ownedCourses.length > 1 ? (
        <PageSectionCarousel
          title={t("purchases.title")}
          description={t("purchases.sectionSubtitle")}
          carouselItemsClassName="basis-full sm:basis-1/2 md:basis-auto"
          carouselOpts={{ loop: ownedCourses.length > 3 }}
        >
          {ownedCourses.map((course) => (
            <ProductCard key={course.id} course={course} />
          ))}
        </PageSectionCarousel>
      ) : null}

      {isLoading && allCourses.length === 0 ? (
        <PageSectionCarousel title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          {Array.from({ length: 12 }).map((_, index) => (
            <ProductCardSkeleton key={index} />
          ))}
        </PageSectionCarousel>
      ) : purchasableCourses.length === 0 ? (
        <PageSectionBase title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          <div className="p-8 text-center">
            <p>{t("courses.empty-state")}</p>
          </div>
        </PageSectionBase>
      ) : purchasableCourses.length === 1 ? (
        <PageSectionBase title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          <div className="flex justify-center py-4">
            <ProductCard course={purchasableCourses[0]} />
          </div>
        </PageSectionBase>
      ) : (
        <PageSectionCarousel
          title={t("courses.title")}
          description={t("courses.sectionSubtitle")}
          carouselItemsClassName="basis-full sm:basis-1/2 md:basis-auto"
          carouselOpts={{ loop: false }}
          isLoadingMore={isLoadingMore}
          hasMore={data?.nextCursor !== null && data?.nextCursor !== undefined}
          onEnd={() => {
            if (data?.nextCursor !== null && data?.nextCursor !== undefined && !isLoadingMore) {
              loadMoreCourses();
            }
          }}
        >
          {purchasableCourses.map((course) => (
            <ProductCard key={`course-${course.id}`} course={course} />
          ))}
        </PageSectionCarousel>
      )}
    </div>
  );
}
