import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  JoinTable,
  ManyToMany,
} from 'typeorm';
import {
  ProductOfferStatus,
  ProductOfferVersion,
} from '@px-shared-account/hermes';
import { FiscalEntity } from '@enums';
import { CommunityAccesses, LMSAccesses, OfferConfig } from '@types';
import { ProductOffer } from '@entities';
import { CheckoutPageTable } from './checkout-page.table';
import { ProductTable } from './product.table';
import { ProductPlanTable } from './product-plan.table';

@Entity({
  name: 'productOffers',
})
export class ProductOfferTable implements ProductOffer {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 255 })
  externalName: string;

  @Column({ type: 'varchar', length: 255 })
  image: string;

  @Column({ type: 'varchar', length: 255 })
  cardImage: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  bannerImage?: string;

  @Column({ type: 'varchar', length: 800, nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  usp?: string;

  @Column({ type: 'varchar', length: 3 })
  currency: string;

  @Column({ type: 'enum', enum: FiscalEntity })
  fiscalEntity: FiscalEntity;

  @Column({ type: 'enum', enum: ProductOfferStatus })
  status: ProductOfferStatus;

  @Column({ type: 'varchar', length: 100 })
  redirectUrl: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  redirectIfDisabled?: string;

  @OneToOne(
    () => CheckoutPageTable,
    (checkoutPage) => checkoutPage.productOffer,
    { cascade: true },
  )
  @JoinColumn({
    name: 'checkoutPage',
  })
  checkoutPage: CheckoutPageTable;

  @Column({ type: 'varchar', length: 100 })
  chargebeeId: string;

  @Column({ type: 'jsonb', nullable: true })
  history?: Omit<this, 'id' | 'history' | 'updateHistory'>[];

  @Column({
    type: 'enum',
    enum: ProductOfferVersion,
    default: ProductOfferVersion.PREVIEW,
  })
  version: ProductOfferVersion;

  @Column({ type: 'jsonb', nullable: true })
  config?: OfferConfig;

  @Column({ type: 'varchar', length: 100, nullable: true })
  paymentGateway?: string;

  @Column({ type: 'varchar', length: 100 })
  slug: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  withProductDelivery?: boolean;

  @Column({ type: 'jsonb', nullable: true })
  lmsIds?: LMSAccesses;

  @Column({ type: 'boolean', default: false, nullable: true })
  suspendable?: boolean;

  @Column({ type: 'int', nullable: true })
  delayPeriod?: number;

  @Column({ type: 'jsonb', default: [], nullable: true })
  communityIds?: CommunityAccesses;

  @ManyToMany(() => ProductTable, (product) => product.offers)
  @JoinTable({
    name: 'ProductOffer_Product',
    joinColumn: {
      name: 'offerId',
      foreignKeyConstraintName: 'FK_ProductOffer_Product_offerId',
    },
    inverseJoinColumn: {
      name: 'productId',
      foreignKeyConstraintName: 'FK_ProductOffer_Product_productId',
    },
  })
  products?: ProductTable[];

  @ManyToMany(() => ProductPlanTable)
  @JoinTable({
    name: 'ProductOffer_Plans',
    joinColumn: {
      name: 'offerId',
      referencedColumnName: 'id',
      foreignKeyConstraintName: 'FK_ProductOffer_Plans_offerId',
    },
    inverseJoinColumn: {
      name: 'planId',
      referencedColumnName: 'id',
      foreignKeyConstraintName: 'FK_ProductOffer_Plans_planId',
    },
  })
  plans?: ProductPlanTable[];

  @Column({ type: 'boolean', default: false, nullable: true })
  isForever?: boolean;

  @Column({ type: 'int', nullable: true, default: 28 })
  trialDaysMonthly?: number;

  @Column({ type: 'int', nullable: true, default: 300 })
  trialDaysYearly?: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
