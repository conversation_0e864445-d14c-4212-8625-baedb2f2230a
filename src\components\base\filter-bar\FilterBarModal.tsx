"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FilterBarProps, FilterStep } from "./types";
import { useFilterBar } from "./useFilterBar";
import { Combobox } from "@/components/base/combobox";
import { Filter } from "lucide-react";

/**
 * Props for the FilterBarModal component
 * Extends FilterBarProps with modal-specific properties
 */
interface FilterBarModalProps extends FilterBarProps {
  /** Whether the modal is in a loading state */
  loading?: boolean;
  /** Callback fired when the Apply button is clicked */
  onApply?: () => void;
  /** Text to display on the trigger button */
  triggerText?: string;
}

/**
 * Mobile-friendly modal interface for the filter bar system
 *
 * @component
 * @example
 * ```tsx
 * <FilterBarModal
 *   steps={[
 *     { type: 'filter', id: 'program', label: 'Program', options: [] }
 *   ]}
 *   onChange={handleChange}
 *   onApply={handleApply}
 * />
 * ```
 *
 * @remarks
 * This component provides a modal interface for filter management on mobile devices.
 * Features include:
 * - Full-screen modal with scrollable content
 * - Group-based filter organization
 * - Apply/Reset functionality
 * - Active filter count indicator
 */
export function FilterBarModal({
  className,
  steps,
  value,
  onChange,
  onOptionsNeeded,
  loading = false,
  onApply,
  triggerText = "Ajouter des filtres",
  ...props
}: FilterBarModalProps) {
  /** Controls the visibility of the modal */
  const [open, setOpen] = React.useState(false);
  /** Tracks the applying state when user clicks Apply */
  const [isApplying, setIsApplying] = React.useState(false);
  /** Tracks which filter dropdowns are open */
  const [openStates, setOpenStates] = React.useState<Record<string, boolean>>({});

  /** Initialize filter bar state and logic */
  const { values, stepsState, handleChange, clearAll, isStepEnabled } = useFilterBar({
    steps,
    value,
    onChange,
    onOptionsNeeded,
  });

  /** Count of currently active filters */
  const activeFiltersCount = Object.values(values).filter(Boolean).length;

  /**
   * Handles the apply action
   * Sets loading state, calls onApply callback, and closes the modal
   */
  const handleApply = async () => {
    setIsApplying(true);
    onApply?.();
    setIsApplying(false);
    setOpen(false);
  };

  /** Process steps into groups for display */
  const groups = steps
    .reduce<FilterStep[][]>((acc, step) => {
      if (step.type === "separator") {
        acc.push([]);
      } else if (step.type === "filter") {
        if (acc.length === 0) acc.push([]);
        acc[acc.length - 1].push(step);
      }
      return acc;
    }, [])
    .filter((group) => group.length > 0);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-fit justify-center rounded-full text-center">
          <Filter className="mr-2 h-4 w-4" />
          {triggerText}
          {activeFiltersCount > 0 && (
            <span className="bg-primary/10 text-primary ml-2 rounded-full px-2 py-0.5 text-xs font-medium">
              {activeFiltersCount}
            </span>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="flex h-screen max-h-screen max-w-screen flex-col !rounded-3xl md:h-auto md:w-auto md:min-w-96">
        <DialogHeader>
          <DialogTitle>Filtres</DialogTitle>
        </DialogHeader>
        <div className="divide-muted-foreground/20 flex flex-1 flex-col divide-y overflow-y-auto py-4">
          {groups.map((group, groupIndex) => (
            <div key={groupIndex} className="flex flex-col gap-6 py-6 first:pt-0 last:pb-0">
              {group.map((step) => {
                const stepState = stepsState[step.id];

                return (
                  <div key={step.id} className="flex flex-col gap-4 p-2">
                    <label htmlFor={step.id} className="text-sm leading-none font-medium">
                      {step.label}
                    </label>
                    <Combobox
                      id={step.id}
                      value={values[step.id]}
                      onChange={(value) => handleChange(step.id, value as string)}
                      options={stepState.options}
                      label={step.label}
                      placeholder={step.placeholder}
                      disabled={!isStepEnabled(step.id)}
                      error={stepState.error}
                      loading={stepState.isLoading}
                      open={openStates[step.id]}
                      onOpenChange={(isOpen) => {
                        setOpenStates((prev) => ({
                          ...prev,
                          [step.id]: isOpen,
                        }));
                      }}
                      contentClassName="z-[60]"
                    />
                    {stepState.error && (
                      <p className="text-destructive text-sm">{stepState.error}</p>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        <DialogFooter className="gap-2">
          {activeFiltersCount > 0 && (
            <Button
              type="button"
              variant="outline"
              onClick={clearAll}
              disabled={loading || isApplying}
              className="rounded-full"
            >
              Réinitialiser
            </Button>
          )}
          <Button
            type="submit"
            onClick={handleApply}
            disabled={activeFiltersCount === 0 || loading || isApplying}
            className="rounded-full"
          >
            Appliquer
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
