import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { frFR, enUS } from "@clerk/localizations";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { getLocale, getMessages } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";

import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from "@/components/layout/ThemeProvider";
import Analytics from "@/components/analytics";
import ImpersonationSheet from "@/components/impersonation/ImpersonationSheet";
import { anton } from "@/lib/fonts/anton";
import { clash } from "@/lib/fonts/clash";

export const metadata: Metadata = {
  title: "Paradox App",
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <ClerkProvider localization={locale === "fr" ? frFR : enUS}>
      <html
        lang={locale}
        suppressHydrationWarning
        className={`${clash.variable} ${anton.variable}`}
      >
        <body>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            enableSystem
            disableTransitionOnChange
          >
            <NextIntlClientProvider messages={messages}>
              <main>{children}</main>
              <ImpersonationSheet />
            </NextIntlClientProvider>
            <Toaster />
          </ThemeProvider>
        </body>
        <Analytics />
      </html>
    </ClerkProvider>
  );
}
