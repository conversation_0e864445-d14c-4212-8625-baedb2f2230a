import { Injectable } from '@nestjs/common';
import {
  TriadParticipantTable,
  TriadTable,
  UserTable,
} from '@services/database/postgres';

@Injectable()
export class TriadParticipantsFactory {
  /**
   * Generates a new triad participant
   * @param userId - The ID of the user to generate the participant for
   * @param triadId - The ID of the triad to generate the participant for
   * @returns The generated triad participant
   */
  generate(userId: number, triadId: number): TriadParticipantTable {
    const triadParticipant = new TriadParticipantTable();
    triadParticipant.user = new UserTable();
    triadParticipant.user.id = userId;
    triadParticipant.triad = new TriadTable();
    triadParticipant.triad.id = triadId;
    triadParticipant.joinedAt = new Date();
    return triadParticipant;
  }
}
