import { SetMetadata } from '@nestjs/common';
import { Permissions } from '@px-shared-account/hermes';

// This type represents any valid permission value from the Permissions enums
type AllPermissionValues =
  | Permissions.User
  | Permissions.Role
  | Permissions.Course
  | Permissions.Cohort
  | Permissions.Triad
  | Permissions.WhatsNew;

export const RequirePermissions = (...permissions: AllPermissionValues[]) => SetMetadata('permissions', permissions);
