{"name": "sirius", "version": "0.1.0", "private": true, "type": "module", "scripts": {"postinstall": "node ./scripts/postinstall.js", "dev": "next dev -p 3005", "build": "next build", "start": "next start -p 3005", "lint": "next lint", "prepare": "husky", "format": "prettier --write ."}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@clerk/localizations": "^3.9.5", "@clerk/nextjs": "^6.9.2", "@hookform/resolvers": "^3.9.1", "@px-shared-account/hermes": "^1.3.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@segment/analytics-next": "^1.81.0", "@tailwindcss/postcss": "^4.1.7", "@tanstack/react-table": "^8.20.6", "@upstash/redis": "^1.34.3", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "embla-carousel-wheel-gestures": "^8.0.2", "flag-icons": "^7.5.0", "framer-motion": "^11.18.2", "html-to-image": "^1.11.13", "ioredis": "^5.4.2", "lucide-react": "^0.511.0", "motion": "^12.13.0", "next": "^15.3.3", "next-intl": "^3.26.2", "next-themes": "^0.4.4", "react": "19.0.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.1", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-phone-number-input": "^3.4.12", "react-qr-code": "^2.0.15", "react-responsive": "^10.0.0", "react-select": "^5.10.0", "react-timezone-select": "^3.2.8", "recharts": "^2.15.3", "sonner": "^1.7.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "vaul": "^1.1.1", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@types/canvas-confetti": "^1.9.0", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.15", "@types/node": "^20", "@types/react": "19.0.2", "@types/react-dom": "19.0.2", "eslint": "^8", "eslint-config-next": "15.1.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "fs-extra": "^11.3.0", "husky": "^9.1.7", "lint-staged": "^15.3.0", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.2.9", "typescript": "^5"}, "lint-staged": {"*.js": "eslint --cache --fix"}, "overrides": {"@types/react": "19.0.2", "@types/react-dom": "19.0.2"}}