import { Injectable } from '@nestjs/common';
import { BankTransferPayment } from '@dtos';
import { BankTransfer } from '@entities';
import { BankTransferStatus } from '@enums';

@Injectable()
export class BankTransferFactory {
  generate(
    bankNameWithEntityId: string,
    payment: BankTransferPayment,
  ): BankTransfer {
    const bankTransfer = new BankTransfer();
    const [bankName, entityId] = bankNameWithEntityId.split('_');
    bankTransfer.bankName = bankName;
    bankTransfer.entityId = entityId;
    bankTransfer.currency = payment.currency;
    bankTransfer.paymentId = payment.paymentId;
    bankTransfer.paymentDate = new Date(payment.paymentDate);
    bankTransfer.senderEmail = payment.senderEmail;
    bankTransfer.paymentAmount = payment.paymentAmount;
    bankTransfer.senderAddress = payment.senderAddress;
    bankTransfer.senderLastName = payment.senderLastName;
    bankTransfer.senderFirstName = payment.senderFirstName;
    bankTransfer.paymentDescription = payment.paymentDescription;
    bankTransfer.status = BankTransferStatus.UNMATCHED;
    bankTransfer.createdAt = new Date();
    return bankTransfer;
  }
}
