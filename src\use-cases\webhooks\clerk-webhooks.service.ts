import { Injectable } from '@nestjs/common';
import { UserWebhookEvent } from '@clerk/backend';
import { UserUseCases } from '../user';

@Injectable()
export class ClerkWebhooksHandler {
  constructor(private readonly userService: UserUseCases) {}

  /**
   * Handles Clerk webhook events. Only `user.created` & `user.updated` events for now
   * @param event Webhook event from Clerk
   * @returns A promise that resolves to `UpdateResult`
   */
  async handle(event: UserWebhookEvent) {
    const { type, data } = event;
    switch (type) {
      case 'user.created':
        return this.userService.createUserOrUpdateSSOId(data);
      case 'user.updated':
        const primaryEmailAddressId = data.primary_email_address_id;
        const primaryEmailAddress = data.email_addresses.find(
          (email) => email.id === primaryEmailAddressId,
        )?.email_address;
        return this.userService.updateUsingEmail(primaryEmailAddress, {
          firstName: data.first_name,
          lastName: data.last_name,
        });
      default:
        throw new Error('Unsupported webhook event type');
    }
  }
}
