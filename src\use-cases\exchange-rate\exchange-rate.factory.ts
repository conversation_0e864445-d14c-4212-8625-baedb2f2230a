import moment from 'moment';
import { Injectable } from '@nestjs/common';
import { ExchangeRate } from '@entities';
import { CreateExchangeRateDTO } from '@dtos';
import { ExchangeRateCurrencies } from '@enums';

@Injectable()
export class ExchangeRateFactory {
  generate(exchangeRateInfo: CreateExchangeRateDTO): ExchangeRate {
    const exchangeRate = new ExchangeRate();
    exchangeRate.from = exchangeRateInfo.from;
    exchangeRate.to = ExchangeRateCurrencies.AED;
    exchangeRate.rate = exchangeRateInfo.rate;
    exchangeRate.date = this.generateExchangeRateTime(exchangeRateInfo.date);
    exchangeRate.metaData = exchangeRateInfo.metaData;
    return exchangeRate;
  }

  /**
   * Generates the exchange rate time in seconds from the given date string
   * Adds 1 day to the given date string and returns the time in milliseconds
   * @param dateString
   * @returns Number of milliseconds
   */
  generateExchangeRateTime(dateString: string): number {
    return moment.utc(dateString).startOf('day').toDate().getTime();
  }
}
