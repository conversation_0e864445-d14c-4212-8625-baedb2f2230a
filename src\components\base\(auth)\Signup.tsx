"use client";

import { SignUp as SignUp<PERSON><PERSON>k, useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function SignUp() {
  const { isLoaded, isSignedIn } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      router.push("/");
    }
  }, [isLoaded, isSignedIn]);

  return (
    <div className="flex h-full items-center justify-center">
      {!isLoaded || isSignedIn ? null : <SignUpClerk />}
    </div>
  );
}
