"use client";

import { IUserBase } from "@px-shared-account/hermes";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useUserInitials } from "@/hooks/user";

type UserAvatarProps = {
  user: IUserBase | undefined;
  size?: 5 | 10 | 12 | 16;
};

export default function UserAvatar({ user, size = 12 }: UserAvatarProps) {
  const { firstLetters } = useUserInitials(user);

  return (
    <Avatar className={`size-${size} rounded-full bg-gray-300`}>
      <AvatarFallback className="uppercase">{firstLetters}</AvatarFallback>
    </Avatar>
  );
}
