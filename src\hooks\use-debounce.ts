import { useEffect, useState } from "react";

/**
 * A hook that delays updating a value for the specified delay time.
 * Useful for preventing excessive API calls or expensive operations when a value changes rapidly.
 *
 * @param value - The value to debounce
 * @param delay - The delay time in milliseconds before the value updates
 * @returns The debounced value that updates after the specified delay
 *
 * @example
 * ```tsx
 * const [searchTerm, setSearchTerm] = useState('');
 * const debouncedSearch = useDebounce(searchTerm, 500);
 *
 * // debouncedSearch will only update 500ms after the last searchTerm change
 * ```
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
