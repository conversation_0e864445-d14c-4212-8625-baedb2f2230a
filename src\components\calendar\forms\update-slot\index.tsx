"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { allTimezones, useTimezoneSelect } from "react-timezone-select";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import EventDetailsSection from "./EventDetailsSection";
import SpeakersSection from "./SpeakersSection";
import AudienceSection from "./AudienceSection";
import { CreateNewSlotFormType, CreateNewSlotFormSchema } from "../types";
import { CalendarEvent } from "@/hooks/store/calendar";
import { differenceInMinutes, format } from "date-fns";

const defaultValues = {
  type: "masterclass" as const,
  date: "",
  tz: "Europe/Amsterdam",
  duration: "60",
  recurrence: "weekly",
  replay: false,
  mainSpeaker: "<PERSON>",
  otherSpeakers: [],
  technicalSupport: "<PERSON>",
  cohort: "1",
  participantMin: 5,
  participantMax: 20,
  program: "math101",
  course: "phys101",
  module: "algebra",
  comment: "",
};

export default function UpdateSlotForm({
  onSubmit,
  data,
  type,
}: {
  onSubmit: (data: CreateNewSlotFormType) => void;
  data?: CalendarEvent;
  type?: "create" | "edit";
}) {
  const { options: timezoneOptions } = useTimezoneSelect({
    labelStyle: "original",
    timezones: allTimezones,
  });
  const t = useTranslations("calendar");
  const duration = data?.end
    ? differenceInMinutes(new Date(data.end), new Date(data.begin)).toString()
    : "60";

  console.log({ data, timezoneOptions });
  const form = useForm<CreateNewSlotFormType>({
    resolver: zodResolver(CreateNewSlotFormSchema),
    defaultValues: {
      type: data?.type || defaultValues.type,
      date: data?.begin ? format(data.begin, `yyyy-MM-dd'T'HH:mm`) : "",
      tz: defaultValues.tz,
      duration: duration,
      recurrence: data?.recurrence || defaultValues.recurrence,
      replay: data?.replay || defaultValues.replay,
      mainSpeaker: data?.mainSpeaker || defaultValues.mainSpeaker,
      otherSpeakers: data?.otherSpeakers || [],
      technicalSupport: data?.technicalSupport || defaultValues.technicalSupport,
      cohort: data?.cohort || defaultValues.cohort,
      participantMin: data?.participantMin || defaultValues.participantMin,
      participantMax: data?.participantMax || defaultValues.participantMax,
      program: data?.program || defaultValues.program,
      course: data?.course || defaultValues.course,
      module: data?.module || defaultValues.module,
      comment: data?.comments || defaultValues.comment,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
        <div className="flex flex-1 flex-col divide-y divide-muted-foreground/20 overflow-y-auto py-4">
          {/* Part 1 */}
          <EventDetailsSection form={form} t={t} />
          {/* Part 2 */}
          <SpeakersSection form={form} t={t} />
          {/* Part 3 */}
          <AudienceSection form={form} t={t} />
          {/* Part 4 */}
          <div className="mb-5 border-none">
            <div className="text-xl font-semibold">{t("any-comment")}</div>
            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("comments")}</FormLabel>
                  <Input value={field.value} onChange={field.onChange} />
                </FormItem>
              )}
            />
          </div>
        </div>
        <Button type="submit">{t(type === "create" ? "add-slot" : "edit-slot")}</Button>
      </form>
    </Form>
  );
}
