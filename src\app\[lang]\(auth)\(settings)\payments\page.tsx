import PaymentsView from "@/components/(settings)/payments/PaymentsView";
import { getTranslations } from "next-intl/server";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

export default function PaymentsPage() {
  return (
    <>
      <BreadcrumbSettings active="/payments" />
      <div className="space-y-4">
        <PaymentsView />
      </div>
    </>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "payments" });
  return {
    title: t("title"),
    description: t("sub-title"),
  };
}
