"use client";

import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, She<PERSON><PERSON><PERSON>ger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { MenuIcon } from "lucide-react";
import { useState } from "react";
import { ChatConversation, Message } from "./ChatConversation";
import { ChatList, ChatListItem } from "./ChatList";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";

export interface ChatContainerProps {
  currentUserId: string;
  conversations: ChatListItem[];
  messages: Record<string, Message[]>;
  typingStates?: Record<string, boolean>;
  onSend: (conversationId: string, message: string) => void;
  onAttachment?: (conversationId: string, file: File) => void;
  onNewMessage?: () => void;
  className?: string;
  disabled?: boolean;
}

const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      duration: 0.1,
      when: "beforeChildren",
    },
  },
  exit: {
    opacity: 0,
    transition: {
      duration: 0.1,
    },
  },
};

const conversationVariants = {
  initial: {
    opacity: 0,
    x: 10,
  },
  animate: {
    opacity: 1,
    x: 0,
    transition: {
      type: "tween",
      duration: 0.1,
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    x: -10,
    transition: {
      duration: 0.1,
    },
  },
};

const drawerVariants = {
  initial: { x: "-100%" },
  animate: {
    x: 0,
    transition: {
      type: "tween",
      duration: 0.15,
      ease: "easeOut",
    },
  },
  exit: {
    x: "-100%",
    transition: {
      duration: 0.1,
    },
  },
};

export function ChatContainer({
  currentUserId,
  conversations,
  messages,
  typingStates = {},
  onSend,
  onAttachment,
  onNewMessage,
  className,
  disabled,
}: ChatContainerProps) {
  const [selectedId, setSelectedId] = useState<string | undefined>(conversations[0]?.id);
  const selectedConversation = conversations.find((c) => c.id === selectedId);
  const [open, setOpen] = useState(false);

  const handleNewMessage = () => {
    if (onNewMessage) {
      onNewMessage();
    } else {
      toast.info("Cette fonctionnalité sera bientôt disponible");
    }
  };

  return (
    <motion.div
      className={cn("flex h-full w-full overflow-hidden", className)}
      variants={containerVariants}
      initial="hidden"
      animate="show"
      exit="exit"
    >
      {/* Desktop chat list */}
      <motion.div
        className="hidden w-[300px] border-r md:block"
        initial={{ x: -100 }}
        animate={{ x: 0 }}
        transition={{
          type: "tween",
          duration: 0.15,
          ease: "easeOut",
        }}
      >
        <ChatList items={conversations} selectedId={selectedId} onSelect={setSelectedId} />
      </motion.div>

      {/* Chat conversation */}
      <AnimatePresence mode="wait">
        {selectedConversation ? (
          <motion.div
            key={selectedConversation.id}
            className="flex h-full w-full"
            variants={conversationVariants}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <Sheet open={open} onOpenChange={setOpen}>
              <ChatConversation
                title={selectedConversation.title}
                avatar={selectedConversation.avatar}
                online={selectedConversation.online}
                messages={messages[selectedConversation.id] || []}
                currentUserId={currentUserId}
                isTyping={typingStates[selectedConversation.id]}
                onSend={(message) => onSend(selectedConversation.id, message)}
                onAttachment={
                  onAttachment ? (file) => onAttachment(selectedConversation.id, file) : undefined
                }
                onNewMessage={handleNewMessage}
                disabled={disabled}
                chatListTrigger={
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="icon" className="mr-2 md:hidden">
                      <MenuIcon className="h-5 w-5" />
                    </Button>
                  </SheetTrigger>
                }
              />
              <SheetContent side="left" className="w-[300px] p-0">
                <motion.div
                  variants={drawerVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                >
                  <ChatList
                    items={conversations}
                    selectedId={selectedId}
                    onSelect={(id) => {
                      setSelectedId(id);
                      setOpen(false);
                    }}
                    className="h-screen"
                  />
                </motion.div>
              </SheetContent>
            </Sheet>
          </motion.div>
        ) : (
          <motion.div
            className="flex flex-1 items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
          >
            <p className="text-muted-foreground">Select a conversation to start</p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
