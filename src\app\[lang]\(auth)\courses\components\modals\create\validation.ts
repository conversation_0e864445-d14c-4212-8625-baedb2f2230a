"use client";

import { z } from "zod";
import { COURSE_THEMES } from "@/services/course/themes";
import { useTranslations } from "next-intl";

// Constants for image validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes
const ACCEPTED_IMAGE_TYPES = ["image/jpeg", "image/jpg", "image/png"];

// Create a function to validate image dimensions
const validateImageDimensions = (file: File, aspectRatio: string) => {
  return new Promise<boolean>((resolve) => {
    const img = document.createElement("img");
    img.onload = () => {
      const actualRatio = img.width / img.height;
      const [targetWidth, targetHeight] = aspectRatio.split("/").map(Number);
      const targetRatio = targetWidth / targetHeight;
      const tolerance = 0.15;
      resolve(Math.abs(actualRatio - targetRatio) <= tolerance);
    };
    img.onerror = () => resolve(false);
    img.src = URL.createObjectURL(file);
  });
};

// Create a function to get the image schema with translations
export const createImageSchema = (t: ReturnType<typeof useTranslations>, aspectRatio: string) =>
  z
    .instanceof(File, {
      message: t("validation.image.required"),
    })
    .superRefine(async (file, ctx) => {
      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t("validation.image.size", { size: 5 }),
        });
        return;
      }

      // Validate file type
      if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t("validation.image.type", { types: "JPG, PNG" }),
        });
        return;
      }

      // Validate aspect ratio
      const isValidRatio = await validateImageDimensions(file, aspectRatio);
      if (!isValidRatio) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t("validation.image.aspect_ratio", { ratio: aspectRatio }),
        });
      }
    })
    .or(z.string().url(t("validation.image.required")));

// Create specialized image schemas for each type
export const createCardImageSchema = (t: ReturnType<typeof useTranslations>) =>
  createImageSchema(t, "9/16");

export const createCoverImageSchema = (t: ReturnType<typeof useTranslations>) =>
  createImageSchema(t, "2/1");

export const createThumbnailImageSchema = (t: ReturnType<typeof useTranslations>) =>
  createImageSchema(t, "1/1");

// Create a function to get the validation schema with translations
export const createCourseValidationSchema = (t: ReturnType<typeof useTranslations>) => {
  return z.object({
    name: z
      .string({
        required_error: t("validation.name.min"),
        invalid_type_error: t("validation.name.min"),
      })
      .min(3, t("validation.name.min"))
      .max(100, t("validation.name.max")),
    description: z
      .string({
        required_error: t("validation.description.min"),
        invalid_type_error: t("validation.description.min"),
      })
      .min(10, t("validation.description.min"))
      .max(1000, t("validation.description.max")),
    theme: z.enum(COURSE_THEMES as unknown as [string, ...string[]], {
      invalid_type_error: t("validation.theme.min"),
      required_error: t("validation.theme.min"),
    }),
    card: createCardImageSchema(t),
    cover: createCoverImageSchema(t),
    thumbnail: createThumbnailImageSchema(t),
  });
};

export type CourseDetailsSchema = z.infer<ReturnType<typeof createCourseValidationSchema>>;
