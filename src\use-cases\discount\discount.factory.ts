import { Injectable } from '@nestjs/common';
import { Coupon } from 'chargebee-typescript/lib/resources/coupon';
import { CreateDiscountDTO } from '@dtos';
import { DiscountStatus } from '@enums';
import { Discount } from '@entities';

@Injectable()
export class DiscountFactoryService {
  generate(discountInfo: CreateDiscountDTO): Discount {
    const discount = new Discount();
    discount.name = discountInfo.name;
    discount.code = discountInfo.code;
    discount.status = discountInfo.status || DiscountStatus.Active;
    discount.invoiceName = discountInfo.invoiceName;
    discount.type = discountInfo.type;
    discount.currency = discountInfo.currency;
    discount.amount = discountInfo.amount;
    discount.applyOn = discountInfo.applyOn;
    discount.durationType = discountInfo.durationType;
    discount.durationPeriodUnit = discountInfo.durationPeriodUnit;
    discount.durationPeriodAmount = discountInfo.durationPeriodAmount;
    discount.maxRedemptions = discountInfo.maxRedemptions;
    if (discountInfo.validUntil) {
      discount.validUntil = new Date(discountInfo.validUntil);
    }
    return discount;
  }

  generateUpdateFromChargeBeeEvent(couponUpdate: Coupon): Discount {
    const discount = new Discount();
    discount.code = couponUpdate.id;
    switch (couponUpdate.status) {
      case 'active':
        discount.status = DiscountStatus.Active;
        break;
      case 'archived':
      case 'deleted':
        discount.status = DiscountStatus.Disabled;
        break;
      case 'expired':
        discount.status = DiscountStatus.Expired;
        break;
      default:
        discount.status = DiscountStatus.Disabled;
    }

    const { max_redemptions, redemptions } = couponUpdate;
    if (max_redemptions && typeof redemptions === 'number') {
      discount.maxRedemptions = max_redemptions - redemptions;
    }
    return discount;
  }
}
