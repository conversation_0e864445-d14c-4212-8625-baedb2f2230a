"use client";

import { useTranslations } from "next-intl";
import { useMemo } from "react";
import { differenceInDays } from "date-fns";
import { ICohortBase } from "@px-shared-account/hermes";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useCohortDetails } from "@/services/cohort";

interface CohortInformationProps {
  id: number;
}

export function CohortInformation({ id }: CohortInformationProps) {
  const t = useTranslations("cohorts");
  const { data: cohort } = useCohortDetails(id);

  // Compute duration and status
  const computedData = useMemo(() => {
    if (!cohort) return null;

    const now = new Date();
    const startDate = new Date(cohort.startDate);
    const endDate = new Date(cohort.endDate);
    const durationInDays = differenceInDays(endDate, startDate);

    let status = "upcoming";
    if (now > endDate) {
      status = "passed";
    } else if (now >= startDate && now <= endDate) {
      status = "running";
    }

    return {
      duration: durationInDays,
      status,
      startDate: startDate.toLocaleDateString(),
      endDate: endDate.toLocaleDateString(),
    };
  }, [cohort]);

  if (!cohort || !computedData) {
    return null;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>{t("information.details")}</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-6">
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">
                {t("information.start_date")}
              </div>
              <div className="text-sm">{computedData.startDate}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">
                {t("information.end_date")}
              </div>
              <div className="text-sm">{computedData.endDate}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">
                {t("information.duration")}
              </div>
              <div className="text-sm">
                {t("information.duration_days", { count: computedData.duration })}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">
                {t("information.status")}
              </div>
              <div className="text-sm">{t(`status.${computedData.status}`)}</div>
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-muted-foreground">
              {t("information.description")}
            </div>
            <div className="whitespace-pre-wrap text-sm">{cohort.description}</div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t("information.course")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">
                {t("information.course_name")}
              </div>
              <div className="text-sm">{cohort.course?.name || t("information.not_available")}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">
                {t("information.participants")}
              </div>
              <div className="text-sm">
                {t("information.participants_count", {
                  current: cohort.currentParticipants || 0,
                  max: cohort.maxParticipants || 0,
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
