import { Step } from "@/components/base/filter-bar-new/types";

export function createTriadFilterSteps(
  showSessionTypeFilter: boolean,
  t: (key: string) => string,
): Step[] {
  const steps: Step[] = [
    {
      type: "dateRange" as const,
      id: "period",
      label: t("view.filters.period"),
    },
    {
      type: "timeRange" as const,
      id: "timeWindow",
      label: t("view.filters.time_window"),
      placeholder: t("view.filters.time_window_placeholder"),
    },
  ];

  // Only add session type filter if user has both access types
  if (showSessionTypeFilter) {
    steps.push({
      type: "filter" as const,
      id: "sessionType",
      label: t("table.session_type"),
      placeholder: t("table.session_type"),
      options: [
        {
          value: "all",
          label: t("view.filters.all"),
        },
        {
          value: "PXS",
          label: "PXS",
        },
        {
          value: "PXL",
          label: "PXL",
        },
      ],
    });
  }

  steps.push(
    {
      type: "filter" as const,
      id: "status",
      label: t("view.filters.status"),
      placeholder: t("view.filters.status_placeholder"),
      options: [
        {
          value: "2slotsandmore",
          label: t("view.filters.status_options.2slotsandmore"),
        },
        {
          value: "1slot",
          label: t("view.filters.status_options.1slot"),
        },
        {
          value: "full",
          label: t("view.filters.status_options.full"),
        },
      ],
    },
    {
      type: "filter" as const,
      id: "registered",
      label: t("view.filters.registered"),
      placeholder: t("view.filters.registered_placeholder"),
      multiple: false,
      options: [
        {
          value: "registered",
          label: t("view.filters.registered_options.registered"),
        },
        {
          value: "not_registered",
          label: t("view.filters.registered_options.not_registered"),
        },
      ],
    },
  );

  return steps;
}
