"use client";

import { useEffect, useState } from "react";
import { FilterBar } from "@/components/base/filter-bar";
import { Step } from "@/components/base/filter-bar/types";
import mockData from "./mock-data";

/**
 * Configuration for the calendar filter system
 * Demonstrates a complex filter setup with dependencies and error simulation
 *
 * @remarks
 * This implementation includes extensive logging and error simulation for testing:
 * - Network errors for specific programs
 * - Server errors for specific courses
 * - Timeout simulation for specific modules
 * - Random error simulation (10% chance)
 * - Detailed logging of all filter operations
 *
 * Error triggers:
 * - Select "Programme Erreur" to simulate network error
 * - Select "Cours Erreur" to simulate server error
 * - Select "Module Erreur" to simulate timeout
 */
const calendarFilters = [
  {
    type: "filter",
    id: "programs",
    label: "Programmes",
    placeholder: "Filtrer par programme...",
    options: [
      { value: "bachelor", label: "Bachelor" },
      { value: "master", label: "Master" },
      { value: "mba", label: "MBA" },
      { value: "executive", label: "Executive" },
      { value: "error_program", label: "Programme Erreur" }, // Triggers network error
    ],
  },
  {
    type: "filter",
    id: "courses",
    label: "Cours",
    placeholder: "Filtrer par cours...",
    options: [], // Dynamically fetched based on program selection
    dependsOn: {
      programs: { required: true },
    },
  },
  {
    type: "filter",
    id: "modules",
    label: "Modules",
    placeholder: "Filtrer par module...",
    options: [], // Will be fetched based on program and optionally course
    dependsOn: {
      programs: { required: true },
      courses: { required: false },
    },
  },
  {
    type: "filter",
    id: "events",
    label: "Évènements",
    placeholder: "Filtrer par évènement...",
    options: [], // Will be fetched based on all previous selections
    dependsOn: {
      programs: { required: true },
      courses: { required: false },
      modules: { required: true },
    },
  },
  {
    type: "separator",
  },
  {
    type: "filter",
    id: "cohorts",
    label: "Cohortes",
    placeholder: "Filtrer par cohorte...",
    options: [
      { value: "all", label: "Toutes" },
      { value: "2024", label: "2024" },
      { value: "2025", label: "2025" },
      { value: "2026", label: "2026" },
      { value: "2027", label: "2027" },
    ],
  },
  {
    type: "separator",
  },
  {
    type: "filter",
    id: "tags",
    label: "Tags",
    placeholder: "Filtrer par tag...",
    options: [
      { value: "tag1", label: "Tag 1" },
      { value: "tag2", label: "Tag 2" },
      { value: "tag3", label: "Tag 3" },
    ],
  },
] as Step[];

export interface CalendarFiltersProps {
  className?: string;
  forceModal?: boolean;
  forceBar?: boolean;
}

/**
 * Calendar filter component with complex dependencies and error handling
 *
 * @component
 * @example
 * ```tsx
 * <CalendarFilters
 *   forceModal={false}
 *   forceBar={true}
 * />
 * ```
 *
 * @remarks
 * Testing Features:
 * - Simulated API delays (1 second)
 * - Error simulation for specific selections
 * - Random error simulation (10% chance)
 * - Console logging for all operations
 *
 * Dependencies:
 * - Courses depend on program selection
 * - Modules depend on program and optionally course
 * - Events depend on program and module
 */
export function CalendarFilters({
  className,
  forceModal: initialForceModal,
  forceBar: initialForceBar,
}: CalendarFiltersProps) {
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [forceModal, setForceModal] = useState(initialForceModal);
  const [forceBar, setForceBar] = useState(initialForceBar);
  const [isClient, setIsClient] = useState(false);

  /**
   * Fetches options for dependent filters with error simulation
   *
   * @param stepId - ID of the filter requesting options
   * @param dependencies - Current values of dependent filters
   * @returns Promise resolving to array of options
   *
   * @throws Error for simulated failure cases
   */
  const fetchOptions = async (stepId: string, dependencies: Record<string, string>) => {
    console.log(`🔍 [${stepId}] Fetching options with dependencies:`, dependencies);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Error simulation cases
    if (dependencies.programs === "error_program") {
      console.log(`❌ [${stepId}] Error: Network error for error_program`);
      throw new Error("Network error");
    }

    // Simulate server errors for specific courses
    if (dependencies.courses === "error_course") {
      console.log(`🔥 [${stepId}] Error: Server error for error_course`);
      throw new Error("Server error");
    }

    // Simulate timeout for specific modules
    if (dependencies.modules === "error_module") {
      console.log(`⏳ [${stepId}] Error: Timeout for error_module`);
      await new Promise((resolve) => setTimeout(resolve, 6000));
      throw new Error("Timeout error");
    }

    // Random error simulation (10% chance)
    if (Math.random() < 0.1) {
      console.log(`🎲 [${stepId}] Error: Random error occurred`);
      throw new Error("Random error occurred");
    }

    // Normal flow with mock data
    switch (stepId) {
      case "courses":
        // Simulate missing data error
        if (!mockData.courses[dependencies.programs as keyof typeof mockData.courses]) {
          console.log(`📭 [${stepId}] Error: Data not found for program ${dependencies.programs}`);
          throw new Error("Data not found");
        }
        console.log(
          `✅ [${stepId}] Success: Returning courses for program ${dependencies.programs}`,
        );
        return mockData.courses[dependencies.programs as keyof typeof mockData.courses];

      case "modules":
        if (
          dependencies.courses &&
          mockData.modules[dependencies.courses as keyof typeof mockData.modules]
        ) {
          console.log(
            `✅ [${stepId}] Success: Returning modules for course ${dependencies.courses}`,
          );
          return mockData.modules[dependencies.courses as keyof typeof mockData.modules];
        }
        console.log(`ℹ️ [${stepId}] Success: Returning general modules`);
        return [
          { value: "general1", label: "Module Général 1" },
          { value: "general2", label: "Module Général 2" },
        ];

      case "events":
        if (
          dependencies.modules &&
          mockData.events[dependencies.modules as keyof typeof mockData.events]
        ) {
          console.log(
            `✅ [${stepId}] Success: Returning events for module ${dependencies.modules}`,
          );
          return mockData.events[dependencies.modules as keyof typeof mockData.events];
        }
        console.log(`ℹ️ [${stepId}] Success: Returning general events`);
        return [
          { value: "orientation", label: "Séance d'orientation" },
          { value: "workshop", label: "Workshop" },
        ];

      default:
        console.log(`⚠️ [${stepId}] Warning: No case matched, returning empty array`);
        return [];
    }
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    isClient && (
      <FilterBar
        steps={calendarFilters}
        value={filters}
        onChange={(newFilters) => {
          console.log("Filters changed:", newFilters);
          setFilters(newFilters);
        }}
        onOptionsNeeded={fetchOptions}
        className={className}
        forceModal={forceModal}
        forceBar={forceBar}
      />
    )
  );
}
