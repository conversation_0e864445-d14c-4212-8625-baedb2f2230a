"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/base/button";
import { MediaGallery } from "./media-gallery";
import { IMediaItem } from "@px-shared-account/hermes";
import { GalleryThumbnails } from "lucide-react";

interface MediaGalleryModalProps {
  onSelect: (media: IMediaItem) => void;
  triggerText?: string;
  buttonVariant?: "default" | "outline" | "destructive" | "secondary" | "ghost" | "link";
}

export function MediaGalleryModal({
  onSelect,
  triggerText,
  buttonVariant = "outline",
}: MediaGalleryModalProps) {
  const t = useTranslations("whats-new.admin");
  const [open, setOpen] = useState(false);

  const handleSelect = (media: IMediaItem) => {
    onSelect(media);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={buttonVariant} type="button" className="gap-2">
          <GalleryThumbnails className="h-4 w-4" />
          {triggerText || t("browse-media")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[80vw] md:max-w-[800px]">
        <MediaGallery isModal isSelectable onSelect={handleSelect} onClose={() => setOpen(false)} />
      </DialogContent>
    </Dialog>
  );
}
