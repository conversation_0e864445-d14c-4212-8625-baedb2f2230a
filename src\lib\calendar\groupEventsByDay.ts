"use client";

import { format, parse } from "date-fns";

import { CalendarEvent } from "@/hooks/store/calendar";

type FormattedEvents = [string, CalendarEvent[]][];

/**
 * Groups a list of calendar events by the day they occur.
 *
 * @param events - An array of `CalendarEvent` objects to be grouped by day.
 * @returns A two-dimensional array where each element is a tuple containing:
 *   - A string representing the date in "EEEE d MMMM" format.
 *   - An array of `CalendarEvent` objects that occur on that day.
 */
export default function groupEventsByDay(events: CalendarEvent[]): FormattedEvents {
  // Reduce the events into a map, grouped by the day
  const grouped = events.reduce((acc: Map<string, CalendarEvent[]>, event) => {
    // Extract the "day" as a key from the `begin` date
    const day = format(new Date(event.begin), "EEEE d MMMM");

    // Initialize the array for this day if it doesn't exist
    if (!acc.has(day)) {
      acc.set(day, []);
    }

    // Add the event to the corresponding day's array
    acc.get(day)!.push(event);

    return acc;
  }, new Map<string, CalendarEvent[]>());

  // Convert the map to an array and sort the entries by the actual date
  const sorted = Array.from(grouped.entries()).sort(([dayA], [dayB]) => {
    // Parse the day strings back into Date objects for sorting
    const dateA = parse(dayA, "EEEE d MMMM", new Date());
    const dateB = parse(dayB, "EEEE d MMMM", new Date());
    return dateA.getTime() - dateB.getTime();
  });

  return sorted;
}
