import { getTranslations } from "next-intl/server";
import { TriadsView } from "@/components/(triads)/TriadsView";

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "navigation.practice" });

  return {
    title: t("title"),
  };
}

export default function TriadsPage() {
  return (
    <div className="space-y-4 py-4">
      <div className="space-y-4 border-b pb-4">
        <div className="flex flex-wrap items-center justify-between"></div>
      </div>
      <TriadsView showSessionTypeFilter={true} />
    </div>
  );
}
