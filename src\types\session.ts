export enum PXSSession {
  S1 = "S1",
  S2 = "S2",
  S3 = "S3",
  S4 = "S4",
  S5 = "S5",
  S6 = "S6",
  S7 = "S7",
  S8 = "S8",
}

// Type for raw Hubspot session values
export type HubspotSession = {
  value: string;
  label: string;
};

// Mapping configuration between Hubspot sessions and our internal enum
export const SESSION_MAPPING: Record<string, PXSSession> = {
  // S1
  "EDEC S1": PXSSession.S1,

  // S2
  "EDEC S2": PXSSession.S2,

  // S3
  "EDEC S3": PXSSession.S3,

  // S4
  "EDEC S4": PXSSession.S4,
  "EDEC S4 - C": PXSSession.S4,
  "EDEC S4 - 1Y": PXSSession.S4,

  // S5
  "EDEC S5": PXSSession.S5,
  "EDEC S5 - C": PXSSession.S5,
  "EDEC S5 - 1Y": PXSSession.S5,

  // S6
  "EDEC S6 - Certificate": PXSSession.S6,
  "EDEC S6 - 1Y": PXSSession.S6,

  // S7
  "PXS S7 - Principle": PXSSession.S7,
  "PXS S7 - Certificate": PXSSession.S7,
  "PXS S7V2 - Principle": PXSSession.S7,
  "PXS S7V2 - Certificate": PXSSession.S7,
  "PXS S7 - Certificate Y2": PXSSession.S7,

  // S8
  "PXS S8 - Certificate": PXSSession.S8,
  "PXS S8 - Principles": PXSSession.S8,
};

// Reverse mapping - for each internal enum value, we keep ALL possible Hubspot values
export const REVERSE_SESSION_MAPPING: Record<PXSSession, string[]> = {
  [PXSSession.S1]: ["EDEC S1"],
  [PXSSession.S2]: ["EDEC S2"],
  [PXSSession.S3]: ["EDEC S3"],
  [PXSSession.S4]: ["EDEC S4", "EDEC S4 - C", "EDEC S4 - 1Y"],
  [PXSSession.S5]: ["EDEC S5", "EDEC S5 - C", "EDEC S5 - 1Y"],
  [PXSSession.S6]: ["EDEC S6 - Certificate", "EDEC S6 - 1Y"],
  [PXSSession.S7]: [
    "PXS S7 - Principle",
    "PXS S7 - Certificate",
    "PXS S7V2 - Principle",
    "PXS S7V2 - Certificate",
    "PXS S7 - Certificate Y2",
  ],
  [PXSSession.S8]: ["PXS S8 - Certificate", "PXS S8 - Principles"],
};

// Utility functions
export function normalizeHubspotSession(hubspotSession: string): PXSSession | undefined {
  return SESSION_MAPPING[hubspotSession];
}

export function normalizeHubspotSessions(hubspotSessions: string[]): PXSSession[] {
  return hubspotSessions
    .map(normalizeHubspotSession)
    .filter((session): session is PXSSession => session !== undefined);
}

export function getUniqueNormalizedSessions(hubspotSessions: HubspotSession[]): PXSSession[] {
  const normalizedSessions = hubspotSessions
    .map((session) => normalizeHubspotSession(session.value))
    .filter((session): session is PXSSession => session !== undefined);

  // Remove duplicates using Array.from instead of spread operator
  return Array.from(new Set(normalizedSessions));
}

// Display utilities
export function getSessionLabel(session: PXSSession): string {
  return `S${session.slice(1)}`; // Removes the 'S' prefix and adds it back for consistent formatting
}

export function getSessionLabels(sessions: PXSSession[]): string[] {
  return sessions.map(getSessionLabel);
}

// Get all possible Hubspot values for a given internal session
export function getHubspotSessionValues(session: PXSSession): string[] {
  return REVERSE_SESSION_MAPPING[session];
}

// Get the primary (first) Hubspot value for a given internal session
export function getPrimaryHubspotSessionValue(session: PXSSession): string {
  return REVERSE_SESSION_MAPPING[session][0];
}
