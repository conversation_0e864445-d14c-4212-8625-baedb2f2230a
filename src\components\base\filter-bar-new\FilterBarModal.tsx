"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FilterBarProps, FilterStep, StepState, Step, DateRangeStep, TimeRangeStep } from "./types";
import { useFilterBar } from "./useFilterBar";
import { Combobox } from "@/components/base/combobox";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { TimeRangePicker } from "@/components/ui/time-range-picker";
import { DateRange } from "react-day-picker";
import { anton } from "@/lib/fonts/anton";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

/**
 * Props for the FilterBarModal component
 * Extends FilterBarProps with modal-specific properties
 */
interface FilterBarModalProps extends Omit<FilterBarProps, "onChange"> {
  /** Whether the modal is in a loading state */
  loading?: boolean;
  /** Callback fired when the Apply button is clicked */
  onApply?: () => void;
  /** Custom trigger element */
  triggerEl?: (activeFiltersCount: number | undefined) => React.ReactNode;
  /** Filter bar state from useFilterBar hook */
  values: Record<string, string | string[]>;
  stepsState: Record<string, StepState>;
  handleChange: (stepId: string, value: string | string[]) => void;
  isStepEnabled: (stepId: string) => boolean;
  clearAll: () => void;
}

/**
 * Mobile-friendly modal interface for the filter bar system
 *
 * @component
 * @example
 * ```tsx
 * <FilterBarModal
 *   steps={[
 *     { type: 'filter', id: 'program', label: 'Program', options: [] }
 *   ]}
 *   onChange={handleChange}
 *   onApply={handleApply}
 * />
 * ```
 *
 * @remarks
 * This component provides a modal interface for filter management on mobile devices.
 * Features include:
 * - Full-screen modal with scrollable content
 * - Group-based filter organization
 * - Apply/Reset functionality
 * - Active filter count indicator
 */
export function FilterBarModal({
  className,
  steps,
  loading = false,
  onApply,
  triggerEl,
  values = {},
  stepsState = {},
  handleChange,
  isStepEnabled,
  clearAll,
  ...props
}: FilterBarModalProps) {
  const t = useTranslations("triad.filter-bar");
  /** Controls the visibility of the modal */
  const [open, setOpen] = React.useState(false);
  /** Tracks the applying state when user clicks Apply */
  const [isApplying, setIsApplying] = React.useState(false);
  /** Tracks which filter dropdowns are open */
  const [openStates, setOpenStates] = React.useState<Record<string, boolean>>({});

  /** Count of currently active filters */
  const activeFiltersCount = Object.values(values || {}).filter(Boolean).length;

  /** Process steps into groups */
  const groups = React.useMemo(() => {
    const result: Step[][] = [];
    let currentGroup: Step[] = [];

    steps.forEach((step) => {
      if (step.type === "separator") {
        if (currentGroup.length > 0) {
          result.push(currentGroup);
          currentGroup = [];
        }
      } else if (step.type === "filter" || step.type === "dateRange" || step.type === "timeRange") {
        if (currentGroup.length === 0) {
          currentGroup = [];
        }
        currentGroup.push(step);
      }
    });

    if (currentGroup.length > 0) {
      result.push(currentGroup);
    }

    return result;
  }, [steps]);

  /**
   * Handles the apply action
   * Sets loading state, calls onApply callback, and closes the modal
   */
  const handleApply = async () => {
    setIsApplying(true);
    onApply?.();
    setIsApplying(false);
    setOpen(false);
  };

  /** Handle open state change for dropdowns */
  React.useEffect(() => {
    if (open) {
      setOpenStates({});
    }
  }, [open]);

  /** Handle dropdown open state change */
  const handleOpenChange = React.useCallback(
    (stepId: string, isOpen: boolean) => {
      if (!open) return;
      setOpenStates((prev) => ({
        ...prev,
        [stepId]: isOpen,
      }));
    },
    [open],
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {triggerEl ? (
          triggerEl(activeFiltersCount)
        ) : (
          <Button variant="outline" className="gap-2">
            {t("button")}
            {activeFiltersCount > 0 && (
              <span className="bg-primary text-primary-foreground flex h-6 w-6 items-center justify-center rounded-full text-xs">
                {activeFiltersCount}
              </span>
            )}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="flex max-h-[calc(100vh-2rem)] flex-col gap-4 overflow-hidden rounded-3xl border-none bg-[#111111] p-6 text-white sm:max-w-md">
        <DialogHeader className="space-y-2">
          <DialogTitle className={cn("text-center text-2xl text-white", anton.className)}>
            {t("title")}
          </DialogTitle>
        </DialogHeader>
        <div className="divide-muted-foreground/20 flex flex-1 flex-col divide-y overflow-y-auto py-4">
          {groups.map((group, groupIndex) => (
            <div key={groupIndex} className="flex flex-col gap-4 py-6 first:pt-0 last:pb-0">
              {group.map((step) => {
                if (step.type === "separator") return null;

                const stepState = stepsState[step.id] || { options: [] };

                if (step.type === "dateRange") {
                  return (
                    <div key={step.id} className="flex flex-col gap-2">
                      <label htmlFor={step.id} className="text-sm leading-none font-medium">
                        {step.label}
                      </label>
                      <div onClick={(e) => e.stopPropagation()}>
                        <DatePickerWithRange
                          key={step.id}
                          value={
                            values[step.id] ? JSON.parse(values[step.id].toString()) : undefined
                          }
                          onChange={(value) => {
                            handleChange(step.id, value ? JSON.stringify(value) : "");
                            handleOpenChange(step.id, false);
                          }}
                          placeholder={step.placeholder}
                          label={step.label}
                        />
                      </div>
                      {stepState.error && (
                        <p className="text-destructive text-sm">{stepState.error}</p>
                      )}
                    </div>
                  );
                }

                if (step.type === "timeRange") {
                  const timeRangeStep = step as TimeRangeStep;
                  return (
                    <div key={timeRangeStep.id} className="flex flex-col gap-2">
                      <label
                        htmlFor={timeRangeStep.id}
                        className="text-sm leading-none font-medium"
                      >
                        {timeRangeStep.label}
                      </label>
                      <div onClick={(e) => e.stopPropagation()}>
                        <TimeRangePicker
                          key={timeRangeStep.id}
                          value={
                            values[timeRangeStep.id]
                              ? JSON.parse(values[timeRangeStep.id].toString())
                              : undefined
                          }
                          onChange={(value) => {
                            handleChange(timeRangeStep.id, value ? JSON.stringify(value) : "");
                            handleOpenChange(timeRangeStep.id, false);
                          }}
                          placeholder={timeRangeStep.placeholder}
                        />
                      </div>
                      {stepState.error && (
                        <p className="text-destructive text-sm">{stepState.error}</p>
                      )}
                    </div>
                  );
                }

                if (step.type === "filter") {
                  const filterStep = step as FilterStep;
                  return (
                    <div key={filterStep.id} className="flex flex-col gap-2">
                      <label htmlFor={filterStep.id} className="text-sm leading-none font-medium">
                        {filterStep.label}
                      </label>
                      <Combobox
                        id={filterStep.id}
                        value={values[filterStep.id] || ""}
                        onChange={(value) => handleChange(filterStep.id, value)}
                        options={stepState.options}
                        label={filterStep.label}
                        placeholder={filterStep.placeholder}
                        disabled={!isStepEnabled(filterStep.id)}
                        error={stepState.error}
                        loading={stepState.isLoading}
                        open={openStates[filterStep.id]}
                        onOpenChange={(isOpen) => handleOpenChange(filterStep.id, isOpen)}
                        contentClassName="z-[60]"
                        multiple={filterStep.multiple}
                      />
                      {stepState.error && (
                        <p className="text-destructive text-sm">{stepState.error}</p>
                      )}
                    </div>
                  );
                }

                return null;
              })}
            </div>
          ))}
        </div>
        <DialogFooter className="grid grid-flow-col gap-4 pt-4">
          {activeFiltersCount > 0 && (
            <Button
              type="button"
              variant="outline"
              onClick={clearAll}
              disabled={loading || isApplying}
              className="h-12 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black"
            >
              {t("reset")}
            </Button>
          )}
          <Button
            type="submit"
            onClick={handleApply}
            disabled={activeFiltersCount === 0 || loading || isApplying}
            className="h-12 rounded-full text-white"
            variant="default"
          >
            {t("apply")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
