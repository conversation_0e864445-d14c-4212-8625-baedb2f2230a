"use client";

import { IRoleBase, IUpdateRole } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { Modal } from "@/components/base/modal";
import { Input } from "@/components/ui/input";
import { Form, FormItem, FormLabel } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useUpdateRole } from "@/services/role";
import PermissionsEditor from "./PermissionsEditor";

type EditRoleModalProps = {
  role: IRoleBase | null;
  onUpdate: () => void;
  setIsModalOpen: (isModalOpen: boolean) => void;
  isModalOpen: boolean;
};

export default function EditRoleModal({
  role,
  setIsModalOpen,
  onUpdate,
  isModalOpen,
}: EditRoleModalProps) {
  const t = useTranslations("gestion.users.roles");
  const commonT = useTranslations("common");
  const { toast } = useToast();
  const { update, isLoading } = useUpdateRole(role?.id);
  const form = useForm<IUpdateRole>({
    defaultValues: {
      name: role?.name,
      permissions: role?.permissions || [],
    },
  });
  const [name, setName] = useState(role?.name);
  const [permissions, setPermissions] = useState<IUpdateRole["permissions"]>(
    role?.permissions || [],
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (role?.id) {
      try {
        await update(role.id, { name, permissions });
        toast({
          title: t("updated"),
          description: t("updated-description"),
        });
        setIsModalOpen(false);
        onUpdate();
      } catch (error) {
        console.error("EditRoleModal-onSubmit", { error });
      }
    }
  };

  return (
    <Modal
      isOpen={isModalOpen}
      onClose={() => setIsModalOpen(false)}
      onOk={async () => {
        try {
          if (role?.id) {
            await update(role.id, { name, permissions });
            toast({
              title: t("updated"),
              description: t("updated-description"),
            });
            setIsModalOpen(false);
            onUpdate();
          }
        } catch (error) {
          console.error("EditRoleModal-onOk", { error });
        }
      }}
      title={`${t("edit_permissions")} - ${role?.name}`}
      okText={t("update")}
      cancelText={t("cancel")}
      showCloseButton={true}
      className="m-8 w-[1000px] max-w-full"
    >
      <div className="flex flex-col">
        {isLoading && <div className="text-center">{commonT("table.loading")}</div>}
        <Form {...form}>
          <form onSubmit={handleSubmit}>
            <FormItem>
              <FormLabel>{t("name")}</FormLabel>
              <Input value={name} onChange={(e) => setName(e.target.value)} />
            </FormItem>
            <FormItem>
              <FormLabel>{t("permissions")}</FormLabel>
              <PermissionsEditor
                data={role?.permissions || []}
                onChange={(permissions) => setPermissions(permissions)}
              />
            </FormItem>
          </form>
        </Form>
      </div>
    </Modal>
  );
}
