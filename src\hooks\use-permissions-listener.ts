"use client";

import { useEffect } from "react";
import usePermissionS<PERSON> from "./store/permission";

export function usePermissionsListener() {
  const permissions = usePermissionStore((s) => s.permissions);
  const setPermissions = usePermissionStore((s) => s.setPermissions);

  const connectToStream = (retryCount = 0, maxRetries = 5) => {
    if (retryCount >= maxRetries) {
      console.error("Max reconnection attempts reached");
      return null;
    }

    const eventSource = new EventSource("/api/stream");
    eventSource.addEventListener("message", (event) => {
      const newPermissions = JSON.parse(event.data);
      setPermissions(newPermissions);
    });

    eventSource.addEventListener("error", () => {
      eventSource.close();
      setTimeout(() => connectToStream(retryCount + 1, maxRetries), 1000);
    });

    // @ts-ignore
    eventSource.onerror = () => {
      setTimeout(() => connectToStream(retryCount + 1, maxRetries), 1000);
    };
    return eventSource;
  };

  useEffect(() => {
    const eventSource = connectToStream();
    return () => {
      eventSource?.close();
    };
  }, []);

  return permissions;
}
