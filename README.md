# paradox-api

## Summary

This repository contains a backend API service for a web application, built using the NestJS framework, a progressive Node.js framework for building efficient, reliable, and scalable server-side applications.

- The repository includes several use-cases, such as handling subscriptions, invoices, product offers, checkout pages, and bank transfers. It interacts with the Chargebee billing service and uses TypeORM for data manipulation over databases.

- The repository also includes Docker support for containerization, which makes the application easier to run in different environments. The Dockerfile includes instructions for building a Docker image of the application.

- The package.json file indicates various scripts for building the application, running it in different environments (development, production), formatting and linting the code, running tests, and handling database migrations.

- The repository also includes a documentation directory, suggesting that it uses Compodoc for generating documentation.

## Src folder structure

```.
├── app.module.ts
├── auth
│   ├── auth-header-api-key.strategy.ts
│   ├── auth.module.ts
│   ├── auth.service.ts
│   └── guards
├── cli.ts
├── config
│   ├── app.config.service.ts
│   └── database.config.ts
├── controllers
│   ├── auth.controller.ts
│   ├── bank-transfers.controller.ts
│   ├── checkoutPage.controller.ts
│   ├── controllers.module.ts
│   ├── credit-notes.controller.ts
│   ├── discounts.controller.ts
│   ├── invoices.controller.ts
│   ├── payment-plans.controller.ts
│   ├── payments.controller.ts
│   ├── products.controller.ts
│   ├── subscriptions.controller.ts
│   ├── transactions.controller.ts
│   └── webhooks.controller.ts
├── core
│   ├── abstracts
│   ├── dtos
│   ├── entities
│   ├── enums
│   └── types
├── frameworks
│   ├── billing-services
│   ├── database-services
│   ├── event-publishers
│   ├── hubspot
│   └── monitoring-services
├── helpers
│   └── global-helpers.ts
├── main.ts
├── services
│   └── database-services
└── use-cases
    ├── activePlans
    ├── bank-transfer
    ├── checkout-pages
    ├── client
    ├── credit-note
    ├── discount
    ├── invoice
    ├── order
    ├── order-items
    ├── payment
    ├── payment-plan
    ├── payment-source
    ├── product-family
    ├── product-line
    ├── product-offers
    ├── product-plan
    ├── product-plan-price
    ├── products
    ├── refund
    ├── subscription
    ├── transaction
    ├── user
    ├── warranty-section
    └── webhooks
```

## Links

- https://www.notion.so/paradoxnation/paradox-api-195f997d69f149fbaeaeac8fcb6d139c?pvs=4
- The basis used to structure the project - https://betterprogramming.pub/clean-node-js-architecture-with-nestjs-and-typescript-34b9398d790f
