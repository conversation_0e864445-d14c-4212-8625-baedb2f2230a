import { z } from "zod";

export const ProductLineBaseSchema = z.object({
    id: z.number().positive(),
    name: z.string().nonempty(),
    description: z.string(),
    status: z.string(),
    chargebeeId: z.string(),
    createdAt: z.date().optional(),
});
export type IProductLineBase = z.infer<typeof ProductLineBaseSchema>;

export const ProductFamilyBaseSchema = z.object({
    id: z.number().positive(),
    name: z.string().nonempty(),
    description: z.string(),
    status: z.string(),
    chargebeeId: z.string(),
    productLine: ProductLineBaseSchema,
    createdAt: z.date().optional(),
});
export type IProductFamilyBase = z.infer<typeof ProductFamilyBaseSchema>;

export const GetProductFamilyByIdSchema = z.object({
    id: z.number().positive(),
});
export type IGetProductFamilyById = z.infer<typeof GetProductFamilyByIdSchema>;

export const ListProductFamiliesSchema = z.object({
    search: z.string().optional(),
    status: z.string().optional(),
    productLineId: z.number().positive().optional(),
});
export type IListProductFamilies = z.infer<typeof ListProductFamiliesSchema>;

export const ListProductFamiliesResponseSchema = z.object({
    data: z.array(ProductFamilyBaseSchema),
    page: z.number(),
    total: z.number(),
});
export type IListProductFamiliesResponse = z.infer<typeof ListProductFamiliesResponseSchema>; 