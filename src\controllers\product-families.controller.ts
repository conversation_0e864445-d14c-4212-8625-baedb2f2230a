import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateProductFamilyDTO, UpdateProductFamilyDTO } from '@dtos';
import { ProductFamilyFactory, ProductFamilyUseCases } from '@useCases';
import { JwtPermissionsGuard } from '@auth';

@ApiTags('product-families')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('product-families')
export class ProductFamiliesController {
  constructor(
    private readonly productFamilyFactory: ProductFamilyFactory,
    private readonly productFamilyUseCases: ProductFamilyUseCases,
  ) {}

  @ApiOperation({ summary: 'Create a product family' })
  @Post('/')
  async createProductFamily(@Body() familyInfo: CreateProductFamilyDTO) {
    const productFamily = this.productFamilyFactory.generate(familyInfo);
    return this.productFamilyUseCases.create(productFamily);
  }

  @ApiOperation({ summary: 'List all product families' })
  @Get('/')
  async getProductFamilies(
    @Query('name') name?: string,
    @Query('status') status?: string,
    @Query('line') line?: string,
    @Query('limit') limit = 500,
    @Query('page') page = 1,
    @Query('order') orderBy?: 'DESC' | 'ASC',
  ) {
    return this.productFamilyUseCases.searchByNameStatusOrLine(
      name,
      status,
      Number(line),
      limit,
      page,
      orderBy,
    );
  }

  @ApiOperation({ summary: 'Get product family for the specified `id`' })
  @Get('/:id')
  async getProductFamily(@Param('id') id: string) {
    return this.productFamilyUseCases.getOne(Number(id));
  }

  @ApiOperation({ summary: 'Update a product family for the specified `id`' })
  @Patch('/:id')
  async updateProductFamily(
    @Param('id') id: string,
    @Body() updates: UpdateProductFamilyDTO,
  ) {
    return this.productFamilyUseCases.updateById(Number(id), updates);
  }
}
