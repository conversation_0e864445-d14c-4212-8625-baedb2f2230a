import { Module } from '@nestjs/common';
import { DataServicesModule } from '@services/database';
import { ProductPlanUseCasesModule } from '../product-plan/product-plan.use-cases.module';
import { CheckoutPageFactoryService, CheckoutPageUseCases } from '.';

@Module({
  imports: [DataServicesModule, ProductPlanUseCasesModule],
  providers: [CheckoutPageFactoryService, CheckoutPageUseCases],
  exports: [CheckoutPageFactoryService, CheckoutPageUseCases],
})
export class CheckoutPageUseCasesModule {}
