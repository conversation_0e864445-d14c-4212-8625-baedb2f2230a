"use client";

import { format, isAfter } from "date-fns";
import {
  Briefcase,
  CircleCheckBig,
  Dot,
  Files,
  GraduationCap,
  MessageCircle,
  MessageCircleMore,
  Newspaper,
  UserRound,
  Video,
  X,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

import { Button } from "../base/button";
import useCalendarStore, { CalendarEvent } from "@/hooks/store/calendar";
import isBeforeMidWeek from "@/lib/calendar/isBeforeMidWeek";
import { cn } from "@/lib/utils";
import UpdateSlotModal from "./UpdateSlotModal";

const iconColor = "#848585";

export default function CalendarDialog() {
  const t = useTranslations();
  const { events, layout, openDialog, setDialog } = useCalendarStore();

  const [evtData, setEvtData] = useState<CalendarEvent | null>(null);
  const [visible, setVisible] = useState(false);

  const hide = () => {
    setVisible(false);
    setDialog({ id: -1 });
  };

  useEffect(() => {
    if (openDialog.id !== -1) {
      setVisible(true);
    }
  }, [openDialog]);

  useEffect(() => {
    if (visible) {
      setEvtData(events.find((event) => event.id === openDialog.id) || null);
    }
  }, [visible, openDialog]);

  useEffect(() => {
    hide();
  }, [layout]);

  return (
    <div
      data-group="event-mark-dialog"
      className={cn("absolute z-50 flex items-center justify-center")}
      style={{
        marginTop: openDialog.top,
        marginLeft: `calc(100% / ${openDialog.len} * ${openDialog.sq})`,
      }}
    >
      {visible && (
        <div
          className={cn(
            `absolute z-50 w-[500px] rounded bg-[#f1f1f1] p-10 text-sm text-black shadow-md`,
            layout === "week" && isBeforeMidWeek(evtData?.begin as string)
              ? "left-[50px]"
              : "-left-[510px] right-0",
            layout === "day" ? "-bottom-[150px] left-0" : "",
          )}
        >
          <div className="flex flex-row items-center justify-between">
            <div className="text-2xl font-semibold"> {evtData?.title}</div>
            <div onClick={hide}>
              <X />
            </div>
          </div>
          <div className="flex flex-row items-center gap-2">
            {evtData?.begin && (
              <>
                <span>{format(new Date(evtData?.begin || ""), "EEEE d MMMM")}</span>
                <Dot />
                <span>
                  {format(new Date(evtData?.begin || ""), "HH:mm")} -{" "}
                  {format(new Date(evtData?.end || ""), "HH:mm")}
                </span>
              </>
            )}
          </div>
          <div>{evtData?.recurrence}</div>
          <div className="mt-6 flex flex-col gap-2">
            <div className="flex flex-row items-center gap-5">
              <Briefcase color={iconColor} />
              {evtData?.type && (
                <span className="flex flex-row items-center gap-2">
                  {t(`calendar.event-types.${evtData?.type}`)}
                  <Dot />
                  {evtData?.replay ? "Replay" : "Live"}
                </span>
              )}
            </div>
            <div className="flex flex-row items-start gap-5">
              <UserRound color={iconColor} />
              <div className="flex flex-col">
                <div className="flex flex-row items-center gap-2">
                  Mastercoach: {evtData?.mainSpeaker}
                  <MessageCircle className="cursor-pointer" strokeWidth={1} />
                </div>
                <div className="flex flex-row items-center gap-2">
                  Technicien: {evtData?.technicalSupport}
                  <MessageCircle className="cursor-pointer" strokeWidth={1} />
                </div>
                <div className="flex flex-row items-center gap-2">
                  Autre: {evtData?.otherSpeakers.join(", ")}
                  <MessageCircle className="cursor-pointer" strokeWidth={1} />
                </div>
              </div>
            </div>
            <div className="flex flex-row items-start gap-5">
              <CircleCheckBig color={iconColor} />
              <span>{evtData?.description}</span>
            </div>
            <div className="flex flex-row items-start gap-5">
              <Video color={iconColor} />
              <span className="flex flex-row items-center gap-2">
                Zoom link
                <Files
                  className="cursor-pointer"
                  strokeWidth={1}
                  onClick={() => {
                    navigator.clipboard.writeText("zoom-link");
                  }}
                />
              </span>
            </div>
            <div className="flex flex-row items-start gap-5">
              <GraduationCap color={iconColor} />
              <span className="flex flex-row items-center">
                {evtData?.cohort}
                <Dot />
                {evtData?.participantMin} - {evtData?.participantMax}
              </span>
            </div>
            <div className="flex flex-row items-start gap-5">
              <Newspaper color={iconColor} />
              <span className="flex flex-row items-center">
                {evtData?.program}
                <Dot />
                {evtData?.course}
                <Dot />
                {evtData?.module}
              </span>
            </div>
            <div className="flex flex-row items-start gap-5">
              <MessageCircleMore color={iconColor} />
              <span>{evtData?.comments}</span>
            </div>
          </div>
          <div className="mt-5 flex flex-row justify-end gap-2">
            <Button variant="ghost" onClick={hide}>
              {t("core.cancel")}
            </Button>
            {isAfter(new Date(evtData?.begin || ""), new Date()) && (
              <UpdateSlotModal type="edit" event={evtData}>
                <Button className="rounded-full">{t("core.edit")}</Button>
              </UpdateSlotModal>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
