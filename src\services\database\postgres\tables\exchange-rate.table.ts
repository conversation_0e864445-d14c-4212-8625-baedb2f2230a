import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ExchangeRateCurrencies } from '@enums';
import { ExchangeRate } from '@entities';

@Entity({
  name: 'exchangeRates',
})
export class ExchangeRateTable implements ExchangeRate {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'enum',
    enum: ExchangeRateCurrencies,
    enumName: 'exchangeRateCurrencies',
  })
  from: ExchangeRateCurrencies;

  @Column({
    type: 'enum',
    enum: ExchangeRateCurrencies,
    enumName: 'exchangeRateCurrencies',
    default: ExchangeRateCurrencies.AED,
  })
  to: ExchangeRateCurrencies;

  @Column({ type: 'decimal', precision: 10, scale: 6 })
  rate: string;

  @Column({ type: 'bigint' })
  date: number;

  @Column({ type: 'jsonb' })
  metaData?: any;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deletedAt?: Date;
}
