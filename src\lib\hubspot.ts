"use server";

export const getPXSEDECPropertyOptions = async () => {
  const response = await fetch(`https://api.hubapi.com/crm/v3/properties/contacts/session_edec`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${process.env.HUBSPOT_PRIVATE_APP_TOKEN}`,
    },
  });

  if (!response.ok) {
    console.error(
      "Caught error while getting session_edec property options in HubSpot",
      await response.text(),
    );
    return [];
  }

  const data = await response.json();
  return data.options && data.options.length > 0
    ? data.options.filter(
        (option: { value: string; hidden: boolean }) => !option.hidden && option.value !== "N/A",
      )
    : [];
};
