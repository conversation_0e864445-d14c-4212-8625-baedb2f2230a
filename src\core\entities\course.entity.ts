import { CourseStatus } from '@px-shared-account/hermes';
import { BaseEntity } from './base.entity';
import { UserEntity } from './user.entity';
import { CohortEntity } from './cohort.entity';
import { ProductOffer } from './product-offer.entity';
import { ProductFamily } from './product-family.entity';

export class CourseEntity extends BaseEntity {
  name: string;
  description: string;
  bannerImage: string;
  cardImage: string;
  thumbnail: string;
  status: CourseStatus;
  managers: UserEntity[];
  cohorts: CohortEntity[];
  offers: ProductOffer[];
  lmsId: string;
  productFamily: ProductFamily;
  ctaLink: string;
}
