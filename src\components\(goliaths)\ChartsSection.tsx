"use client";

import { motion } from "framer-motion";
import { useMemo } from "react";
import { CardContent } from "../ui/card";
import { chartData3, itemVariants } from "@/lib/goliaths";
import { chartData1 } from "@/lib/goliaths";
import AreaChart from "./AreaChart";
import { chartData2 } from "@/lib/goliaths";
import { useTranslations } from "next-intl";

type ChartsSectionProps = {
  onOpenModal: () => void;
};

export default function ChartsSection({ onOpenModal }: ChartsSectionProps) {
  const t = useTranslations("goliaths");

  return useMemo(
    () => (
      <div className="jusw-10/12 flex h-full w-full flex-col items-center">
        <CardContent className="mx-auto mt-3 flex w-full flex-col items-center justify-center gap-4">
          <motion.div variants={itemVariants} className="w-full">
            <AreaChart
              color="#8D2146"
              data={chartData1}
              title={t("radial-chart.base-protective")}
            />
          </motion.div>
          <motion.div variants={itemVariants} className="w-full">
            <AreaChart
              color="#5865F2"
              blurred={true}
              data={chartData2}
              onButtonClick={onOpenModal}
              title={t("radial-chart.controlled-growth")}
            />
          </motion.div>
          <motion.div variants={itemVariants} className="w-full">
            <AreaChart
              color="#C6712B"
              blurred={true}
              data={chartData3}
              onButtonClick={onOpenModal}
              title={t("radial-chart.ambitious-returns")}
            />
          </motion.div>
        </CardContent>
      </div>
    ),
    [],
  );
}
