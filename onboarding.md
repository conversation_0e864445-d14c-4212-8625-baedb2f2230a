# Sirius Project Onboarding Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Project Overview](#project-overview)
3. [Technology Stack](#technology-stack)
4. [Project Architecture](#project-architecture)
5. [Key Features](#key-features)
6. [Development Environment Setup](#development-environment-setup)
7. [Project Structure](#project-structure)
8. [Authentication & Authorization](#authentication--authorization)
9. [Internationalization (i18n)](#internationalization-i18n)
10. [UI Components & Design System](#ui-components--design-system)
11. [State Management](#state-management)
12. [API Services Architecture](#api-services-architecture)
13. [Deployment](#deployment)
14. [Contributing Guidelines](#contributing-guidelines)
15. [Useful Resources](#useful-resources)

## Introduction

Welcome to the Sirius project! This document provides a comprehensive overview of the project, its architecture, and guidelines for development. It's designed to help you get up to speed quickly and understand the project's structure and capabilities.

## Project Overview

Sirius is a Next.js-based web application for managing educational content, courses, cohorts, and user management. It's built with modern web technologies and follows best practices for scalability, maintainability, and user experience.

The application serves as a platform for:

- Course management and creation
- Cohort organization and student management
- User management with role-based access control
- Calendar and event scheduling
- Messaging and communication
- Objectives tracking

## Technology Stack

The project uses the following technologies:

### Core Technologies

- **Frontend Framework**: [Next.js 15.1.3](https://nextjs.org/) (React 19.0.0)
- **Language**: TypeScript
- **Styling**: Tailwind CSS with CSS variables for theming
- **State Management**: Zustand for global state
- **Form Handling**: React Hook Form with Zod validation
- **Authentication**: Clerk
- **Internationalization**: next-intl

### UI Libraries

- **Component Library**: Custom components built on top of Radix UI primitives
- **Icons**: Lucide React
- **Data Tables**: TanStack Table (formerly React Table)
- **Charts**: Recharts
- **Date Handling**: date-fns
- **Notifications**: Sonner

### Development Tools

- **Package Manager**: npm
- **Linting**: ESLint with Next.js configuration
- **Formatting**: Prettier
- **Git Hooks**: Husky

## Project Architecture

The Sirius project follows a modular architecture with clear separation of concerns:

### App Router Structure

The project uses Next.js App Router with the following structure:

- `/src/app/[lang]/(auth)/` - Protected routes requiring authentication
- `/src/app/[lang]/sign-in` and `/sign-up` - Authentication routes
- `/src/app/api` - API routes for server-side operations

### Component Organization

- **UI Components**: Reusable UI primitives in `/src/components/ui`
- **Base Components**: Common components with business logic in `/src/components/base`
- **Composite Components**: Complex components combining multiple base components
- **Templates**: Page layouts and structural components
- **Feature Components**: Components specific to a feature area (courses, cohorts, etc.)

### Service Layer

- **API Services**: Organized by domain (course, cohort, user, role)
- **Server vs. Client**: Clear separation between server-side and client-side API calls

### State Management

- **Global State**: Zustand stores for app-wide state
- **Local State**: React's useState and useReducer for component-specific state
- **Form State**: React Hook Form for form state management

## Key Features

### Course Management

- Create, edit, and manage courses
- Organize courses into modules and lessons
- Track course status (draft, published, archived)
- Associate courses with cohorts and offers

### Cohort Management

- Create and manage student cohorts
- Track cohort progress and status
- Manage student enrollment and participation
- Set cohort capacity and date ranges

### User Management

- Role-based access control
- User permissions management
- User attributions and settings

### Calendar & Events

- Schedule and manage events
- Different event types (mentoring, masterclass, etc.)
- Recurrence and timezone support

### Messaging

- Real-time chat functionality
- Conversation management
- Attachment support

### Internationalization

- Multi-language support (English and French)
- Localized routes and content
- Language switching

## Development Environment Setup

### Prerequisites

- Node.js (v18 or later recommended)
- npm (v8 or later)
- Git

### Installation Steps

1. Clone the repository:

   ```bash
   git clone https://github.com/davidlaroche/sirius.git
   cd sirius
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:
   Create a `.env.local` file in the root directory with the following variables:

   ```
   NEXT_PUBLIC_API_URL=your_api_url
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
   CLERK_SECRET_KEY=your_clerk_secret_key
   KV_URL=your_redis_url
   ```

4. Run the development server:

   ```bash
   npm run dev
   ```

5. Open [http://localhost:3005](http://localhost:3005) in your browser to see the application.

### Working with Hermes

The project uses a shared library called Hermes for types and interfaces. During development, the library is linked locally:

```bash
# The postinstall script will automatically link the local hermes repository
# Make sure you have the hermes repository cloned at the same level as this project
```

## Project Structure

```
sirius/
├── public/                  # Static assets and localization files
│   ├── locales/             # Translation files
│   │   ├── en/              # English translations
│   │   └── fr/              # French translations
├── scripts/                 # Build and setup scripts
├── src/                     # Source code
│   ├── app/                 # Next.js App Router pages
│   │   ├── [lang]/          # Language-specific routes
│   │   │   ├── (auth)/      # Protected routes
│   │   │   ├── sign-in/     # Authentication routes
│   │   │   └── sign-up/     # Authentication routes
│   │   ├── api/             # API routes
│   │   ├── globals.css      # Global styles
│   │   └── layout.tsx       # Root layout
│   ├── components/          # React components
│   │   ├── base/            # Base components
│   │   ├── composite/       # Composite components
│   │   ├── layout/          # Layout components
│   │   ├── templates/       # Page templates
│   │   └── ui/              # UI primitives
│   ├── hooks/               # Custom React hooks
│   │   ├── store/           # Zustand store hooks
│   │   └── use-*.ts         # Feature-specific hooks
│   ├── i18n/                # Internationalization config
│   ├── lib/                 # Utility functions
│   │   ├── server/          # Server-only utilities
│   │   └── utils.ts         # Common utilities
│   ├── services/            # API services
│   │   ├── cohort/          # Cohort-related services
│   │   ├── course/          # Course-related services
│   │   ├── role/            # Role-related services
│   │   └── user/            # User-related services
│   └── types/               # TypeScript type definitions
├── .env.local               # Local environment variables (not in repo)
├── .eslintrc.json           # ESLint configuration
├── .gitignore               # Git ignore file
├── .prettierrc.json         # Prettier configuration
├── components.json          # UI component configuration
├── next.config.ts           # Next.js configuration
├── package.json             # npm package configuration
├── postcss.config.mjs       # PostCSS configuration
├── tailwind.config.ts       # Tailwind CSS configuration
└── tsconfig.json            # TypeScript configuration
```

## Authentication & Authorization

### Authentication with Clerk

The project uses Clerk for authentication, providing:

- User sign-up and sign-in
- Session management
- Profile management
- Multi-language support

Key files:

- `/src/app/[lang]/sign-in/[[...sign-in]]/page.tsx` - Sign-in page
- `/src/app/[lang]/sign-up/[[...sign-up]]/page.tsx` - Sign-up page
- `/src/middleware.ts` - Clerk middleware integration

### Authorization with Role-Based Access Control

The application implements role-based access control (RBAC) with:

- Role definitions from Hermes library
- Permission checks at component and page levels
- Server-side role verification

Key files:

- `/src/lib/permissions.ts` - Permission utilities
- `/src/lib/server/role.ts` - Server-side role checking
- `/src/hooks/use-check-role.ts` - Client-side role checking

Example of role-based access control:

```typescript
// Server-side role check
const isAdmin = await checkRole(Roles.ADMIN);
if (!isAdmin) {
  redirect("/dashboard");
}

// Client-side permission check
const { hasAccess } = useCanAccess(CoursePermissions.CREATE_COURSE);
if (hasAccess) {
  // Show create course button
}
```

## Internationalization (i18n)

The project supports multiple languages (English and French) using next-intl:

### Key Features

- Localized routes
- Translation files for UI text
- Language switching
- Date and number formatting

### Implementation

- Translation files in `/public/locales/{locale}/common.json`
- Route configuration in `/src/i18n/routing.ts`
- Language switcher component in `/src/components/base/LocaleSwitcher`

### Usage Example

```typescript
// Server component
import { getTranslations } from "next-intl/server";

export async function generateMetadata({ params }: { params: { locale: string } }) {
  const t = await getTranslations({ locale, namespace: "courses" });
  return {
    title: t("title"),
  };
}

// Client component
import { useTranslations } from "next-intl";

export function Component() {
  const t = useTranslations("courses");
  return <h1>{t("title")}</h1>;
}
```

## UI Components & Design System

The project uses a custom design system built on top of Radix UI primitives and styled with Tailwind CSS:

### Design Tokens

- Colors defined as CSS variables in `/src/app/globals.css`
- Typography and spacing scales in Tailwind configuration
- Responsive breakpoints defined in `/tailwind.config.ts`

### Component Library

- UI primitives in `/src/components/ui/`
- Shadcn UI-inspired component architecture
- Consistent styling and behavior

### Key Components

- **Button**: Primary interaction element with multiple variants
- **Dialog/Modal**: For displaying content in an overlay
- **Form components**: Input, Select, Checkbox, etc.
- **DataTable**: For displaying and interacting with tabular data
- **Sidebar**: Navigation component with collapsible sections
- **Toaster**: For displaying notifications

### Theme Support

- Light and dark mode support
- Theme switching via ThemeProvider

## State Management

The project uses Zustand for global state management:

### Stores

- **User Store**: User information and preferences
- **Permission Store**: User permissions
- **API Store**: API request state

### Usage Example

```typescript
// Define store
const useUserStore = create<UserStore>((set) => ({
  language: "",
  role: "",
  setLanguage: (language) => set({ language }),
  setRole: (role) => set({ role }),
}));

// Use store in component
const { language, setLanguage } = useUserStore();
```

## API Services Architecture

The project implements a comprehensive API services layer that follows a domain-driven design approach. Each domain (courses, cohorts, users, roles) has its own service module with a consistent structure.

### Service Module Structure

Each service module (e.g., `/src/services/course/`) follows a consistent pattern:

```
services/
├── course/                  # Domain-specific service
│   ├── core.ts              # Core types and endpoint definitions
│   ├── client.ts            # Client-side hooks
│   ├── server.ts            # Server-side functions
│   └── index.ts             # Public exports
```

### Core Module (`core.ts`)

The core module defines:

1. **API Endpoint Definitions**: URL patterns and HTTP methods
2. **Type Definitions**: TypeScript interfaces for request/response data
3. **Parameter Types**: Types for filtering, pagination, and sorting

Example from `course/core.ts`:

```typescript
// API endpoint definitions
export const COURSE_API_ENDPOINTS = {
  list: (params: ListCoursesParams = {}) => {
    const { search, status, page = 1, limit = 10 } = params;
    const queryParams = new URLSearchParams();

    if (search) queryParams.append("search", search);
    if (status) queryParams.append("status", status);
    queryParams.append("page", String(page));
    queryParams.append("limit", String(limit));

    return {
      url: `/course/list?${queryParams.toString()}`,
      method: "GET",
    };
  },

  getById: (id: string) => ({
    url: `/course/${id}`,
    method: "GET",
  }),

  create: () => ({
    url: `/course`,
    method: "POST",
  }),

  update: (id: string) => ({
    url: `/course/${id}`,
    method: "PATCH",
  }),

  publish: (id: string) => ({
    url: `/course/${id}/publish`,
    method: "POST",
  }),

  archive: (id: string) => ({
    url: `/course/${id}/archive`,
    method: "POST",
  }),

  duplicate: (id: string) => ({
    url: `/course/${id}/duplicate`,
    method: "POST",
  }),
};

// Type definitions for API responses
export type CourseApiTypes = {
  list: IListCoursesResponse;
  getById: ICourseDetails;
  create: ICourseBase;
  update: UpdateResultWithItemInfo<ICourseBase>;
  publish: PXActionResult;
  archive: PXActionResult;
  duplicate: ICourseBase;
};

// Type definitions for API payloads
export type CourseApiPayloads = {
  create: ICreateCourse;
  update: IUpdateCourse;
};

// Parameter types for list endpoints
export interface ListCoursesParams {
  search?: string;
  status?: CourseStatus;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}
```

### Client-Side Module (`client.ts`)

The client module provides React hooks for interacting with the API:

1. **Data Fetching Hooks**: For retrieving data (e.g., `useCourseList`)
2. **Mutation Hooks**: For creating, updating, or deleting data (e.g., `useCreateCourse`)
3. **Specialized Action Hooks**: For specific domain actions (e.g., `usePublishCourse`)

Each hook:

- Handles loading and error states
- Provides optimistic updates where appropriate
- Manages cache invalidation and revalidation
- Includes toast notifications for success/error feedback

Example from `course/client.ts`:

```typescript
"use client";

import { useCallback, useMemo } from "react";
import { useApi } from "@/hooks/store/use-api";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { COURSE_API_ENDPOINTS, CourseApiTypes, ListCoursesParams, CourseApiPayloads } from "./core";

/**
 * Hook for fetching and managing course lists
 * @param params Filter and pagination parameters
 * @returns SWR response with course list data
 */
export function useCourseList(params: ListCoursesParams = {}) {
  const apiConfig = useMemo(() => {
    const { url } = COURSE_API_ENDPOINTS.list(params);
    return url;
  }, [params]);

  return useApi<CourseApiTypes["list"]>(apiConfig);
}

/**
 * Hook for creating a new course
 * @returns Object with create function and loading state
 */
export function useCreateCourse() {
  const { toast } = useToast();
  const router = useRouter();

  const { url, method } = COURSE_API_ENDPOINTS.create();
  const { trigger, isMutating } = useApi<CourseApiTypes["create"]>(url, { method });

  const createCourse = useCallback(
    async (data: CourseApiPayloads["create"]) => {
      try {
        const result = await trigger(data);
        toast({
          title: "Course created",
          description: "The course has been created successfully",
        });
        router.refresh();
        return result;
      } catch (error) {
        toast({
          title: "Error creating course",
          description: error.message || "An error occurred while creating the course",
          variant: "destructive",
        });
        throw error;
      }
    },
    [trigger, toast, router],
  );

  return { createCourse, isLoading: isMutating };
}

/**
 * Hook for publishing a course
 * @param id Course ID to publish
 * @returns Object with publish function and loading state
 */
export function usePublishCourse(id: string) {
  const { toast } = useToast();

  const { url, method } = COURSE_API_ENDPOINTS.publish(id);
  const { trigger, isMutating } = useApi<CourseApiTypes["publish"]>(url, { method });

  const publishCourse = useCallback(async () => {
    try {
      const result = await trigger();
      toast({
        title: "Course published",
        description: "The course has been published successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error publishing course",
        description: error.message || "An error occurred while publishing the course",
        variant: "destructive",
      });
      throw error;
    }
  }, [trigger, toast]);

  return { publishCourse, isLoading: isMutating };
}
```

### Server-Side Module (`server.ts`)

The server module provides functions for server components:

1. **Direct API Functions**: For use in server components and API routes
2. **Authentication Handling**: Automatically retrieves and includes auth tokens
3. **Error Handling**: Proper error handling for server-side requests

Example from `course/server.ts`:

```typescript
import { auth } from "@clerk/nextjs/server";
import serverFetcher from "@/lib/server/server-fetcher";
import { COURSE_API_ENDPOINTS, CourseApiTypes, ListCoursesParams, CourseApiPayloads } from "./core";

/**
 * Creates a server-side token getter for the fetcher
 * @returns Promise with authentication token or null
 */
const getServerToken = async () => {
  const session = await auth();
  return session?.getToken() || null;
};

// Initialize the fetcher with server-side token getter
const apiFetcher = serverFetcher(getServerToken);

/**
 * Server-side function for fetching course list
 * @param params Filter and pagination parameters
 * @returns Promise with course list data
 */
export async function getCourseList(
  params: ListCoursesParams = {},
): Promise<CourseApiTypes["list"]> {
  const { url } = COURSE_API_ENDPOINTS.list(params);
  return apiFetcher(url);
}

/**
 * Server-side function for fetching a specific course by ID
 * @param id Course ID to fetch
 * @returns Promise with course data
 */
export async function getCourseById(id: string): Promise<CourseApiTypes["getById"]> {
  const { url } = COURSE_API_ENDPOINTS.getById(id);
  return apiFetcher(url);
}

/**
 * Alias for getCourseById to maintain consistency with other services
 * @param id Course ID to fetch
 * @returns Promise with course data
 */
export const getCourseDetails = getCourseById;

/**
 * Server-side function for creating a new course
 * @param data Course creation data
 * @returns Promise with created course data
 */
export async function createCourse(
  data: CourseApiPayloads["create"],
): Promise<CourseApiTypes["create"]> {
  const { url, method } = COURSE_API_ENDPOINTS.create();
  return apiFetcher(url, {
    method,
    body: data,
  });
}
```

### Index Module (`index.ts`)

The index module exports the public API of the service:

```typescript
// Export types from core
export * from "./core";

// Do NOT export server-side functions directly from the main index
// This would cause "server-only" errors when imported in client components

// Export client-side API hooks
export {
  useCourseList,
  useCourseDetails,
  useCreateCourse,
  useUpdateCourse,
  usePublishCourse,
  useArchiveCourse,
  useDuplicateCourse,
} from "./client";

// Re-export specific types that might be needed
export type { ListCoursesParams, CourseApiTypes, CourseApiPayloads } from "./core";
```

### API Fetcher Utilities

The API services are built on top of two core utilities:

1. **Client-Side Fetcher** (`/src/lib/api-fetcher.ts`):

   - Uses SWR for data fetching and caching
   - Handles authentication token retrieval via Clerk
   - Provides consistent error handling

2. **Server-Side Fetcher** (`/src/lib/server/server-fetcher.ts`):
   - Similar interface to client-side fetcher
   - Designed for server components
   - Uses server-side authentication

### Integration with Components

Components use these services through custom hooks:

```tsx
"use client";

import { useCourseList } from "@/services/course";

export function CourseList() {
  const { data, isLoading, error } = useCourseList({
    page: 1,
    limit: 10,
    status: "published",
  });

  if (isLoading) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;

  return (
    <ul>
      {data.items.map((course) => (
        <li key={course.id}>{course.name}</li>
      ))}
    </ul>
  );
}
```

Server components use the server-side functions:

```tsx
import { getCourseDetails } from "@/services/course/server";

export default async function CoursePage({ params }: { params: { id: string } }) {
  const course = await getCourseDetails(params.id);

  return (
    <div>
      <h1>{course.name}</h1>
      <p>{course.description}</p>
    </div>
  );
}
```

## Deployment

The project is configured for deployment on Vercel:

### Environment Variables

- Production environment variables set in Vercel dashboard
- Development variables in `.env.local`

### Build Process

- Next.js build process optimized for production
- Static and server components properly separated
- API routes deployed as serverless functions

### Continuous Integration

- GitHub Actions for CI/CD
- Automated testing and linting
- Preview deployments for pull requests

## Contributing Guidelines

### Code Style

- Follow the ESLint and Prettier configurations
- Use TypeScript for type safety
- Follow the component structure and naming conventions

### Git Workflow

- Create feature branches from `main`
- Use conventional commit messages
- Submit pull requests for review
- Ensure tests pass before merging

### Documentation

- Document new components and features
- Update this documentation when necessary
- Add comments for complex logic

## Useful Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Clerk Documentation](https://clerk.com/docs)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [next-intl Documentation](https://next-intl-docs.vercel.app/)
- [Radix UI Documentation](https://www.radix-ui.com/docs/primitives/overview/introduction)
