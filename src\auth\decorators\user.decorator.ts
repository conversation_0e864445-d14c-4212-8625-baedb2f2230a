import { createParamDecorator, ExecutionContext } from '@nestjs/common';

/**
 * Decorator that returns the full user entity attached to the request by the CurrentUserInterceptor.
 *
 * Example usage:
 * ```
 * @Get()
 * async someEndpoint(@CurrentUser() user: UserEntity) {
 *   // Use user.id, user.email, etc.
 * }
 * ```
 */
export const CurrentUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.fullUser || null;
  },
);
