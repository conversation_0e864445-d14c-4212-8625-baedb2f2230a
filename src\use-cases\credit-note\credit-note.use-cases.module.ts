import { Module } from '@nestjs/common';
import { CreditNoteFactoryService, CreditNoteUseCases } from '.';
import { ChargebeeBillingModule } from '@services/billing';
import { DataServicesModule } from '@services/database';

@Module({
  imports: [DataServicesModule, ChargebeeBillingModule],
  providers: [CreditNoteFactoryService, CreditNoteUseCases],
  exports: [CreditNoteFactoryService, CreditNoteUseCases],
})
export class CreditNoteUseCasesModule {}
