import { Injectable, NotFoundException } from '@nestjs/common';
import { CourseFactory } from './course.factory';
import { CourseTable } from '@tables';
import { CreateCourseDto, UpdateCourseDto } from '@dtos';
import { CourseEntity, CustomerAccess } from '@entities';
import {
  CourseStatus,
  IListCoursesResponse,
  PXActionResult,
  UpdateResultWithItemInfo,
  IGetStudentsForCourseResponse,
  IListCourseCatalogResponse,
  ICourseCatalogItem,
} from '@px-shared-account/hermes';
import { FindOptionsWhere } from 'typeorm';
import { IDataServices } from '@abstracts';
import { ListCourseCatalogDto } from '@dtos';
import { UserEntity } from '@entities';
import { AccessTarget, CustomerAccessType } from '@enums';
import { CourseAccess } from '@types';

@Injectable()
export class CourseUseCases {
  constructor(
    private courseFactory: CourseFactory,
    private readonly databaseService: IDataServices,
  ) { }

  /**
   * Creates a new course
   * @param courseInfo - Course information
   * @returns {Promise<CourseEntity>} - A promise that resolves with the created course
   */
  async create(courseInfo: CreateCourseDto): Promise<CourseEntity> {
    const newCourse = this.courseFactory.generate(courseInfo);
    return this.databaseService.course.create(newCourse);
  }

  /**
   * Gets a course by id
   * @param id - The id of the course
   * @returns {Promise<CourseEntity>} - A promise that resolves with the course
   */
  async getById(id: number): Promise<CourseEntity> {
    const courseTableName = this.databaseService.course.getTableName();
    const courseQueryBuilder = this.databaseService.course
      .getRepository()
      .createQueryBuilder(courseTableName);

    const result = await courseQueryBuilder
      .where(`${courseTableName}.id = :id`, {
        id,
      })
      .leftJoin(`${courseTableName}.offers`, 'offers')
      .addSelect([
        'offers.id',
        'offers.createdAt',
        'offers.updatedAt',
        'offers.status',
        'offers.image',
        'offers.name',
      ])
      .getOne();
    return result;
  }

  /**
   * Updates a course
   * @param id - The id of the course
   * @param courseInfo - The updated course information
   * @returns {Promise<UpdateResultWithItemInfo<CourseEntity>>} - A promise that resolves with the updated course
   */
  async update(
    id: number,
    courseInfo: UpdateCourseDto,
  ): Promise<UpdateResultWithItemInfo<CourseEntity>> {
    const courseToUpdate = await this.databaseService.course.getOneBy({ id });
    if (!courseToUpdate) {
      throw new NotFoundException(`Course with ID ${id} not found.`);
    }

    // Get the processed updates from the factory.
    // courseUpdates is Partial<CourseEntity>, with 'offers' and 'managers' correctly formatted.
    const courseUpdates = this.courseFactory.generateCourseUpdates(courseInfo);

    // Apply all updates from courseUpdates to courseToUpdate.
    // For relational arrays like 'offers' or 'managers', this means the entire array
    // on courseToUpdate will be replaced if the key exists in courseUpdates.
    // This is the correct way for TypeORM's save() to handle relation updates.
    for (const key in courseUpdates) {
      if (Object.prototype.hasOwnProperty.call(courseUpdates, key)) {
        courseToUpdate[key] = courseUpdates[key];
      }
    }

    // Save the entity. The 'create' method in PostgresGenericRepository uses 'repository.save()',
    // which correctly handles updates to relations, including many-to-many.
    const updatedCourse = await this.databaseService.course.create(
      courseToUpdate,
    );

    return {
      success: !!updatedCourse, // True if updatedCourse is not null/undefined
      updatedItem: updatedCourse,
    };
  }

  /**
   * Gets all courses
   * @param query Query to search for
   * @param status Course status to filter by
   * @param limit Number of courses to return
   * @param page Page number
   * @returns A list of courses
   */
  async list(
    query?: string,
    status?: CourseStatus,
    limit = 10,
    page = 1,
  ): Promise<IListCoursesResponse> {
    const filters: FindOptionsWhere<CourseEntity>[] = [];

    if (status) {
      filters.push({
        status,
      });
    }
    const searchResult = await this.databaseService.course.search(
      CourseTable,
      query,
      ['name'],
      filters,
      true,
      { limit, page },
    );

    return {
      data: searchResult.items,
      page,
      total: searchResult.total,
    };
  }

  async listCatalog(
    params: ListCourseCatalogDto,
    user: UserEntity,
  ): Promise<IListCourseCatalogResponse> {
    const { limit = 10, cursor = 0, search, status, owned } = params;
    const numericCursor = Number(cursor) || 0;
    const numericLimit = Number(limit) || 10;
    const courseTableName = this.databaseService.course.getTableName();
    const customerAccessesTableName = this.databaseService.customerAccess.getTableName();
    const queryBuilder = this.databaseService.course
      .getRepository()
      .createQueryBuilder(courseTableName)
      .leftJoinAndSelect(`${courseTableName}.productFamily`, 'productFamily')
      .leftJoinAndSelect('productFamily.productLine', 'productLine');

    if (search) {
      queryBuilder.andWhere(
        `(${courseTableName}.name ILIKE :search OR ${courseTableName}.description ILIKE :search)`,
        { search: `%${search}%` },
      );
    }

    if (status) {
      queryBuilder.andWhere(`${courseTableName}.status = :status`, { status });
    }

    const customerAccesses =
      await this.databaseService.customerAccess.getAllMatchingWithRelationIds(
        customerAccessesTableName,
        'customerId',
        user.id
      );
    const ownedLmsIds = new Map<string, string>();
    for (const access of customerAccesses) {
      if (access.type === CustomerAccessType.LMS) {
        const courseAccess = access.details as CourseAccess;
        ownedLmsIds.set(courseAccess.slug, courseAccess.link);
      }
    }

    if (owned === true) {
      if (ownedLmsIds.size === 0) {
        return { data: [], nextCursor: null };
      }
      queryBuilder.andWhere(
        `${courseTableName}.lmsId IN (:...ownedLmsIds)`,
        {
          ownedLmsIds: Array.from(ownedLmsIds.keys()),
        },
      );
    } else if (owned === false) {
      queryBuilder.andWhere(
        `${courseTableName}.lmsId NOT IN (:...ownedLmsIds)`,
        {
          ownedLmsIds: Array.from(ownedLmsIds.keys()),
        },
      );
    }

    queryBuilder
      .orderBy(`${courseTableName}.updatedAt`, 'DESC')
      .skip(numericCursor)
      .take(numericLimit + 1); // Fetch one extra item for lookahead pagination

    const courses = await queryBuilder.getMany();
    const hasMore = courses.length > numericLimit;
    const data = hasMore ? courses.slice(0, numericLimit) : courses;
    const nextCursor = hasMore ? numericCursor + numericLimit : null;

    const mappedData = data.map((course: CourseEntity) => {
      const isOwned = ownedLmsIds.has(course.lmsId);
      const access: CustomerAccess | undefined = isOwned
        ? customerAccesses.find(
          (access) => access.details.target === AccessTarget.LW_COURSE &&
            (access.details as CourseAccess).slug === course.lmsId
        )
        : undefined;

      return {
        ...course,
        owned: isOwned,
        accessStatus: access ? access.status : undefined,
        lmsUrl: isOwned ? ownedLmsIds.get(course.lmsId) : undefined,
      } as ICourseCatalogItem;
    });

    return {
      data: mappedData,
      nextCursor,
    };
  }

  /**
   * Publishes a course
   * @param id `id` of the course to publish
   * @returns A promise that resolves to `PXActionResult`
   */
  async publish(id: number): Promise<PXActionResult> {
    const result = await this.databaseService.course.update(
      { id },
      {
        status: CourseStatus.PUBLISHED,
      },
    );

    if (result?.affected === 0) {
      return {
        success: false,
        message: `The course was not published`,
      };
    }

    return {
      success: true,
      message: `Course has been published`,
    };
  }

  /**
   * Archives a course
   * @param id `id` of the course to archive
   * @returns A promise that resolves to `PXActionResult`
   */
  async archive(id: number): Promise<PXActionResult> {
    const result = await this.databaseService.course.update(
      { id },
      {
        status: CourseStatus.ARCHIVED,
      },
    );

    if (result?.affected === 0) {
      return {
        success: false,
        message: `The course was not archived`,
      };
    }

    return {
      success: true,
      message: `Course has been archived`,
    };
  }

  /**
   * Gets all courses associated with a specific offer
   * @param offerId - The ID of the offer to find courses for
   * @returns Promise resolving to an array of courses
   */
  async getByOfferId(offerId: number): Promise<CourseEntity[]> {
    const courseTableName = this.databaseService.course.getTableName();
    const courseQueryBuilder = this.databaseService.course
      .getRepository()
      .createQueryBuilder(courseTableName);

    return courseQueryBuilder
      .where(`${courseTableName}.offers @> :targetOffer`, {
        targetOffer: JSON.stringify([{ id: offerId }]),
      })
      .getMany();
  }

  /**
   * Gets all students for a course from all associated cohorts
   * @param courseId - The ID of the course
   * @param search - Optional search query to filter students
   * @param limit - Number of students to return
   * @param page - Page number
   * @returns Promise resolving to a paginated list of students with cohort information
   */
  async getStudentsForCourse(
    courseId: number,
    search?: string,
    limit = 10,
    page = 1,
  ): Promise<IGetStudentsForCourseResponse> {
    // First, check if the course exists
    const course = await this.databaseService.course.getOneBy({ id: courseId });
    if (!course) {
      throw new NotFoundException(`Course with ID ${courseId} not found`);
    }

    // Get all cohorts for the course
    const cohortQueryBuilder = this.databaseService.cohort
      .getRepository()
      .createQueryBuilder(this.databaseService.cohort.getTableName());
    const cohorts = await cohortQueryBuilder
      .where('cohorts.course.id = :courseId', { courseId })
      .leftJoinAndSelect('cohorts.students', 'students')
      .leftJoinAndSelect('students.role', 'role')
      .getMany();

    // If no cohorts or no students in cohorts, return empty result
    if (!cohorts.length) {
      return {
        data: [],
        page,
        total: 0,
      };
    }

    // Collect all students from all cohorts with cohort information
    let allStudents: any[] = [];
    for (const cohort of cohorts) {
      if (cohort.students && cohort.students.length > 0) {
        const studentsWithCohortInfo = cohort.students.map((student) => ({
          ...student,
          cohortId: cohort.id,
          cohortName: cohort.name,
        }));
        allStudents.push(...studentsWithCohortInfo);
      }
    }

    // Apply search filter if provided
    if (search && search.trim() !== '') {
      const searchLowerCase = search.toLowerCase();
      allStudents = allStudents.filter(
        (student) =>
          student.firstName.toLowerCase().includes(searchLowerCase) ||
          student.lastName.toLowerCase().includes(searchLowerCase) ||
          student.email.toLowerCase().includes(searchLowerCase),
      );
    }

    // Apply pagination
    const total = allStudents.length;
    const startIndex = (page - 1) * limit;
    const endIndex = Math.min(startIndex + limit, total);
    const paginatedStudents = allStudents.slice(startIndex, endIndex);

    return {
      data: paginatedStudents,
      page,
      total,
    };
  }
}
