import { Injectable } from '@nestjs/common';
import { CreateCheckoutPageDTO } from '@dtos';
import { CheckoutPage } from '@entities';

@Injectable()
export class CheckoutPageFactoryService {
  generate(checkoutInfo: CreateCheckoutPageDTO): CheckoutPage {
    const checkout = new CheckoutPage();
    checkout.bannerImage = checkoutInfo.bannerImage;
    checkout.mainColor = checkoutInfo.mainColor;
    checkout.url = checkoutInfo.url;
    return checkout;
  }
}
