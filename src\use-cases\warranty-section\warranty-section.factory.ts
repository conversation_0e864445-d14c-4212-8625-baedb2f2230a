import { Injectable } from '@nestjs/common';
import { CheckoutPage, WarrantySection } from '@entities';
import { CreateWarrantySectionDTO } from '@dtos';

@Injectable()
export class WarrantySectionFactoryService {
  generate(sectionInfo: CreateWarrantySectionDTO): WarrantySection {
    const warrantySection = new WarrantySection();
    warrantySection.icon = sectionInfo.icon;
    warrantySection.message = sectionInfo.message;
    const linkedCheckoutPage = new CheckoutPage();
    linkedCheckoutPage.id = sectionInfo.checkoutPageId;
    warrantySection.checkoutPage = linkedCheckoutPage;
    return warrantySection;
  }
}
