"use client";

import { UseFormReturn } from "react-hook-form";

import { Form<PERSON>ield, FormItem, FormLabel } from "@/components/ui/form";
import MultipleSelector, { Option } from "@/components/ui/multiple-selector";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CreateNewSlotFormType } from "../types";

type SpeakersSectionProps = {
  t: (key: string) => string;
  form: UseFormReturn<CreateNewSlotFormType>;
};

const mainSpeakerOptions = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
const otherSpeakersOptions: Option[] = [
  { value: "<PERSON>", label: "<PERSON>" },
  { value: "<PERSON>", label: "<PERSON>" },
  { value: "<PERSON>", label: "<PERSON>" },
  { value: "<PERSON>", label: "<PERSON>" },
];
const technicalSupportOptions: Option[] = [
  { value: "<PERSON>", label: "<PERSON>" },
  { value: "<PERSON>", label: "<PERSON>" },
  { value: "<PERSON>", label: "<PERSON>" },
  { value: "<PERSON>", label: "<PERSON>" },
];

export default function SpeakersSection({ t, form }: SpeakersSectionProps) {
  return (
    <div className="mb-5 border-none">
      <div className="text-xl font-semibold">{t("who-are-the-speakers")}</div>
      <div className="grid gap-4 md:grid-cols-2">
        {/* Main speaker */}
        <FormField
          control={form.control}
          name="mainSpeaker"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("main-speaker")}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("main-speaker")} />
                </SelectTrigger>
                <SelectContent>
                  {mainSpeakerOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        {/* Technical support */}
        <FormField
          control={form.control}
          name="technicalSupport"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("technical-support")}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("technical-support")} />
                </SelectTrigger>
                <SelectContent>
                  {technicalSupportOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        {/* Other speakers */}
        <FormField
          control={form.control}
          name="otherSpeakers"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("other-speakers")}</FormLabel>
              <MultipleSelector
                defaultOptions={otherSpeakersOptions}
                placeholder={t("other-speakers")}
                onChange={(el) => field.onChange(el.map((e) => e.value))}
              />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
