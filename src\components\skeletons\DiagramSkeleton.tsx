import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { BaseProps } from "@/types";

export const DiagramSkeleton = ({ className }: BaseProps) => {
  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* Legend */}
      <div className="flex items-center gap-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center gap-2">
            <Skeleton className="size-3 rounded-full" />
            <Skeleton className="h-4 w-16" />
          </div>
        ))}
      </div>

      {/* Chart Area */}
      <div className="relative h-[200px] w-full">
        {/* Y-Axis Labels */}
        <div className="absolute left-0 top-0 flex h-full w-12 flex-col justify-between py-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <Skeleton key={i} className="h-4 w-8" />
          ))}
        </div>

        {/* Chart Grid */}
        <div className="absolute inset-y-4 left-12 right-4">
          {/* Horizontal Grid Lines */}
          <div className="relative h-full">
            {[0, 1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="absolute w-full border-t border-dashed border-muted"
                style={{ top: `${(i * 100) / 4}%` }}
              />
            ))}
          </div>

          {/* Chart Content */}
          <div className="absolute inset-0 flex items-end">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="flex-1 px-1">
                <Skeleton
                  className="w-full rounded-t-sm"
                  style={{
                    height: `${Math.random() * 60 + 20}%`,
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        {/* X-Axis Labels */}
        <div className="absolute bottom-0 left-12 right-4 flex justify-between">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Skeleton key={i} className="h-4 w-8" />
          ))}
        </div>
      </div>
    </div>
  );
};

DiagramSkeleton.displayName = "DiagramSkeleton";
