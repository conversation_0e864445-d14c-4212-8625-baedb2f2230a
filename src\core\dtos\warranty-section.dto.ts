import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsEmpty, IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateWarrantySectionDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  icon: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  message: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  checkoutPageId: number;
}

export class UpdateWarrantySectionDTO extends PartialType(
  CreateWarrantySectionDTO,
) {
  @ApiPropertyOptional()
  @IsEmpty({ message: "Checkout page for a warranty section can't be updated" })
  checkoutPageId?: number;
}
