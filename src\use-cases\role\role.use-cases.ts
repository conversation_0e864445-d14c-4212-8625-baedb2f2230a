import { Injectable, Logger } from '@nestjs/common';
import { RoleFactory } from './role.factory';
import {
  IListRole,
  IListRolesResponse,
  IUpdateRole,
  PXActionResult,
  RoleGroup,
  UpdateResultWithItemInfo,
} from '@px-shared-account/hermes';
import { CreateRoleDto } from '@dtos';
import { RoleEntity } from '@entities';
import { RedisPublisher } from '@services/event-publishers';
import { UserTable } from '@tables';
import { IDataServices } from '@abstracts';

@Injectable()
export class RoleUseCases {
  private readonly logger = new Logger(RoleUseCases.name);

  constructor(
    private readonly databaseService: IDataServices,
    private readonly factory: RoleFactory,
    private readonly redisPublisher: RedisPublisher,
  ) {}

  /**
   * Creates a new role
   * @param roleInfo Role information
   * @returns RoleEntity
   */
  async create(roleInfo: CreateRoleDto): Promise<RoleEntity> {
    const newRole = this.factory.generate(roleInfo);
    return this.databaseService.role.create(newRole);
  }

  /**
   * Gets a role by role name
   * @param roleName Role name
   * @returns RoleEntity
   */
  async getByRoleName(roleName: string): Promise<RoleEntity> {
    return this.databaseService.role.getOneBy({
      name: roleName,
    });
  }

  /**
   * Gets a role's permissions by role name
   * @param roleName Role name
   * @returns A list of permissions attached to the role
   */
  async getPermissionsByRoleName(roleName: string): Promise<string[]> {
    const role = await this.databaseService.role.getOneBy({
      name: roleName,
    });

    return role?.permissions || [];
  }

  /**
   * Gets all roles for a role group, including `members` count
   * @param roleGroup Role group to filter by
   * @param limit Number of roles to return
   * @param page Page number
   * @param search Optional search term to filter roles by name
   * @returns A list of roles
   */
  async getAllForRoleGroup(
    roleGroup: RoleGroup,
    limit?: number,
    page?: number,
    search?: string,
  ): Promise<IListRolesResponse> {
    const queryBuilder = this.databaseService.role
      .getRepository()
      .createQueryBuilder(this.databaseService.role.getTableName());

    if (roleGroup) {
      queryBuilder.andWhere('"roles"."group" = :group', { group: roleGroup });
    }

    if (search) {
      queryBuilder.andWhere('"roles"."name" ILIKE :search', { search: `%${search}%` });
    }

    queryBuilder.loadRelationCountAndMap(
      'roles.members',
      `${this.databaseService.role.getTableName()}.members`,
    );
    queryBuilder.take(limit || 10);
    queryBuilder.skip((page - 1) * limit || 0);
    const [items, total] = await queryBuilder.getManyAndCount();
    const formattedItems: IListRole[] = items.map((item) => ({
      ...item,
      id: item.id,
      members: item.members as unknown as number,
    }));

    return { data: formattedItems, total, page };
  }

  /**
   * Gets a role by ID
   * @param id Role ID
   * @returns RoleEntity
   */
  async getById(id: number): Promise<RoleEntity> {
    return this.databaseService.role.getOneBy({ id });
  }

  /**
   * Updates a role's permissions
   * @param id Role ID
   * @param permissions List of permissions to update
   * @returns Updated role
   */
  async updatePermissions(
    id: number,
    updates: IUpdateRole,
  ): Promise<UpdateResultWithItemInfo<RoleEntity>> {
    const updateResult = await this.databaseService.role.updateAndReturnItem(
      'id',
      id.toString(),
      {
        ...updates,
      },
    );
    if (updateResult?.success) {
      const updatedRole = updateResult.updatedItem;
      if (updates?.permissions) {
        try {
          await this.syncPermissionsWithRedis(
            updatedRole.name,
            updates.permissions,
          );
          await this.notifyRolePermissionsChanged();
        } catch (error) {
          // Log the error but don't fail the update operation
          this.logger.error(`Failed to sync permissions with Redis: ${error.message}`, error.stack);
        }
      }
    }
    return updateResult;
  }

  /**
   * Syncs permissions with Redis
   * @param roleName Role name
   * @param permissions List of updated permissions for the role
   */
  async syncPermissionsWithRedis(roleName: string, permissions: string[]) {
    try {
      await this.redisPublisher.updateHash('permissions', roleName, permissions);
      return true;
    } catch (error) {
      this.logger.error(`Error updating Redis hash: ${error.message}`, error.stack);
      // Don't rethrow, allow the operation to continue
      return false;
    }
  }

  /**
   * Publishes a message to Redis to notify subscribers of updated role permissions
   * @param roleName Role name
    * 
   * BUGFIX: This function was previously using publishMessage which attempts to set a Redis key.
   * The error "ERR only (P)SUBSCRIBE / (P)UNSUBSCRIBE / PING / QUIT are allowed in this context"
   * occurred because the Redis client was in subscription mode, where only subscription-related
   * commands are allowed. 
   * 
   * The fix:
   * 1. Changed to publishMessageToChannel which uses the Redis PUBLISH command
   * 2. Added error handling to prevent Redis issues from breaking the main workflow
   */
  async notifyRolePermissionsChanged() {
    try {
      const permissions = await this.redisPublisher.getHash('permissions');
      if (permissions) {
        // Use publishMessageToChannel instead of publishMessage
        await this.redisPublisher.publishMessageToChannel(
          'permissions',
          JSON.stringify(permissions),
        );
      }
      return true;
    } catch (error) {
      this.logger.error(`Error publishing Redis message: ${error.message}`, error.stack);
      // Don't rethrow, allow the operation to continue
      return false;
    }
  }

  /**
   * Deletes a role if it has no members
   * @param id Role ID
   * @returns Updated role
   */
  async delete(id: number): Promise<PXActionResult> {
    const result = await this.databaseService.role
      .getRepository()
      .createQueryBuilder(this.databaseService.role.getTableName())
      .where('id = :id', { id })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('COUNT(*)')
          .from(UserTable, 'user')
          .where('user.roleId = roles.id')
          .getQuery();
        return `(${subQuery}) = 0`;
      })
      .softDelete()
      .execute();
    return {
      success: result.affected > 0,
      message: result.affected > 0 ? 'Role deleted' : 'Role not deleted',
    };
  }

  /**
   * Gets the default student role
   * @returns RoleEntity
   */
  async getDefaultStudentRole(): Promise<RoleEntity> {
    return this.databaseService.role.getOneBy({
      group: RoleGroup.STUDENT,
      isDefault: true,
    });
  }
}
