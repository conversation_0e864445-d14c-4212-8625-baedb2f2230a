import {
  IWhatsNewVersion,
  IWhatsNewListResponse,
  ICreateWhatsNewPayload,
  IUpdateWhatsNewPayload,
  IReactionsResponse,
  IToggleReactionPayload,
  IToggleReactionResponse,
  IUploadResponse,
  IMediaListResponse,
} from "@px-shared-account/hermes";

/**
 * Core API endpoints for What's New operations
 * These are the base paths and methods used by both client and server implementations
 */
export const WHATS_NEW_API_ENDPOINTS = {
  /**
   * List all What's New versions endpoint
   * @returns Endpoint configuration
   */
  list: () => ({
    url: "/whats-new",
    method: "GET",
  }),

  /**
   * Get specific What's New version by ID endpoint
   * @param id Version ID
   * @returns Endpoint configuration
   */
  getById: (id: number) => ({
    url: `/whats-new/${id}`,
    method: "GET",
  }),

  /**
   * Create new What's New version endpoint
   * @returns Endpoint configuration
   */
  create: () => ({
    url: "/whats-new",
    method: "POST",
  }),

  /**
   * Update existing What's New version endpoint
   * @param id Version ID
   * @returns Endpoint configuration
   */
  update: (id: number) => ({
    url: `/whats-new/${id}`,
    method: "PATCH",
  }),

  /**
   * Delete What's New version endpoint
   * @param id Version ID
   * @returns Endpoint configuration
   */
  delete: (id: number) => ({
    url: `/whats-new/${id}`,
    method: "DELETE",
  }),

  /**
   * Get reactions for a specific version endpoint
   * @param versionId Version ID
   * @returns Endpoint configuration
   */
  getReactions: (versionId: number) => ({
    url: `/whats-new/${versionId}/reactions`,
    method: "GET",
  }),

  /**
   * Toggle reaction for a version endpoint
   * @returns Endpoint configuration
   */
  toggleReaction: () => ({
    url: "/whats-new/reactions",
    method: "POST",
  }),

  /**
   * Upload image endpoint
   * @returns Endpoint configuration
   */
  uploadImage: () => ({
    url: "/whats-new/upload",
    method: "POST",
  }),

  /**
   * List media gallery endpoint
   * @param prefix Optional prefix for filtering files
   * @param limit Optional limit for number of files
   * @returns Endpoint configuration
   */
  listMedia: (prefix?: string, limit?: number) => {
    const params = new URLSearchParams();
    if (prefix) params.append("prefix", prefix);
    if (limit) params.append("limit", limit.toString());

    const queryString = params.toString();
    return {
      url: `/whats-new/media${queryString ? `?${queryString}` : ""}`,
      method: "GET",
    };
  },

  /**
   * Delete media from gallery endpoint
   * @param pathname Media pathname to delete
   * @returns Endpoint configuration
   */
  deleteMedia: (pathname: string) => ({
    url: `/whats-new/media/${encodeURIComponent(pathname)}`,
    method: "DELETE",
  }),
};

/**
 * Type definitions for API responses
 */
export type WhatsNewApiTypes = {
  list: IWhatsNewListResponse;
  getById: IWhatsNewVersion;
  create: IWhatsNewVersion;
  update: IWhatsNewVersion;
  delete: { success: boolean };
  getReactions: IReactionsResponse;
  toggleReaction: IToggleReactionResponse;
  uploadImage: IUploadResponse;
  listMedia: IMediaListResponse;
  deleteMedia: { success: boolean };
};

/**
 * Type definitions for API request payloads
 */
export type WhatsNewApiPayloads = {
  create: ICreateWhatsNewPayload;
  update: IUpdateWhatsNewPayload;
  toggleReaction: IToggleReactionPayload;
  uploadImage: FormData;
  deleteMedia: { pathname: string };
};
