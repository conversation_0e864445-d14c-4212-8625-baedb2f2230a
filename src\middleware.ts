import createMiddleware from "next-intl/middleware";
import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

import { routing } from "./i18n/routing";

const isPublicRoute = createRouteMatcher([
  routing.pathnames["/sign-in"].en,
  routing.pathnames["/sign-in"].fr,
  routing.pathnames["/sign-up"].en,
  routing.pathnames["/sign-up"].fr,
  routing.pathnames["/sign-out"].en,
  routing.pathnames["/sign-out"].fr,
]);

const intlMiddleware = createMiddleware(routing);

export default clerkMiddleware(async (auth, req) => {
  if (!isPublicRoute(req)) await auth.protect();
  return intlMiddleware(req);
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};
