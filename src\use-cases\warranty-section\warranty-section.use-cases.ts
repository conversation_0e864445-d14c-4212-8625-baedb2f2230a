import { Injectable } from '@nestjs/common';
import { DeleteResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { ProductOfferVersion } from '@px-shared-account/hermes';
import { WarrantySectionTable } from '@tables';
import { UpdateResultWithItemInfo } from '@types';
import { ProductOfferUseCases } from '../product-offers';
import { WarrantySection } from '@entities';
import { IDataServices } from '@abstracts';

@Injectable()
export class WarrantySectionUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly offerUseCases: ProductOfferUseCases,
  ) {}

  /**
   * Creates a new warranty section in database
   * @param warrantySection Warranty Section Page entity
   * @returns Warranty Section record from database
   */
  async create(warrantySection: WarrantySection): Promise<WarrantySection> {
    const createResult = await this.dataServices.warrantySection.create(
      warrantySection,
    );

    const linkedOffer = await this.offerUseCases.getOneBy({
      checkoutPage: { id: warrantySection.checkoutPage.id },
    });
    await this.offerUseCases.update(linkedOffer.id, {
      version: ProductOfferVersion.PREVIEW,
    });
    return createResult;
  }

  /**
   * Fetches a `WarrantySection` record matching the provided `id`
   * @param id `id` of the `WarrantySection` record to fetch
   * @returns `WarrantySection` record from database
   */
  async getOne(id: number): Promise<WarrantySection> {
    return this.dataServices.warrantySection.getOneBy({ id });
  }

  /**
   * Updates a warranty section in database and also updates
   * the `ProductOfferVersion` of the associated `ProductOffer`
   * @param id `id` of the warranty section to update
   * @param updates Updates for the Warranty Section
   */
  async update(
    id: number,
    updates: QueryDeepPartialEntity<WarrantySection>,
  ): Promise<UpdateResultWithItemInfo<WarrantySection>> {
    const repository = this.dataServices.warrantySection.getRepository();
    const warrantySectionToUpdate = await repository.findOne({
      where: { id },
      relations: ['checkoutPage', 'checkoutPage.productOffer'],
    });

    if (!warrantySectionToUpdate) {
      throw new Error('WarrantySection not found');
    }

    const updateResult =
      await this.dataServices.warrantySection.updateAndReturnItem(
        'id',
        id.toString(),
        updates,
      );
    const offerId = warrantySectionToUpdate.checkoutPage?.productOffer?.id;
    await this.offerUseCases.update(offerId, {
      version: ProductOfferVersion.PREVIEW,
    });
    return updateResult;
  }

  /**
   * Soft Deletes a warranty section from database and also updates
   * the `ProductOfferVersion` of the associated `ProductOffer`
   * @param id `id` of the warranty section to delete
   */
  async delete(id: number): Promise<DeleteResult> {
    const warrantySectionRepository =
      this.dataServices.warrantySection.getRepository();
    const warrantySectionManager = warrantySectionRepository.manager;

    const warrantySection = await warrantySectionManager.findOne(
      WarrantySectionTable,
      {
        where: { id },
        relations: ['checkoutPage', 'checkoutPage.productOffer'],
      },
    );

    if (!warrantySection) {
      throw new Error('WarrantySection not found');
    }

    const deleteResult = await this.dataServices.warrantySection.delete({ id });
    const offerId = warrantySection.checkoutPage?.productOffer?.id;
    await this.offerUseCases.update(offerId, {
      version: ProductOfferVersion.PREVIEW,
    });
    return deleteResult;
  }

  /**
   * Gets all warranty section records for a specific `CheckoutPage`
   * @param checkoutPageId `id` of the `CheckoutPage` to get warranty sections for
   * @returns Warranty section record from the database
   */
  async getAllForCheckoutPage(
    checkoutPageId: number,
  ): Promise<{ items: WarrantySection[]; total: number }> {
    return this.dataServices.warrantySection.getAllBy(
      {
        checkoutPage: {
          id: checkoutPageId,
        },
      },
      50,
      1,
      {
        createdAt: 'DESC',
      },
    );
  }
}
