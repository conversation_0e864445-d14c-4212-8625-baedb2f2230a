"use client";

import { addDays, isBefore, startOfWeek } from "date-fns";

/**
 * Checks if a given date is before the middle of the week.
 *
 * @param date - The date to check.
 * @param weekStartsOn - The day the week starts on (0 for Sunday, 1 for Monday, etc.). Defaults to Sunday.
 * @returns {boolean} - True if the date is before the middle of the week, false otherwise.
 */
export default function isBeforeMidWeek(date: string, weekStartsOn: number = 0): boolean {
  // Convert the date string to a Date object
  const dateObj = new Date(date);

  // Get the start of the week for the given date
  const weekStart = startOfWeek(date, { weekStartsOn: 1 });

  // Calculate the middle of the week (3 days after the start)
  const midWeek = addDays(weekStart, 3);

  // Check if the date is before the middle of the week
  return isBefore(dateObj, midWeek);
}
