"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useApi } from "@/hooks/store/use-api";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { USER_API_ENDPOINTS, UserApiTypes, ListUsersParams, UserApiPayloads } from "./core";

/**
 * Hook for fetching and managing user lists
 * @param params Filter and pagination parameters
 * @returns SWR response with user list data
 */
export function useUserList(params: ListUsersParams = {}) {
  const apiConfig = useMemo(() => {
    const { url } = USER_API_ENDPOINTS.list(params);
    return url;
  }, [params]);

  return useApi<UserApiTypes["list"]>(apiConfig);
}

/**
 * Hook for fetching a specific user by ID
 * @param id User ID to fetch
 * @returns SWR response with user data
 */
export function useUserDetails(id?: number) {
  const apiConfig = useMemo(() => {
    if (!id) return null;
    const { url } = USER_API_ENDPOINTS.getById(id);
    return url;
  }, [id]);

  return useApi<UserApiTypes["getById"]>(apiConfig);
}

/**
 * Hook for fetching a specific user by SSO ID
 * @param id User SSO ID to fetch
 * @returns SWR response with user data
 */
export function useUserDetailsBySSOId(id?: string) {
  const apiConfig = useMemo(() => {
    if (!id) return null;
    const { url } = USER_API_ENDPOINTS.getBySSOId(id);
    return url;
  }, [id]);

  return useApi<UserApiTypes["getBySSOId"]>(apiConfig);
}

/**
 * Hook for creating a new user
 * @returns Functions and state for user creation
 */
export function useCreateUser() {
  const { toast } = useToast();
  const t = useTranslations("services.user");
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = USER_API_ENDPOINTS.create();
  const { trigger, isLoading } = useApi<UserApiTypes["create"]>(url, {
    method: method as "POST",
  });

  const create = useCallback(
    async (data: UserApiPayloads["create"]) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        const result = await trigger(data);
        toast({
          title: t("create.success.title"),
          description: t("create.success.description"),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("create.error.title"),
          description: errorMessage || t("create.error.description"),
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger, t],
  );

  return { create, isLoading, error };
}

/**
 * Hook for updating an existing user
 * @param id Optional initial user ID
 * @returns Functions and state for user updates
 */
export function useUpdateUser(id?: number) {
  const { toast } = useToast();
  const t = useTranslations("services.user");
  const [error, setError] = useState<Error | null>(null);
  const [userId, setUserId] = useState<number | null>(id || null);
  const [updateData, setUpdateData] = useState<UserApiPayloads["update"] | null>(null);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  const { trigger, isLoading } = useApi<UserApiTypes["update"]>(
    userId ? USER_API_ENDPOINTS.update(userId).url : null,
    { method: "PATCH" },
  );

  const performUpdate = async (data?: UserApiPayloads["update"]) => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!userId) throw new Error("User ID is required");
      if (!updateData && !data) throw new Error("Update data is required");

      const result = await trigger(data || updateData);
      toast({
        title: t("update.success.title"),
        description: t("update.success.description"),
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: t("update.error.title"),
        description: errorMessage || t("update.error.description"),
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldUpdate(false);
      setUpdateData(null);
    }
  };

  useEffect(() => {
    if (shouldUpdate && updateData) {
      performUpdate();
    }
  }, [shouldUpdate, userId, updateData]);

  const update = useCallback(
    async (overrideId: number, data: UserApiPayloads["update"]) => {
      setError(null);
      setUpdateData(data);
      if (userId !== overrideId) {
        setUserId(overrideId || id || null);
        setShouldUpdate(true);
      } else {
        await performUpdate(data);
      }
    },
    [userId, id],
  );

  return { update, isLoading, error };
}

/**
 * Hook for bulk updating users
 * @returns Functions and state for bulk user updates
 */
export function useBulkUpdateUsers() {
  const { toast } = useToast();
  const t = useTranslations("services.user");
  const [error, setError] = useState<Error | null>(null);
  const [updateData, setUpdateData] = useState<UserApiPayloads["bulkUpdate"] | null>(null);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  const { url, method } = USER_API_ENDPOINTS.bulkUpdate();
  const { trigger, isLoading } = useApi<UserApiTypes["bulkUpdate"]>(url, {
    method: method as "POST",
  });

  const performBulkUpdate = async (data?: UserApiPayloads["bulkUpdate"]) => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!updateData && !data) throw new Error("Update data is required");

      const result = await trigger(data || updateData);
      toast({
        title: t("bulk_update.success.title"),
        description: t("bulk_update.success.description"),
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: t("bulk_update.error.title"),
        description: errorMessage || t("bulk_update.error.description"),
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldUpdate(false);
      setUpdateData(null);
    }
  };

  useEffect(() => {
    if (shouldUpdate && updateData) {
      performBulkUpdate();
    }
  }, [shouldUpdate, updateData]);

  const bulkUpdate = useCallback(async (data: UserApiPayloads["bulkUpdate"]) => {
    setError(null);
    setUpdateData(data);
    setShouldUpdate(true);
  }, []);

  return { bulkUpdate, isLoading, error };
}

/**
 * Hook for creating a new phone number for a user
 * @param id User ID to create phone number for
 * @returns Functions and state for creating a new phone number
 */
export function useCreatePhoneNumber(id: string) {
  const { toast } = useToast();
  const t = useTranslations("services.user");
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = USER_API_ENDPOINTS.createPhoneNumber(id);
  const { trigger, isLoading } = useApi(url, {
    method: method as "POST",
  });

  const createPhoneNumber = useCallback(
    async (data: UserApiPayloads["createPhoneNumber"]) => {
      setError(null);
      try {
        if (!trigger) throw new Error("API not initialized");
        const result = await trigger(data);
        toast({
          title: t("create_phone_number.success.title"),
          description: t("create_phone_number.success.description"),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("create_phone_number.error.title"),
          description: errorMessage || t("create_phone_number.error.description"),
          variant: "destructive",
        });
        throw error;
      }
    },
    [trigger, toast, t],
  );

  return { createPhoneNumber, isLoading, error };
}

/**
 * Hook for unlinking a phone number from a user
 * @param phoneNumberId Phone number ID to unlink
 * @returns Functions and state for unlinking a phone number
 */
export function useUnlinkPhoneNumber(id: string) {
  const { toast } = useToast();
  const t = useTranslations("services.user");
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = USER_API_ENDPOINTS.unlinkPhoneNumber(id);
  const { trigger, isLoading } = useApi(url, {
    method: method as "POST",
  });

  const unlinkPhoneNumber = useCallback(
    async (data: UserApiPayloads["unlinkPhoneNumber"]) => {
      setError(null);
      try {
        if (!trigger) throw new Error("API not initialized");
        await trigger(data);
        toast({
          title: t("unlink_phone_number.success.title"),
          description: t("unlink_phone_number.success.description"),
          variant: "default",
        });
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("unlink_phone_number.error.title"),
          description: errorMessage || t("unlink_phone_number.error.description"),
          variant: "destructive",
        });
        throw error;
      }
    },
    [trigger, toast, t],
  );

  return { unlinkPhoneNumber, isLoading, error };
}
