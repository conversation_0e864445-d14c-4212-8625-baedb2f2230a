import { IsEnum, IsInt, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { FiscalEntity, InvoiceStatus } from '@enums';

export class ListInvoicesDTO {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsEnum(FiscalEntity)
  fiscalEntity?: FiscalEntity;

  @IsOptional()
  @IsString()
  customerId?: string;

  @IsOptional()
  @IsString()
  subscriptionId?: string;

  @IsOptional()
  @IsEnum(InvoiceStatus)
  status?: InvoiceStatus;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  paidAtStart?: number;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  paidAtEnd?: number;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  limit?: number;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  page?: number;

  @IsOptional()
  @IsEnum(['DESC', 'ASC'])
  orderBy?: 'DESC' | 'ASC';
}
