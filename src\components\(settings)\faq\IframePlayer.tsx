"use client";

import { useFaqIframe } from "@/hooks/use-faq";

type IframePlayerProps = {
  src: string;
  title: string;
  minHeight?: string;
};

export default function IframePlayer({ src, title, minHeight = "400px" }: IframePlayerProps) {
  const { iframeHeight } = useFaqIframe();

  return (
    <div className="border-secondary bg-background-secondary h-full w-full flex-grow overflow-hidden rounded-lg border">
      <iframe
        src={src}
        width="100%"
        height={iframeHeight}
        className="w-full border-none"
        style={{ minHeight }}
        title={title}
        loading="lazy"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      />
    </div>
  );
}
