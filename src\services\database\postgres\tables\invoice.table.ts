import {
  InvoiceLinkedPayment,
  InvoiceLineItem,
  InvoiceLineItemTax,
  InvoiceTax,
  InvoiceIssuedCreditNote,
  InvoiceAdjustmentCreditNote,
  InvoiceDiscount,
  InvoiceBillingAddress,
  InvoiceShippingAddress,
  InvoiceLineItemDiscount,
} from 'chargebee-typescript/lib/resources';
import {
  Entity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  PrimaryGeneratedColumn,
  AfterLoad,
} from 'typeorm';
import { Currencies, InvoicePriceType, InvoiceStatus } from '@enums';
import { Invoice } from '@entities';

@Entity({
  name: 'invoices',
})
export class InvoiceTable implements Invoice {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', unique: true })
  chargebeeId: string;

  @Column({ type: 'varchar' })
  customerId: string;

  @Column({ type: 'varchar' })
  customerEmail: string;

  @Column({ type: 'varchar' })
  customerName: string;

  @Column({ type: 'varchar' })
  subscriptionId: string;

  @Column({ type: 'varchar' })
  status: InvoiceStatus;

  @Column({ type: 'varchar' })
  priceType: InvoicePriceType;

  @Column({ type: 'int' })
  date: number;

  @Column({ type: 'int' })
  dueDate: number;

  @Column({ type: 'int', nullable: true })
  paidAt?: number;

  @Column({ type: 'int' })
  netTermDays: number;

  @Column({ type: 'decimal', nullable: true })
  exchangeRate: number;

  @Column({ type: 'int' })
  total: number;

  @Column({ type: 'int' })
  amountPaid: number;

  @Column({ type: 'int' })
  amountAdjusted: number;

  @Column({ type: 'int' })
  writeOffAmount: number;

  @Column({ type: 'int' })
  amountDue: number;

  @Column({ type: 'int' })
  amountToCollect: number;

  @Column({ type: 'varchar' })
  currencyCode: Currencies;

  @Column({ type: 'varchar', nullable: true })
  baseCurrencyCode: Currencies;

  @Column({ type: 'bigint' })
  generatedAt: number;

  @Column({ type: 'varchar' })
  channel: string;

  @Column({ type: 'int' })
  tax: number;

  @Column({ type: 'jsonb' })
  lineItems: InvoiceLineItem[];

  @Column({ type: 'jsonb', nullable: true })
  discounts?: InvoiceDiscount[];

  @Column({ type: 'jsonb', nullable: true })
  lineItemDiscounts?: InvoiceLineItemDiscount[];

  @Column({ type: 'jsonb', nullable: true })
  taxes?: InvoiceTax[];

  @Column({ type: 'jsonb', nullable: true })
  lineItemTaxes: InvoiceLineItemTax[];

  @Column({ type: 'int' })
  subTotal: number;

  @Column({ type: 'jsonb', nullable: true })
  linkedPayments: InvoiceLinkedPayment[];

  @Column({ type: 'jsonb', nullable: true })
  adjustmentCreditNotes: InvoiceAdjustmentCreditNote[];

  @Column({ type: 'jsonb', nullable: true })
  issuedCreditNotes: InvoiceIssuedCreditNote[];

  @Column({ type: 'jsonb', nullable: true })
  dunningAttempts: any[];

  @Column({ type: 'jsonb', nullable: true, default: {} })
  billingAddress: InvoiceBillingAddress;

  @Column({ type: 'jsonb', nullable: true, default: {} })
  shippingAddress: InvoiceShippingAddress;

  @Column({ type: 'varchar' })
  businessEntityId: string;

  @Column({ type: 'varchar', nullable: true })
  crmId?: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  overdueCheckPassed: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deletedAt?: Date;

  @AfterLoad()
  convertExchangeRateToNumber() {
    this.exchangeRate = parseFloat(this.exchangeRate.toString());
  }
}
