import { Plus } from "lucide-react";
import { Button } from "@/components/base/button";

export default function FloatingActionButton({
  onClick,
  label,
}: {
  onClick?: () => void;
  label: string;
}) {
  return (
    <Button
      className="fixed bottom-4 right-4 h-14 w-14 rounded-full shadow-lg lg:hidden"
      onClick={onClick ?? (() => {})}
    >
      <Plus className="h-6 w-6" />
      <span className="sr-only">{label}</span>
    </Button>
  );
}
