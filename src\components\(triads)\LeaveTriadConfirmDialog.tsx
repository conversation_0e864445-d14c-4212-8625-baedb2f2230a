"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

interface LeaveTriadConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
}

export function LeaveTriadConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  loading,
}: LeaveTriadConfirmDialogProps) {
  const t = useTranslations("triad");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="rounded-3xl border-none bg-[#111111] text-white sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-xl">{t("confirm_leave.title")}</DialogTitle>
          <div className="space-y-2 text-center text-gray-400">
            <p>{t("confirm_leave.description")}</p>
            <p className="mx-auto mt-4 max-w-[90%] text-sm break-words italic">
              {t("confirm_leave.note")}
            </p>
          </div>
        </DialogHeader>
        <DialogFooter className="flex flex-row gap-4 sm:gap-4">
          <Button
            variant="outline"
            onClick={onCancel}
            className="flex-1 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black"
          >
            {t("confirm_leave.cancel")}
          </Button>
          <Button
            onClick={onConfirm}
            disabled={loading}
            className="flex-1 rounded-full bg-red-500 text-white hover:bg-red-600"
          >
            {loading ? t("list.loading") : t("confirm_leave.confirm")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
