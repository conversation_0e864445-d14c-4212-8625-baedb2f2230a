import { Module } from '@nestjs/common';
import { TransactionUseCases, TransactionFactoryService } from '.';
import { ChargebeeBillingModule } from '@services/billing';
import { DataServicesModule } from '@services/database';

@Module({
  imports: [DataServicesModule, ChargebeeBillingModule],
  providers: [TransactionFactoryService, TransactionUseCases],
  exports: [TransactionFactoryService, TransactionUseCases],
})
export class TransactionUseCasesModule {}
