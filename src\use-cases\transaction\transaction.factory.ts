import { Injectable } from '@nestjs/common';
import { Transaction as ChargebeeTransaction } from 'chargebee-typescript/lib/resources';
import { Transaction } from '@entities';
import {
  Currencies,
  TransactionStatus,
  TransactionType,
  PaymentMethod,
} from '@enums';

@Injectable()
export class TransactionFactoryService {
  /**
   * Generates a new transaction
   * @param chargebeeTransaction Transaction info from Chargebee
   */
  generateFromChargebeeEvent(
    chargebeeTransaction: ChargebeeTransaction,
  ): Transaction {
    const transaction = new Transaction();
    transaction.chargebeeId = chargebeeTransaction.id;
    transaction.status =
      TransactionStatus[chargebeeTransaction.status.toUpperCase()];
    transaction.date = chargebeeTransaction.date;
    transaction.type = TransactionType[chargebeeTransaction.type.toUpperCase()];
    transaction.currency =
      Currencies[chargebeeTransaction.currency_code.toUpperCase()];
    transaction.amount = chargebeeTransaction.amount;
    transaction.amountUnused = chargebeeTransaction.amount_unused;
    transaction.subscriptionId = chargebeeTransaction.subscription_id;
    transaction.customerId = chargebeeTransaction.customer_id;
    transaction.exchangeRate = chargebeeTransaction.exchange_rate;
    transaction.referenceNumber = chargebeeTransaction.reference_number;
    transaction.gateway = chargebeeTransaction.gateway;
    transaction.idAtGateway = chargebeeTransaction.id_at_gateway;
    transaction.maskedCardNumber = chargebeeTransaction.masked_card_number;
    transaction.paymentMethod =
      PaymentMethod[chargebeeTransaction.payment_method.toUpperCase()];
    if (
      !transaction.paymentMethod &&
      chargebeeTransaction?.custom_payment_method_name
    ) {
      transaction.paymentMethod = PaymentMethod.BANK_TRANSFER;
    }
    transaction.businessEntityId = chargebeeTransaction.business_entity_id;
    transaction.paymentMethodDetails =
      chargebeeTransaction.payment_method_details;
    transaction.errorCode = chargebeeTransaction?.error_code;
    transaction.errorText = chargebeeTransaction?.error_text;

    const {
      linked_credit_notes: linkedCreditNotes,
      linked_invoices: linkedInvoices,
      linked_refunds: linkedRefunds,
    } = chargebeeTransaction;

    transaction.linkedInvoices = linkedInvoices;
    transaction.linkedCreditNotes = linkedCreditNotes;
    transaction.linkedRefunds = linkedRefunds;
    return transaction;
  }
}
