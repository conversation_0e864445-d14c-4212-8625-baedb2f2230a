import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsInt,
  IsNumber,
  IsNotEmpty,
  IsArray,
  IsBoolean,
} from 'class-validator';
import {
  SubscriptionAddon,
  SubscriptionCoupon,
  SubscriptionDiscount,
  SubscriptionItemTier,
  SubscriptionReferralInfo,
  SubscriptionSubscriptionItem,
} from 'chargebee-typescript/lib/resources';
import {
  ChargeBeeChannel,
  ChargeBeeDurationPeriodUnit,
  SubscriptionCancelReason,
  SubscriptionOrderStatus,
  SubscriptionStatus,
  TrialEndAction,
} from '@enums';
import { Subscription } from '@entities';

export class ListSubscriptionsDTO {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsEnum(SubscriptionOrderStatus)
  orderStatus?: SubscriptionOrderStatus;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  product?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  offer?: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => JSON.parse(value))
  isForever?: boolean;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value) || 10)
  limit?: number;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value) || 1)
  page?: number;

  @IsOptional()
  @IsEnum(['DESC', 'ASC'])
  orderBy?: 'DESC' | 'ASC';
}

export class UpdateSubscriptionItemDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  updatedAmount: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  updatedBillingCycles: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  updatedQuantity: number;
}

export class UpdateSubscriptionDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  updatedBillingCycles: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @Type(() => UpdateSubscriptionItemDTO)
  updatedItems: UpdateSubscriptionItemDTO[];
}

export class GetSubscriptionsStatusDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @Type(() => String)
  subscriptionIds: string[];
}

export class ExportSubscriptionsDTO {
  @ApiProperty()
  @IsNotEmpty()
  startDate: number;

  @ApiProperty()
  @IsNotEmpty()
  endDate: number;
}

export class ChargeBeeSubscriptionDTO implements Partial<Subscription> {
  @Expose({ name: 'id' })
  chargebeeId: string;

  @Expose({ name: 'currency_code' })
  currencyCode: string;

  @Expose({ name: 'start_date' })
  startDate: number;

  @Expose({ name: 'trial_end' })
  trialEnd: number;

  @Expose({ name: 'billing_period' })
  billingPeriod: number;

  @Expose({ name: 'remaining_billing_cycles' })
  @Transform(
    ({ value }) => {
      return value || 0;
    },
    { toClassOnly: true },
  )
  remainingBillingCycles = 0;

  @Expose({ name: 'plan_id' })
  planId: string;

  @Expose({ name: 'customer_id' })
  customerId: string;

  @Expose({ name: 'status' })
  @Transform(
    ({ value }) => {
      console.log(`value of status: ${value}`);
      return SubscriptionStatus[value.toUpperCase()];
    },
    {
      toClassOnly: true,
    },
  )
  status: SubscriptionStatus;

  @Expose({ name: 'trial_start' })
  trialStart: number;

  @Expose({ name: 'trial_end_action' })
  @Transform(({ value }) => TrialEndAction[value?.toUpperCase()] || null, {
    toClassOnly: true,
  })
  trialEndAction: TrialEndAction;

  @Expose({ name: 'current_term_start' })
  currentTermStart: number;

  @Expose({ name: 'current_term_end' })
  currentTermEnd: number;

  @Expose({ name: 'next_billing_at' })
  nextBillingAt: number;

  @Expose({ name: 'created_at' })
  createdAt: number;

  @Expose({ name: 'started_at' })
  startedAt: number;

  @Expose({ name: 'activated_at' })
  activatedAt: number;

  @Expose({ name: 'cancelled_at' })
  cancelledAt: number;

  @Expose({ name: 'paused_date' })
  pauseDate: number;

  @Expose({ name: 'resume_date' })
  resumeDate: number;

  @Expose({ name: 'updated_at' })
  updatedAt: number;

  @Expose({ name: 'created_from_ip' })
  createdFromIp: string;

  @Expose({ name: 'resource_version' })
  resourceVersion?: number;

  @Expose({ name: 'has_scheduled_advance_invoices' })
  hasScheduledAdvanceInvoices?: boolean;

  @Expose({ name: 'payment_source_id' })
  paymentSourceId?: string;

  @Expose({ name: 'cancel_schedule_created_at' })
  cancelScheduleCreatedAt?: number;

  @Expose({ name: 'net_term_days' })
  netTermDays?: number;

  @Expose({ name: 'due_invoices_count' })
  dueInvoicesCount?: number;

  @Expose({ name: 'due_since' })
  dueSince?: number;

  @Expose({ name: 'total_dues' })
  totalDues? = 0;

  @Expose({ name: 'exchange_rate' })
  exchangeRate?: number;

  @Expose({ name: 'base_currency_code' })
  baseCurrencyCode?: string;

  @Expose({ name: 'invoice_notes' })
  invoiceNotes?: string;

  @Expose({ name: 'deleted' })
  deleted?: boolean;

  @Expose({ name: 'changes_scheduled_at' })
  changesScheduledAt?: number;

  @Expose({ name: 'cancel_reason_code' })
  cancelReasonCode?: string;

  @Expose({ name: 'coupons' })
  coupons?: SubscriptionCoupon[];

  @Expose({ name: 'shipping_address' })
  shippingAddress: any;

  @Expose({ name: 'cancel_reason' })
  @Transform(
    ({ value }) => SubscriptionCancelReason[value?.toUpperCase()] || null,
    {
      toClassOnly: true,
    },
  )
  cancelReason: SubscriptionCancelReason;

  @Expose({ name: 'channel' })
  @Transform(({ value }) => ChargeBeeChannel[value?.toUpperCase()] || null, {
    toClassOnly: true,
  })
  channel: ChargeBeeChannel;

  @Expose({ name: 'free_period' })
  freePeriod?: number;

  @Expose({ name: 'free_period_unit' })
  @Transform(
    ({ value }) => ChargeBeeDurationPeriodUnit[value?.toUpperCase()] || null,
    {
      toClassOnly: true,
    },
  )
  freePeriodUnit: ChargeBeeDurationPeriodUnit;

  @Expose({ name: 'cf_is_forever' })
  @Transform(({ value }) => !!value, { toClassOnly: true })
  isForever: boolean;

  @Expose({ name: 'cf_deal_id' })
  @Transform(({ value }) => value || null, { toClassOnly: true })
  crmId: string;

  @Expose({ name: 'meta_data' })
  metadata: Record<string, any>;

  @Expose({ name: 'custom_fields' })
  @Transform(({ obj }) =>
    Object.fromEntries(
      Object.entries(obj).filter(([key]) => key.startsWith('cf_')),
    ),
  )
  customFields: Record<string, any>;

  @Expose({ name: 'business_entity_id' })
  businessEntityId?: string;

  @Expose({ name: 'subscription_items' })
  subscriptionItems?: SubscriptionSubscriptionItem[];

  @Expose({ name: 'addons' })
  addons?: SubscriptionAddon[];

  @Expose({ name: 'discounts' })
  discounts?: SubscriptionDiscount[];

  @Expose({ name: 'create_pending_invoices' })
  createPendingInvoices?: boolean;

  @Expose({ name: 'item_tiers' })
  itemTiers?: SubscriptionItemTier[];

  @Expose({ name: 'referral_info' })
  referralInfo?: SubscriptionReferralInfo;

  @Expose({ name: 'contract_term' })
  contractTerm?: any;

  @Expose({ name: 'auto_collection' })
  autoCollection?: string;
}
