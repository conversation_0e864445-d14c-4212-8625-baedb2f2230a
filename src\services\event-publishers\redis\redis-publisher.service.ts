import { Injectable } from '@nestjs/common';
import { RedisClientType } from 'redis';

@Injectable()
export class RedisPublisher {
  constructor(private readonly client: RedisClientType) {}

  async publishMessage(key: string, value: string): Promise<any> {
    await this.client.set(key, value);
  }

  async removeMessage(key: string): Promise<any> {
    await this.client.del(key);
  }

  async getMessage(key: string): Promise<any> {
    return this.client.get(key);
  }

  /**
   * Sets the hash value of a key in Redis
   * @param key - The key to update the value for
   * @param field - The field in hash to update the value for
   * @param value - The updated value to set
   * @returns A promise that resolves when the key is updated
   */
  async updateHash(
    key: string,
    field: string,
    value: object | Array<any> | string | number | boolean,
  ): Promise<any> {
    await this.client.hSet(key, field, JSON.stringify(value));
  }

  /**
   * Publish a message to a channel in Redis
   * @param channel - The channel to publish the message to
   * @param message - The message to publish
   * @returns A promise that resolves when the message is published
   */
  async publishMessageToChannel(
    channel: string,
    message: string,
  ): Promise<any> {
    await this.client.publish(channel, message);
  }

  /**
   * Get the hash value of a key in Redis
   * @param key - The key to get the value for
   * @returns A promise that resolves to the value of the key
   */
  async getHash(key: string): Promise<any> {
    return await this.client.hGetAll(key);
  }

  /**
   * Sets the string value of a key in Redis
   * @param key - The key to update the value for
   * @param value - The updated value to set, must be a string
   * @returns A promise that resolves when the key is updated
   */
  async setString(key: string, value: string): Promise<any> {
    await this.client.set(key, value);
  }

  /**
   * Subscribe to a channel in Redis
   * @param channel - The channel to subscribe to
   * @param listener - The listener function to call when a message is received
   * @returns A promise that resolves when the subscription is successful
   */
  async subscribeToChannel(
    channel: string,
    listener: (message: string, channel: string) => void,
  ): Promise<void> {
    await this.client.subscribe(channel, listener);
  }
}
