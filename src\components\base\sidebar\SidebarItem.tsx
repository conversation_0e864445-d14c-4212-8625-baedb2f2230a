"use client";

import { ChevronDown } from "lucide-react";
import { useState } from "react";

import { SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
import { NavigationItem } from "@/types";
import { cn } from "@/lib/utils";
import { Link } from "@/i18n/routing";

export const SidebarItem = ({
  item,
  isActive,
}: {
  item: NavigationItem;
  isActive: (href: string) => boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { setOpenMobile } = useSidebar();
  const href = item.href || `/${item.id}`;
  const hasChildren = item.children && item.children.length > 0;
  const isCurrentlyActive = isActive(href);
  const isChildActive =
    hasChildren &&
    item.children?.some((child) => {
      const childHref = `/${child.id}`;
      return isActive(childHref);
    });

  // Keep accordion open if current route is a child
  if (isChildActive && !isOpen) {
    setIsOpen(true);
  }

  if (hasChildren) {
    return (
      <SidebarMenuItem>
        <div className="flex w-full flex-col">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className={cn(
              "text-muted-foreground flex w-full items-center gap-2 rounded-md text-sm",
              (isCurrentlyActive || isChildActive) && "font-medium",
              "hover:bg-accent hover:text-accent-foreground",
              "px-2 py-1.5",
            )}
          >
            {item.icon && <item.icon className="mr-2 h-4 w-4" />}
            <span>{item.label}</span>
            <ChevronDown
              className={cn(
                "ml-auto h-4 w-4 cursor-pointer transition-transform",
                isOpen && "rotate-180",
              )}
            />
          </button>
          {isOpen && (
            <div className="mt-1 ml-4 space-y-1">
              {item.children?.map((child) => (
                <SidebarMenuButton
                  key={child.id}
                  asChild
                  isActive={isActive(`/${child.id}`)}
                  className={cn(
                    "text-muted-foreground",
                    isActive(`/${child.id}`) && "text-foreground font-medium",
                  )}
                >
                  {/* This is a workaround to make the link work with dynamic routes. TS pops up an error but it works. */}
                  {/* @ts-expect-error */}
                  <Link href={`/${child.id}`} className="pl-6" target={child.target ?? "_self"}>
                    <span>{child.label}</span>
                  </Link>
                </SidebarMenuButton>
              ))}
            </div>
          )}
        </div>
      </SidebarMenuItem>
    );
  }

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={isCurrentlyActive}
        className={cn("text-muted-foreground", isCurrentlyActive && "text-foreground font-medium")}
      >
        <Link
          href={href as any}
          className="flex items-center justify-start"
          onClick={() => setOpenMobile(false)}
        >
          <span className="mr-2 h-4 w-4">{item.icon && <item.icon className="size-full" />}</span>
          <span className="">{item.label}</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
};
