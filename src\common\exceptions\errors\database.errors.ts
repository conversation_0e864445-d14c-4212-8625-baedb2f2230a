import { HttpException, HttpStatus } from '@nestjs/common';
import { QueryFailedError, EntityNotFoundError } from 'typeorm';

/**
 * Base class for all database-related errors
 * Provides common functionality for handling database exceptions
 */
export class DatabaseError extends HttpException {
  /**
   * Creates a new DatabaseError instance
   * @param message - The error message
   * @param status - The HTTP status code to return
   * @param originalError - The original error that caused this exception
   */
  constructor(
    message: string,
    status: HttpStatus,
    public readonly originalError?: Error,
  ) {
    super(message, status);
    this.name = 'DatabaseError';
  }

  /**
   * Ensures sensitive information is not leaked to the client
   * @returns The sanitized public message
   */
  getPublicMessage(): string {
    return this.message;
  }

  /**
   * Gets the original error for logging purposes
   * @returns The original error or this error if no original error exists
   */
  getOriginalErrorForLogging(): Error {
    return this.originalError || this;
  }

  /**
   * Gets additional context for Sentry error reporting
   * @returns An object containing error context information
   */
  getContextForSentry(): Record<string, any> {
    return {
      errorType: this.name,
      statusCode: this.getStatus(),
    };
  }
}

/**
 * Error class for when a database entity record is not found
 * Returns a 404 Not Found HTTP status code
 */
export class EntityNotFoundDbError extends DatabaseError {
  /**
   * Creates a new EntityNotFoundDbError instance
   * @param entityName - The name of the entity that wasn't found
   * @param identifiers - Optional record of identifiers used in the lookup
   * @param originalError - The original EntityNotFoundError from TypeORM
   */
  constructor(
    entityName: string,
    identifiers?: Record<string, any>,
    originalError?: EntityNotFoundError,
  ) {
    const message = `Entity ${entityName} not found`;
    super(message, HttpStatus.NOT_FOUND, originalError);
    this.name = 'EntityNotFoundDbError';
  }

  /**
   * Gets additional context for Sentry error reporting
   * @returns An object containing error context with entity name
   */
  getContextForSentry(): Record<string, any> {
    return {
      ...super.getContextForSentry(),
      entityName: this.message.split(' ')[1],
    };
  }
}

/**
 * Error class for when a duplicate entry is found in the database
 * Returns a 409 Conflict HTTP status code
 */
export class DuplicateEntryDbError extends DatabaseError {
  /**
   * Creates a new DuplicateEntryDbError instance
   * @param entityName - The name of the entity with the duplicate
   * @param field - The field that has the duplicate value
   * @param originalError - The original error from the database
   */
  constructor(entityName: string, field: string, originalError?: Error) {
    const message = `A ${entityName} with this ${field} already exists`;
    super(message, HttpStatus.CONFLICT, originalError);
    this.name = 'DuplicateEntryDbError';
  }

  /**
   * Gets additional context for Sentry error reporting
   * @returns An object containing error context with entity name and field
   */
  getContextForSentry(): Record<string, any> {
    return {
      ...super.getContextForSentry(),
      entityName: this.message.split(' ')[1],
      field: this.message.split('this ')[1].split(' already')[0],
    };
  }
}

/**
 * Error class for when a foreign key constraint fails
 * Returns a 400 Bad Request HTTP status code
 */
export class ForeignKeyDbError extends DatabaseError {
  /**
   * Creates a new ForeignKeyDbError instance
   * @param entityName - The name of the entity with the foreign key
   * @param relationName - The name of the relation that failed
   * @param originalError - The original error from the database
   */
  constructor(entityName: string, relationName: string, originalError?: Error) {
    const message = `Referenced ${relationName} in ${entityName} does not exist`;
    super(message, HttpStatus.BAD_REQUEST, originalError);
    this.name = 'ForeignKeyDbError';
  }
}

/**
 * Error class for when a database query fails - handles generic DB errors
 * Returns a 500 Internal Server Error HTTP status code
 */
export class QueryFailedDbError extends DatabaseError {
  /**
   * Creates a new QueryFailedDbError instance
   * @param originalError - The original QueryFailedError from TypeORM
   */
  constructor(originalError: QueryFailedError) {
    super(
      'Database operation failed',
      HttpStatus.INTERNAL_SERVER_ERROR,
      originalError,
    );
    this.name = 'QueryFailedDbError';
  }

  /**
   * Gets additional context for Sentry error reporting
   * @returns An object containing error context with query details
   */
  getContextForSentry(): Record<string, any> {
    const originalError = this.originalError as QueryFailedError;
    return {
      ...super.getContextForSentry(),
      query: originalError?.query,
      parameters: originalError?.parameters,
      driverError: originalError?.driverError,
    };
  }
}

/**
 * Error class for when a database transaction fails
 * Returns a 500 Internal Server Error HTTP status code
 */
export class TransactionFailedDbError extends DatabaseError {
  /**
   * Creates a new TransactionFailedDbError instance
   * @param originalError - The original error from the database
   */
  constructor(originalError?: Error) {
    super(
      'Database transaction failed',
      HttpStatus.INTERNAL_SERVER_ERROR,
      originalError,
    );
    this.name = 'TransactionFailedDbError';
  }
}

/**
 * Error class for when a connection to the database fails
 * Returns a 503 Service Unavailable HTTP status code
 */
export class ConnectionFailedDbError extends DatabaseError {
  /**
   * Creates a new ConnectionFailedDbError instance
   * @param originalError - The original error from the database connection attempt
   */
  constructor(originalError?: Error) {
    super(
      'Database connection failed',
      HttpStatus.SERVICE_UNAVAILABLE,
      originalError,
    );
    this.name = 'ConnectionFailedDbError';
  }
}

/**
 * Error class for when a check constraint is violated in the database
 * Returns a 400 Bad Request HTTP status code
 */
export class CheckViolationDbError extends DatabaseError {
  /**
   * Creates a new CheckViolationDbError instance
   * @param entity - The name of the entity with the check constraint
   * @param constraint - The name or description of the constraint that was violated
   * @param originalError - The original error from the database
   */
  constructor(entity: string, constraint: string, originalError?: Error) {
    super(`Check violation in ${entity}: ${constraint}`, 400, originalError);
    this.name = 'CheckViolationDbError';
  }
}

/**
 * Error class for when a NOT NULL constraint is violated in the database
 * Returns a 400 Bad Request HTTP status code
 */
export class NotNullViolationDbError extends DatabaseError {
  /**
   * Creates a new NotNullViolationDbError instance
   * @param entity - The name of the entity with the NOT NULL constraint
   * @param field - The field that cannot be null
   * @param originalError - The original error from the database
   */
  constructor(entity: string, field: string, originalError?: Error) {
    super(
      `Not null violation: ${field} in ${entity} cannot be null`,
      400,
      originalError,
    );
    this.name = 'NotNullViolationDbError';
  }
}

/**
 * Error class for general data exceptions in the database
 * Returns a 400 Bad Request HTTP status code
 */
export class DataExceptionDbError extends DatabaseError {
  /**
   * Creates a new DataExceptionDbError instance
   * @param detail - Details about the data exception
   * @param originalError - The original error from the database
   */
  constructor(detail: string, originalError?: Error) {
    super(`Data exception: ${detail}`, 400, originalError);
    this.name = 'DataExceptionDbError';
  }
}

/**
 * Error class for integrity constraint violations in the database
 * Returns a 400 Bad Request HTTP status code
 */
export class IntegrityConstraintViolationDbError extends DatabaseError {
  /**
   * Creates a new IntegrityConstraintViolationDbError instance
   * @param detail - Details about the integrity constraint violation
   * @param originalError - The original error from the database
   */
  constructor(detail: string, originalError?: Error) {
    super(`Integrity constraint violation: ${detail}`, 400, originalError);
    this.name = 'IntegrityConstraintViolationDbError';
  }
}

/**
 * Error class for invalid transaction states in the database
 * Returns a 500 Internal Server Error HTTP status code
 */
export class InvalidTransactionStateDbError extends DatabaseError {
  /**
   * Creates a new InvalidTransactionStateDbError instance
   * @param detail - Details about the invalid transaction state
   * @param originalError - The original error from the database
   */
  constructor(detail: string, originalError?: Error) {
    super(`Invalid transaction state: ${detail}`, 500, originalError);
    this.name = 'InvalidTransactionStateDbError';
  }
}

/**
 * Error class for serialization failures due to concurrent updates
 * Returns a 409 Conflict HTTP status code
 */
export class SerializationFailureDbError extends DatabaseError {
  /**
   * Creates a new SerializationFailureDbError instance
   * @param originalError - The original error from the database
   */
  constructor(originalError?: Error) {
    super(
      'Serialization failure: could not serialize access due to concurrent update',
      409,
      originalError,
    );
    this.name = 'SerializationFailureDbError';
  }
}

/**
 * Error class for when a deadlock is detected between concurrent transactions
 * Returns a 409 Conflict HTTP status code
 */
export class DeadlockDetectedDbError extends DatabaseError {
  /**
   * Creates a new DeadlockDetectedDbError instance
   * @param originalError - The original error from the database
   */
  constructor(originalError?: Error) {
    super(
      'Deadlock detected: concurrent transactions deadlock',
      409,
      originalError,
    );
    this.name = 'DeadlockDetectedDbError';
  }
}

/**
 * Error class for when the database has insufficient resources
 * Returns a 503 Service Unavailable HTTP status code
 */
export class InsufficientResourcesDbError extends DatabaseError {
  /**
   * Creates a new InsufficientResourcesDbError instance
   * @param detail - Details about the resource insufficiency
   * @param originalError - The original error from the database
   */
  constructor(detail: string, originalError?: Error) {
    super(`Insufficient resources: ${detail}`, 503, originalError);
    this.name = 'InsufficientResourcesDbError';
  }
}

/**
 * Error class for SQL syntax errors
 * Returns a 400 Bad Request HTTP status code
 */
export class SyntaxErrorDbError extends DatabaseError {
  /**
   * Creates a new SyntaxErrorDbError instance
   * @param detail - Details about the syntax error
   * @param originalError - The original error from the database
   */
  constructor(detail: string, originalError?: Error) {
    super(`SQL syntax error: ${detail}`, 400, originalError);
    this.name = 'SyntaxErrorDbError';
  }
}

/**
 * Error class for invalid column references in SQL queries
 * Returns a 400 Bad Request HTTP status code
 */
export class InvalidColumnReferenceDbError extends DatabaseError {
  /**
   * Creates a new InvalidColumnReferenceDbError instance
   * @param column - The name of the invalid column
   * @param originalError - The original error from the database
   */
  constructor(column: string, originalError?: Error) {
    super(`Invalid column reference: ${column}`, 400, originalError);
    this.name = 'InvalidColumnReferenceDbError';
  }
}

/**
 * Error class for unique constraint violations in the database
 * Extends DuplicateEntryDbError for backward compatibility
 * Returns a 409 Conflict HTTP status code
 */
export class UniqueViolationDbError extends DuplicateEntryDbError {
  /**
   * Creates a new UniqueViolationDbError instance
   * @param entity - The name of the entity with the unique constraint
   * @param field - The field that must be unique
   * @param originalError - The original error from the database
   */
  constructor(entity: string, field: string, originalError?: Error) {
    super(entity, field, originalError);
    this.name = 'UniqueViolationDbError';
  }
}
