---
type: "agent_requested"
description: "Internationalization guidelines when redacting copy in the website."
---

# Internationalization (i18n) Implementation Ruleset

## 1. Configuration Structure

### A. Routing Configuration

```typescript
// src/i18n/routing.ts
export const routing = defineRouting({
  locales: ["fr", "en"],
  localePrefix: "as-needed",
  defaultLocale: "fr",
  pathnames: {
    // Route definitions
  },
});
```

### B. Required Properties

1. **Locale Definition**

   - Must define supported locales array
   - Must specify default locale
   - Must use locale prefix strategy

2. **Route Mapping**
   ```typescript
   pathnames: {
     "/route-path": {
       en: "/english-path",
       fr: "/french-path"
     }
   }
   ```

## 2. Translation File Structure

### A. File Organization

```
public/locales/
├── en/
│   └── common.json
└── fr/
    └── common.json
```

### B. JSON Structure Rules

1. **Namespace Hierarchy**

   ```json
   {
     "core": {
       "action": "value"
     },
     "components": {
       "component-name": {
         "key": "value"
       }
     }
   }
   ```

2. **Key Naming Convention**
   - Use lowercase with hyphens
   - Use descriptive namespaces
   - Follow consistent hierarchy

## 3. Implementation Rules

### A. Component Level

1. **Hook Usage**

   ```typescript
   // Client Components
   const t = useTranslations("namespace.path");

   // Server Components
   const t = await getTranslations("namespace.path");
   ```

2. **Required Patterns**
   - Must specify namespace
   - Must use type-safe keys
   - Must handle dynamic content

### B. Navigation

1. **Link Component Usage**

   ```typescript
   import { Link } from "@/i18n/routing";

   <Link href="/path">Content</Link>
   ```

2. **Router Usage**
   ```typescript
   const router = useRouter();
   router.push("/path");
   ```

## 4. Translation Content Rules

### A. Text Structure

1. **Basic Text**

   ```json
   {
     "key": "Simple text",
     "with_params": "Text with {param}"
   }
   ```

2. **Pluralization**
   ```json
   {
     "items": {
       "one": "One item",
       "other": "{count} items"
     }
   }
   ```

### B. Namespace Organization

1. **Core Elements**

   ```json
   {
     "core": {
       "actions": {},
       "labels": {},
       "messages": {}
     }
   }
   ```

2. **Component-specific**
   ```json
   {
     "components": {
       "component-name": {
         "labels": {},
         "actions": {},
         "errors": {}
       }
     }
   }
   ```

## 5. Type Safety Rules

### A. Type Definitions

```typescript
export type Pathnames = keyof typeof routing.pathnames;
export type Locale = (typeof routing.locales)[number];
```

### B. Usage Requirements

1. **Path Types**

   - Must use typed path constants
   - Must validate dynamic paths
   - Must handle locale prefixes

2. **Translation Keys**
   - Must use type-safe translation keys
   - Must validate parameters
   - Must handle missing translations

## 6. Request Handling

### A. Configuration

```typescript
export default getRequestConfig(async ({ requestLocale }) => {
  // Locale validation
  // Message loading
  return {
    locale,
    messages,
  };
});
```

### B. Implementation Rules

1. **Locale Detection**

   - Must validate requested locale
   - Must fallback to default locale
   - Must handle invalid locales

2. **Message Loading**
   - Must use dynamic imports
   - Must handle loading errors
   - Must cache messages when possible

## 7. Best Practices

### A. Performance

1. **Message Loading**

   - Use dynamic imports
   - Implement proper caching
   - Optimize bundle size

2. **Component Rendering**
   - Memoize translation functions
   - Avoid unnecessary re-renders
   - Use proper key management

### B. Maintenance

1. **Translation Management**

   - Keep translations organized
   - Document all namespaces
   - Maintain consistent structure

2. **Version Control**
   - Track translation changes
   - Document breaking changes
   - Maintain changelog

## 8. Error Handling

### A. Missing Translations

1. **Fallback Strategy**

   - Must provide fallback text
   - Must log missing translations
   - Must handle runtime errors

2. **Development Checks**
   - Must validate translation files
   - Must check for missing keys
   - Must verify parameter usage
