import { cn } from "@/lib/utils";
import { LayoutField as LayoutFieldType } from "../../types";
import { FieldComponent } from "./field-component";

interface LayoutFieldProps {
  field: LayoutFieldType;
}

export const LayoutField = ({ field }: LayoutFieldProps) => {
  const layout = field.meta.layout;

  const layoutClasses = cn(
    // Base styles
    "w-full",
    // Use className directly for grid/flex layouts
    layout.className,
    // Add field meta className if it exists
    field.meta.className,
  );

  return (
    <div className={layoutClasses}>
      {layout.fields.map((nestedField) => (
        <div
          key={nestedField.id}
          className={cn("flex w-full flex-col", nestedField.meta?.className)}
        >
          <FieldComponent field={nestedField} />
        </div>
      ))}
    </div>
  );
};
