"use client";

import { format } from "date-fns";
import { Briefcase, CalendarSync, Dot } from "lucide-react";
import { useTranslations } from "next-intl";

import { CalendarEvent } from "@/hooks/store/calendar";
import formatHours from "@/lib/calendar/formatHour";
import CalendarEditStatus from "./EditStatus";

type EventListItemProps = {
  event: CalendarEvent;
};

const iconColor = "#848585";

export default function EventListItem({ event }: EventListItemProps) {
  const t = useTranslations("calendar");
  const { start, end } = formatHours(event.begin as string, event.end as string);

  return (
    <div key={event.id} className="mb-4 flex flex-col">
      <div className="flex gap-4">
        <div className="relative h-36 w-60 rounded-lg bg-gray-200">
          <div className="absolute left-3 top-3 flex size-14 flex-col items-center justify-center rounded-md bg-white">
            <span className="text-2xl font-bold">{format(new Date(event.begin), "d")}</span>
            <span className="text-sm font-medium uppercase">
              {format(new Date(event.begin), "MMM")}
            </span>
          </div>
        </div>
        <div className="flex flex-col">
          <div className="flex items-center gap-2 text-sm font-extrabold text-[#848484]">
            {start}-{end}
            <Dot />
            {event.mainSpeaker}
          </div>
          <div className="text-2xl font-semibold">{event.title}</div>
          <div className="flex items-center gap-2">
            <Briefcase color={iconColor} />
            <span className="text-sm font-medium">{t(`event-types.${event?.type}`)}</span>
            <Dot />
            <CalendarSync color={iconColor} />
            <span className="text-sm font-medium">{event.recurrence}</span>
          </div>
          <CalendarEditStatus event={event} />
        </div>
      </div>
    </div>
  );
}
