{"core": {"cancel": "Cancel", "confirm": "Confirm", "edit": "Edit"}, "subscription": {"details": {"title": "Payment details", "started": "Started", "last-payment": "Last Payment", "next-payment": "Next Payment", "expires": "Expires On", "amount": "Amount", "change-payment-method": "Change Payment Method", "cancel": "Cancel Payment"}, "status": {"active": "Active", "in trial": "Trial", "non_renewing": "Non-Renewing", "cancelled": "Cancelled", "past_due": "Past Due", "unpaid": "Unpaid", "pending": "Pending", "default": "Unknown"}, "invoices": {"title": "Invoices", "no-invoices": "No invoices found", "select-year": "Select Year", "all-years": "All Years", "invoice-number": "Invoice Number", "download": "Download", "status": {"paid": "Paid", "pending": "Pending", "failed": "Failed", "refunded": "Refunded"}}, "payment-method": {"title": "Payment Method", "no-payment-method": "No payment method found", "credit-card": "Credit Card", "expires": "Expires", "unknown": "Unknown", "expired": "Expired"}, "cancel": {"title": "Cancel payment plan", "description": "Are you sure you want to cancel your payment plan? This action cannot be undone.", "warning": "Your payment plan will be cancelled immediately. You will lose access to all payment plan benefits at the end of your current billing period.", "error": "An error occurred while cancelling your payment plan. Please try again later.", "confirm-button": "Yes, Cancel payment plan", "cancel-button": "No, Keep payment plan", "cancelling": "Cancelling..."}}, "impersonation": {"errors": {"empty_email": "Please enter an email address", "invalid_email": "Please enter a valid email address", "cannot_impersonate_admin": "Cannot impersonate admin users"}, "started": "Now impersonating {email}", "stopped": "Impersonation stopped", "status": {"title": "User Access Status", "email": "Email", "pxs_access": "PXS Access", "pxl_access": "PXL Access", "pom_access": "POM Access", "is_paradox": "Paradox Admin", "pxs_session": "PXS Session", "loading": "Loading...", "yes": "Yes", "no": "No"}, "indicator": {"active": "Impersonation Active", "message": "You ({originalEmail}) are currently viewing as {impersonatedEmail}"}, "currently_impersonating": "Currently impersonating", "your_email": "Your email", "stop": "Stop Impersonation", "user_email_label": "User Email", "user_email_placeholder": "Enter user email to impersonate", "start": "Start Impersonation", "close": "Close"}, "components": {"data-table": {"search": "Search", "selected": "selected", "clear": "Clear"}, "tree-view": {"search_placeholder": "Search...", "selection_text": "selected", "expand_all": "Expand All", "collapse_all": "Collapse All", "checkbox_labels": {"check": "Check", "uncheck": "Uncheck"}}, "badge": {"new": "New", "locked": "Locked"}, "toolCard": {"accessibleWithCourse": "Tool accessible with the course"}, "multi-step-modal": {"required_fields_indicator": "This section contains required fields", "section_error_count": "({count} error{s})", "next": "Next", "previous": "Previous", "complete": "Complete", "reset": "Reset", "defaults": {"title": "Please fill in the required fields", "description": "Some fields are required and must be filled in to continue", "confirm": "Continue", "cancel": "Cancel"}, "image-upload": {"drag_or_click": "Drag and drop an image here or click to select one", "drop_here": "Drop the image here", "uploading": "Uploading...", "error": "Upload error", "success": "Image uploaded successfully", "remove": "Remove image", "requirements": "Accepted formats: JPG, PNG. Max size: 5MB", "aspect_ratio": "Image must have a {ratio} aspect ratio"}, "badge": {"new": "New"}, "toolCard": {"accessibleWithCourse": "Tool accessible with the course"}}}, "modal": {"confirm": {"close": {"title": "Unsaved Changes", "description": "You have unsaved changes. Are you sure you want to close this dialog? All changes will be lost.", "confirm": "Close", "cancel": "Continue editing"}, "reset": {"title": "Reset Form", "description": "Are you sure you want to reset the form? All changes will be lost.", "confirm": "Reset", "cancel": "Cancel"}}}, "validation": {"required": "This field is required", "min": "Must be at least {min} characters", "max": "Must be at most {max} characters", "email": "Must be a valid email address", "url": "Must be a valid URL", "number": "Must be a valid number", "integer": "Must be a valid integer", "positive": "Must be a positive number", "negative": "Must be a negative number", "date": "Must be a valid date", "min_date": "Must be after {min}", "max_date": "Must be before {max}", "min_number": "Must be greater than {min}", "max_number": "Must be less than {max}", "matches": "Must match {field}", "not_matches": "Must not match {field}", "different": "Fields do not match", "same": "Fields must match", "errors_found": "Please fix the errors below to continue", "errors_in_steps": "Additional errors found in steps: {steps}", "name": {"min": "Name must be at least 3 characters", "max": "Name must be less than 100 characters"}, "description": {"min": "Description must be at least 10 characters", "max": "Description must be less than 1000 characters"}, "theme": {"min": "Select at least one theme"}, "image": {"size": "Image must be less than {size}MB"}, "common": {"no_results": "No results found"}}, "navigation": {"sidebar": {"my-profile": "My Profile"}, "communities": {"title": "Community", "title_plural": "Communities", "pxl": "PXL", "pxs": "PXS"}, "cohorts": {"title": "Cohorts"}, "practice": {"title": "Practice", "stats": {"title": "Practice Stats"}}, "goliaths": "Goliaths", "faq": "FAQ", "sign-in": "Sign in", "sign-up": "Sign up", "logout": "Logout", "dashboard": "Home", "calendars": "Calendars", "courses": "Products", "objectives": "My Objectives", "messages": "Messaging", "users": "User Management", "products": {"programs": "Programs", "courses": "Courses", "events": "Events", "cohorts": "Cohorts"}, "user-management": {"users": "Users", "roles": "Roles"}}, "cohorts": {"details": {"title": "Cohort Details"}, "tabs": {"information": "Information", "students": "Students", "offers": "Offers"}, "information": {"duration_days": "{count} days", "start_date": "Start Date", "end_date": "End Date", "duration": "Duration", "status": "Status", "description": "Description", "details": "Details", "course": "Course", "course_name": "Course Name", "participants": "Participants", "participants_count": "{current} / {max} participants", "not_available": "Not available"}, "details-page": {"title": "Cohort", "plural-title": "Cohorts", "tabs": {"informations": "Information", "students": "Students", "offers": "Offers"}, "info": {"details-title": "Details", "content-title": "Content", "start": "Start", "end": "End", "duration": "Duration", "duration_days": "{count} days", "status": "Status", "seats": "Seats", "score": "Score", "description": "Description", "course": "Course", "course_name": "Course Name", "participants": "Participants", "participants_count": "{current} / {max} participants"}}, "title": "Cohorts", "created": "Cohort created", "created-description": "The cohort has been created successfully", "updated": "Cohort updated", "updated-description": "The cohort has been updated successfully", "edit": "Edit Cohort", "edit-submit": "Save Changes", "error": "Error", "error-description": "An error occurred while creating the cohort", "description": "Description", "description_description": "Enter the description of the cohort", "description_validation": "The description is required", "maxParticipants": "Max Participants", "maxParticipants_description": "Enter the maximum number of participants", "maxParticipants_validation": "The maximum number of participants is required", "startDate": "Start Date", "startDate_description": "Enter the start date of the cohort", "endDate": "End Date", "endDate_description": "Enter the end date of the cohort", "communityInfo": "Communities", "communityInfo_description": "Select the communities of the cohort", "name": "Name", "name_description": "Enter the name of the cohort", "name_validation": "The name is required", "create": "Create a cohort", "errors": {"no-access": "You do not have access to this page", "no-results": "No cohorts found", "loading-failed": "An error occurred while loading the data"}, "table": {"title": "Cohorts", "name": "Name", "start_date": "Start Date", "end_date": "End Date", "max_participants": "Slots", "current_participants": "Students", "status": {"self": "Status", "active": "Active", "draft": "Draft", "completed": "Completed", "cancelled": "Cancelled"}, "no_results": "No cohorts found", "actions": {"header": "Actions", "view_details": "View details for {name}"}}, "status": {"upcoming": "Upcoming", "running": "Running", "passed": "Passed"}, "actions": {"see_calendar": "See Calendar", "edit": "Edit", "add_student": "Add Student", "view_course": "View Course"}, "students": {"title": "Students", "name": "Name", "email": "Email", "status": "Status", "joined_at": "Joined At", "actions": "Actions", "view_details": "View details for {name}", "open_menu": "Open menu for {name}", "no_results": "No students found", "add_title": "Add Students to Cohort", "add_submit": "Add Students", "select_label": "Select Students", "select_placeholder": "Search and select students...", "select_description": "Select one or more students to add to this cohort", "select_description_with_limit": "Select one or more students to add to this cohort. Currently {current}/{max} students ({remaining} slots remaining)", "max_participants_exceeded": "Cannot add more students. The cohort has reached its maximum capacity of {max} participants.", "error_title": "Error Adding Students", "error_description": "An error occurred while adding students to the cohort", "add_success_title": "Students Added", "add_success_description": "Students have been added to the cohort successfully", "remove_success_title": "Students Removed", "remove_success_description": "Students have been removed from the cohort successfully", "remove_error_title": "Error Removing Students", "remove_error_description": "An error occurred while removing students from the cohort", "remove": "Remove Student", "remove_title": "Remove Students", "remove_description_single": "Are you sure you want to remove {name} from this cohort?", "remove_description_multiple": "Are you sure you want to remove {count} students from this cohort?", "remove_confirm": "Remove", "remove_cancel": "Cancel", "removing": "Removing...", "selected": "students selected", "all": "all", "select_all": "Select All", "clear_selection": "Clear Selection", "move_to_cohort": "Move to Cohort", "remove_from_cohort": "Remove", "move_title": "Move Students", "move_description": "Move {count} students to another cohort", "move_description_single": "Move {name} to another cohort", "target_cohort": "Target Cohort", "select_cohort_placeholder": "Select a cohort", "select_target_cohort": "Please select a target cohort", "move_confirm": "Move", "moving": "Moving...", "moved_title": "Students Moved", "moved_description": "Students have been moved successfully", "move_error": "An error occurred while moving students", "cancel": "Cancel", "current_capacity": "Current capacity: {current}/{max} students ({remaining} slots remaining)"}, "offers": {"title": "Offers", "name": "Name", "id": "ID", "status": "Status", "courses": "Courses", "created_at": "Created At", "no_results": "No offers found", "status_active": "Active", "status_inactive": "Inactive", "status_draft": "Draft", "status_archived": "Archived"}, "filters": {"status": {"label": "Status", "options": {"active": "Active", "past": "Past", "future": "Future"}}}}, "locale-switcher": {"locale": "{locale, select, fr {Français} en {English} other {Unknown}}"}, "gestion": {"users": {"filter-by-role": "Filter by role", "filters": "Filters", "selected": "{count} selected", "errors": {"no-access": "You do not have access to this page", "no-results": "No users are part of this list", "loading-failed": "An error occurred while loading the data"}, "bulk-update-role": "Update role", "bulk-update-role-error": "Please select a role", "bulk-update-role-error-description": "The role is required", "bulk-update-role-success": "Role updated", "bulk-update-role-success-description": "The role has been updated successfully", "created-at": "Created at", "updated-at": "Updated at", "score": "Score", "title": "Users", "confirmation-title": "Update role", "confirmation-description": "Are you sure you want to update the user role?", "update": "Update", "cancel": "Cancel", "profile": "Profile", "authorizations": "Authorizations", "status": "Status", "actions-header": "Actions", "name": "Name", "members": "Members", "permissions": "Permissions", "role": "Role", "edit": "Edit", "delete": "Delete", "duplicate": "Duplicate", "updated": "Updated", "updated-description": "The user has been updated successfully", "information": {"title": "Informations", "role": "Role", "profile": "Profile", "job": "Job", "description": "Description", "date-creation": "Creation date"}, "activities": {"title": "Activities", "score-nps": "Score NPS", "cohorts-management": "Cohortes Management", "last-connection": "Last Connection", "last-courses": "Last courses"}, "authorization": {"title": "Authorizations", "courses": "Gestion des cours", "students": "Gestion des étudiants", "contents": "Gestion des contenus"}, "profiles": {"create": "Create a new profile"}, "user": {"modal-title": "Edit user", "role": "Role", "firstName": "First name", "lastName": "Last name", "email": "Email", "save": "Save", "saving": "Saving...", "error": "Error", "error-description": "An error occurred while updating the user", "updated": "Updated", "updated-description": "The user has been updated successfully"}, "roles": {"created": "Role created", "created-description": "The role has been created successfully", "group": "Group", "group_description": "Select the group of the role", "group_validation": "The group is required", "name": "Name", "name_description": "Enter the name of the role", "name_validation": "The name is required", "members": "Members", "permissions": "Permissions", "roles": "Roles", "create": "Create a new role", "update": "Update", "cancel": "Cancel", "edit": "Edit", "add_permissions": "Add permissions", "error": "Error", "error-delete-description": "An error occurred while deleting the role", "duplicate": "Duplicate", "delete": "Delete", "delete-title": "Delete a role", "delete-description": "Are you sure you want to delete this role?", "deleted": "Role deleted", "deleted-description": "The role has been deleted successfully", "edit_permissions": "Edit permissions", "duplicate_role": "Duplicate role", "updated": "Role updated", "updated-description": "The role has been updated successfully", "no-results": "No roles found", "filter-by-group": "Filter by group", "filter-by-role": "Filter by role", "filters": "Filters", "all-groups": "All groups", "all-roles": "All roles"}, "actions": {"create": "Add a team"}, "filter": {"roles": {"placeholder": "Filter by role", "all": "All roles"}, "team": {"placeholder": "Filter by team", "all": "All teams"}}}, "permissions": {"users": "User Management", "user:create": "Create a user", "user:read": "Read a user", "user:update": "Update a user", "user:delete": "Delete a user", "roles": "Roles Management", "role:create": "Create a role", "role:read": "Read a role", "role:update": "Update a role", "role:delete": "Delete a role", "courses": "Courses Management", "course:create": "Create a course", "course:read": "Read a course", "course:update": "Update a course", "course:archive": "Archive a course", "cohorts": "Cohorts Management", "cohort:create": "Create a cohort", "cohort:read": "Read a cohort", "cohort:update": "Update a cohort", "cohort:delete": "Delete a cohort", "whats-new": "\"What's New\" Management", "whats-new:create": "Create a \"What's New\" version", "whats-new:read": "Read a \"What's New\" version", "whats-new:update": "Update a \"What's New\" version", "whats-new:delete": "Delete a \"What's New\" version", "whats-new:upload": "Upload a \"What's New\" version", "whats-new:react": "React to a \"What's New\" version"}}, "table": {"loading": "Loading...", "no-data": "No data found", "no-results": "No results found", "of": "of", "rows-selected": "rows selected", "select-all": "Select all", "deselect-all": "Deselect all", "page": "Page", "goto-first": "Go to first page", "goto-last": "Go to last page", "goto-prev": "Go to previous page", "goto-next": "Go to next page", "search": "Search", "view_details": "View details of {name}"}, "calendar": {"title": "Calendars", "sub-title": "Manage your schedule", "today": "Today", "day": "Day", "week": "Week", "month": "Month", "suggest-a-slot": "Suggest a slot", "new-slot": "New slot", "what-is-this-event": "What is this event?", "type-of-event": "Type of event", "event-types": {"mentoring": "Mentoring", "masterclass": "Masterclass", "supervision": "Supervision", "guided-triad": "Guided triad", "customized-slot": "Customized slot"}, "date-and-time": "Date and time", "time-zone": "Time zone", "duration": "Duration", "recurrence": "Recurrence", "replay": "Replay", "who-are-the-speakers": "Who are the speakers?", "main-speaker": "Main speaker", "other-speakers": "Other speakers", "technical-support": "Technical support", "what-kind-of-audience": "What kind of audience is involved?", "cohort": "Cohort", "participants": "Number of participants", "program": "Program", "course": "Course", "module": "<PERSON><PERSON><PERSON>", "any-comment": "Any comment?", "comments": "Comments", "add-slot": "Add slot", "edit-slot": "Edit slot", "all-events": "All events", "upcoming-events": "Upcoming", "past-events": "Past"}, "dashboard": {"title": "Home", "sub-title": "Dashboard of your personal space", "purchases": {"unknown": "Unavailable course", "card": {"action": {"purchase": "Discover the course", "consume": "Launch the course"}}, "status": {"active": "Active", "paid": "Paid", "pending": "Pending", "voided": "Voided", "not_paid": "Not paid", "posted": "Posted", "stopped": "Stopped", "overdue": "Overdue", "in_trial": "In trial", "granted": "Active", "revoked": "Revoked", "suspended": "Suspended", "error": "In error"}, "title": "My purchased training courses", "empty-state": "You don't have any courses for the moment.", "inactive": "Inactive payments", "sectionSubtitle": "Access your purchased courses. <em>*what has been offered to you will not appear here.</em>", "sectionActionLabel": "See all my payments", "see-more": "See more"}, "courses": {"title": "All courses", "sectionSubtitle": "Discover all Paradox courses.", "empty-state": "No courses found"}, "eventsSection": {"title": "Upcoming Events", "subtitle": "Don't miss out on our latest events.", "sampleCard": {"description": "Join us for an exclusive event to boost your skills and network with professionals.", "details": {"improveConfidence": "Improve your confidence", "noFearOfJudgment": "No fear of judgment", "focusOnStrengths": "Focus on your strengths", "organizeBetter": "Organize better", "manageTimeEffectively": "Manage time effectively", "unleashPotential": "Unleash your potential"}, "actionButtonLabel": "Learn More"}}, "heroCard": {"title": "Welcome {firstname} to Paradox", "subtitle": "The place where you shape the person you aspire to be.", "actionButtonLabel": "Discover our training courses"}, "wallOfDreams": {"title": "SHARE YOUR DREAM", "subtitle": "Share your dream by recording your video testimony to be featured on the wall of 100,000 dreams.", "buttonWriteStory": "Write your own story", "buttonDiscoverStories": "Discover their story"}}, "my-courses": {"title": "Products", "sub-title": "Access your products and pedagogical resources"}, "my-objectives": {"title": "My objectives", "sub-title": "Follow your objectives and track your progress"}, "messages": {"title": "Messages", "new-message": "New message", "online": "Online", "offline": "Offline", "type-message": "Type a message..."}, "courses": {"title": "Courses", "search": {"placeholder": "Search for a course..."}, "filters": {"button": "Filter", "status": {"label": "Status", "placeholder": "Select a status"}, "theme": {"label": "Theme", "placeholder": "Select theme"}}, "status": {"published": "Published", "draft": "Draft", "archived": "Archived"}, "offer_status": {"active": "Active", "disabled": "Disabled"}, "themes": {"MATHEMATICS": "Mathematics", "PHYSICS": "Physics", "CHEMISTRY": "Chemistry", "BIOLOGY": "Biology", "COMPUTER_SCIENCE": "Computer Science", "LITERATURE": "Literature", "HISTORY": "History", "GEOGRAPHY": "Geography", "ECONOMICS": "Economics", "PHILOSOPHY": "Philosophy", "ART": "Art", "MUSIC": "Music", "SPORTS": "Sports", "LANGUAGES": "Languages", "programming": "Programming", "design": "Design", "business": "Business", "marketing": "Marketing", "management": "Management", "leadership": "Leadership", "communication": "Communication", "productivity": "Productivity"}, "create": {"button": "Create Course", "title": "Create New Course", "submit": {"publish": "Publish", "create": "Create", "draft": "Save as draft"}, "success": {"title": "Course Created", "description": "The course has been created successfully"}, "error": {"title": "Error Creating Course", "description": "An error occurred while creating the course"}, "image": {"drag_or_click": "Drag and drop an image here or click to select one", "drop_here": "Drop the image here", "uploading": "Uploading...", "error": "Upload error", "success": "Image uploaded successfully", "remove": "Remove image", "requirements": "Accepted formats: JPG, PNG. Max size: 5MB"}}, "fields": {"name": "Name", "description": "Description", "theme": "Theme", "theme_placeholder": "Select theme", "card_image": "Card Image (9:16)", "cover_image": "Cover Image (2:1)", "thumbnail_image": "<PERSON><PERSON><PERSON><PERSON> (1:1)", "offers": "Offers", "offers_placeholder": "Select offers", "offers_description": "Select the offers that will include this course"}, "validation": {"name": {"min": "Name must be at least 3 characters", "max": "Name must be less than 100 characters"}, "description": {"min": "Description must be at least 10 characters", "max": "Description must be less than 1000 characters"}, "theme": {"min": "Select at least one theme"}, "image": {"required": "Image is required", "size": "Image must be less than {size}MB", "url": "Image URL is not valid", "type": "Image must be one of these types: {types}", "aspect_ratio": "Image must have a {ratio} aspect ratio"}, "url": "URL is not valid"}, "table": {"select_all": "Select all courses", "select_row": "Select course {id}", "name": "Name", "description": "Description", "no_description": "No description available", "status": "Status", "theme": "Theme", "created_at": "Created At", "no_results": "No courses found", "rows_per_page": "Rows per page", "page_x_of_y": "Page {current} of {total}", "first_page": "Go to first page", "previous_page": "Go to previous page", "next_page": "Go to next page", "last_page": "Go to last page", "open_menu": "Open menu for {name}", "view_details": "View details of {name}", "actions": {"header": "Actions", "edit": "Edit", "publish": "Publish", "unpublish": "Unpublish", "delete": "Delete"}}, "delete": {"title": "Delete Course", "description": "Are you sure you want to delete {name}? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete"}, "actions": {"delete": {"confirm": {"title": "Delete Course", "description": "Are you sure you want to delete {name}? This action cannot be undone.", "action": "Delete"}, "success": "Course deleted successfully", "error": "Failed to delete course"}, "publish": {"label": "Publish", "success": "Course published successfully", "error": "Failed to publish course"}, "unpublish": {"success": "Course unpublished successfully", "error": "Failed to unpublish course"}, "archive": "Archive", "duplicate": "Duplicate", "edit": "Edit course"}, "details": {"title": "Course Details", "not_found": "Course not found", "general_info": "Information", "visuals": "Visuals", "name": "Name", "description": "Description", "banner": "Banner Image", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "card": "Card Image", "no_banner": "No banner image", "no_thumbnail": "No thumbnail image", "no_card": "No card image", "students": {"title": "Students", "no_results": "No students found", "name": "Name", "joined_at": "Joined At", "cohort": "Cohort", "actions": "Actions", "view_details": "View details for {name}", "view_cohort": "View cohort {name}"}, "offers": {"title": "Offers", "no_results": "No offers found", "name": "Name", "status": "Status", "image": "Image", "created_at": "Created At"}, "cohorts": {"title": "Cohorts", "create": "Create Cohort", "name": "Name", "start_date": "Start Date", "end_date": "End Date", "status": {"self": "Status", "active": "Active", "inactive": "Inactive", "completed": "Completed"}, "no_results": "No cohorts found", "search": "Search cohorts..."}, "tabs": {"information": "Information", "students": "Students", "offers": "Offers", "cohorts": "Cohorts"}}, "edit": {"title": "Edit Course", "submit": {"save": "Save Changes"}, "success": {"title": "Course Updated", "description": "The course has been updated successfully"}, "error": {"title": "Error Updating Course", "description": "An error occurred while updating the course"}}}, "products": {"programs": {"title": "Programs", "filter": {"programs": {"placeholder": "Filter by program", "all": "All programs"}}, "actions": {"create": "Create program", "edit": "Edit", "add_course": "Add a course", "duplicate": "Duplicate", "delete": "Delete", "edit_program": "Edit program", "add_courses": "Add courses"}, "tabs": {"active": "Active", "inactive": "Inactive"}, "status": {"active": "Active", "inactive": "Inactive"}, "modal": {"create": {"title": "Create New Program", "steps": {"basic": {"title": "Basic Information", "description": "Enter the basic information about your program", "fields": {"name": {"label": "Program Name", "description": "The name that will be displayed to users", "placeholder": "Enter program name"}, "description": {"label": "Description", "description": "A detailed description of the program", "placeholder": "Enter program description"}, "isActive": {"label": "Active Status", "description": "Enable this to make the program visible to users"}}}, "details": {"title": "Program Details", "description": "Configure specific details about the program", "fields": {"duration": {"label": "Duration (in weeks)", "description": "The total duration of the program in weeks", "placeholder": "Enter program duration"}, "type": {"label": "Program Type", "description": "Select the type of program", "options": {"certification": "Certification", "course": "Course", "workshop": "Workshop"}}}}}}}, "detail": {"tabs": {"courses": "Courses", "students": "Students", "community": "Community", "offers": "Offers", "information": "Information"}, "courses": {"table": {"name": "Course Name", "score": "Score", "program": "Program", "description": "Description", "associatedCourse": "Associated Course"}}, "students": {"table": {"name": "Student", "score": "Score", "cohort": "Cohort", "lastConnection": "Last Connection", "creationDate": "Creation Date", "lastCourses": "Last Courses", "student": "student", "students": "students"}}, "offers": {"table": {"name": "Offer Name", "id": "ID", "status": "Status", "programs": "Programs", "courses": "Courses", "created_at": "Created At", "offer": "offer", "offers": "offers", "active": "Active", "inactive": "Inactive"}}}}}, "profile": {"title": "Profile", "sub-title": "Manage your profile information", "upload": "Change avatar", "settings": {"payments": {"title": "Payments", "sub-title": "Manage your payments"}, "payments-details": {"title": "Details", "sub-title": "Manage your payment details"}, "account": {"title": "Account", "sub-title": "Manage your account information", "form": {"modify": "Modify", "save": "Save", "no-phone": "No phone number", "no-email": "No email address", "success": "Account information updated successfully", "firstName": {"label": "First Name", "placeholder": "Enter your first name"}, "lastName": {"label": "Last Name", "placeholder": "Enter your last name"}, "email": {"label": "Email", "placeholder": "Enter your email address"}, "phone": {"cancel": "Cancel", "label": "Phone", "placeholder": "Enter your phone number", "submit": "Change phone number", "unlink": "Unlink phone number", "save": "Save phone number"}, "password": {"label": "Password", "placeholder": "Enter your password", "current-password": "Current password", "new-password": "New password", "confirm-password": "Confirm new password", "submit": "Change password", "updated": "Password updated successfully", "cancel": "Cancel", "error": {"passwords-not-match": "New password and confirm password do not match", "form_password_validation_failed": "Current password is incorrect", "form_password_length_too_short": "Password must be at least 8 characters", "form_password_size_in_bytes_exceeded": "Password is too long (maximum is 72 characters)", "form_password_pwned": "The new password is too common", "cannot-update": "Cannot update password"}}, "billingAddress": {"label": "Billing Address", "placeholder": "Enter your address"}}, "info": {"one": {"title": "Why isn't my information displayed here?", "description": "We hide some details of your account to protect your identity."}, "two": {"title": "What information can be modified?", "description": "At Paradox, the ability to modify your personal information is essential to ensuring an optimal user experience. Your profile management is updated to meet your needs and to ensure that all data entered is up-to-date and correct. We offer you the flexibility to modify your information so that you can adapt your profile to your new requirements without having to contact support."}}}, "whats-new": {"title": "What's New", "sub-title": "Latest updates"}, "faq": {"title": "FAQ", "sub-title": "Frequently Asked Questions", "pxs": "PXS", "pxl": "PXL", "goliaths": "Goliaths"}, "courses": {"title": "Courses", "titleId": "Course {id}", "sub-title": "Manage your courses"}, "cohorts": {"title": "Cohorts", "titleId": "Cohort {id}", "sub-title": "Manage your cohorts", "no-cohorts": "No cohorts found", "create-cohort": "Create Cohort"}}}, "common": {"cancel": "Cancel", "search": "Search", "no_results": "No results found", "back": "Back", "filters": "Filters"}, "services": {"user": {"create": {"success": {"title": "User created", "description": "The user has been created successfully"}, "error": {"title": "Error creating user", "description": "An error occurred while creating the user"}}, "update": {"success": {"title": "User updated", "description": "The user has been updated successfully"}, "error": {"title": "Error updating user", "description": "An error occurred while updating the user"}}, "bulk_update": {"success": {"title": "Users updated", "description": "The users have been updated successfully"}, "error": {"title": "Error updating users", "description": "An error occurred while updating the users"}}, "create_phone_number": {"success": {"title": "Phone number created", "description": "The phone number has been saved successfully"}, "error": {"title": "Error creating phone number", "description": "An error occurred while creating the phone number"}}, "unlink_phone_number": {"success": {"title": "Phone number unlinked", "description": "The phone number has been unlinked successfully"}, "error": {"title": "Error unlinking phone number", "description": "An error occurred while unlinking the phone number"}}}}, "toolsSection": {"title": "My Tools", "subtitle": "Discover tools to help you on your journey.", "nav": {"goliaths": "Goliaths", "goliathsDescription": "Trade with <PERSON> on Goliaths", "chatAi": "Chat AI", "chatAiDescription": "Your virtual coach to learn and excel in certified Paradox coaching.", "testDisc": "Test DISC", "testDiscDescription": "Discover who you are through your behavioral profile, your relationship with money, and much more.", "cic": "Compound Interest Calc.", "cicDescription": "Simulate the growth of your savings with compound interest.", "ff": "Financial Freedom Calc.", "ffDescription": "Plan your path to financial independence with personalized calculations.", "practice": "Practice", "practiceDescription": "Practice coaching scenarios in a group setting."}, "requiredCourse": {"psychologyOfMoney": "Psychology of Money", "pxs": "Paradox School"}}, "product": {"modal": {"title": "Product Details", "details-title": "Details", "payments-button": "My Payments", "community-button": "Community", "courses-title": "Courses", "course-item": "Course {id}", "access-course": "Access course", "access-all-courses": "Access all courses", "default-description": "Paradox is a project founded by <PERSON>, entrepreneur and international speaker, designed to help everyone transform their dreams into concrete goals. Paradox offers training, tools, and immersive experiences to strengthen self-confidence and achieve ambitious goals. Thanks to his dynamic approach, <PERSON> has already impacted thousands of lives around the world, encouraging everyone to dare and move towards a life in accordance with their deep aspirations. You have chosen this training: get ready, it will change your life."}}, "goliaths": {"radial-chart": {"base-protective": "BASE PROTECTIVE", "controlled-growth": "CONTROLLED GROWTH", "ambitious-returns": "AMBITIOUS RETURNS"}, "followers": "Followers", "following": "Following", "trades": "Trades", "giftTitle": "a gift for you", "giftGreeting": "Hello {name},", "giftLine1": "As a member, we have a <strong>gift</strong> for you…", "giftLine2": "To mark the launch of our new program", "giftProgramName": "Psychologie de l’Argent", "giftLine2Suffix": ", Paradox partners with Goliaths to offer you:", "giftList1": "Access to the Paradox space <strong>in the Goliaths app</strong>, to invest alongside <PERSON> and the community <strong>starting from €2</strong>.", "giftList2": "{discount}% discount on your Goliaths subscription thanks to your unique code", "giftExpiration": "This coupon is valid for a limited time.", "giftCTA": "<strong>Click the button</strong> below and follow the instructions to create your Goliaths account and make <strong>your first investments</strong>!", "showCode": "Show code", "hideCode": "Hide code", "invest-with-david": "Write your own story", "products": "Paradox Products", "products-description": "These products have been created by experts in the financial markets after numerous analyses.", "strategy": "Paradox Strategy", "strategy-description": "The Barbell strategy allows you to maintain a level of risk control without closing yourself off to opportunities.", "giftCodeCopied": "Discount code copied!", "giftCodeCopiedDescription": "You can paste it during your registration.", "modal": {"errors": {"already_registered": "You probably already have a Goliaths account. Go to the app to trade with <PERSON>.", "phone_number_already_exists": "This phone number is already used by another user, please use another one.", "email_already_exists": "This email is already used by another user, please use another one.", "too_many_requests": "We are receiving a lot of requests, please try again in a few moments!", "error": "Error sending the form, please try again in a few moments."}, "success": "Go to the app to continue the registration", "title": "Join <PERSON> on Goliaths", "create-account": "Create your account in a few seconds to start investing with <PERSON> 🚀", "email": "Email", "phone": "Phone", "submit": "Create my account", "risk": "Investing carries risks", "is-pom": "As a student of the Paradox Money Psychology Program, you benefit from a special reduced access to the Paradox space in Goliaths"}}, "whats-new": {"page-title": "Paradox App - What's New?", "title": "What's New", "no-entries": "No updates available yet.", "reactions": {"sign-in-required": "Please sign in to react to this update", "be-first": "Be the first to react with {emoji}", "count": "{count} {count, plural, one {person} other {people}} reacted with {emoji}", "add-reaction": "Add reaction"}, "admin": {"add-version": "Add Version", "edit-version": "Edit Version", "create-version": "Create Version", "update-version": "Update Version", "confirm-delete": "Are you sure you want to delete this version?", "creating": "Creating...", "updating": "Updating...", "cancel": "Cancel", "next": "Next", "previous": "Previous", "required-fields": "All fields marked with * are required.", "export-image": "Export as image", "media-management": "Media Management", "upload-new-media": "Upload New Media", "browse-media": "Browse Media", "media-upload-note": "Upload images to the media gallery to be used in the \"What's New\" section.", "media-gallery": "Media Gallery", "no-media": "No media found", "copy-url": "Copy URL", "delete-media": "Delete Media", "confirm-media-delete": "Are you sure you want to delete this media?", "url-copied": "URL copied to clipboard", "url-copied-description": "The URL has been copied to clipboard", "select": "Select", "steps": {"version-info": "Version Info", "english-content": "English Content", "french-content": "French Content", "preview": "Preview"}, "date": "Release Date", "version-code": "Version Code", "fields": {"version-code": "Version Code", "version-code-placeholder": "e.g., 1.0.0", "release-date": "Release Date", "title-en": "Title (English)", "title-en-placeholder": "Enter title in English", "description-en": "Description (English)", "description-en-placeholder": "Enter description in English", "title-fr": "<PERSON><PERSON><PERSON> (Français)", "description-fr": "Description (Français)", "thumbnails": "Thumbnail Images"}, "features": {"added": "Added Features", "changed": "Changed Features", "fixed": "Fixed Issues", "removed": "Removed Features", "placeholder": "Type a feature and press Enter or click +", "thumbnails": "Thumbnail Images"}, "drag-images": "Drag and drop images here", "click-to-upload": "or click to upload", "uploading": "Uploading images...", "paste-image": "or paste from clipboard", "success": {"created": "Version created successfully", "created-description": "The version has been created successfully", "updated": "Version updated successfully", "updated-description": "The version has been updated successfully", "deleted": "Version deleted successfully", "deleted-description": "The version has been deleted successfully", "uploaded": "Images uploaded successfully", "uploaded-description": "The images have been uploaded successfully", "media-deleted": "Image deleted successfully", "media-deleted-description": "The image has been deleted successfully"}, "error": {"create": "Failed to create version", "create-description": "An error occurred while creating the version", "update": "Failed to update version", "update-description": "An error occurred while updating the version", "delete": "Failed to delete version", "delete-description": "An error occurred while deleting the version", "upload": "Failed to upload images", "upload-description": "An error occurred while uploading the images", "media-delete": "Failed to delete media"}}, "changelog": {"added": "Added", "changed": "Changed", "fixed": "Fixed", "removed": "Removed"}}, "payments": {"title": "My payments", "sub-title": "Manage your payments", "loading": "Loading...", "table": {"title": "Payments ({count})", "name": "Name", "status": {"title": "Status", "options": {"paid": "Paid", "active": "Active", "stopped": "Stopped", "paused": "Paused", "refunded": "Refunded", "overdue": "Overdue"}}, "started-at": "Start Date", "actions": {"header": "View Details", "view-details": "View Details"}}, "list": {"title": "Payments ({count})", "loading": "Loading...", "status": {"title": "Status", "options": {"paid": "Paid", "active": "Active", "stopped": "Stopped", "paused": "Paused", "refunded": "Refunded", "overdue": "Overdue"}}, "started-at": "Start Date", "no-more-results": "No more results"}, "details": {"title": "Payment {id}", "plural-title": "Payments", "started-at": "Started At", "next-payment": "Next Payment", "no-next-payment": "No payments left", "paid-amount": "<PERSON><PERSON>", "total-amount": "Total Amount", "payment-method": "Payment Method", "remaining-billing-cycles": "Remaining Billing Cycles", "download-all": "Download All", "information": "Information", "invoices": {"title": "Invoices ({count})", "table": {"number": "Number", "status": "Status", "amount": "Amount", "total": "Total", "date": "Date", "download": "Download"}, "list": {"title": "Invoices ({count})", "title-number": "Invoice# {id}", "loading": "Loading...", "no-more-results": "No more results", "amount": "Amount", "total": "Total", "status": "Status", "date": "Date"}, "filters": {"year": "Year", "year-placeholder": "Select year", "all-years": "All"}, "status": {"paid": "Paid", "active": "Active", "stopped": "Stopped", "paused": "Paused", "refunded": "Refunded", "overdue": "Overdue"}}, "errors": {"no-results": "No invoices found"}}, "errors": {"no-results": "No payments found"}}, "triad": {"group": "Group {letter}", "list": {"at": "at", "title": "Title:", "duration": "Duration:", "duration_value": "{hours}h", "participants": "Participants:", "no_participants": "-", "access": "Access:", "meeting_link": "Meeting link", "delete": "Delete", "leave": "Leave", "full": "Full", "join": "Join", "loading": "Loading..."}, "table": {"title": "Title", "availability": "Availability", "session_time": "Session Time", "duration": "Duration", "duration_value": "{hours}h", "session_type": "Type", "sessions": "Sessions", "no_session_restriction": "-", "participant2": "Participant 2", "participant3": "Participant 3", "access": "Access", "meeting_link": "Meeting link", "no_triads": "No groups yet.", "delete": "Delete", "leave": "Leave", "full": "Full", "join": "Join", "loading": "Loading...", "success": {"deleted": "Group deleted successfully", "left": "You have left the group", "joined": "You have joined the group"}, "errors": {"failed_to_delete": "Failed to delete the group", "failed_to_join": "Failed to join the group", "failed_to_leave": "Failed to leave the group", "unauthorized": "You must be logged in to perform this action", "forbidden": "You don't have permission to perform this action", "not_found": "The requested group was not found", "cannot_join_own_triad": "You cannot join your own group", "already_joined": "You have already joined this group", "triad_full": "This group is already full"}, "participants": "Participants", "no_participants": "No participants"}, "view": {"loading": "Loading groups...", "count": "GROUPS", "description": "Create or join a group to practice together", "create": "Create a new group", "search": "Search", "filters": {"period": "Period", "status": "Free seats", "status_placeholder": "Search...", "registered": "Registration status", "registered_placeholder": "Search...", "all": "All", "status_options": {"8slots": "8 spots", "7slots": "7 spots", "6slots": "6 spots", "5slots": "5 spots", "4slots": "4 spots", "3slots": "3 spots", "2slotsandmore": "2 spots and more", "2slots": "2 spots", "1slot": "1 spot", "full": "Full"}, "registered_options": {"registered": "My slots", "not_registered": "Available slots"}, "time_window": "Time window", "time_window_placeholder": "Time window", "time_window_start": "Start time", "time_window_end": "End time", "time_window_error": "End time must be after start time"}}, "errors": {"unauthorized": "You must be logged in to perform this action", "forbidden": "You don't have permission to perform this action", "not_found": "The requested group was not found", "fetch": "Failed to fetch groups. Please try again later.", "already_joined": "You have already joined this group", "not_participant": "You are not a participant in this group", "group_full": "This group is already full", "invalid_meeting_link": "Invalid meeting link format. Must be a valid Google Meet or Zoom URL", "missing_fields": "Please provide all required fields", "internal_error": "An unexpected error occurred. Please try again later", "failed_to_delete": "Failed to delete the group", "failed_to_join": "Failed to join the group", "failed_to_leave": "Failed to leave the group", "cannot_join_own_group": "You cannot join your own group", "date_in_past": "Cannot create a group in the past", "failed_to_create": "Failed to create the group", "too_many_open_triads": "You reached the maximum number of groups you can create for now. Please try again later.", "session_too_far_in_future": "Cannot schedule sessions more than {days} days in advance", "too_many_open_triads_with_limit": "You can only have {max} open group(s) within the next {days} day(s), except for groups of 2 people (currently have {current}). Join other groups to create more", "too_many_open_triads_weekly": "You've reached the weekly limit of groups you can organize for this week. Join other groups to create more.", "no_access": "You don't have access to this type of session", "no_access_short": "No access", "unknown": "An unknown error occurred", "no_triads": "No groups yet."}, "filters": {"period": "Period", "status": {"label": "Free seats", "placeholder": "Search...", "options": {"2_slots_and_more": "2 spots and more", "1_slot": "1 spot", "full": "Full"}}, "session_time": {"label": "Time window", "placeholder": "Select time window"}, "registered": "Registration status", "registered_placeholder": "Search...", "all": "All", "registered_options": {"registered": "My slots", "not_registered": "Available slots"}}, "success": {"deleted": "Group deleted successfully", "joined": "You have joined the group", "left": "You have left the group", "created": "Group created successfully"}, "session": "Session", "sessions": "Sessions", "restricted_to": "Restricted to", "no_session_restriction": "-", "session_badge": "{session}", "confirm_join": {"title": "Join Group Confirmation", "description": "You already have an upcoming group on {date}. Are you sure you want to join another group?", "confirm": "Join Anyway", "cancel": "Cancel"}, "confirm_leave": {"title": "Leave Group Confirmation", "description": "Are you sure you want to leave this group?", "note": "Please consider that your spot will be available for other participants once you leave.", "confirm": "Leave Group", "cancel": "Cancel"}, "confirm_delete": {"title": "Delete Group Confirmation", "description": "This group has {count} registered participant(s). Are you sure you want to delete it?", "note": "This action cannot be undone. Please note that other participants will not be notified of this deletion.", "confirm": "Delete Group", "cancel": "Cancel"}, "stats": {"title": "Triads Statistics", "description": "Comprehensive analytics and insights of the Triads and Practice module", "export": "Export", "tabs": {"overview": "Overview", "engagement": "Engagement", "growth": "Growth", "organizers": "Organizers"}, "time_range": {"label": "Time Range", "placeholder": "Select time range", "pick_date": "Pick a date", "options": {"day": "Daily", "week": "Weekly", "month": "Monthly", "year": "Yearly", "custom": "Custom Range"}}, "overview": {"loading": "Loading statistics...", "error": "Error loading statistics", "metrics": {"total_triads": "Total Triads", "total_participants": "Total Participants", "avg_participants": "Avg. Participants/Triad", "completion_rate": "Completion Rate"}, "charts": {"growth_trends": "Growth Trends", "participant_growth": "Participant Growth", "triads_created": "Triads Created", "participants_joined": "Participants Joined", "time_period": "Last {period}"}}, "engagement": {"loading": "Loading statistics...", "error": "Error loading statistics", "recurring_participants": {"title": "Recurring Participants", "description": "Participants who have joined more than one triad session"}, "session_duration": {"distribution": {"title": "Session Duration Distribution", "by_hours": "By Hours", "sessions": "Number of Sessions"}, "breakdown": {"title": "Session Duration Breakdown", "distribution": "Distribution", "hours": "{hours}h", "sessions": "Number of Sessions", "description": "{count} sessions with a duration of {hours}"}}}, "growth": {"loading": "Loading statistics...", "error": "Error loading statistics", "charts": {"cumulative_growth": {"title": "Cumulative Growth", "total_triads": "Total Triads", "time_period": "Last {period}"}, "monthly_growth": {"title": "Monthly Growth Rate", "subtitle": "Month over Month", "new_triads": "New Triads", "growth_rate": "Growth Rate (%)"}, "comparison": {"title": "Triads vs Participants", "subtitle": "Comparison", "new_triads": "New Triads", "new_participants": "New Participants"}}}, "time_periods": {"day": "Day", "week": "Week", "month": "Month", "year": "Year", "custom": "Custom", "last": "Last {period}"}, "organizers": {"loading": "Loading statistics...", "error": "Error loading statistics", "no_data": "No organizer data available", "top_organizers": {"title": "Top Organizers", "triads_organized": "Triads Organized"}, "leaderboard": {"title": "Organizer Leaderboard", "columns": {"rank": "Rank", "organizer": "Organizer", "triads": "Triads Organized", "contribution": "Contribution %"}}}}, "filter": {"description": "Filter triads"}, "create": {"title": "NEW GROUP", "select_type": "SELECT GROUP TYPE", "date": "Date:", "time": "Time:", "duration": "Duration (in hours):", "access": "Access:", "sessions": "Sessions", "title_field": "Title:", "max_participants": "Maximum participants:", "placeholder": {"link": "Google Meet, Zoom, etc.", "sessions": "Search sessions...", "title": "Enter a title for your group", "max_participants": "Select number of participants (max 8)"}, "cancel": "Cancel", "back": "Back", "creating": "Creating...", "create": "Create", "celebration": {"title": "Group Created Successfully!", "description": "Your group has been created and is now ready for participants to join!", "loading": "We're searching how good you are at organizing groups..."}, "stats": {"total_triads": "Groups Created", "total_participants": "Total Participants"}}, "leave": {"celebration": {"title": "Group Left Successfully!", "description": "You have left the group and are no longer a participant."}}, "join": {"celebration": {"title": "Group Joined Successfully!", "description": "You have joined the group and are now a participant.", "loading": "We're searching how good you are at joining groups..."}}, "delete": {"celebration": {"title": "Group Deleted Successfully!", "description": "Your group has been deleted and is no longer available."}}, "date_range_picker": {"select_date": "Select date range", "apply": "Apply", "clear": "Clear"}, "time_picker": {"select_time": "Select time", "hours": "Hours", "minutes": "Minutes", "local_time": "Local time"}, "time_range_picker": {"start_time": "Start time", "end_time": "End time", "select_start": "Select start time", "select_end": "Select end time", "apply": "Apply", "clear": "Clear"}, "testimonial": {"title": "Share your feedback", "rating": "How would you rate your experience?", "content": "Would you like to share more? (optional)", "content_placeholder": "Tell us about your experience...", "submit": "Submit feedback", "submitting": "Submitting...", "cancel": "Cancel", "success": "Thank you for your feedback!", "error": "Failed to submit feedback. Please try again."}, "filter-bar": {"title": "FILTERS", "button": "Filters", "reset": "Reset", "apply": "Apply"}, "availability-badge": {"full": "Full", "one-spot": "1 spot", "spots": "{count} spots"}}, "date_range_picker": {"select_date": "Select date range", "apply": "Apply", "clear": "Clear"}, "time_picker": {"select_time": "Select time", "hours": "Hours", "minutes": "Minutes", "local_time": "Local time"}, "time_range_picker": {"start_time": "Start time", "end_time": "End time", "select_start": "Select start time", "select_end": "Select end time", "apply": "Apply", "clear": "Clear"}, "actions": {"close": "Close"}}