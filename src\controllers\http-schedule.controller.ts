import {
  <PERSON>,
  All,
  Query,
  <PERSON>m,
  <PERSON><PERSON>,
  Body,
  Req,
  BadRequestException,
  NotFoundException,
  Logger,
  Get,
} from '@nestjs/common';
import { Request } from 'express';
import { HttpScheduleService } from '../use-cases/http-schedule';
import { ScheduleHttpRequestParams } from '../use-cases/http-schedule/http-schedule.types';
import { ScheduleRequestDto, ListJobsDto } from '@dtos';
import { FilteredHeaders } from '../common/decorators/filtered-headers.decorator';

@Controller('http-schedule')
export class HttpScheduleController {
  private readonly logger = new Logger(HttpScheduleController.name);

  constructor(private readonly httpScheduleService: HttpScheduleService) { }

  /**
   * List all scheduled jobs in the queue
   */
  @Get('jobs')
  async listJobs(@Query() listJobsDto: ListJobsDto) {
    try {
      return await this.httpScheduleService.listScheduledJobs(listJobsDto);
    } catch (error) {
      this.logger.error('Failed to retrieve job list:', error);
      throw new BadRequestException('Could not retrieve job list.');
    }
  }

  /**
 * Cancel a scheduled HTTP request
 */
  @All('cancel/:id')
  async cancelRequest(@Param('id') jobId: string) {
    if (!jobId) {
      throw new BadRequestException('Job ID is required');
    }

    try {
      this.logger.log(`Attempting to cancel job: ${jobId}`);

      const cancelled = await this.httpScheduleService.cancelScheduledRequest(
        jobId,
      );

      if (!cancelled) {
        throw new NotFoundException(
          'Job not found or already completed/cancelled',
        );
      }

      return {
        success: true,
        message: 'HTTP request cancelled successfully',
        data: {
          jobId,
          cancelled: true,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to cancel job ${jobId}:`, error);

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to cancel request: ${error.message}`,
      );
    }
  }

  /**
   * Schedule an HTTP request for future execution
   * Supports all HTTP methods and extracts all request data
   */
  @All()
  async scheduleRequest(
    @Query() queryDto: ScheduleRequestDto,
    @FilteredHeaders() headers: Record<string, string>,
    @Body() body: any,
    @Req() req: Request,
  ) {
    const { url, at } = queryDto;

    try {
      // Extract query parameters (excluding our control parameters)
      const queryParams = { ...req.query };
      delete queryParams.url;
      delete queryParams.at;

      // Prepare scheduling parameters
      const scheduleParams: ScheduleHttpRequestParams = {
        url,
        at,
        method: req.method,
        headers,
        body: body || undefined,
        query:
          Object.keys(queryParams).length > 0
            ? (queryParams as Record<string, string>)
            : undefined,
      };

      this.logger.log(`Scheduling ${req.method} request to ${url} at ${at}`);

      // Schedule the HTTP request
      const result = await this.httpScheduleService.scheduleHttpRequest(
        scheduleParams,
      );

      return {
        success: true,
        message: 'HTTP request scheduled successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Failed to schedule HTTP request:', error);
      throw new BadRequestException(
        `Failed to schedule request: ${error.message}`,
      );
    }
  }
}
