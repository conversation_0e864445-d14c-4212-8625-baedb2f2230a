import {
  Controller,
  Get,
  Param,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { TransactionUseCases } from '@useCases';
import { ListTransactionsDTO } from '@dtos';

@ApiTags('transactions')
@Controller('transactions')
export class TransactionsController {
  constructor(private readonly transactionUseCases: TransactionUseCases) {}

  @ApiOperation({
    summary: 'List transactions',
  })
  @Get('/')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  )
  async list(@Query() query: ListTransactionsDTO) {
    return this.transactionUseCases.searchAll(
      query.query,
      query.paymentMethod,
      query.subscriptionId,
      query.customerId,
      query.status,
      query.type,
      query.limit,
      query.page,
      query.orderBy,
    );
  }

  @ApiOperation({
    summary: 'Get a transaction',
  })
  @Get('/:id')
  async getOne(@Param('id') id: string) {
    return this.transactionUseCases.getOne(id);
  }
}
