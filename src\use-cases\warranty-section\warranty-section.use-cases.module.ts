import { Module } from '@nestjs/common';
import { DataServicesModule } from '@services/database';
import { WarrantySectionFactoryService, WarrantySectionUseCases } from '.';
import { ProductOfferUseCasesModule } from '../product-offers';

@Module({
  imports: [DataServicesModule, ProductOfferUseCasesModule],
  providers: [WarrantySectionFactoryService, WarrantySectionUseCases],
  exports: [WarrantySectionFactoryService, WarrantySectionUseCases],
})
export class WarrantySectionUseCasesModule {}
