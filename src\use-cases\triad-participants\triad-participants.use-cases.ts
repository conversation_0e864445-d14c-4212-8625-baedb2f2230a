import { IDataServices } from '@abstracts';
import { Injectable } from '@nestjs/common';
import { TriadParticipantEntity } from '@entities';

@Injectable()
export class TriadParticipantsUseCases {
  constructor(private readonly dataServices: IDataServices) {}

  /**
   * Creates a new triad participant
   * @param participant - The participant to create
   * @returns The created triad participant
   */
  async create(
    participant: TriadParticipantEntity,
  ): Promise<TriadParticipantEntity> {
    return this.dataServices.triadParticipant.create(participant);
  }

  /**
   * Deletes a triad participant record
   * @param participantId - The ID of the participant
   * @param triadId - The ID of the triad
   */
  async delete(participantId: number, triadId: number): Promise<void> {
    await this.dataServices.triadParticipant.hardDelete({
      user: {
        id: participantId,
      },
      triad: {
        id: triadId,
      },
    });
  }
}
