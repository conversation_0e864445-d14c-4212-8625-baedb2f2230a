"use client";

import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { useUser } from "@clerk/nextjs";

import { Button } from "@/components/base/button";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { PasswordInput } from "@/components/base/(auth)/PasswordInput";

const formSchema = z.object({
  currentPassword: z.string().min(1),
  newPassword: z.string().min(1),
  confirmNewPassword: z.string().min(1),
});

type ChangePasswordFormProps = {
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function ChangePasswordForm({ onSuccess, onCancel }: ChangePasswordFormProps) {
  const t = useTranslations("profile.settings.account.form");
  const { user } = useUser();
  const [error, setError] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmNewPassword: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setError(null);
    if (values.newPassword !== values.confirmNewPassword) {
      setError(t("password.error.passwords-not-match"));
      return;
    }
    setIsLoading(true);
    try {
      await user?.updatePassword({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
        signOutOfOtherSessions: true,
      });
      console.log("password updated");
      toast.success(t("password.updated"));
      onSuccess?.();
    } catch (error: any) {
      if (error.errors) {
        setError(t(`password.error.${error.errors[0].code}`));
      }
      toast.error(t("password.error.cannot-update"), {
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Display error */}
        {error && <div className="text-sm font-medium text-red-500">{error}</div>}

        <FormField
          control={form.control}
          name="currentPassword"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <PasswordInput
                  placeholder={t("password.current-password")}
                  autoComplete="current-password"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="newPassword"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <PasswordInput
                  placeholder={t("password.new-password")}
                  autoComplete="new-password"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmNewPassword"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <PasswordInput
                  placeholder={t("password.confirm-password")}
                  autoComplete="new-password"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <div className="mt-2 flex gap-2">
          {onCancel && (
            <Button type="button" variant="secondary" onClick={onCancel}>
              {t("password.cancel")}
            </Button>
          )}
          <Button type="submit" disabled={isLoading}>
            {t("password.submit")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
