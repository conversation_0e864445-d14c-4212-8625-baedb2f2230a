"use client";

import * as React from "react";
import { Check, ChevronsUpDown, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";

export interface Option {
  value: string;
  label: string;
  render?: () => React.ReactNode;
}

interface MultiSelectProps {
  options: Option[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  emptyMessage?: string;
  className?: string;
}

export function MultiSelect({
  options,
  value,
  onChange,
  placeholder,
  emptyMessage,
  className,
}: MultiSelectProps) {
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);

  // Filter out any values that don't have corresponding options and get the selected options
  const selectedOptions = value
    .map((v) => options.find((opt) => opt.value === v))
    .filter((option): option is Option => !!option); // Type guard to filter out undefined

  // Handle wheel events to prevent propagation
  const handleWheel = (e: React.WheelEvent) => {
    e.stopPropagation();
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal={true}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-auto min-h-[2.5rem] w-full justify-between",
            !selectedOptions.length && "text-muted-foreground",
            className,
          )}
        >
          <div className="flex flex-wrap gap-1">
            {selectedOptions.length ? (
              <>
                {selectedOptions.slice(0, 5).map((option: any) => (
                  <Badge
                    variant="secondary"
                    key={option.value}
                    className="mb-1 mr-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      onChange(value.filter((v) => v !== option.value));
                    }}
                  >
                    {option.label}
                    <X className="ml-1 h-3 w-3" />
                  </Badge>
                ))}
                {selectedOptions.length > 5 && (
                  <Badge variant="secondary" className="mb-1 mr-1">
                    +{selectedOptions.length - 5}
                  </Badge>
                )}
              </>
            ) : (
              <span>{placeholder}</span>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start" sideOffset={4} onWheel={handleWheel}>
        <Command>
          <CommandInput placeholder={t("common.search")} />
          <CommandList onWheel={handleWheel}>
            <CommandEmpty>{emptyMessage || t("common.no_results")}</CommandEmpty>
            <CommandGroup className="max-h-[200px] overflow-auto">
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  onSelect={() => {
                    onChange(
                      value.includes(option.value)
                        ? value.filter((v) => v !== option.value)
                        : [...value, option.value],
                    );
                    setOpen(true);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value.includes(option.value) ? "opacity-100" : "opacity-0",
                    )}
                  />
                  {option.render ? option.render() : option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
