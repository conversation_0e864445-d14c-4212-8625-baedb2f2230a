/**
 * Core API endpoints for payment operations
 * These are the base paths and methods used by both client and server implementations
 */
export const PAYMENT_API_ENDPOINTS = {
  /**
   * List payments endpoint
   * @returns Formatted URL with query parameters
   */
  list: () => ({
    url: "/subscriptions/user",
    method: "GET",
  }),

  /**
   * Get payment by Chargebee ID endpoint
   * @param id Chargebee ID
   * @returns Endpoint configuration
   */
  getByChargebeeId: (id: string) => ({
    url: `/subscriptions/${id}/with-invoices`,
    method: "GET",
  }),
};

/**
 * Type definitions for API responses
 */
export type PaymentApiTypes = {
  list: any;
  getByChargebeeId: {
    startedAt: string;
    nextPaymentDate: string;
    orderStatus: string;
    amountPaid: number;
    totalAmount: number;
    remainingBillingCycles: number;
    paymentMethod: string;
    invoices: {
      id: string;
      status: string;
      date: string;
      downloadURL: string;
    }[];
  };
};
