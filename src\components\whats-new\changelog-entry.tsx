import { cn } from "@/lib/utils";
import { ILocalizedWhatsNew, IThumbnailImage } from "@px-shared-account/hermes";
import { motion } from "framer-motion";
import { Download, Edit2, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRef, useState } from "react";
import { Button } from "@/components/base/button";
import ReactMarkdown, { Components } from "react-markdown";
import EmojiReactions from "./emoji-reactions";
import ImageCarousel from "./image-carousel";
import { MasonryItem } from "./masonry/masonry-item";
import { MasonryGallery } from "./masonry/masonry-gallery";
import * as htmlToImage from "html-to-image";

export default function ChangelogEntry({
  version,
  onEdit,
  onDelete,
  isAdmin,
}: {
  version: ILocalizedWhatsNew & { thumbnails: IThumbnailImage[] };
  onEdit?: () => void;
  onDelete?: () => void;
  isAdmin?: boolean;
}) {
  const t = useTranslations();
  const cardRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [imageDisplayMode, setImageDisplayMode] = useState<"carousel" | "masonry">("carousel");

  const getChangeTypeColor = (type: "added" | "changed" | "fixed" | "removed") => {
    switch (type) {
      case "added":
        return "bg-green-500/10 text-green-500";
      case "changed":
        return "bg-blue-500/10 text-blue-500";
      case "fixed":
        return "bg-yellow-500/10 text-yellow-500";
      case "removed":
        return "bg-red-500/10 text-red-500";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const markdownComponents: Components = {
    p: ({ children }) => <span className="inline">{children}</span>,
  };

  const markdownListComponents: Components = {
    p: ({ children }) => (
      <span className="prose prose-invert prose-sm inline max-w-none">{children}</span>
    ),
  };

  const exportToImage = async () => {
    if (!cardRef.current || isExporting) return;
    const previousPadding = cardRef.current.style.padding;

    try {
      setIsExporting(true);
      setImageDisplayMode("masonry"); // Switch to Masonry for export

      // Find admin buttons
      const adminButtons = cardRef.current.querySelector("[data-admin-buttons]") as HTMLElement;

      // Temporarily hide admin buttons if they exist
      if (adminButtons) {
        adminButtons.style.display = "none";
      }

      cardRef.current.style.padding = "24px";

      // Wait for a brief moment for the layout to update
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Wait for all images to load before capturing
      const imgElements = cardRef.current.querySelectorAll("img");
      await Promise.all(
        Array.from(imgElements).map((img) =>
          img.complete
            ? Promise.resolve()
            : new Promise((resolve) => {
                img.onload = resolve;
                img.onerror = resolve; // Proceed even if an image fails to load
              }),
        ),
      );

      // Add another brief delay after images are loaded
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Use the node directly
      const dataUrl = await htmlToImage.toJpeg(cardRef.current, {
        cacheBust: true,
        pixelRatio: 2,
        backgroundColor: "#121212",
      });

      // Restore admin buttons
      if (adminButtons) {
        adminButtons.style.display = "";
      }

      // Create download link
      const link = document.createElement("a");
      link.download = `changelog-v${version.versionCode}.png`;
      link.href = dataUrl;
      link.click();
    } catch (error) {
      console.error("Error exporting image:", error);
      alert("Failed to export image. Please try again.");
    } finally {
      cardRef.current.style.padding = previousPadding;
      setImageDisplayMode("carousel"); // Switch back to Carousel after export
      setIsExporting(false);
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 1, height: "auto" }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.2 }}
      className="before:bg-secondary-foreground after:bg-secondary-foreground/20 relative pl-8 before:absolute before:top-0 before:left-0 before:h-full before:w-[2px] before:content-[''] after:absolute after:bottom-[-3rem] after:left-0 after:h-12 after:w-[2px] after:content-['']"
    >
      <div ref={cardRef} className="changelog-card">
        <div className="mb-6">
          <div className="flex items-baseline gap-4">
            <h2 className={"font-anton text-2xl font-semibold"}>
              <ReactMarkdown
                components={markdownComponents}
              >{`v${version.versionCode} - ${version.title}`}</ReactMarkdown>
            </h2>
            <time className="text-muted-foreground text-sm">
              {new Date(version.date).toLocaleDateString()}
            </time>
            {isAdmin && (
              <div className="ml-auto flex gap-2" data-admin-buttons>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportToImage}
                  className="border-foreground hover:bg-foreground/10"
                  title={t("whats-new.admin.export-image")}
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <div className="border-foreground/20 border-t-foreground h-4 w-4 animate-spin rounded-full border-2" />
                  ) : (
                    <Download className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onEdit}
                  className="border-foreground hover:bg-foreground/10"
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={onDelete}
                  className="border-0 bg-red-900/50 hover:bg-red-900/75"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-6">
          {version.description && (
            <div className="prose prose-invert prose-sm mb-6 max-w-none">
              <ReactMarkdown>{version.description}</ReactMarkdown>
            </div>
          )}

          {/* Conditional Image Display */}
          {version.thumbnails && version.thumbnails.length > 0 && (
            <div className="mb-6">
              {imageDisplayMode === "carousel" ? (
                <ImageCarousel thumbnails={version.thumbnails} />
              ) : (
                <MasonryGallery>
                  {version.thumbnails.map((thumbnail, index) => (
                    <MasonryItem
                      key={index}
                      src={thumbnail.url}
                      alt={`${version.title} thumbnail ${index + 1}`}
                      width={thumbnail.width}
                      height={thumbnail.height}
                      onClick={() => window.open(thumbnail.url, "_blank")} // Keep original onClick for masonry view
                    />
                  ))}
                </MasonryGallery>
              )}
            </div>
          )}

          {version.added?.length > 0 && (
            <div>
              <div className="mb-3">
                <span
                  className={cn(
                    "inline-block rounded-full px-3 py-1 text-xs font-medium capitalize",
                    getChangeTypeColor("added"),
                  )}
                >
                  {t("whats-new.changelog.added")}
                </span>
              </div>
              <ul className="space-y-2">
                {version.added.map((item, index) => (
                  <li
                    key={index}
                    className="text-muted-foreground before:bg-muted relative pl-6 before:absolute before:top-[0.6em] before:left-0 before:h-1.5 before:w-1.5 before:rounded-full"
                  >
                    <ReactMarkdown components={markdownListComponents}>{item}</ReactMarkdown>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {version.changed?.length > 0 && (
            <div>
              <div className="mb-3">
                <span
                  className={cn(
                    "inline-block rounded-full px-3 py-1 text-xs font-medium capitalize",
                    getChangeTypeColor("changed"),
                  )}
                >
                  {t("whats-new.changelog.changed")}
                </span>
              </div>
              <ul className="space-y-2">
                {version.changed.map((item, index) => (
                  <li
                    key={index}
                    className="text-muted-foreground before:bg-muted relative pl-6 before:absolute before:top-[0.6em] before:left-0 before:h-1.5 before:w-1.5 before:rounded-full"
                  >
                    <ReactMarkdown components={markdownListComponents}>{item}</ReactMarkdown>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {version.fixed?.length > 0 && (
            <div>
              <div className="mb-3">
                <span
                  className={cn(
                    "inline-block rounded-full px-3 py-1 text-xs font-medium capitalize",
                    getChangeTypeColor("fixed"),
                  )}
                >
                  {t("whats-new.changelog.fixed")}
                </span>
              </div>
              <ul className="space-y-2">
                {version.fixed.map((item, index) => (
                  <li
                    key={index}
                    className="text-muted-foreground before:bg-muted relative pl-6 before:absolute before:top-[0.6em] before:left-0 before:h-1.5 before:w-1.5 before:rounded-full"
                  >
                    <ReactMarkdown components={markdownListComponents}>{item}</ReactMarkdown>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {version.removed?.length > 0 && (
            <div>
              <div className="mb-3">
                <span
                  className={cn(
                    "inline-block rounded-full px-3 py-1 text-xs font-medium capitalize",
                    getChangeTypeColor("removed"),
                  )}
                >
                  {t("whats-new.changelog.removed")}
                </span>
              </div>
              <ul className="space-y-2">
                {version.removed.map((item, index) => (
                  <li
                    key={index}
                    className="text-muted-foreground before:bg-muted relative pl-6 before:absolute before:top-[0.6em] before:left-0 before:h-1.5 before:w-1.5 before:rounded-full"
                  >
                    <ReactMarkdown components={markdownListComponents}>{item}</ReactMarkdown>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <EmojiReactions versionId={version.id} className="mt-6" />
      </div>
    </motion.div>
  );
}
