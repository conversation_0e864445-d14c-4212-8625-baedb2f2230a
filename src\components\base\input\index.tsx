"use client";

import { memo } from "react";
import { Input as ShadcnInput } from "@/components/ui/input";
import { cn } from "@/lib/utils";

/**
 * Custom input component that extends the base ShadcnInput with additional features
 * @param props - Input component props
 * @returns Enhanced input component with custom styling and features
 */
export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "type"> {
  type?: "text" | "email" | "password" | "search" | "tel" | "url" | "number" | "file";
  error?: boolean;
  fullWidth?: boolean;
  className?: string;
  prefixIcon?: React.ReactNode;
}

export const Input = memo(
  ({ className, type = "text", error, fullWidth, prefixIcon, ...props }: InputProps) => {
    return (
      <div className="relative flex w-full items-center">
        {prefixIcon && (
          <div className="pointer-events-none absolute left-3 flex items-center">{prefixIcon}</div>
        )}
        <ShadcnInput
          type={type}
          className={cn(
            // Default rounded style
            "!rounded-full",
            // Error state
            error && "border-destructive",
            // Full width option
            fullWidth && "w-full",
            // Search type specific styles
            type === "search" && "pr-10 pl-4",
            // Add padding-left when prefixIcon is present
            prefixIcon && "pl-10",
            // Allow className override
            className,
          )}
          {...props}
        />
      </div>
    );
  },
);

Input.displayName = "Input" as const;
