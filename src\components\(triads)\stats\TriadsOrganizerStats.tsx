import { Card } from "@/components/ui/card";
import { Loader2, Trophy } from "lucide-react";
import useS<PERSON> from "swr";
import { cn } from "@/lib/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useEffect } from "react";
import { useTranslations } from "next-intl";
import { anton } from "@/lib/fonts/anton";
import { PlatformTriadStats } from "@/types/triad";
import { usePlatformTriadStats } from "@/services/triad/client";
interface TimeRange {
  start: Date;
  end: Date;
  type: string;
}

interface TriadsOrganizerStatsProps {
  timeRange: TimeRange;
  onDataChange?: (data: PlatformTriadStats | null) => void;
}

export default function TriadsOrganizerStats({
  timeRange,
  onDataChange,
}: TriadsOrganizerStatsProps) {
  const t = useTranslations("triad.stats");
  const {
    stats: triadStats,
    isLoading,
    error,
  } = usePlatformTriadStats(timeRange.start.toISOString(), timeRange.end.toISOString());

  useEffect(() => {
    if (triadStats && onDataChange) {
      onDataChange(triadStats);
    }
  }, [triadStats, onDataChange]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-white/50" />
      </div>
    );
  }

  if (error) {
    return <div className="text-center text-red-400">{t("organizers.error")}</div>;
  }

  if (!triadStats) return null;

  const { organizers } = triadStats;

  // Sort organizers by triad count
  const sortedOrganizers = [...organizers.topOrganizers].sort(
    (a, b) => b.triadsCount - a.triadsCount,
  );

  // Calculate the total triads for contribution percentage
  const totalTriads = sortedOrganizers.reduce((sum, org) => sum + org.triadsCount, 0);

  return (
    <div className="space-y-6">
      <Card className="overflow-hidden border-0 bg-[#1a1a1a]">
        <div className="p-6">
          <div className="mb-6 flex items-center justify-between">
            <h3 className={cn("text-xl font-semibold text-white", anton.className)}>
              {t("organizers.leaderboard.title")}
            </h3>
            <div className="rounded-full bg-[#8D2146]/20 px-3 py-1 text-xs font-medium text-pink-300">
              Top {sortedOrganizers.length > 1 ? sortedOrganizers.length : 10}
            </div>
          </div>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-[#2a2a2a]">
                  <TableHead className="w-16 text-gray-400">
                    {t("organizers.leaderboard.columns.rank")}
                  </TableHead>
                  <TableHead className="text-gray-400">
                    {t("organizers.leaderboard.columns.organizer")}
                  </TableHead>
                  <TableHead className="text-right text-gray-400">
                    {t("organizers.leaderboard.columns.triads")}
                  </TableHead>
                  <TableHead className="text-right text-gray-400">
                    {t("organizers.leaderboard.columns.contribution")}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedOrganizers.map((organizer, index) => {
                  const contributionPercentage = (
                    (organizer.triadsCount / totalTriads) *
                    100
                  ).toFixed(1);

                  // Set medal colors for top 3
                  const getMedalColor = (rank: number) => {
                    switch (rank) {
                      case 0:
                        return "text-yellow-400"; // Gold
                      case 1:
                        return "text-gray-300"; // Silver
                      case 2:
                        return "text-amber-600"; // Bronze
                      default:
                        return "text-gray-500";
                    }
                  };

                  return (
                    <TableRow
                      key={organizer.id}
                      className={cn(
                        "border-b border-[#2a2a2a]/50",
                        index < 3 ? "bg-[#2a2a2a]/10" : "",
                      )}
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          {index < 3 ? (
                            <Trophy className={cn("mr-1.5 h-4 w-4", getMedalColor(index))} />
                          ) : (
                            <span className="mr-1.5 inline-block w-6 text-right text-gray-500">
                              {index + 1}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium text-gray-300">{organizer.name}</TableCell>
                      <TableCell className="text-right text-gray-300">
                        {organizer.triadsCount}
                      </TableCell>
                      <TableCell className="text-right">
                        <span
                          className={cn(
                            "rounded-full px-2 py-1 text-xs",
                            parseFloat(contributionPercentage) > 20
                              ? "bg-[#8D2146]/20 text-pink-300"
                              : "bg-[#2a2a2a] text-gray-300",
                          )}
                        >
                          {contributionPercentage}%
                        </span>
                      </TableCell>
                    </TableRow>
                  );
                })}

                {sortedOrganizers.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={4} className="py-6 text-center text-gray-400">
                      {t("organizers.no_data")}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </Card>
    </div>
  );
}
