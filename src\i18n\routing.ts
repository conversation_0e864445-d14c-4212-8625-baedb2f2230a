import { defineRouting } from "next-intl/routing";
import { createNavigation } from "next-intl/navigation";

const faqPathnames = {
  "/faq": {
    en: "/faq",
    fr: "/faq",
  },
  "/faq/pxs": {
    en: "/faq/pxs",
    fr: "/faq/pxs",
  },
  "/faq/pxl": {
    en: "/faq/pxl",
    fr: "/faq/pxl",
  },
  "/faq/goliaths": {
    en: "/faq/goliaths",
    fr: "/faq/goliaths",
  },
};

const authPathnames = {
  "/sign-in": {
    en: "/sign-in",
    fr: "/connexion",
  },
  "/sign-up": {
    en: "/sign-up",
    fr: "/inscription",
  },
  "/sign-out": {
    en: "/sign-out",
    fr: "/deconnexion",
  },
};

const communitiesPathnames = {
  "/communities/pxl": {
    en: "/communities/pxl",
    fr: "/communautes/pxl",
  },
  "/communities/pxs": {
    en: "/communities/pxs",
    fr: "/communautes/pxs",
  },
};

export const routing = defineRouting({
  locales: ["fr", "en"], // Define in this line the possible languages for translation
  localePrefix: "as-needed",
  pathnames: {
    "/": "/",
    "/dashboard": {
      en: "/home",
      fr: "/accueil",
    },
    "/calendar": {
      en: "/calendars",
      fr: "/calendriers",
    },
    "/courses": {
      en: "/courses",
      fr: "/cours",
    },
    "/courses/[id]": {
      en: "/courses/[id]",
      fr: "/cours/[id]",
    },
    "/cohorts": {
      en: "/cohorts",
      fr: "/cohortes",
    },
    "/cohorts/[id]": {
      en: "/cohorts/[id]",
      fr: "/cohortes/[id]",
    },
    "/objectives": {
      en: "/objectives",
      fr: "/objectifs",
    },
    "/user-management/users": {
      en: "/user-management/users",
      fr: "/gestion-utilisateurs/utilisateurs",
    },
    "/user-management/users/[id]": {
      en: "/user-management/users/[id]",
      fr: "/gestion-utilisateurs/utilisateurs/[id]",
    },
    "/user-management/roles": {
      en: "/user-management/roles",
      fr: "/gestion-utilisateurs/roles",
    },
    "/profile": {
      en: "/profile",
      fr: "/profil",
    },
    "/whats-new": {
      en: "/whats-new",
      fr: "/quoi-de-neuf",
    },
    "/practice": {
      en: "/practice",
      fr: "/pratique",
    },
    ...authPathnames,
    ...faqPathnames,
    ...communitiesPathnames,
  },
  defaultLocale: "fr", // Define in this line the default language to be shown
});

export type Pathnames = keyof typeof routing.pathnames;
export type Locale = (typeof routing.locales)[number];

export const { Link, redirect, usePathname, useRouter } = createNavigation(routing);
