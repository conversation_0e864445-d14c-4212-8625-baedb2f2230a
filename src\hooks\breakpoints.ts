"use client";

import { useEffect, useMemo, useState } from "react";

import tailwindConfig from "../../tailwind.config";

type Breakpoints = Record<string, number>;

/**
 * Takes an object with string values and transforms it into an object with the same keys but with values as integers.
 * @param obj - The object to transform.
 * @returns The transformed object.
 */
function transform(obj: Record<string, string>): Breakpoints {
  return Object.fromEntries(Object.entries(obj).map(([key, value]) => [key, parseInt(value, 10)]));
}

const defaultBreakpoints: Breakpoints = transform(tailwindConfig.theme.screens);

export type BreakpointKey = keyof typeof defaultBreakpoints;

/**
 * A React hook that returns the current breakpoint based on window width.
 * The breakpoint is determined by finding the first breakpoint whose value is greater than the current window width.
 *
 * @param breakpoints - An object mapping breakpoint names to their minimum width values. Defaults to Tailwind's breakpoints.
 * @returns The key of the current active breakpoint, or undefined if no breakpoint matches.
 *
 * @example
 * ```tsx
 * const breakpoint = useBreakpoints();
 * console.log(breakpoint); // 'sm', 'md', 'lg', etc.
 * ```
 */
export const useBreakpoints = (
  breakpoints: Breakpoints = defaultBreakpoints,
): keyof Breakpoints | undefined => {
  const searchBreakpoint = (breakpoints: { key: string; value: number }[]) => {
    return breakpoints.find((x) => window.innerWidth < x.value)?.key;
  };

  const entries = useMemo(() => {
    return Object.entries(breakpoints)
      .sort((a, b) => a[1] - b[1])
      .map(([key, value]) => ({ key, value }));
  }, [breakpoints]);

  const [breakpoint, setBreakpoint] = useState<keyof Breakpoints | undefined>(
    typeof window !== "undefined" ? searchBreakpoint(entries) : undefined,
  );

  useEffect(() => {
    const onResize = () => setBreakpoint(searchBreakpoint(entries));

    // Set initial breakpoint
    setBreakpoint(searchBreakpoint(entries));

    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, [entries, searchBreakpoint]);

  return breakpoint;
};
