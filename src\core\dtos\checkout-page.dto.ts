import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Testimonial } from '@types';
import { IsHexColor, IsOptional, IsString, IsUrl } from 'class-validator';

export class CreateCheckoutPageDTO {
  @ApiProperty()
  @IsUrl()
  bannerImage: string;

  @ApiProperty()
  @IsHexColor()
  mainColor: string;

  @ApiPropertyOptional()
  @IsUrl()
  @IsOptional()
  url?: string;

  @ApiProperty()
  @IsString()
  template: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  cancellationPolicy?: string;

  @ApiPropertyOptional()
  @IsOptional()
  testimony?: Testimonial;
}

export class UpdateCheckoutPageDTO extends PartialType(CreateCheckoutPageDTO) {
  @ApiProperty()
  id: number;
}
