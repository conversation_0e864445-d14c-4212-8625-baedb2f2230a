"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useApi } from "@/hooks/store/use-api";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { WHATS_NEW_API_ENDPOINTS, WhatsNewApiTypes, WhatsNewApiPayloads } from "./core";

/**
 * Hook for fetching and managing What's New versions list
 * @returns SWR response with versions list data
 */
export function useWhatsNewVersions() {
  const { url } = WHATS_NEW_API_ENDPOINTS.list();
  return useApi<WhatsNewApiTypes["list"]>(url);
}

/**
 * Hook for fetching a specific What's New version by ID
 * @param id Version ID to fetch
 * @returns SWR response with version data
 */
export function useWhatsNewVersion(id?: number) {
  const apiConfig = useMemo(() => {
    if (!id) return null;
    const { url } = WHATS_NEW_API_ENDPOINTS.getById(id);
    return url;
  }, [id]);

  return useApi<WhatsNewApiTypes["getById"]>(apiConfig);
}

/**
 * Hook for creating a new What's New version
 * @returns Functions and state for version creation
 */
export function useCreateWhatsNewVersion() {
  const { toast } = useToast();
  const t = useTranslations("whats-new.admin");
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = WHATS_NEW_API_ENDPOINTS.create();
  const { trigger, isLoading } = useApi<WhatsNewApiTypes["create"]>(url, {
    method: method as "POST",
  });

  const create = useCallback(
    async (data: WhatsNewApiPayloads["create"]) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        const result = await trigger(data);
        toast({
          title: t("success.created"),
          description: t("success.created-description"),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("error.create"),
          description: errorMessage || t("error.create-description"),
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger, t],
  );

  return { create, isLoading, error };
}

/**
 * Hook for updating an existing What's New version
 * @returns Functions and state for version updates
 */
export function useUpdateWhatsNewVersion() {
  const { toast } = useToast();
  const t = useTranslations("whats-new.admin");
  const [error, setError] = useState<Error | null>(null);
  const [versionId, setVersionId] = useState<number | null>(null);
  const [updateData, setUpdateData] = useState<WhatsNewApiPayloads["update"] | null>(null);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  const { trigger, isLoading } = useApi<WhatsNewApiTypes["update"]>(
    versionId ? WHATS_NEW_API_ENDPOINTS.update(versionId).url : null,
    { method: "PATCH" },
  );

  const performUpdate = async (data?: WhatsNewApiPayloads["update"]) => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!versionId) throw new Error("Version ID is required");
      if (!updateData && !data) throw new Error("Update data is required");

      const result = await trigger(data || updateData);
      toast({
        title: t("success.updated"),
        description: t("success.updated-description"),
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: t("error.update"),
        description: errorMessage || t("error.update-description"),
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldUpdate(false);
      setUpdateData(null);
    }
  };

  useEffect(() => {
    if (shouldUpdate && updateData) {
      performUpdate();
    }
  }, [shouldUpdate, versionId, updateData]);

  const update = useCallback(
    async (data: WhatsNewApiPayloads["update"]) => {
      setError(null);
      setUpdateData(data);
      if (versionId !== data.id) {
        setVersionId(data.id);
        setShouldUpdate(true);
      } else {
        await performUpdate(data);
      }
    },
    [versionId],
  );

  return { update, isLoading, error };
}

/**
 * Hook for deleting a What's New version
 * @returns Functions and state for version deletion
 */
export function useDeleteWhatsNewVersion() {
  const { toast } = useToast();
  const t = useTranslations("whats-new.admin");
  const [error, setError] = useState<Error | null>(null);
  const [versionId, setVersionId] = useState<number | null>(null);
  const [shouldDelete, setShouldDelete] = useState(false);

  const { trigger, isLoading } = useApi<WhatsNewApiTypes["delete"]>(
    versionId ? WHATS_NEW_API_ENDPOINTS.delete(versionId).url : null,
    { method: "DELETE" },
  );

  const performDelete = async () => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!versionId) throw new Error("Version ID is required");

      const result = await trigger();
      toast({
        title: t("success.deleted"),
        description: t("success.deleted-description"),
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: t("error.delete"),
        description: errorMessage || t("error.delete-description"),
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldDelete(false);
    }
  };

  useEffect(() => {
    if (shouldDelete) {
      performDelete();
    }
  }, [shouldDelete, versionId]);

  const deleteVersion = useCallback(
    async (id: number) => {
      setError(null);
      if (versionId !== id) {
        setVersionId(id);
        setShouldDelete(true);
      } else {
        await performDelete();
      }
    },
    [versionId],
  );

  return { deleteVersion, isLoading, error };
}

/**
 * Hook for fetching reactions for a specific version
 * @param versionId Version ID to fetch reactions for
 * @returns SWR response with reactions data
 */
export function useWhatsNewReactions(versionId: number) {
  const { url } = WHATS_NEW_API_ENDPOINTS.getReactions(versionId);
  return useApi<WhatsNewApiTypes["getReactions"]>(url);
}

/**
 * Hook for toggling reactions on a version
 * @returns Functions and state for reaction toggling
 */
export function useToggleReaction() {
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = WHATS_NEW_API_ENDPOINTS.toggleReaction();
  const { trigger, isLoading } = useApi<WhatsNewApiTypes["toggleReaction"]>(url, {
    method: method as "POST",
  });

  const toggleReaction = useCallback(
    async (data: WhatsNewApiPayloads["toggleReaction"]) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        return await trigger(data);
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        throw error;
      }
    },
    [trigger],
  );

  return { toggleReaction, isLoading, error };
}

/**
 * Hook for uploading images via the backend API
 * @returns Functions and state for image uploads
 */
export function useUploadImage() {
  const { toast } = useToast();
  const t = useTranslations("whats-new.admin");
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = WHATS_NEW_API_ENDPOINTS.uploadImage();
  const { trigger, isLoading } = useApi<WhatsNewApiTypes["uploadImage"]>(url, {
    method: method as "POST",
  });

  const uploadImage = useCallback(
    async (file: File): Promise<WhatsNewApiTypes["uploadImage"]> => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        const formData = new FormData();
        formData.append("file", file);

        const result = await trigger(formData);
        toast({
          title: t("success.uploaded"),
          description: t("success.uploaded-description"),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("error.upload"),
          description: errorMessage || t("error.upload-description"),
          variant: "destructive",
        });
        throw error;
      }
    },
    [trigger, toast, t],
  );

  return { uploadImage, isLoading, error };
}

/**
 * Hook for fetching the media gallery items
 * @param options Configuration options
 * @returns SWR response with media items
 */
export function useMediaGallery(options?: {
  prefix?: string;
  limit?: number;
  revalidateOnFocus?: boolean;
  revalidateOnReconnect?: boolean;
}) {
  const { prefix, limit } = options || {};

  const { url } = WHATS_NEW_API_ENDPOINTS.listMedia(prefix, limit);

  return useApi<WhatsNewApiTypes["listMedia"]>(url);
}

/**
 * Hook for deleting a media item from the gallery
 * @returns Functions and state for media deletion
 */
export function useDeleteMedia() {
  const { toast } = useToast();
  const t = useTranslations("whats-new.admin");
  const [error, setError] = useState<Error | null>(null);
  const [pathname, setPathname] = useState<string | null>(null);
  const [shouldDelete, setShouldDelete] = useState(false);

  // Create a new API call when the pathname changes
  const { trigger, isLoading } = useApi<WhatsNewApiTypes["deleteMedia"]>(
    pathname ? WHATS_NEW_API_ENDPOINTS.deleteMedia(pathname).url : null,
    { method: "DELETE" },
  );

  // Function to perform the actual deletion
  const performDelete = useCallback(async () => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!pathname) throw new Error("Pathname is required");

      const result = await trigger();
      toast({
        title: t("success.media-deleted"),
        description: t("success.media-deleted-description"),
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: t("error.media-delete"),
        description: errorMessage || t("error.media-delete-description"),
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldDelete(false);
      setPathname(null);
    }
  }, [trigger, pathname, toast, t]);

  // Effect to trigger deletion when state changes
  useEffect(() => {
    if (shouldDelete && pathname) {
      performDelete();
    }
  }, [shouldDelete, pathname, performDelete]);

  // Public function to initiate media deletion
  const deleteMedia = useCallback(async (mediaPathname: string) => {
    setError(null);
    setPathname(mediaPathname);
    setShouldDelete(true);
  }, []);

  return { deleteMedia, isLoading, error };
}
