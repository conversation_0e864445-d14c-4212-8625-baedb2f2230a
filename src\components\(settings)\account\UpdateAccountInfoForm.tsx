"use client";

import { useUser } from "@clerk/nextjs";

import { loadChargebeeScript } from "@/lib/chargebee";
import UnitAccountFormElement from "./UnitAccountFormElement";
import { useUserDetailsBySSOId } from "@/services/user";
import { useCreateChargebeePortalSession } from "@/services/portal-session/client";

export type FieldType = {
  key: string;
  use: "clerk" | "chargebee";
  defaultValue: string;
};

const fieldType: FieldType[] = [
  { key: "firstName", use: "clerk", defaultValue: "firstName" },
  { key: "lastName", use: "clerk", defaultValue: "lastName" },
  { key: "email", use: "clerk", defaultValue: "email" },
  { key: "password", use: "clerk", defaultValue: "password" },
  { key: "phone", use: "clerk", defaultValue: "phone" },
  { key: "address", use: "chargebee", defaultValue: "address" },
];

export function UpdateAccountInfoForm() {
  const { user } = useUser();
  const { data: userDetails } = useUserDetailsBySSOId(user?.id);
  const { create } = useCreateChargebeePortalSession();

  const onModifyBillingAddress = async () => {
    await loadChargebeeScript();
    const chargebee = window.Chargebee;
    chargebee.init({
      site: process.env.NEXT_PUBLIC_CHARGEBEE_DOMAIN,
      publishableKey: process.env.NEXT_PUBLIC_CHARGEBEE_RO_API_KEY,
    });
    const cbInstance = chargebee.getInstance();
    cbInstance.setPortalSession(async function () {
      return await create({
        customerId: userDetails?.chargebeeIds?.PG || "",
      });
    });
    const cbPortal = cbInstance.createChargebeePortal();
    cbPortal.openSection({
      sectionType: "portal_address",
    });
  };

  return (
    <>
      <UnitAccountFormElement fieldType={fieldType} name="firstName" className="rounded-t-2xl" />
      <UnitAccountFormElement fieldType={fieldType} name="lastName" />
      <UnitAccountFormElement fieldType={fieldType} name="email" />
      <UnitAccountFormElement fieldType={fieldType} name="password" />
      <UnitAccountFormElement fieldType={fieldType} name="phone" />
      <UnitAccountFormElement
        fieldType={fieldType}
        name="billingAddress"
        className="rounded-b-2xl"
        onEditBillingAddress={onModifyBillingAddress}
      />
    </>
  );
}
