import { cn } from "@/lib/utils";
import { PlusCircle, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Button } from "@/components/base/button";
import { Input } from "@/components/base/input";

export default function FeatureInput({
  features,
  onChange,
  placeholder,
  className,
  type,
  ghostItems,
}: {
  features: string[];
  onChange: (features: string[]) => void;
  placeholder: string;
  className?: string;
  type: "added" | "changed" | "fixed" | "removed";
  ghostItems?: string[];
}) {
  const t = useTranslations();
  const [newFeature, setNewFeature] = useState("");

  const typeColors = {
    added: "text-green-500 border-green-500/20 bg-green-500/10",
    changed: "text-blue-500 border-blue-500/20 bg-blue-500/10",
    fixed: "text-yellow-500 border-yellow-500/20 bg-yellow-500/10",
    removed: "text-red-500 border-red-500/20 bg-red-500/10",
  };

  // Find the next untranslated item
  const nextUntranslatedItem = ghostItems?.find((item, index) => !features[index]);

  const handleAddFeature = () => {
    if (newFeature.trim()) {
      onChange([...features, newFeature.trim()]);
      setNewFeature("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddFeature();
    }
  };

  const handleRemoveFeature = (index: number) => {
    const newFeatures = [...features];
    newFeatures.splice(index, 1);
    onChange(newFeatures);
  };

  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex gap-2">
        <Input
          value={newFeature}
          onChange={(e) => setNewFeature(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={nextUntranslatedItem || t("whats-new.admin.features.placeholder")}
        />
        <Button type="button" onClick={handleAddFeature}>
          <PlusCircle className="h-4 w-4" />
        </Button>
      </div>
      <div className="space-y-2">
        {features.length > 0 && (
          <ul className="text-muted-foreground ml-2 list-inside list-disc space-y-2">
            {features.map((feature, index) => (
              <li key={index} className="group flex items-center gap-2">
                <Button type="button" variant="ghost" onClick={() => handleRemoveFeature(index)}>
                  <X className="h-4 w-4" />
                </Button>
                <div className="flex flex-1 items-center gap-2">
                  <span className="text-muted-foreground">•</span>
                  <span className="text-muted-foreground/20">{feature}</span>
                </div>
              </li>
            ))}
          </ul>
        )}
        {ghostItems && (
          <ul className="ml-2 space-y-2">
            {ghostItems.map((item, index) => {
              // Only show ghost items that haven't been translated yet
              if (index >= features.length) {
                return (
                  <li key={index} className="text-muted-foreground/20 flex items-center">
                    <span className="mr-2">•</span>
                    {item}
                  </li>
                );
              }
              return null;
            })}
          </ul>
        )}
      </div>
    </div>
  );
}
