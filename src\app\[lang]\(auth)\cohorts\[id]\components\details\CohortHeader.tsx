"use client";

import { useTranslations } from "next-intl";
import { Book, Calendar, Edit, UserPlus } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { StatusBadge } from "@/components/base/courses/status-badge";
import { AddStudentsModal } from "../modals/add-students/AddStudentsModal";
import EditCohortModal from "@/components/(cohorts)/EditCohortModal";
import { ICohortWithStudents } from "@/types/cohort";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

interface CohortHeaderProps {
  cohort: ICohortWithStudents;
}

export function CohortHeader({ cohort }: CohortHeaderProps) {
  const t = useTranslations("cohorts");
  const router = useRouter();
  const [isAddStudentsModalOpen, setIsAddStudentsModalOpen] = useState(false);
  const [isEditCohortModalOpen, setIsEditCohortModalOpen] = useState(false);

  // Compute status based on dates
  const now = new Date();
  const startDate = new Date(cohort.startDate);
  const endDate = new Date(cohort.endDate);

  let computedStatus = "upcoming";
  if (now > endDate) {
    computedStatus = "passed";
  } else if (now >= startDate && now <= endDate) {
    computedStatus = "running";
  }

  // Function to handle edit completion
  const handleEditComplete = async () => {
    setIsEditCohortModalOpen(false);
    // Refresh the page to revalidate all components
    // This will trigger a full page revalidation, including server components
    // The useApi hook will also revalidate all client-side data fetches
    router.refresh();
  };

  return (
    <>
      <div className="space-y-4">
        <BreadcrumbSettings active={`/cohorts/${cohort.id}`} />

        {/* Header content */}
        <div className="flex justify-between gap-4">
          <div className="flex flex-col items-start gap-2 md:flex-row md:items-center">
            <h1 className="text-3xl font-semibold tracking-tight">{cohort.name}</h1>
            <StatusBadge status={t(`status.${computedStatus}`)} />
          </div>

          {/* Action buttons */}
          <div className="hidden items-center gap-2 lg:flex">
            {cohort.course?.id && (
              <Button
                variant="secondary"
                size="sm"
                className="rounded-full p-5"
                onClick={() => router.push(`/courses/${cohort.course.id}`)}
              >
                <Book className="h-4 w-4" />
                <span className="ml-2">{t("actions.view_course")}</span>
              </Button>
            )}
            <Button
              variant="secondary"
              size="sm"
              className="rounded-full p-5"
              onClick={() => setIsEditCohortModalOpen(true)}
            >
              <Edit className="h-4 w-4" />
              <span className="ml-2">{t("actions.edit")}</span>
            </Button>
            <Button
              size="sm"
              className="rounded-full p-5"
              onClick={() => setIsAddStudentsModalOpen(true)}
            >
              <UserPlus className="h-4 w-4" />
              <span className="ml-2">{t("actions.add_student")}</span>
            </Button>
          </div>

          {/* Mobile buttons */}
          <div className="flex items-center gap-2 lg:hidden">
            {cohort.course?.id && (
              <Button
                variant="secondary"
                size="icon"
                className="rounded-full p-5"
                onClick={() => router.push(`/courses/${cohort.course.id}`)}
              >
                <Book className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="secondary"
              size="icon"
              className="rounded-full p-5"
              onClick={() => router.push("/calendar")}
            >
              <Calendar className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="rounded-full p-5"
              onClick={() => setIsEditCohortModalOpen(true)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile FAB */}
      <Button
        className="fixed right-6 bottom-6 h-14 w-14 rounded-full p-0 shadow-2xl lg:hidden"
        size="icon"
        onClick={() => setIsAddStudentsModalOpen(true)}
      >
        <UserPlus className="h-6 w-6" />
      </Button>

      {/* Modals */}
      <AddStudentsModal
        cohortId={cohort.id}
        isOpen={isAddStudentsModalOpen}
        onOpenChange={setIsAddStudentsModalOpen}
        onComplete={() => {
          setIsAddStudentsModalOpen(false);
          router.refresh();
        }}
      />

      <EditCohortModal
        cohort={cohort}
        isOpen={isEditCohortModalOpen}
        onOpenChange={setIsEditCohortModalOpen}
        onComplete={handleEditComplete}
      />
    </>
  );
}
