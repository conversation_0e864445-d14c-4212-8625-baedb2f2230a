"use client";

import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { ListItemCard } from "@/components/ui/ListItemCard";
import { Badge, BadgeProps } from "@/components/ui/badge";

interface PaymentsListProps {
  payments: any[];
  isLoading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  getStatusBadgeVariant: (status: string) => BadgeProps["variant"];
}

export default function PaymentsList({
  payments,
  isLoading,
  hasMore,
  onLoadMore,
  getStatusBadgeVariant,
}: PaymentsListProps) {
  const t = useTranslations("payments.list");
  const router = useRouter();

  // Infinite scroll handler
  useEffect(() => {
    if (!hasMore || isLoading) return;
    const handleScroll = () => {
      if (
        window.innerHeight + window.scrollY >= document.body.offsetHeight - 100 &&
        !isLoading &&
        hasMore
      ) {
        onLoadMore();
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [isLoading, hasMore, onLoadMore]);

  if (!payments.length && isLoading) {
    return <div className="text-muted-foreground p-4 text-center">{t("loading")}</div>;
  }

  if (!payments.length) {
    return <div className="text-muted-foreground p-4 text-center">{t("errors.no-results")}</div>;
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="p-2 text-2xl font-bold">{t("title", { count: payments.length })}</div>
      {payments.map((payment: any) => (
        <ListItemCard
          key={payment.id}
          title={payment.name}
          badge={
            <Badge variant={getStatusBadgeVariant(payment.orderStatus)}>
              {t(`status.options.${payment.orderStatus?.toLowerCase?.() || "unknown"}`)}
            </Badge>
          }
          showImage={true}
          imageUrl={payment.image}
          imageAlt={payment.name}
          infos={{
            [t("started-at")]: payment.startedAt
              ? new Date(payment.startedAt).toLocaleDateString()
              : "-",
            [t("status.title")]: t(
              `status.options.${payment.orderStatus?.toLowerCase?.() || "unknown"}`,
            ),
          }}
          onClick={() => router.push(`/payments/${payment.chargebeeid}`)}
        />
      ))}
      {isLoading && (
        <div className="text-muted-foreground p-2 text-center text-xs">{t("loading")}</div>
      )}
      {!hasMore && payments.length > 0 && (
        <div className="text-muted-foreground p-2 text-center text-xs">{t("no-more-results")}</div>
      )}
    </div>
  );
}
