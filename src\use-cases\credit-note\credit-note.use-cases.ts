import { Injectable } from '@nestjs/common';
import { UpdateResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { CreditNoteFactoryService } from './credit-note.factory';
import { ChargebeeBillingService } from '@services/billing';
import { CreditNoteStatus, FiscalEntity } from '@enums';
import { ChargeBeeWebhookPayload, PXActionResult } from '@types';
import { IDataServices } from '@abstracts';
import { CreditNote } from '@entities';

@Injectable()
export class CreditNoteUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly creditNoteFactory: CreditNoteFactoryService,
    private readonly chargebeeService: ChargebeeBillingService,
  ) {}

  /**
   * Finds a credit note or creates a new one if it doesn't exist
   * @param creditNote CreditNote entity
   * @returns CreditNote item record from database
   */
  async findOrCreate(creditNote: CreditNote): Promise<CreditNote> {
    const existingCreditNote = await this.dataServices.creditNote.getOneBy({
      chargebeeId: creditNote.chargebeeId,
    });

    if (existingCreditNote) {
      return existingCreditNote;
    }

    return this.dataServices.creditNote.create(creditNote);
  }

  /**
   * Creates a `CreditNote` from a Chargebee event.
   *
   * @param chargebeeEventContent - The content of the Chargebee webhook payload.
   * @returns A Promise that resolves to the created Transaction.
   */
  async createOrUpdateFromChargebeeEvent(
    chargebeeEventContent: ChargeBeeWebhookPayload['content'],
    action: 'create' | 'update',
  ): Promise<PXActionResult> {
    try {
      const creditNote =
        await this.creditNoteFactory.generateFromChargebeeEvent(
          chargebeeEventContent.credit_note,
        );

      const { first_name, last_name, email } =
        await this.chargebeeService.getCustomerInfo(creditNote.customerId);
      creditNote.customerEmail = email?.toLowerCase()?.trim();
      creditNote.customerName = `${first_name} ${last_name}`;
      await this.creditNoteFactory.addImageAndDescriptionToLineItems(
        this.dataServices,
        creditNote.lineItems,
      );

      let success = false;
      let message = '';

      if (action === 'create') {
        const createResult = await this.findOrCreate(creditNote);
        success = !!createResult?.id;
        message = success
          ? 'Credit Note created successfully'
          : 'No Credit Note created';
      } else {
        const updateResult = await this.update(
          creditNote.chargebeeId,
          creditNote,
        );
        success = !!updateResult?.affected;
        message = success
          ? 'Credit Note updated successfully'
          : 'No Credit Note updated';
      }

      return {
        data: { id: creditNote.chargebeeId },
        success,
        message,
      };
    } catch (err) {
      return {
        data: { id: chargebeeEventContent?.credit_note?.id },
        success: false,
        message: `Error occurred while updating credit note: ${err}`,
      };
    }
  }

  /**
   * Updates a `CreditNote` with the specified `chargebeeId`.
   *
   * @param chargebeeId - The `chargebeeId` of the `CreditNote` to update.
   * @param updates - The updates to apply to the `CreditNote`.
   * @returns A promise that resolves to the result of the update operation.
   */
  async update(
    chargebeeId: string,
    updates: QueryDeepPartialEntity<CreditNote>,
  ): Promise<UpdateResult> {
    return this.dataServices.creditNote.update(
      {
        chargebeeId,
      },
      updates,
    );
  }

  /**
   * Retrieves all `CreditNote` records matching provided filters
   * @param limit Number of `CreditNote`s to fetch
   * @param page Page number
   */
  async list(
    limit?: number,
    page?: number,
  ): Promise<{ items: CreditNote[]; total: number }> {
    return this.dataServices.creditNote.getAll(limit, page, {
      createdAt: 'DESC',
    });
  }

  /**
   * Searches and returns a `CreditNote` record from database
   * @param id `id` of the `CreditNote` to search for
   * @returns `CreditNote` record from database OR null
   */
  async getOne(id: string): Promise<CreditNote> {
    return this.dataServices.creditNote.getOneBy({ chargebeeId: id });
  }

  /**
   * Searches for credit notes based on the provided search criteria.
   *
   * @param fiscalEntity - The fiscal entity associated with the credit notes.
   * @param customer - The customer ID to filter credit notes.
   * @param subscription - The subscription ID to filter credit notes.
   * @param status - The status of the credit notes.
   * @param limit - The maximum number of credit notes to return.
   * @param page - The page number of the results.
   * @param orderBy - The order in which the credit notes should be sorted. Defaults to 'DESC'.
   * @returns A promise that resolves to an object containing the credit note items and the total count.
   */
  async searchAll(
    searchQuery?: string,
    fiscalEntity?: FiscalEntity,
    customer?: string,
    subscription?: string,
    status?: CreditNoteStatus,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: CreditNote[]; total: number }> {
    const queryBuilder = this.dataServices.creditNote
      .getRepository()
      .createQueryBuilder('creditNote');

    if (searchQuery) {
      queryBuilder.where('creditNote.customerEmail ILIKE :searchQuery', {
        searchQuery: `%${searchQuery}%`,
      });
      queryBuilder.orWhere('creditNote.customerName ILIKE :searchQuery', {
        searchQuery: `%${searchQuery}%`,
      });
    }

    if (status) {
      queryBuilder.andWhere('creditNote.status = :status', { status });
    }

    if (customer) {
      queryBuilder.andWhere('creditNote.customerId = :customer', { customer });
    }

    if (subscription) {
      queryBuilder.andWhere('creditNote.subscriptionId = :subscription', {
        subscription,
      });
    }

    if (fiscalEntity) {
      queryBuilder.andWhere('creditNote.businessEntityId = :fiscalEntity', {
        fiscalEntity,
      });
    }

    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.orderBy('creditNote.createdAt', orderBy || 'DESC');

    const [items, total] = await queryBuilder.getManyAndCount();
    return {
      items,
      total,
    };
  }
}
