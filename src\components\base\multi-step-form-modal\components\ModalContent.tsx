"use client";

import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";
import { useEffect } from "react";

import { StepConfig } from "../types";
import { FieldComponent } from "./fields/field-component";

interface ModalContentProps {
  step: StepConfig;
  allSteps: StepConfig[];
  currentStep: number;
}

export const ModalContent = ({ step, allSteps, currentStep }: ModalContentProps) => {
  const {
    formState: { errors },
    watch,
  } = useFormContext();
  const hasErrors = Object.keys(errors).length > 0;
  const t = useTranslations("validation");

  // Find which steps have errors
  const stepsWithErrors = allSteps
    .map((step, index) => {
      const stepFields = step.fields.map((f) => f.id);
      const hasStepErrors = Object.keys(errors).some((errorKey) => stepFields.includes(errorKey));
      return hasStepErrors ? index : -1;
    })
    .filter((index) => index !== -1);

  return (
    <div className="grid gap-6">
      {step.description && <p className="text-xs text-muted-foreground">{step.description}</p>}
      {hasErrors && (
        <div className="rounded-md bg-destructive/10 p-3">
          <p className="text-sm font-medium text-destructive">
            {t("errors_found")}
            {stepsWithErrors.length > 0 &&
              stepsWithErrors.some((stepIndex) => stepIndex !== currentStep) && (
                <>
                  <br />
                  <span className="text-xs">
                    {t("errors_in_steps", {
                      steps: stepsWithErrors
                        .filter((stepIndex) => stepIndex !== currentStep)
                        .map((stepIndex) => allSteps[stepIndex].title)
                        .join(", "),
                    })}
                  </span>
                </>
              )}
          </p>
        </div>
      )}
      {step.fields.map((field) => (
        <FieldComponent key={field.id} field={field} />
      ))}
    </div>
  );
};
