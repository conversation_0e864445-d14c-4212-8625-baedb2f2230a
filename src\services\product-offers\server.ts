import { auth } from "@clerk/nextjs/server";
import serverFetcher from "@/lib/server/server-fetcher";
import { PRODUCT_OFFERS_API_ENDPOINTS, ProductOffersApiTypes } from "./core";

/**
 * Creates a server-side token getter for the fetcher
 * @returns Promise with authentication token or null
 */
const getServerToken = async () => {
  const session = await auth();
  return session?.getToken() || null;
};

// Initialize the fetcher with server-side token getter
const apiFetcher = serverFetcher(getServerToken);

/**
 * Server-side function for fetching all product offers
 * @returns Promise with offers list data
 */
export async function listAllOffers(): Promise<ProductOffersApiTypes["listAllOffers"]> {
  const { url } = PRODUCT_OFFERS_API_ENDPOINTS.listAllOffers();
  return apiFetcher(url);
}
