"use client";

import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

const badgeColors = {
  blue: "bg-blue-100 text-blue-500",
  gray: "bg-[#BBBBBB] text-[#222222]",
  green: "bg-[#E5F7D1] text-[#296218]",
  orange: "bg-[#FCEFD4] text-[#C6712B]",
  red: "bg-[#FBE2D5] text-[#BE2E2D]",
  white: "bg-white text-[#222222]",
};
// To be ported to Hermes
export enum InvoiceStatus {
  PAID = "paid",
  PENDING = "pending",
  VOIDED = "voided",
  NOT_PAID = "not_paid",
  POSTED = "posted",
  PAYMENT_DUE = "payment_due",
}

const computeStatusColor = (status: InvoiceStatus) => {
  switch (status) {
    case InvoiceStatus.PAID:
      return badgeColors.green;
    case InvoiceStatus.VOIDED:
      return badgeColors.orange;
    case InvoiceStatus.PAYMENT_DUE:
    case InvoiceStatus.NOT_PAID:
      return badgeColors.red;
    default:
      return badgeColors.gray;
  }
};

export default function BadgeInvoiceStatus({ status }: { status: InvoiceStatus }) {
  const t = useTranslations();

  return (
    <div
      className={cn(
        computeStatusColor(status),
        "z-20 flex h-[28px] w-max items-center rounded-[20px] border-none px-[0.75rem] py-[0.375rem] text-[12px] font-medium",
      )}
    >
      {t(`invoices.status.${status}`)}
    </div>
  );
}
