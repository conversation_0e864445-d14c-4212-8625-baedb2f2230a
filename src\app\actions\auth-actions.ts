"use server";

import { auth, clerkClient } from "@clerk/nextjs/server";
import { fetchUserDetailsByEmail } from "@/services/customers/server";

/**
 * Gets the isParadox flag from user access details for a specific email
 *
 * @param email The email to check
 * @returns Promise resolving to a boolean indicating whether the email belongs to a Paradox admin
 */
export async function getUserIsParadoxStatus(email: string): Promise<boolean> {
  if (!email || !email.includes("@")) {
    console.error("getUserIsParadoxStatus: Invalid email provided");
    return false;
  }

  try {
    const userDetails = await fetchUserDetailsByEmail(email);
    return userDetails?.accessInfo?.isParadox ?? false;
  } catch (error) {
    console.error(`Error checking paradox status for ${email}:`, error);
    return false;
  }
}

/**
 * Checks if the currently authenticated user is a Paradox admin
 *
 * @returns Promise resolving to a boolean indicating whether the current user is a Paradox admin
 */
export async function checkCurrentUserAdminStatus(): Promise<boolean> {
  try {
    // Get the current user session from Clerk
    const session = await auth();
    const userEmail = session?.sessionClaims?.emailAddress as string;

    if (!userEmail) {
      console.warn("checkCurrentUserAdminStatus: No email found in session");
      return false;
    }

    // Check if the current user is a Paradox admin
    return await getUserIsParadoxStatus(userEmail);
  } catch (error) {
    console.error("Error checking current user admin status:", error);
    return false;
  }
}

/**
 * Update the role of a user
 * @param id The id of the user
 * @param role The role to update the user to
 * @returns The role of the user
 */
export const updateUserRole = async (id: string, role: string) => {
  const clerk = await clerkClient();
  const user = await clerk.users.updateUserMetadata(id, {
    publicMetadata: {
      role,
    },
  });
  return role;
};
