import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedTimestampTriadsSessionTime1749111026367 implements MigrationInterface {
    name = 'AddedTimestampTriadsSessionTime1749111026367'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "triads" ADD "sessionTime_temp" TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`UPDATE "triads" SET "sessionTime_temp" = "sessionTime" AT TIME ZONE 'UTC' WHERE "sessionTime" IS NOT NULL`);
        await queryRunner.query(`ALTER TABLE "triads" DROP COLUMN "sessionTime"`);
        await queryRunner.query(`ALTER TABLE "triads" RENAME COLUMN "sessionTime_temp" TO "sessionTime"`);
        await queryRunner.query(`ALTER TABLE "triads" ALTER COLUMN "sessionTime" SET NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "triads" ADD "sessionTime_temp" TIMESTAMP`);
        await queryRunner.query(`UPDATE "triads" SET "sessionTime_temp" = "sessionTime"::timestamp WHERE "sessionTime" IS NOT NULL`);
        await queryRunner.query(`ALTER TABLE "triads" DROP COLUMN "sessionTime"`);
        await queryRunner.query(`ALTER TABLE "triads" RENAME COLUMN "sessionTime_temp" TO "sessionTime"`);
        await queryRunner.query(`ALTER TABLE "triads" ALTER COLUMN "sessionTime" SET NOT NULL`);
    }
}
