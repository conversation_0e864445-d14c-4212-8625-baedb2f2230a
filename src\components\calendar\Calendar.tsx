"use client";

import { addDays, addMonths, addWeeks, subDays, subMonths, subWeeks } from "date-fns";
import { useEffect, useState } from "react";

import CalendarDesktop from "./CalendarDesktop";
import CalendarList from "./layouts/List";
import CalendarToolbar from "./Toolbar";
import useCalendarStore from "@/hooks/store/calendar";
import { useBreakpoints, BreakpointKey } from "@/hooks/breakpoints";

/**
 * Determines the display mode based on the breakpoint size.
 *
 * @param size - The breakpoint size which determines the layout.
 * @returns "list" for smaller screens and "full" for larger screens
 */

function computeDisplayMode(size: BreakpointKey) {
  switch (size) {
    case "xs":
    case "sm":
    case "md":
      return "list";
    default:
      return "full";
  }
}

export default function Calendar() {
  const size = useBreakpoints();
  const { selectedDate, layout, setSelectedDate } = useCalendarStore();
  const [isClient, setIsClient] = useState(false);

  const mode = computeDisplayMode(size as string);

  const goToToday = () => {
    const newDate = new Date();
    setSelectedDate(newDate);
  };

  const next = () => {
    let newDate;
    switch (layout) {
      case "week":
        newDate = addWeeks(selectedDate, 1);
        break;

      case "day":
        newDate = addDays(selectedDate, 1);
        break;

      default:
        newDate = addMonths(selectedDate, 1);
        break;
    }
    setSelectedDate(newDate);
  };

  const previous = () => {
    let newDate;
    switch (layout) {
      case "week":
        newDate = subWeeks(selectedDate, 1);
        break;

      case "day":
        newDate = subDays(selectedDate, 1);
        break;

      default:
        newDate = subMonths(selectedDate, 1);
        break;
    }
    setSelectedDate(newDate);
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="flex h-full w-full flex-col">
      {isClient && mode === "full" && (
        <>
          <CalendarToolbar goToToday={goToToday} next={next} previous={previous} />
          <CalendarDesktop />
        </>
      )}
      {isClient && mode === "list" && <CalendarList />}
    </div>
  );
}
