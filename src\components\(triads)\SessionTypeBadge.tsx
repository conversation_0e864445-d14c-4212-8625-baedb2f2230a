"use client";

import Image from "next/image";
import { SessionType } from "@/types/triad";
import { cn } from "@/lib/utils";

interface SessionTypeBadgeProps {
  type: SessionType;
  className?: string;
}

export function SessionTypeBadge({ type, className }: SessionTypeBadgeProps) {
  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
        type === "PXS" ? "bg-[#5765F1]/20 text-[#5765F1]" : "bg-primary/20 text-primary",
        className,
      )}
    >
      <Image
        src={type === "PXS" ? "/pxs.svg" : "/pxl.svg"}
        alt={type}
        width={35}
        height={20}
        className="object-contain"
      />
    </span>
  );
}
