"use client";

import * as React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Image as ImageIcon } from "lucide-react";

export interface ListItemCardProps {
  title: string;
  badge?: React.ReactNode;
  showImage?: boolean;
  imageUrl?: string;
  imageAlt?: string;
  infos: Record<string, React.ReactNode>;
  icon?: React.ReactNode;
  iconClick?: () => void;
  className?: string;
  onClick?: () => void;
}

export function ListItemCard({
  title,
  badge,
  showImage = false,
  imageUrl,
  imageAlt,
  infos,
  icon,
  iconClick,
  className,
  onClick,
}: ListItemCardProps) {
  return (
    <div
      className={cn(
        "bg-background border-border hover:bg-accent/30 flex cursor-pointer items-center gap-4 rounded-xl border p-4 shadow-sm transition",
        className,
      )}
      onClick={onClick}
      tabIndex={onClick ? 0 : undefined}
      role={onClick ? "button" : undefined}
    >
      {showImage && (
        <div className="bg-muted flex h-16 w-16 flex-shrink-0 items-center justify-center overflow-hidden rounded-lg">
          {imageUrl ? (
            <Image
              src={imageUrl}
              alt={imageAlt || title}
              width={64}
              height={64}
              className="h-full w-full object-cover"
            />
          ) : (
            <ImageIcon className="text-muted-foreground h-8 w-8" />
          )}
        </div>
      )}
      <div className="min-w-0 flex-1">
        <div className="mb-4 flex items-center gap-4">
          <span className="truncate text-base font-semibold">{title}</span>
          {badge && <div className="ml-1">{badge}</div>}
          {icon && (
            <div onClick={iconClick} className="ml-auto">
              {icon}
            </div>
          )}
        </div>
        <div className="flex flex-col gap-0.5">
          {Object.entries(infos).map(([label, value]) => (
            <div key={label} className="text-muted-foreground flex justify-between text-sm">
              <span className="truncate">{label}</span>
              <span className="text-foreground ml-2 truncate text-right font-medium">{value}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
