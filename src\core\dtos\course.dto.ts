import { createZodDto } from 'nestjs-zod';
import {
  CreateCourseSchema,
  GetCourseByIdSchema,
  ListCourseSchema,
  ListCoursesResponseSchema,
  PaginationParamsSchema,
  UpdateCourseSchema,
  GetCourseByOfferIdSchema,
  GetStudentsForCourseSchema,
  GetStudentsForCourseResponseSchema,
  ListCourseCatalogParamsSchema,
  ListCourseCatalogResponseSchema,
} from '@px-shared-account/hermes';

export class CreateCourseDto extends createZodDto(CreateCourseSchema) {}
export class UpdateCourseDto extends createZodDto(UpdateCourseSchema) {}
export class GetCourseByIdDto extends createZodDto(GetCourseByIdSchema) {}
export class ListCoursesDTO extends createZodDto(
  ListCourseSchema.merge(PaginationParamsSchema),
) {}
export class ListCoursesResponseDto extends createZodDto(
  ListCoursesResponseSchema,
) {}
export class GetCoursesByOfferIdDTO extends createZodDto(
  GetCourseByOfferIdSchema,
) {}
export class GetStudentsForCourseDto extends createZodDto(
  GetStudentsForCourseSchema.merge(PaginationParamsSchema),
) {}
export class GetStudentsForCourseResponseDto extends createZodDto(
  GetStudentsForCourseResponseSchema,
) {}
export class ListCourseCatalogDto extends createZodDto(
  ListCourseCatalogParamsSchema,
) {}
export class ListCourseCatalogResponseDto extends createZodDto(
  ListCourseCatalogResponseSchema,
) {}
