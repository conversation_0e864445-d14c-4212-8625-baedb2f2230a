"use client";

import React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import Button from "@/components/base/button";

interface ButtonConfig {
  text: string;
  variant: "primary" | "secondary";
  ariaLabel?: string;
  href?: string;
}

export interface HeroCardProps {
  imageUrl: string;
  title: string;
  subtitle?: string;
  buttons: ButtonConfig[];
  className?: string;
  allowContentOverflow?: boolean;
}

export function HeroCard({
  imageUrl,
  title,
  subtitle,
  buttons,
  className,
  allowContentOverflow = false,
}: HeroCardProps) {
  return (
    <div
      className={cn(
        "relative flex min-h-[33svh] w-full flex-col justify-center overflow-visible",
        className,
      )}
    >
      <Image
        src={imageUrl}
        alt={title}
        fill
        className="absolute inset-0 z-[-1] h-full min-h-[55svh] w-full object-cover"
        sizes="100vw"
        priority
      />
      <div className="to-background absolute inset-0 z-[-1] h-full min-h-[55svh] w-full bg-gradient-to-b from-transparent" />

      <div
        className={cn(
          "relative mx-auto flex h-full w-full flex-col items-start justify-center text-left text-white",
          "px-8 pt-[5rem] pb-6 md:pb-8 lg:pt-[6.5rem] lg:pb-10",
          allowContentOverflow && "md:max-w-screen-md lg:max-w-screen-lg xl:max-w-screen-xl",
        )}
      >
        <h1 className="font-anton text-shadow-xl text-4xl uppercase lg:text-5xl">{title}</h1>
        {subtitle && (
          <p className="mt-3 max-w-xl text-lg text-neutral-200 text-shadow-lg md:mt-4 lg:text-xl">
            {subtitle}
          </p>
        )}
        {buttons && buttons.length > 0 && (
          <div className="mt-8 flex flex-wrap justify-start gap-3 md:gap-4">
            {buttons.map((button, index) => (
              <Button
                key={index}
                onClick={() => {
                  if (button.href) {
                    window.open(button.href, "_blank");
                  }
                }}
                aria-label={button.ariaLabel || button.text}
                variant={button.variant === "primary" ? "default" : "secondary"}
                className={cn(
                  "rounded-full px-8 py-3 text-base font-medium drop-shadow-lg",
                  button.variant === "secondary" &&
                    "bg-neutral-700 text-white hover:bg-neutral-600",
                )}
              >
                {button.text}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

HeroCard.displayName = "HeroCard";
