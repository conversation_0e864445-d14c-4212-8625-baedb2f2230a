"use client";

import React from "react";
import Image from "next/image";
import { CheckCircle2 } from "lucide-react";
import { cn } from "@/lib/utils";
import Button from "@/components/base/button";

export interface EventCardProps {
  imageUrl: string;
  title: string;
  description: string;
  detailsList: string[];
  actionButtonLabel: string;
  className?: string;
  href?: string;
}

export function EventCard({
  imageUrl,
  title,
  description,
  detailsList,
  actionButtonLabel,
  className,
  href,
}: EventCardProps) {
  return (
    <div
      className={cn(
        "relative flex flex-1 flex-col justify-end overflow-hidden rounded-lg shadow-lg md:min-h-[50svh]",
        className,
      )}
    >
      <Image
        src={imageUrl}
        alt={title}
        fill
        className="object-cover"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
      <div className="bg-background/60 absolute inset-0" />
      <div className="to-background absolute inset-0 bg-gradient-to-b from-transparent via-transparent" />
      <div className="relative flex h-full flex-col items-start justify-end p-8 text-white">
        <h2 className="font-anton text-shadow-xl lg:text-3xl">{title}</h2>
        <p className="mt-4 text-base text-neutral-200 text-shadow-lg md:mt-6">{description}</p>
        <ul className="mt-4 space-y-2 md:mt-6">
          {detailsList.map((item, index) => (
            <li key={index} className="flex items-center">
              <CheckCircle2 className="text-primary mr-2 h-5 w-5 flex-shrink-0" />
              <span className="text-shadow-xl text-sm text-neutral-200 md:text-base">{item}</span>
            </li>
          ))}
        </ul>
        <Button
          variant="outline"
          className="mt-6 rounded-full bg-transparent px-6 py-3 text-base font-medium text-white hover:bg-white/10 md:mt-8"
          onClick={() => {
            if (href) {
              window.open(href, "_blank");
            }
          }}
        >
          {actionButtonLabel}
        </Button>
      </div>
    </div>
  );
}

EventCard.displayName = "EventCard";
