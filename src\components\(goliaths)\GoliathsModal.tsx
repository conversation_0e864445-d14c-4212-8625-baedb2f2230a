"use client";

import { useUser } from "@clerk/nextjs";
import { useTranslations } from "next-intl";
import React, { useState, FormEvent, useEffect } from "react";
import PhoneInput from "react-phone-number-input";
import QRCode from "react-qr-code";
import "react-phone-number-input/style.css";

import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Input } from "../ui/input";
import Button from "../base/button";
import { signupGoliath } from "@/app/actions/goliaths-actions";
import { useCustomerAccessDetails } from "@/services/customers/client";

export default function GoliathModal({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const t = useTranslations("goliaths");
  const { user, isLoaded } = useUser();
  const { data: userAccessData } = useCustomerAccessDetails(user?.emailAddresses[0].emailAddress);
  const [btnDisabled, setBtnDisabled] = useState(false);
  const [result, setResult] = useState("");
  const [email, setEmail] = useState(user?.emailAddresses[0].emailAddress);
  const [phone, setPhone] = useState("");

  const isPOM = userAccessData?.accessInfo?.isPOM;

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setBtnDisabled(true);
    if (email && phone) {
      try {
        const response = await signupGoliath({
          phone,
          email,
          uuid: user?.id || "",
          hasPom: !!isPOM,
        });

        if (response.error) {
          switch (response.error) {
            case "already_registered":
              setResult(t("modal.errors.already_registered"));
              break;
            case "phone_number_already_exists":
              setResult(t("modal.errors.phone_number_already_exists"));
              break;
            case "email_already_exists":
              setResult(t("modal.errors.email_already_exists"));
              break;
            case "too_many_requests":
              setResult(t("modal.errors.too_many_requests"));
              break;
            default:
              setResult(t("modal.errors.error"));
              break;
          }
        } else {
          setResult(t("modal.success"));
          console.log("Form submitted successfully", response);
        }
      } catch (error) {
        setResult(t("modal.errors.error"));
        console.error("Error submitting form:", error);
      } finally {
        setBtnDisabled(false);
      }
    }
  };

  const isDisabled = !email || !phone;
  useEffect(() => {
    if (isLoaded) {
      setEmail(user?.emailAddresses[0].emailAddress);
      if (user?.phoneNumbers.length) {
        setPhone(user?.phoneNumbers[0].phoneNumber);
      }
    }
  }, [isLoaded, user]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="rounded-3xl border-none bg-[#111111] text-white sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl">{t("modal.title")}</DialogTitle>
          <div className="space-y-2 text-center text-gray-400">
            {!result ? (
              <form onSubmit={handleSubmit} className="mt-4 space-y-4">
                <div>
                  <div className="my-3">{t("modal.create-account")}</div>
                  <label
                    htmlFor="email"
                    className="block text-left text-sm font-medium text-gray-300"
                  >
                    {t("modal.email")}
                  </label>
                  <Input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    disabled
                    className="mt-1 block w-full rounded-md border border-gray-700 bg-gray-800 px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  />
                </div>

                <div>
                  <label
                    htmlFor="phone"
                    className="block text-left text-sm font-medium text-gray-300"
                  >
                    {t("modal.phone")}
                  </label>
                  <PhoneInput
                    international
                    countryCallingCodeEditable={false}
                    defaultCountry="FR"
                    value={phone}
                    onChange={(e) => setPhone(e || "")}
                    className="mt-1 block w-full rounded-md border border-gray-700 bg-gray-800 px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:outline-none"
                    numberInputProps={{
                      className: "bg-transparent",
                    }}
                  />
                </div>

                <div className="flex flex-col gap-4">
                  <Button type="submit" disabled={isDisabled || btnDisabled}>
                    {t("modal.submit")}
                  </Button>
                  <small>{t("modal.risk")}</small>
                  {isPOM ? <small>{t("modal.is-pom")}</small> : null}
                </div>
              </form>
            ) : (
              <div className="flex flex-col items-center justify-center gap-4">
                <div className="text-center">
                  <Button
                    onClick={() => {
                      setResult("");
                    }}
                  >
                    Retour
                  </Button>
                  <p>{result}</p>
                </div>
                <QRCode value="https://qrco.de/goliaths" />
              </div>
            )}
          </div>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
