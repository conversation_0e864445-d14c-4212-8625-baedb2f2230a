"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useApi } from "@/hooks/store/use-api";
import { useToast } from "@/hooks/use-toast";
import { ROLE_API_ENDPOINTS, RoleApiTypes, ListRolesParams, RoleApiPayloads } from "./core";

/**
 * Hook for fetching and managing role lists
 * @param params Filter and pagination parameters
 * @returns SWR response with role list data
 */
export function useRoleList(params: ListRolesParams = {}) {
  const apiConfig = useMemo(() => {
    const { url } = ROLE_API_ENDPOINTS.list(params);
    return url;
  }, [params]);

  return useApi<RoleApiTypes["list"]>(apiConfig);
}

/**
 * Hook for fetching a specific role by ID
 * @param id Role ID to fetch
 * @returns SWR response with role data
 */
export function useRoleDetails(id?: number) {
  const apiConfig = useMemo(() => {
    if (!id) return null;
    const { url } = ROLE_API_ENDPOINTS.getById(id);
    return url;
  }, [id]);

  return useApi<RoleApiTypes["getById"]>(apiConfig);
}

/**
 * Hook for creating a new role
 * @returns Functions and state for role creation
 */
export function useCreateRole() {
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = ROLE_API_ENDPOINTS.create();
  const { trigger, isLoading } = useApi<RoleApiTypes["create"]>(url, {
    method: method as "POST",
  });

  const create = useCallback(
    async (data: RoleApiPayloads["create"]) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        const result = await trigger(data);
        toast({
          title: "Role created",
          description: "The role has been created successfully",
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: "Error creating role",
          description: errorMessage || "An error occurred while creating the role",
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger],
  );

  return { create, isLoading, error };
}

/**
 * Hook for updating an existing role
 * @param id Optional initial role ID
 * @returns Functions and state for role updates
 */
export function useUpdateRole(id?: number) {
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);

  const { trigger, isLoading } = useApi<RoleApiTypes["update"]>(
    id ? ROLE_API_ENDPOINTS.update(id).url : null,
    { method: "PATCH" },
  );

  const update = useCallback(
    async (roleId: number, data: RoleApiPayloads["update"]) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        const result = await trigger(data);
        toast({
          title: "Role updated",
          description: "The role has been updated successfully",
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: "Error updating role",
          description: errorMessage || "An error occurred while updating the role",
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger],
  );

  return { update, isLoading, error };
}

/**
 * Hook for deleting a role
 * @param id Optional initial role ID
 * @returns Functions and state for role deletion
 */
export function useDeleteRole(id?: number) {
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);

  const { trigger, isLoading } = useApi<RoleApiTypes["delete"]>(
    id ? ROLE_API_ENDPOINTS.delete(id).url : null,
    { method: "DELETE" },
  );

  const deleteRole = useCallback(
    async (roleId: number) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        const result = await trigger();
        toast({
          title: "Role deleted",
          description: "The role has been deleted successfully",
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: "Error deleting role",
          description: errorMessage || "An error occurred while deleting the role",
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger],
  );

  return { deleteRole, isLoading, error };
}
