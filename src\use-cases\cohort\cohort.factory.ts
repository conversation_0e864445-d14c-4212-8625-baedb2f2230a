import { CreateCohortDto, UpdateCohortDto } from '@dtos';
import { CohortEntity, CourseEntity } from '@entities';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CohortFactory {
  /**
   * Generates a new cohort
   * @param cohortInfo cohort information
   * @returns CohortEntity
   */
  generate(cohortInfo: CreateCohortDto): CohortEntity {
    const cohort = new CohortEntity();
    cohort.name = cohortInfo.name;
    cohort.description = cohortInfo.description;
    cohort.maxParticipants = cohortInfo.maxParticipants;
    cohort.currentParticipants = cohortInfo.currentParticipants || 0;
    cohort.startDate = cohortInfo.startDate;
    cohort.endDate = cohortInfo.endDate;
    cohort.status = cohortInfo.status;
    const linkedCourse = new CourseEntity();
    linkedCourse.id = cohortInfo.courseId;
    cohort.course = linkedCourse;
    cohort.communityInfo = cohortInfo.communityInfo;
    return cohort;
  }

  /**
   * Generates updates for a cohort
   * @param cohortUpdates Cohort updates
   * @returns Cohort updates
   */
  generateCourseUpdates(cohortUpdates: UpdateCohortDto): Partial<CohortEntity> {
    const updates: Partial<CohortEntity> = {};
    for (const [key, value] of Object.entries(cohortUpdates)) {
      if (key === 'course') {
        const course = new CourseEntity();
        course.id = value;
        updates['course'] = course;
        continue;
      }
      updates[key] = value;
    }
    return updates;
  }
}
