import { Module } from '@nestjs/common';
import { ClerkModule } from '@services/sso';
import { LearnworldsModule } from '@services/lms';
import { CircleModule } from '@services/community';
import { ProductDeliveryService } from './product-delivery.service';
import { CustomerAccessModule } from '../customer-access';
import { SlackModule } from '@services/notification';
import { UserModule } from '../user';

@Module({
  imports: [
    UserModule,
    CustomerAccessModule,
    ClerkModule,
    LearnworldsModule,
    CircleModule,
    SlackModule,
  ],
  providers: [ProductDeliveryService],
  exports: [ProductDeliveryService],
})
export class ProductDeliveryModule {}
