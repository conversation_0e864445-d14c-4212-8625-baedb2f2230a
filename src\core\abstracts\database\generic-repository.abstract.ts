import {
  DeleteResult,
  Enti<PERSON>Target,
  FindOptionsOrder,
  FindOptionsWhere,
  Repository,
  UpdateResult,
} from 'typeorm';
import { IPaginationParams } from '@px-shared-account/hermes';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { UpdateResultWithItemInfo } from '@types';

export abstract class IGenericRepository<T> {
  abstract getAll(
    limit?: number,
    page?: number,
    sortOptions?: FindOptionsOrder<T>,
  ): Promise<{ items: T[]; total: number }>;
  abstract getOneBy(findOptions: FindOptionsWhere<T>): Promise<T>;
  abstract getAllBy(
    findOptions: FindOptionsWhere<T>,
    limit: number,
    page: number,
    sortOptions?: FindOptionsOrder<T>,
  ): Promise<{ items: T[]; total: number }>;
  abstract findAllWithRelations(
    whereOptions: FindOptionsWhere<T>,
    limit: number,
    page: number,
    sortOptions?: FindOptionsOrder<T>,
  ): Promise<{ items: T[]; total: number }>;
  abstract getOneWithRelations(
    tableToSearch: string,
    columnToMatch: string,
    lookupValue: string | number | boolean,
  ): Promise<T>;
  abstract getOneWithLeftJoin(
    columnToMatch: string,
    lookupValue: string | number | boolean,
    tableToJoin: string,
    joiningColumnInParent: string,
  ): Promise<T>;
  abstract getAllWithMatchingIds(
    idsToSearch: number[],
    joiningTableName: string,
    joiningColumn: string,
  ): Promise<T[]>;
  abstract getByUsingQueryBuilder(
    tableToSearch: string,
    columnToMatch: string,
    lookupValue: string | number | boolean,
  ): Promise<T>;
  abstract getAllMatchingWithRelationIds(
    tableToSearch: string,
    columnToMatch: string,
    lookupValue: string | number | boolean,
  ): Promise<T[]>;
  abstract create(item: T): Promise<T>;
  abstract update(
    findOptions: FindOptionsWhere<T>,
    updates: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult>;
  abstract updateAndReturnItem(
    lookupColumnName: string,
    lookupColumnValue: string,
    updates: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResultWithItemInfo<T>>;
  abstract delete(lookupOptions: FindOptionsWhere<T>): Promise<DeleteResult>;
  abstract hardDelete(
    lookupOptions: FindOptionsWhere<T>,
  ): Promise<DeleteResult>;
  abstract search(
    entity: EntityTarget<T>,
    query: string,
    queryColumns: (keyof T)[],
    filters: FindOptionsWhere<T>[],
    loadRelations?: boolean,
    paginationParams?: IPaginationParams,
  ): Promise<{ items: T[]; total: number }>;
  abstract getTableName(): string;
  abstract getRepository(): Repository<T>;
}
