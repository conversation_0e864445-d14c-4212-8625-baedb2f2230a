"use server";

import { uploadFile } from "@/lib/server/s3";
import { v4 as uuidv4 } from "uuid";

/**
 * Uploads an image to S3 and returns the public URL
 *
 * @known_issue The S3 bucket public URLs are currently not accessible due to bucket ACL settings.
 * This is not a code issue but rather a configuration one. To fix this:
 * 1. The S3 bucket needs to be configured to allow public access
 * 2. The bucket policy needs to be updated to grant public read access
 * 3. The objects' ACL needs to be set to public-read
 *
 * This has been confirmed to work on other S3 buckets in the project, just needs
 * to be configured for this new bucket. Until then, uploaded images will not be
 * viewable through their public URLs.
 */
export async function uploadImageToS3(formData: FormData) {
  try {
    const file = formData.get("file") as File;
    if (!file) {
      throw new Error("No file provided");
    }

    // Convert file to base64
    const buffer = Buffer.from(await file.arrayBuffer());
    const base64String = `data:${file.type};base64,${buffer.toString("base64")}`;

    // Generate a unique filename with UUID
    const filename = `${uuidv4()}.${file.name.split(".").pop()}`;

    // Upload to S3
    const response = await uploadFile({
      filename,
      image: base64String,
      contentType: file.type,
    });

    if (!response || response.$metadata?.httpStatusCode !== 200) {
      throw new Error("Failed to upload file");
    }

    // Return the URL using the public bucket URL
    return `${process.env.NEXT_PUBLIC_AWS_BUCKET_PUBLIC_URL}${filename}`;
  } catch (error) {
    console.error("Error uploading to S3:", error);
    throw new Error("Failed to upload file to S3");
  }
}
