"use client";

import { useMemo, useState } from "react";
import { useApi } from "@/hooks/store/use-api";
import { PAYMENT_API_ENDPOINTS, PaymentApiTypes } from "./core";

/**
 * Hook for fetching and managing payments lists (supports pagination)
 * @param page Page number (1-based)
 * @param limit Number of items per page
 * @returns SWR response with payments list data
 */
export function usePaymentsList(page: number = 1, limit: number = 10) {
  const apiConfig = useMemo(() => {
    const { url } = PAYMENT_API_ENDPOINTS.list();
    // Add pagination params
    const params = new URLSearchParams({ page: String(page), limit: String(limit) });
    return `${url}?${params.toString()}`;
  }, [page, limit]);

  return useApi<PaymentApiTypes["list"]>(apiConfig);
}

/**
 * Hook for fetching a specific payment by ID
 * @param id Payment ID to fetch
 * @returns SWR response with payment data
 */
export function usePaymentDetails(id?: string | null) {
  const apiConfig = useMemo(() => {
    if (!id) return null;
    const { url } = PAYMENT_API_ENDPOINTS.getByChargebeeId(id);
    return url;
  }, [id]);

  return useApi<PaymentApiTypes["getByChargebeeId"]>(apiConfig);
}

export function useFilteredInvoices(payment: any) {
  const [selectedYears, setSelectedYears] = useState<string[]>([]);
  const invoiceYears = useMemo(() => {
    if (!payment?.invoices) return [];
    return (
      Array.from(
        new Set(payment.invoices.map((inv: any) => new Date(inv.date).getFullYear().toString())),
      ) as string[]
    ).sort((a, b) => b.localeCompare(a));
  }, [payment]);
  const filteredInvoices = useMemo(() => {
    if (!payment?.invoices) return [];
    if (!selectedYears.length) return payment.invoices;
    return payment.invoices.filter((inv: any) =>
      selectedYears.includes(new Date(inv.date).getFullYear().toString()),
    );
  }, [payment, selectedYears]);

  return { invoices: filteredInvoices, invoiceYears, selectedYears, setSelectedYears };
}
