export type TriadStatus = "available" | "one_spot" | "full";
export type TriadGroup = "A" | "B" | "C";
export type SessionType = "PXS" | "PXL";

export interface TriadFilters {
  cohort?: string;
  period?: string;
  group?: TriadGroup;
  status?: TriadStatus;
}

export interface PlatformTriadStats {
  overview: {
    totalTriads: number;
    totalParticipants: number;
    averageParticipantsPerTriad: number;
    completionRate: number;
  };
  timeSeriesData: {
    triadsCreated: Array<{ date: string; value: number }>;
    participantsJoined: Array<{ date: string; value: number }>;
  };
  engagement: {
    recurringParticipants: number;
    sessionDurationDistribution: Array<{ duration: number; count: number }>;
  };
  organizers: {
    topOrganizers: Array<{
      id: string;
      name: string;
      triadsCount: number;
    }>;
  };
}
