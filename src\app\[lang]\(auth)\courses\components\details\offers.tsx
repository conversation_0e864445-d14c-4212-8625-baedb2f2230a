"use client";

import { useTranslations } from "next-intl";
import { DataTable } from "@/components/table";
import { useCourseDetails } from "@/services/course";
import { ColumnDef } from "@tanstack/react-table";
import { ProductOfferStatus, ICourseOffer } from "@px-shared-account/hermes";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { useMemo, useState, useCallback, useEffect } from "react";
import { format } from "date-fns";

interface CourseOffersProps {
  id: number;
}

export function CourseOffers({ id }: CourseOffersProps) {
  const t = useTranslations("courses.details");
  const tOfferStatus = useTranslations("courses.offer_status");
  // Fetch the course details to get the associated offers
  const { data: courseData, isLoading } = useCourseDetails(id);
  const [sortState, setSortState] = useState<{ sortBy?: string; sortOrder?: "asc" | "desc" }>({
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  // Extract and normalize the offers from the course data
  const courseOffers = useMemo(() => {
    if (!courseData?.offers || !Array.isArray(courseData.offers)) return [];

    // Get the offers from course data
    const offers = courseData.offers;

    // Apply client-side sorting if needed
    if (sortState.sortBy === "createdAt") {
      return [...offers].sort((a, b) => {
        const dateA = new Date(a.createdAt || 0).getTime();
        const dateB = new Date(b.createdAt || 0).getTime();
        return sortState.sortOrder === "asc" ? dateA - dateB : dateB - dateA;
      });
    }

    return offers;
  }, [courseData, sortState]);

  // Handle table state changes including sorting
  const handleTableStateChange = useCallback((state: any) => {
    if (state.sortBy) {
      setSortState({
        sortBy: state.sortBy.id,
        sortOrder: state.sortBy.desc ? "desc" : "asc",
      });
    }
  }, []);

  const columns: ColumnDef<ICourseOffer>[] = [
    {
      accessorKey: "name",
      header: t("offers.name"),
      cell: ({ row }) => (
        <div className="flex items-center gap-4">
          {row.original.image ? (
            <div className="relative aspect-square size-16 flex-shrink-0 overflow-hidden rounded-xl">
              <Image
                src={row.original.image}
                alt={row.original.name}
                fill
                sizes="40px"
                className="object-cover"
              />
            </div>
          ) : (
            <div className="h-8 w-8"></div>
          )}
          <span>{row.original.name}</span>
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: t("offers.status"),
      cell: ({ row }) => {
        const status = row.original.status.toLowerCase() as Lowercase<ProductOfferStatus>;
        return <Badge>{tOfferStatus(status)}</Badge>;
      },
      enableSorting: true,
    },
    {
      id: "createdAt",
      accessorKey: "createdAt",
      header: t("offers.created_at"),
      cell: ({ row }) => (
        <span>
          {row.original.createdAt ? format(new Date(row.original.createdAt), "MMM dd, yyyy") : "-"}
        </span>
      ),
      enableSorting: true,
    },
  ];
  return (
    <div className="overflow-x-auto">
      <DataTable
        columns={columns}
        data={courseOffers}
        enableSearch
        enableSorting
        useClientPagination={true}
        isLoading={isLoading}
        noResults={t("offers.no_results")}
        totalRows={courseOffers.length}
        getRowId={(row: ICourseOffer) => String(row.id)}
        onStateChange={handleTableStateChange}
        title={`${t("offers.title")} (${courseOffers.length})`}
      />
    </div>
  );
}
