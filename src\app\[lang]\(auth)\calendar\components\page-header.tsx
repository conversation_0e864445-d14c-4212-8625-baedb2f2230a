"use client";

import { List, PlusIcon, Calendar, Eye } from "lucide-react";
import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import { Switcher } from "@/components/base/switcher";
import { Toggle } from "@/components/ui/toggle";
import UpdateSlotModal from "@/components/calendar/UpdateSlotModal";

export default function PageHeader() {
  const t = useTranslations("calendar");

  return (
    <div className="flex items-center justify-between">
      <div className="flex w-full justify-between gap-4 md:w-auto">
        <h1 className="text-2xl font-bold">{t("title")}</h1>
        <div className="flex items-center justify-between gap-4">
          <Toggle
            className="!aspect-square !h-8 !w-auto !min-w-0 rounded-full !px-0 text-muted-foreground transition-shadow data-[state=on]:shadow-inner md:hidden"
            variant="default"
          >
            <Eye className="h-4 w-4" />
          </Toggle>
          <Switcher activeIcon={Calendar} inactiveIcon={List} className="h-8 w-14" />
        </div>
      </div>
      <UpdateSlotModal type="create">
        <Button className="hidden rounded-full lg:flex">
          <PlusIcon className="h-4 w-4" />
          <span>{t("suggest-a-slot")}</span>
        </Button>
      </UpdateSlotModal>
    </div>
  );
}
