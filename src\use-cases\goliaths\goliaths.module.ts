import { forwardRef, Module } from '@nestjs/common';
import { GoliathService } from './goliaths.use-cases';
import { DataServicesModule } from '@services/database';
import { RedisClientsModule } from '@services/event-publishers';
import { CustomerioModule } from 'src/services/notification/';
import { UserModule } from '../user';

@Module({
  imports: [
    DataServicesModule,
    RedisClientsModule,
    CustomerioModule,
    forwardRef(() => UserModule),
  ],
  providers: [GoliathService],
  exports: [GoliathService],
})
export class GoliathsModule {}
