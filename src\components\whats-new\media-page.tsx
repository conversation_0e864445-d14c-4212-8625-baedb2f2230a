"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { ImageUpload } from "./image-upload";
import { MediaGallery } from "./media-gallery";
import { IThumbnailImage } from "@px-shared-account/hermes";
import { useCustomerAccessDetails } from "@/services/customers";
import { useSession } from "@clerk/nextjs";
import { Loader2, ArrowLeft } from "lucide-react";
import { Button } from "@/components/base/button";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function MediaPage() {
  const t = useTranslations();
  const router = useRouter();
  const [thumbnails, setThumbnails] = useState<IThumbnailImage[]>([]);

  // Fetch user access details
  const { session } = useSession();
  const userEmail = session?.user?.primaryEmailAddress?.emailAddress;
  const { data: accessData, isLoading: isAccessLoading } = useCustomerAccessDetails(userEmail);

  // Determine if user is admin (Paradox email)
  const isAdmin = accessData?.accessInfo.isParadox ?? false;

  if (isAccessLoading) {
    return (
      <div className="container mx-auto flex items-center justify-center px-4 py-16">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="rounded-lg border border-red-500 bg-red-900/20 p-4">
          <p className="text-red-500">{t("common.permission-denied")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl px-4 py-16">
      <div className="mb-6">
        <Link href="/whats-new">
          <Button variant="ghost" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            {t("common.back")}
          </Button>
        </Link>
      </div>

      <h1 className="font-anton mb-8 text-4xl font-bold">
        {t("whats-new.admin.media-management")}
      </h1>

      <div className="space-y-8">
        <div className="border-secondary-foreground bg-secondary/80 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">{t("whats-new.admin.upload-new-media")}</h2>
          <ImageUpload thumbnails={thumbnails} onThumbnailsChange={setThumbnails} />
          <p className="text-muted-foreground mt-2 text-sm">
            {t("whats-new.admin.media-upload-note")}
          </p>
        </div>

        <div className="border-secondary-foreground bg-secondary/80 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">{t("whats-new.admin.media-gallery")}</h2>
          <MediaGallery />
        </div>
      </div>
    </div>
  );
}
