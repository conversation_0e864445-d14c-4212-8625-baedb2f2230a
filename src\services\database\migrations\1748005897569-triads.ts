import { MigrationInterface, QueryRunner } from 'typeorm';

export class Triads1748005897569 implements MigrationInterface {
  name = 'Triads1748005897569';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "triadParticipants" ("id" SERIAL NOT NULL, "joinedAt" TIMESTAMP NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, "deletedAt" TIMESTAMP, "userId" integer, "triadId" integer, CONSTRAINT "UQ_6f0c38e56fd0fb8b00269baeb7c" UNIQUE ("userId", "triadId"), CONSTRAINT "PK_c90826cd241eb9cc8485272266f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_triadParticipants_userId" ON "triadParticipants" ("userId") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."triads_sessiontype_enum" AS ENUM('PXS', 'PXL')`,
    );
    await queryRunner.query(
      `CREATE TABLE "triads" ("id" SERIAL NOT NULL, "title" character varying NOT NULL, "sessionTime" TIMESTAMP NOT NULL, "duration" double precision NOT NULL, "meetingLink" character varying NOT NULL, "maxParticipants" integer NOT NULL, "sessions" text array NOT NULL DEFAULT '{}', "sessionType" "public"."triads_sessiontype_enum" NOT NULL DEFAULT 'PXS', "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, "deletedAt" TIMESTAMP, "organizerId" integer, CONSTRAINT "PK_8d42f9ed507f24f7f5d92950fcc" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "triadParticipants" ADD CONSTRAINT "FK_b824edec802ca40d05befd5cfe5" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "triadParticipants" ADD CONSTRAINT "FK_c0eef47b43bec3a1658ec7a8466" FOREIGN KEY ("triadId") REFERENCES "triads"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "triads" ADD CONSTRAINT "FK_6037b617433c53ee32596ff99ff" FOREIGN KEY ("organizerId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "triads" DROP CONSTRAINT "FK_6037b617433c53ee32596ff99ff"`,
    );
    await queryRunner.query(
      `ALTER TABLE "triadParticipants" DROP CONSTRAINT "FK_c0eef47b43bec3a1658ec7a8466"`,
    );
    await queryRunner.query(
      `ALTER TABLE "triadParticipants" DROP CONSTRAINT "FK_b824edec802ca40d05befd5cfe5"`,
    );
    await queryRunner.query(`DROP TABLE "triads"`);
    await queryRunner.query(`DROP TYPE "public"."triads_sessiontype_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_triadParticipants_userId"`,
    );
    await queryRunner.query(`DROP TABLE "triadParticipants"`);
  }
}
