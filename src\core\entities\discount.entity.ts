import {
  DiscountStatus,
  DiscountType,
  DiscountDurationType,
  DiscountDurationPeriodUnit,
  DiscountApplication,
} from '@enums';
import { ProductPlanPrice } from '.';
import { DiscountConstraint } from '@types';

export class Discount {
  id: number;
  name: string;
  code: string;
  status: DiscountStatus;
  invoiceName?: string;
  type: DiscountType;
  currency?: string;
  amount: number;
  applyOn: DiscountApplication;
  durationType: DiscountDurationType;
  durationPeriodUnit?: DiscountDurationPeriodUnit;
  durationPeriodAmount?: number;
  validUntil?: Date;
  maxRedemptions?: number;
  attachedTo?: DiscountConstraint[];
  pricesAttachedTo?: ProductPlanPrice[];
}
