import { z } from "zod";
import { CourseBaseSchema } from "./course.interface";
import { CohortStatus } from "../enums";

export const SpaceInfoSchema = z.object({
  id: z.number(),
  slug: z.string(),
  name: z.string(),
});
export type ISpaceInfo = z.infer<typeof SpaceInfoSchema>;

export const CommunityInfoSchema = z
  .object({
    PXS: z.array(SpaceInfoSchema),
    PXL: z.array(SpaceInfoSchema),
  })
  .refine((data) => data.PXS.length > 0 || data.PXL.length > 0, {
    message: "At least one of PXS or PXL must contain at least one item.",
  });
export type ICommunityInfo = z.infer<typeof CommunityInfoSchema>;

export const CohortBaseSchema = z.object({
  id: z.number(),
  name: z.string().max(100).nonempty(),
  description: z.string().max(1500).nonempty(),
  maxParticipants: z.number().min(1),
  currentParticipants: z.number().min(0),
  startDate: z.date(),
  endDate: z.date(),
  status: z.nativeEnum(CohortStatus),
  course: CourseBaseSchema,
  communityInfo: CommunityInfoSchema,
});
export type ICohortBase = z.infer<typeof CohortBaseSchema>;

export const CreateCohortSchema = CohortBaseSchema.partial({
  id: true,
  status: true,
  currentParticipants: true,
  course: true,
}).extend({
  courseId: z.number().positive(),
  startDate: z.coerce.date(),
  endDate: z.coerce.date(),
});
export type ICreateCohort = z.infer<typeof CreateCohortSchema>;

export const UpdateCohortSchema = z.object({
  name: z.string().max(100).optional(),
  description: z.string().max(1500).optional(),
  maxParticipants: z.number().min(1).optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  status: z.nativeEnum(CohortStatus).optional(),
});
export type IUpdateCohort = z.infer<typeof UpdateCohortSchema>;

export const GetCohortByIdSchema = z.object({
  id: z.number().positive(),
});
export type IGetCohortById = z.infer<typeof GetCohortByIdSchema>;

export const ListCohortsForCourseSchema = z.object({
  courseId: z.number().positive(),
});
export type IGetAllForCourse = z.infer<typeof ListCohortsForCourseSchema>;

export const ListCohortsSchema = z.object({
  search: z.string().optional(),
  status: z.nativeEnum(CohortStatus).optional(),
  courseId: z.number().positive().optional(),
});
export type IListCohort = z.infer<typeof ListCohortsSchema>;

export const UpdateCohortStudentsSchema = z.object({
  studentIds: z.array(z.number().positive()),
});
export type IUpdateCohortStudents = z.infer<typeof UpdateCohortStudentsSchema>;

export const ListCohortsResponseSchema = z.object({
  data: z.array(
    ListCohortsSchema.extend({
      createdAt: z.date(),
      updatedAt: z.date(),
      id: z.number().positive(),
    })
  ),
  page: z.number(),
  total: z.number(),
});
export type IListCohortsResponse = z.infer<typeof ListCohortsResponseSchema>;
