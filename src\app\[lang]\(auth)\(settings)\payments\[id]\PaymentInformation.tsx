import { Banknote } from "lucide-react";
import Image from "next/image";
import React from "react";

export default function PaymentInformation({ payment, t }: { payment: any; t: any }) {
  const getPaymentMethod = (source: any) => {
    switch (source?.type) {
      case "card":
        return (
          <div className="inline-flex items-center gap-2">
            <Image
              src={`/${source?.card?.brand}.svg`}
              alt={source?.card?.brand}
              width={20}
              height={20}
            />
            <span>{source?.card?.maskedNumber}</span>
          </div>
        );
      case "bank_transfer":
        return (
          <div className="inline-flex items-center gap-2">
            <Banknote className="h-4 w-4" />
            <span>Bank Transfer</span>
          </div>
        );
      default:
        return source?.type;
    }
  };
  const infoRows = [
    {
      label: t("started-at"),
      value: payment?.startedAt ? new Date(payment.startedAt).toLocaleDateString() : "-",
    },
    {
      label: t("next-payment"),
      value: payment?.nextPaymentDate
        ? new Date(payment.nextPaymentDate).toLocaleDateString()
        : t("no-next-payment"),
    },
    { label: t("paid-amount"), value: payment ? `€${(payment.amountPaid / 100).toFixed(2)}` : "-" },
    {
      label: t("total-amount"),
      value: payment ? `€${(payment.totalAmount / 100).toFixed(2)}` : "-",
    },
    { label: t("remaining-billing-cycles"), value: payment?.remainingBillingCycles ?? "0" },
    { label: t("payment-method"), value: getPaymentMethod(payment?.paymentSource) ?? "-" },
  ];

  return (
    <div className="w-full">
      <div className="mb-4 text-xl font-semibold">{t("information")}</div>
      <div className="grid w-full grid-cols-2 gap-y-2">
        {infoRows.map((row) => (
          <React.Fragment key={row.label}>
            <span className="text-muted-foreground text-base">{row.label}</span>
            <span className="text-right text-base">{row.value}</span>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
