"use client";

import { IR<PERSON>Base, RoleGroup } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

import { Modal } from "@/components/base/modal";
import { Input } from "@/components/base/input";
import { useToast } from "@/hooks/use-toast";
import { useCreateRole } from "@/services/role";

type DuplicateRoleModalProps = {
  isModalOpen: IRoleBase | null;
  setIsModalOpen: (isModalOpen: IRoleBase | null) => void;
  onDuplicate: () => void;
};

export default function DuplicateRoleModal({
  isModalOpen,
  setIsModalOpen,
  onDuplicate,
}: DuplicateRoleModalProps) {
  const t = useTranslations("gestion.users.roles");
  const commonT = useTranslations("common");
  const { toast } = useToast();
  const { create, isLoading } = useCreateRole();
  const [roleName, setRoleName] = useState(isModalOpen?.name || "");

  useEffect(() => {
    if (isModalOpen) {
      setRoleName(`${isModalOpen.name} (Copy)`);
    }
  }, [isModalOpen]);

  return (
    <Modal
      isOpen={!!isModalOpen}
      onClose={() => setIsModalOpen(null)}
      onOk={async () => {
        if (isModalOpen) {
          await create({
            group: isModalOpen.group as RoleGroup,
            name: roleName as string,
            permissions: isModalOpen.permissions as string[],
          });
          toast({
            title: t("created"),
            description: t("created-description"),
          });
          setIsModalOpen(null);
          onDuplicate();
        }
      }}
      title={t("duplicate_role")}
      okText={t("duplicate")}
      cancelText={t("cancel")}
      showCloseButton={true}
      className="m-8 w-[1000px] max-w-full"
    >
      <div className="flex flex-col">
        {isLoading && <div className="mb-4 text-center">{commonT("table.loading")}</div>}
        <Input
          type="text"
          placeholder="Role name"
          value={roleName}
          onChange={(e) => setRoleName(e.target.value)}
        />
      </div>
    </Modal>
  );
}
