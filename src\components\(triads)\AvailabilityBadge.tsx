import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

export function AvailabilityBadge({
  availableSpots,
  maxSpots,
}: {
  availableSpots: number;
  maxSpots: number;
}) {
  const t = useTranslations("triad");
  const takenSpots = maxSpots - availableSpots;

  let text = "";
  let textColor = "";

  if (availableSpots <= 0) {
    text = t("availability-badge.full");
    textColor = "text-red-500";
  } else if (availableSpots === 1) {
    text = t("availability-badge.one-spot");
    textColor = "text-yellow-500";
  } else {
    text = t("availability-badge.spots", { count: availableSpots });
    textColor = "text-emerald-500";
  }

  return (
    <div className="flex flex-col items-center gap-0.5">
      <div className="flex items-center gap-0.5">
        {[...Array(maxSpots)].map((_, i) => (
          <div
            key={i}
            className={cn(
              "h-1 w-1 rounded-full",
              i < takenSpots ? "bg-red-500/50" : "bg-emerald-500/50",
            )}
          />
        ))}
      </div>
      <span className={cn("text-xs font-medium", textColor)}>{text}</span>
    </div>
  );
}
