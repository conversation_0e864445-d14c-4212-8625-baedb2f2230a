"use client";

import { useState, useEffect, useMemo } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { anton } from "@/lib/fonts/anton";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { DatePicker } from "@/components/ui/date-picker";
import { TimePicker } from "@/components/ui/time-picker";
import { motion, AnimatePresence } from "framer-motion";
import { getPXSEDECPropertyOptions } from "@/lib/hubspot";
import { Combobox } from "@/components/base/combobox";
import { PXSSession, getUniqueNormalizedSessions, getSessionLabel } from "@/types/session";
import { ConfettiDrawer } from "@/components/ui/confetti-drawer";
import { useCustomerAccessDetails } from "@/services/customers";
import { useCreateTriad } from "@/services/triad";
import { useAuth } from "@clerk/nextjs";
import { SessionType } from "@px-shared-account/hermes";
import { useTriadStats } from "@/services/triad/client";

const CelebrationDescription = () => {
  const t = useTranslations("triad");
  const { data: stats, isLoading: statsLoading } = useTriadStats();
  if (statsLoading) return t("create.celebration.loading");
  if (!stats) return t("create.celebration.description");

  return (
    <>
      {t("create.celebration.description")}
      <div className="bg-background mt-4 grid grid-cols-2 gap-4 rounded-xl p-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{stats.totalTriadsOrganized}</div>
          <div className="text-sm text-white/60">{t("create.stats.total_triads")}</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{stats.totalParticipants}</div>
          <div className="text-sm text-white/60">{t("create.stats.total_participants")}</div>
        </div>
      </div>
    </>
  );
};

interface CreateTriadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const fadeVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 },
};

export function CreateTriadDialog({ open, onOpenChange }: CreateTriadDialogProps) {
  const t = useTranslations("triad");
  const { userId } = useAuth();
  const { data: userAccessData, isLoading: accessLoading } = useCustomerAccessDetails();
  const accessInfo = userAccessData?.accessInfo;
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<1 | 2>(1);
  const [showCelebration, setShowCelebration] = useState(false);

  // Initialize sessionType based on access
  const initialSessionType = !accessInfo?.hasBothAccess
    ? accessInfo?.isPXL
      ? SessionType.PXL
      : accessInfo?.isPXS || accessInfo?.isParadox
        ? SessionType.PXS
        : null
    : null;

  const [sessionType, setSessionType] = useState<SessionType | null>(initialSessionType);

  // Ensure sessionType is set correctly
  useEffect(() => {
    if (!accessInfo?.hasBothAccess && !sessionType) {
      const type = accessInfo?.isPXL
        ? SessionType.PXL
        : accessInfo?.isPXS || accessInfo?.isParadox
          ? SessionType.PXS
          : null;
      setSessionType(type);
    }
  }, [
    accessInfo?.hasBothAccess,
    accessInfo?.isPXL,
    accessInfo?.isPXS,
    accessInfo?.isParadox,
    sessionType,
  ]);

  const { create, isLoading: createLoading } = useCreateTriad();
  const [date, setDate] = useState(new Date());
  const [sessions, setSessions] = useState<{ value: string; label: string }[]>([]);
  const [selectedSessions, setSelectedSessions] = useState<PXSSession[]>([]);

  // Set initial step based on access type
  useEffect(() => {
    if (!accessInfo?.hasBothAccess) {
      setStep(2);
    }
  }, [accessInfo?.hasBothAccess]);

  // Handle dialog open/close state
  useEffect(() => {
    if (open) {
      // Set step and session type based on access
      if (!accessInfo?.hasBothAccess) {
        setSessionType(initialSessionType);
        setStep(2);
      } else {
        setSessionType(null);
        setStep(1);
      }
    } else {
      // Reset state when dialog closes
      setSessionType(accessInfo?.hasBothAccess ? null : initialSessionType);
      setStep(accessInfo?.hasBothAccess ? 1 : 2);
      setSelectedSessions([]);
    }
  }, [open, accessInfo?.hasBothAccess, initialSessionType]);

  useEffect(() => {
    const loadSessions = async () => {
      const hubspotSessions = await getPXSEDECPropertyOptions();
      const normalizedSessions = getUniqueNormalizedSessions(hubspotSessions);
      setSessions(
        normalizedSessions.map((session) => ({
          value: session,
          label: getSessionLabel(session),
        })),
      );
    };
    loadSessions();
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Double check session type based on access
    let finalSessionType = sessionType;
    if (!accessInfo?.hasBothAccess && !finalSessionType) {
      finalSessionType = accessInfo?.isPXL
        ? SessionType.PXL
        : accessInfo?.isPXS || accessInfo?.isParadox
          ? SessionType.PXS
          : null;
    }

    if (loading || !finalSessionType) {
      return;
    }

    // Prevent creating triads for session types the user doesn't have access to
    if (finalSessionType === SessionType.PXL && !accessInfo?.isPXL && !accessInfo?.isParadox) {
      toast.error(t("errors.no_access"));
      return;
    }

    if (finalSessionType === SessionType.PXS && !accessInfo?.isPXS && !accessInfo?.isParadox) {
      toast.error(t("errors.no_access"));
      return;
    }

    setLoading(true);

    const formData = new FormData(e.currentTarget);
    const data = {
      title: formData.get("title") as string,
      sessionTime: date,
      time: `${date.getHours().toString().padStart(2, "0")}:${date
        .getMinutes()
        .toString()
        .padStart(2, "0")}`,
      duration: parseFloat(formData.get("duration") as string),
      meetingLink: formData.get("link") as string,
      maxParticipants: parseInt(formData.get("maxParticipants") as string),
      sessions: finalSessionType === SessionType.PXL ? [] : selectedSessions,
      sessionType: finalSessionType,
      organizerId: userId as string,
    };

    try {
      await create(data);
      setShowCelebration(true);
      onOpenChange(false);
    } catch (error: any) {
      if (error.message) {
        let translationKey = "errors.failed_to_create";
        let translationParams = {};

        if (error.message === "date_in_past") {
          translationKey = "errors.date_in_past";
        } else if (error.message === "invalid_meeting_link") {
          translationKey = "errors.invalid_meeting_link";
        } else if (error.message === "missing_fields") {
          translationKey = "errors.missing_fields";
        } else if (error.message === "triad_limit_exceeded") {
          if (error.code === "session_too_far_in_future") {
            translationKey = "errors.session_too_far_in_future";
            translationParams = { days: error.maxDays };
          } else if (error.code === "too_many_open_triads") {
            // Check if this is a weekly limit error (has weekNumber property)
            if (error.violatedLimit.weekNumber) {
              translationKey = "errors.too_many_open_triads_weekly";
              // No parameters needed for the simplified message
            } else {
              translationKey = "errors.too_many_open_triads_with_limit";
              translationParams = {
                days: error.violatedLimit.days,
                max: error.violatedLimit.maxTriads,
                current: error.violatedLimit.currentTriads,
              };
            }
          }
        } else {
          translationKey = `errors.${error.message}`;
        }
        toast.error(t(translationKey, translationParams));
      }
      toast.error(t("errors.failed_to_create"));
    } finally {
      setLoading(false);
    }
  };

  const handleTypeSelection = (type: SessionType) => {
    // Prevent selecting session types the user doesn't have access to
    if (type === "PXL" && !accessInfo?.isPXL && !accessInfo?.isParadox) {
      toast.error(t("errors.no_access"));
      return;
    }

    if (type === "PXS" && !accessInfo?.isPXS && !accessInfo?.isParadox) {
      toast.error(t("errors.no_access"));
      return;
    }

    setSessionType(type);
    // Clear selected sessions if switching to PXL
    if (type === SessionType.PXL) {
      setSelectedSessions([]);
    }
    setStep(2);
  };

  const handleDrawerDismissed = () => {
    onOpenChange(false);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent
          className={cn(
            "max-h-screen w-screen overflow-y-auto rounded-3xl border-none bg-[#111111] text-white lg:h-auto lg:!max-h-[90dvh] lg:max-w-lg",
            step === 2 && "h-screen max-w-screen",
          )}
        >
          <DialogHeader className="space-y-2">
            <DialogTitle className={cn("text-center text-2xl text-white", anton.className)}>
              {accessLoading
                ? t("view.loading")
                : step === 1
                  ? t("create.select_type")
                  : t("create.title")}
            </DialogTitle>
          </DialogHeader>

          {accessLoading ? (
            <div className="flex items-center justify-center p-10 text-gray-400">
              <span>{t("view.loading")}...</span>
            </div>
          ) : (
            <AnimatePresence mode="wait">
              {step === 1 && accessInfo?.hasBothAccess ? (
                <motion.div
                  key="step1"
                  variants={fadeVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="space-y-4"
                >
                  <div className="grid grid-cols-2 gap-4">
                    <button
                      onClick={() => handleTypeSelection(SessionType.PXS)}
                      className="bg-background flex flex-col items-center justify-center gap-4 rounded-xl p-6 transition-colors hover:bg-[#333333]"
                    >
                      <Image
                        src="/pxs.svg"
                        alt="PXS"
                        width={100}
                        height={40}
                        className="object-contain"
                      />
                    </button>
                    <button
                      onClick={() => handleTypeSelection(SessionType.PXL)}
                      className="bg-background flex flex-col items-center justify-center gap-4 rounded-xl p-6 transition-colors hover:bg-[#333333]"
                    >
                      <Image
                        src="/pxl.svg"
                        alt="PXL"
                        width={100}
                        height={40}
                        className="object-contain"
                      />
                    </button>
                  </div>
                </motion.div>
              ) : step === 2 && sessionType ? (
                <motion.div
                  key="step2"
                  variants={fadeVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  <form onSubmit={handleSubmit} className="space-y-6 pt-4">
                    <div>
                      <Label htmlFor="title">{t("create.title_field")}</Label>
                      <Input
                        id="title"
                        type="text"
                        name="title"
                        placeholder={t("create.placeholder.title")}
                        className="bg-background h-12 border-none text-white"
                        required
                      />
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="flex-1">
                        <Label htmlFor="date">{t("create.date")}</Label>
                        <DatePicker
                          value={date}
                          onChange={(newDate) => setDate(newDate || new Date())}
                        />
                      </div>
                      <div className="flex-1">
                        <Label htmlFor="time">{t("create.time")}</Label>
                        <TimePicker
                          value={date}
                          onChange={(newDate) => {
                            if (newDate && date) {
                              const updatedDate = new Date(date);
                              updatedDate.setHours(newDate.getHours(), newDate.getMinutes());
                              setDate(updatedDate);
                            }
                          }}
                        />
                        <span className="mt-1 block text-xs text-gray-400">
                          {t("time_picker.local_time")}
                        </span>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="duration">{t("create.duration")}</Label>
                      <Input
                        id="duration"
                        type="number"
                        name="duration"
                        step="0.5"
                        min="0.5"
                        className="bg-background h-12 border-none text-white"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="maxParticipants">{t("create.max_participants")}</Label>
                      <Input
                        id="maxParticipants"
                        type="number"
                        name="maxParticipants"
                        min="2"
                        max="8"
                        defaultValue="3"
                        className="bg-background h-12 border-none text-white"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="link">{t("create.access")}</Label>
                      <Input
                        id="link"
                        type="text"
                        name="link"
                        placeholder={t("create.placeholder.link")}
                        className="bg-background h-12 border-none text-white"
                        required
                      />
                    </div>

                    {sessionType === "PXS" && (
                      <div className="space-y-2">
                        <Label htmlFor="sessions">{t("create.sessions")}</Label>
                        <Combobox
                          id="sessions"
                          options={sessions}
                          value={selectedSessions}
                          onChange={(value) => setSelectedSessions(value as PXSSession[])}
                          label={t("create.sessions")}
                          placeholder={t("create.placeholder.sessions")}
                          multiple
                        />
                      </div>
                    )}

                    <div className="grid grid-cols-2 gap-4 pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          if (accessInfo?.hasBothAccess) {
                            setStep(1);
                            setSessionType(null);
                          } else {
                            onOpenChange(false);
                          }
                        }}
                        className="h-12 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black"
                        disabled={loading}
                      >
                        {accessInfo?.hasBothAccess ? t("create.back") : t("create.cancel")}
                      </Button>
                      <Button
                        variant={"default"}
                        type="submit"
                        disabled={loading}
                        className="h-12 rounded-full text-white"
                      >
                        {loading ? t("create.creating") : t("create.create")}
                      </Button>
                    </div>
                  </form>
                </motion.div>
              ) : (
                <motion.div
                  key="error"
                  variants={fadeVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="text-center"
                  onAnimationStart={() => {
                    if (accessInfo?.hasBothAccess) {
                      setStep(1);
                    } else {
                      setSessionType(accessInfo?.isPXL ? SessionType.PXL : SessionType.PXS);
                    }
                  }}
                >
                  <span className="text-sm">Redirecting...</span>
                </motion.div>
              )}
            </AnimatePresence>
          )}
        </DialogContent>
      </Dialog>

      <ConfettiDrawer
        isOpen={showCelebration}
        onClose={() => setShowCelebration(false)}
        onDismissed={handleDrawerDismissed}
        title={t("create.celebration.title")}
        timer={10000}
      >
        <CelebrationDescription />
      </ConfettiDrawer>
    </>
  );
}
