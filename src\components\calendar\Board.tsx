"use client";

import { useMemo } from "react";

import EventMark from "./EventMark";
import LineDivisor from "./LineDivisor";
import useCalendarStore, { CalendarEvent } from "@/hooks/store/calendar";
import getCalendarEvent from "@/lib/calendar/getCalendarEvents";
import computeViewLayout from "@/lib/calendar/computeViewLayout";
import CalendarDialog from "./Dialog";

type CalendarBoardProps = {
  selectedWeekIndex: number;
  selectedWeek: Date[];
};

export default function CalendarBoard({ selectedWeekIndex, selectedWeek }: CalendarBoardProps) {
  const { events, defaultEventDuration, layout, selectedDate } = useCalendarStore();

  const viewLayout = computeViewLayout(layout);

  const getEventData = (day: Date) => {
    const { dayEvents, eventsByHour } = getCalendarEvent(events, day);

    return eventsByHour.map((evHour: any) => {
      return dayEvents
        .filter((event: CalendarEvent) => new Date(event.begin).getHours() === evHour.hour)
        .map((event: CalendarEvent, index: number) => (
          <EventMark key={`event-${event.id}`} calendarEvent={event} sq={index} len={evHour.len} />
        ));
    });
  };

  const viewLayoutEl = useMemo(() => {
    return viewLayout.map((index) => {
      const day = layout === "week" ? selectedWeek[index] : selectedDate;
      const eventsOfDay = getEventData(day);

      return (
        <div
          suppressHydrationWarning
          key={`board-day-column-${layout}-${selectedWeekIndex}-${day}-${index}`}
          id={`day${index + 1}`}
          data-group="day-column"
          data-date={day}
          className="relative z-20 flex h-full flex-auto flex-col border-r border-gray-200 pr-3"
        >
          <div className="relative h-full w-full">{eventsOfDay}</div>
        </div>
      );
    });
  }, [
    // classes,
    defaultEventDuration,
    layout,
    selectedDate,
    selectedWeek,
    selectedWeekIndex,
    viewLayout,
    events.length,
  ]);

  return (
    <div className="flex h-full w-full flex-row items-start justify-center overflow-hidden">
      <LineDivisor />
      <div className="h-full border-r border-gray-200 pr-2" />
      {viewLayoutEl}
      <CalendarDialog />
    </div>
  );
}
