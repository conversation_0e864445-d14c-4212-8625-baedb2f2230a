import { ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateProductDTO, UpdateProductDTO } from '@dtos';
import { ProductFactory, ProductUseCases } from '@useCases';
import { JwtPermissionsGuard } from '@auth';

@ApiTags('products')
@UseGuards(JwtPermissionsGuard)
@Controller('products')
export class ProductsController {
  constructor(
    private readonly productUseCases: ProductUseCases,
    private readonly productFactory: ProductFactory,
  ) {}

  @ApiOperation({
    summary: 'Get all products that match provided billing cycles',
  })
  @Get('/getIdsForBillingCycles')
  async getAllForBillingCycles(@Query('cycles') cycles: string[]) {
    let billingCycles: number[];
    if (Array.isArray(cycles)) {
      billingCycles = cycles.map(Number);
    }
    if (typeof cycles === 'string') {
      billingCycles = [Number(cycles)];
    }
    return this.productUseCases.getProductIdsByBillingCycles(billingCycles);
  }

  @ApiOperation({ summary: 'Create a product' })
  @Post('/')
  async createProduct(@Body() productInfo: CreateProductDTO) {
    const product = this.productFactory.generateProduct(productInfo);
    const createdProduct = await this.productUseCases.create(product);
    if (!!createdProduct) {
      return createdProduct;
    } else {
      throw new HttpException(
        'Failed to create product.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @ApiOperation({ summary: 'List Products' })
  @Get('/')
  async getProducts(
    @Query('searchQuery') searchQuery: string,
    @Query('family') family: string,
    @Query('line') line: string,
    @Query('status') status: string,
    @Query('isForever') isForever: boolean,
    @Query('page') page = 1,
    @Query('limit') limit = 500,
    @Query('order') orderBy?: 'DESC' | 'ASC',
  ) {
    const { items, total } = await this.productUseCases.searchAll(
      searchQuery,
      line,
      family,
      status,
      isForever,
      limit,
      page,
      orderBy,
    );

    return {
      items,
      total,
      currentPage: Number(page),
    };
  }

  @ApiOperation({ summary: 'Update a product' })
  @Patch('/:id')
  async updatePRoduct(
    @Param('id') id: string,
    @Body() updates: UpdateProductDTO,
  ) {
    return this.productUseCases.update(Number(id), updates);
  }

  @ApiOperation({
    summary: 'Get a Product where `product.code` matches `productCode`',
  })
  @Get('/:productCode')
  async getProductByCode(@Param('productCode') productCode: string) {
    const product = await this.productUseCases.getOneBy({
      code: productCode,
    });

    if (!product) {
      throw new NotFoundException({
        message: 'Product not found',
        productCode,
      });
    }

    return product;
  }
}
