import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { CheckoutPage } from '@entities';
import { Testimonial } from '@types';
import { ProductOfferTable } from './product-offers.table';
import { WarrantySectionTable } from './warranty-section.table';

@Entity({
  name: 'checkoutPages',
})
export class CheckoutPageTable implements CheckoutPage {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 100 })
  bannerImage: string;

  @Column({ type: 'varchar', length: 10 })
  mainColor: string;

  @OneToOne(
    () => ProductOfferTable,
    (productOffer) => productOffer.checkoutPage,
    {
      eager: true,
    },
  )
  productOffer: ProductOfferTable;

  @Column({ type: 'boolean', nullable: true, default: false })
  isLive: boolean;

  @Column({ type: 'varchar', length: 100, nullable: true })
  url?: string;

  @Column({ type: 'varchar', length: 5, nullable: true, default: 'A' })
  template?: string;

  @Column({ type: 'varchar', length: 800, nullable: true })
  cancellationPolicy?: string;

  @Column({ type: 'jsonb', nullable: true, default: {} })
  testimony?: Testimonial;

  @OneToMany(
    () => WarrantySectionTable,
    (warrantySection) => warrantySection.checkoutPage,
    {
      eager: true,
      cascade: true,
    },
  )
  warrantySections: WarrantySectionTable[];

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
