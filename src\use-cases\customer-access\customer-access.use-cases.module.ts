import { Module } from '@nestjs/common';
import { CustomerAccessFactory } from './customer-access.factory';
import { CustomerAccessUseCases } from './customer-access.use-cases';
import { ClerkModule } from '@services/sso';
import { DataServicesModule } from '@services/database';
import { CircleModule } from '@services/community';
import { LearnworldsModule } from '@services/lms';
import { GoliathsModule } from '../goliaths';

@Module({
  imports: [
    DataServicesModule,
    ClerkModule,
    CircleModule,
    LearnworldsModule,
    GoliathsModule,
  ],
  providers: [CustomerAccessFactory, CustomerAccessUseCases],
  exports: [CustomerAccessFactory, CustomerAccessUseCases],
})
export class CustomerAccessModule {}
