import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { z } from 'zod';
import { IDataServices } from '@abstracts';
import { GoliathPromoCode as GoliathsPromoCode, RequestMethods } from '@types';
import { RedisClientsService } from '@services/event-publishers';
import { UserUseCases } from '../user';
import { ConfigService } from '@config';
import { GlobalHelpers } from '@helpers';

@Injectable()
export class GoliathService {
  private readonly GOLIATHS_X_API_KEY_DEV: string;
  private readonly GOLIATHS_X_API_KEY_PROD: string;
  private readonly GOLIATHS_ENDPOINT_DEV: string;
  private readonly GOLIATHS_ENDPOINT_PROD: string;
  private readonly ENVIRONMENT: string;

  constructor(
    private readonly dataServices: IDataServices,
    private readonly redisClientsService: RedisClientsService,
    @Inject(forwardRef(() => UserUseCases))
    private readonly userService: UserUseCases,
    private readonly configService: ConfigService,
  ) {
    const goliathsSecrets = this.configService.goliathsSecrets;
    this.GOLIATHS_X_API_KEY_DEV = goliathsSecrets.GOLIATHS_X_API_KEY_DEV;
    this.GOLIATHS_X_API_KEY_PROD = goliathsSecrets.GOLIATHS_X_API_KEY_PROD;
    this.GOLIATHS_ENDPOINT_DEV = goliathsSecrets.GOLIATHS_ENDPOINT_DEV;
    this.GOLIATHS_ENDPOINT_PROD = goliathsSecrets.GOLIATHS_ENDPOINT_PROD;
    this.ENVIRONMENT = this.configService.appSecrets.NODE_ENV;
  }

  /**
   * Based on a Clerk user email, we should return if the user purchase POM or not
   * @param email Clerk user email
   * @returns Customer entity
   */
  async isPOMStudent(email: string) {
    const dataSource = this.dataServices.getDataSource();
    const result = await dataSource.query(
      `SELECT * FROM goliaths_subscription_details s WHERE lower(s."customer_email") = lower($1)`,
      [email],
    );
    return { isPOMStudent: result.length > 0, details: result };
  }

  /**
   * Check if the code is valid.
   * @param code Discount code
   * @returns true if valid, false otherwise
   */
  private isValidCode(code: string): boolean {
    return (
      typeof code === 'string' &&
      /^[A-Z0-9]{4}$/.test(code) &&
      /[A-Z]/.test(code) &&
      /\d/.test(code)
    );
  }

  /**
   * Generate a random code of the specified length (4 by default).
   * The code contains only uppercase letters and digits.
   * The code is shuffled to ensure randomness.
   * @param length Length of the code
   * @returns Random code
   */
  private generateCode(length = 4): string {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const digits = '0123456789';
    const lettersAndDigits = letters + digits;

    const codeParts = [
      letters[Math.floor(Math.random() * letters.length)],
      digits[Math.floor(Math.random() * digits.length)],
    ];

    for (let i = 0; i < length - 2; i++) {
      codeParts.push(
        lettersAndDigits[Math.floor(Math.random() * lettersAndDigits.length)],
      );
    }

    for (let i = codeParts.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [codeParts[i], codeParts[j]] = [codeParts[j], codeParts[i]];
    }

    return codeParts.join('');
  }

  /**
   * Generate unique codes and push into Redis set "discount_codes".
   * Loops until it has *new* entries equal to `numberOfCodesToGenerate`.
   * @param numberOfCodesToGenerate number of codes to generate
   * @returns Discount codes and total number of codes in Redis
   * @throws Error if `numberOfCodesToGenerate` is not between 1 and 1,000,000
   */
  public async generateCodes(
    numberOfCodesToGenerate: number,
  ): Promise<{ codes: string[]; totalCodes: number }> {
    this.validateCodeQuantity(numberOfCodesToGenerate);
    const redis = await this.redisClientsService.getPxApiRedisClient();
    const codes: string[] = [];

    while (codes.length < numberOfCodesToGenerate) {
      let code: string;
      do {
        code = this.generateCode();
      } while (!this.isValidCode(code));
      if ((await redis.sAdd('discount_codes', code)) === 1) {
        codes.push(code);
      }
    }

    const totalCodes = await redis.sCard('discount_codes');
    return { codes, totalCodes };
  }

  /**
   * Validate the number of codes to be generated.
   * @param numberOfCodes Number of codes to generate
   * @returns Validated number of codes
   */
  validateCodeQuantity(numberOfCodes: number) {
    try {
      z.number()
        .min(1, 'Number of codes must be greater than or equal to 1')
        .max(999_999, 'Number of codes must be less than or equal to 999,999')
        .parse(numberOfCodes);
    } catch (zodError) {
      const message = zodError?.issues[0]?.message || 'Invalid number of codes';
      throw new BadRequestException(message);
    }
  }

  /**
   * Issue a discount code to a user based on their email.
   * The discount code is generated based on the user's purchase history and POM status.
   * @param email User's email address
   * @returns DiscountCode object or null if no code is issued
   */
  public async issueAndPush(
    email: string,
    isAlreadyPOM = false,
  ): Promise<GoliathsPromoCode | null | { success: false; error: string }> {
    try {
      const user = await this.userService.getByEmail(email);
      if (!user) {
        throw new Error('User not found');
      }

      const userMetaData = (user.metaData as Record<string, any>) || {};
      const lastDiscount: number = userMetaData.discountCode?.discount ?? 0;

      const purchaseResult = await this.dataServices
        .getDataSource()
        .query(
          `SELECT * FROM user_access_purchases_view WHERE lower("userEmail") = lower($1)`,
          [user.email],
        );
      const hasPurchased = purchaseResult.some(
        (item) => item.offerName !== null,
      );
      const isPOM =
        isAlreadyPOM || (await this.isPOMStudent(user.email))?.isPOMStudent;

      let newTier: 'pom' | 'purchase' | 'registration' | null = null;
      if (isPOM && lastDiscount < 80) {
        newTier = 'pom';
      } else if (hasPurchased && lastDiscount < 20) {
        newTier = 'purchase';
      } else if (lastDiscount < 10) {
        newTier = 'registration';
      }
      if (!newTier) {
        return null;
      }

      const discountMap = { registration: 10, purchase: 20, pom: 80 } as const;
      const discount = discountMap[newTier];

      let code: string;
      const redis = await this.redisClientsService.getPxApiRedisClient();
      if (!userMetaData.discountCode || !userMetaData.discountCode.code) {
        const raw = (await redis.sPop('discount_codes')) as unknown as string;
        if (!raw) {
          throw new Error('Plus de codes disponibles');
        }
        code = raw;
      } else {
        code = userMetaData.discountCode.code;
      }

      const promo = this.generatePromoCode(code, discount);

      userMetaData.discountCode = promo;
      user.metaData = userMetaData;
      await this.dataServices
        .getDataSource()
        .query('UPDATE users SET "metaData" = $1 WHERE id = $2', [
          user.metaData,
          user.id,
        ]);
      await this.notifyGoliaths(promo, user.ssoId);
      promo.pushed = true;
      userMetaData.discountCode = promo;
      user.metaData = userMetaData;
      await this.dataServices
        .getDataSource()
        .query('UPDATE users SET "metaData" = $1 WHERE id = $2', [
          user.metaData,
          user.id,
        ]);

      return promo;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate a promo code with a discount and expiration date.
   * @param code Promo code
   * @param discount Discount percentage
   * @returns GoliathsPromoCode object
   */
  generatePromoCode(code: string, discount: number): GoliathsPromoCode {
    const now = new Date();
    const expirationDate = new Date(now);
    const monthDelay = 3;
    expirationDate.setMonth(expirationDate.getMonth() + monthDelay);
    return {
      code,
      discount,
      createdAt: now,
      expirationDate,
      pushed: false,
    };
  }

  /**
   * Notify Goliaths about the promo code.
   * @param promo Promo code
   * @param userSsoId User SSO ID
   */
  async notifyGoliaths(
    promo: GoliathsPromoCode,
    userSsoId: string,
  ): Promise<void> {
    const url =
      this.ENVIRONMENT === 'production'
        ? this.GOLIATHS_ENDPOINT_PROD
        : this.GOLIATHS_ENDPOINT_DEV;
    const key =
      this.ENVIRONMENT === 'production'
        ? this.GOLIATHS_X_API_KEY_PROD
        : this.GOLIATHS_X_API_KEY_DEV;
    await GlobalHelpers.makeAxiosRequest(
      url,
      RequestMethods.POST,
      null,
      { 'x-api-key': key },
      {
        code: promo.code,
        discount: promo.discount,
        expiration_date: promo.expirationDate,
        paradox_uid: userSsoId,
      },
    );
  }

  /**
   * Resend Goliaths promo notifications for all users who have a discountCode in metaData.
   */
  public async resendAllNotifications(): Promise<{
    successCount: number;
    failureCount: number;
  }> {
    const users = await this.dataServices.user
      .getRepository()
      .createQueryBuilder('user')
      .where(`"metaData"->'discountCode' IS NOT NULL`)
      .andWhere(`"ssoId" IS NOT NULL`)
      .getMany();
    let successCount = 0;
    let failureCount = 0;
    for (const { metaData, ssoId, email } of users) {
      const discountCode = metaData?.discountCode;
      if (!discountCode?.code || !ssoId) continue;

      try {
        await this.notifyGoliaths(discountCode, ssoId);
        successCount++;
      } catch (error) {
        console.error(
          `Failed to notify Goliaths for user ${email}: ${error.message}`,
        );
        failureCount++;
      }
    }
    console.log(
      `Resending completed. Success: ${successCount}, Failure: ${failureCount}`,
    );
    return { successCount, failureCount };
  }

  /**
   * Get user discountCode metadata by email
   * @param email User email
   * @returns User metadata with discount code
   */
  async getUserDiscountCode(email: string): Promise<GoliathsPromoCode | null> {
    const user = await this.userService.getByEmail(email);
    if (!user) {
      return null;
    }
    const userMetaData = (user.metaData as Record<string, any>) || {};
    return userMetaData.discountCode || null;
  }
}
