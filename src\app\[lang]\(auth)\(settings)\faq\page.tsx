import FAQSelector from "@/components/(settings)/faq/FAQSelector";
import IframePlayer from "@/components/(settings)/faq/IframePlayer";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

export default function Page() {
  return (
    <>
      <div className="px-4 pt-6">
        <BreadcrumbSettings active="/faq/pxs" />
      </div>
      <div className="px-4">
        <FAQSelector isActive="pxs" />
        <div className="h-12" />
        <IframePlayer src="https://pxs.frontkb.com/" title="PXS FAQ" />
      </div>
    </>
  );
}

export async function generateMetadata() {
  return {
    title: "FAQ",
  };
}
