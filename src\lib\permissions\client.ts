"use client";

import { Permissions } from "@px-shared-account/hermes";

import usePermissionStore from "@/hooks/store/permission";
import { useUser } from "@clerk/nextjs";
import { useUserDetailsBySSOId } from "@/services/user/client";
import { existingPermissions } from "./shared";

/**
 * Interface for permission check parameters
 */
type HasAccessProps = {
  permissionsMap: Record<string, string>;
  permission: (typeof existingPermissions)[number];
  roleKey: string; // Renamed from userKey to roleKey for clarity
};

/**
 * Checks if a user key has access to a specific permission
 * @param {HasAccessProps} props - The permission check parameters
 * @returns {{ hasAccess: boolean, canAccess: boolean }} Access status object
 */
function checkAccess({ permissionsMap, permission, roleKey }: HasAccessProps): {
  hasAccess: boolean;
  canAccess: boolean;
} {
  if (!existingPermissions.includes(permission)) return { hasAccess: false, canAccess: false };

  const rolePermissionsString = permissionsMap[roleKey];
  const rolePermissions = JSON.parse(rolePermissionsString);
  if (!rolePermissions || !Array.isArray(rolePermissions) || rolePermissions.length === 0)
    return { hasAccess: false, canAccess: false };

  const hasAccess = rolePermissions.includes(permission);
  return { hasAccess, canAccess: hasAccess };
}

/**
 * Hook to check if the current user has access to a specific permission
 * @param {(typeof existingPermissions)[number]} permission - The permission to check
 * @returns {{ hasAccess: boolean, canAccess: (p: string) => { hasAccess: boolean, canAccess: boolean }}} Access check result and function
 */
export const useCanAccess = (permission: (typeof existingPermissions)[number]) => {
  const { user: clerkUser, isLoaded: isClerkLoaded } = useUser();
  const permissionsFromStore = usePermissionStore((s) => s.permissions);

  const {
    data: detailedUser,
    isLoading: isUserDetailsLoading,
    error: userDetailsError,
  } = useUserDetailsBySSOId(clerkUser?.id);
  if (!permission) {
    return { hasAccess: false, canAccess: () => ({ hasAccess: false, canAccess: false }) };
  }

  // Handle loading states and errors
  if (!isClerkLoaded || isUserDetailsLoading) {
    const canAccessFn = () => ({ hasAccess: false, canAccess: false });
    return { hasAccess: false, canAccess: canAccessFn, isLoading: true };
  }

  if (
    userDetailsError ||
    !detailedUser ||
    !detailedUser.role ||
    typeof detailedUser.role.name !== "string"
  ) {
    // console.error("Error fetching user details or role.name not found/not a string:", userDetailsError, detailedUser);
    const canAccessFn = () => ({ hasAccess: false, canAccess: false });
    return { hasAccess: false, canAccess: canAccessFn, isLoading: false };
  }

  // Ensure permissions map is populated
  if (Object.keys(permissionsFromStore).length === 0) {
    const canAccessFn = () => ({ hasAccess: false, canAccess: false });
    return { hasAccess: false, canAccess: canAccessFn, isLoading: true }; // Permissions not yet loaded
  }

  const currentUserRoleName = detailedUser.role.name; // Assuming role.name is the string role key

  const canAccess = (checkPermission: (typeof existingPermissions)[number]) => {
    return checkAccess({
      permissionsMap: permissionsFromStore,
      permission: checkPermission,
      roleKey: currentUserRoleName,
    });
  };

  const accessStatus = checkAccess({
    permissionsMap: permissionsFromStore,
    permission,
    roleKey: currentUserRoleName,
  });

  return {
    ...accessStatus,
    canAccess,
    isLoading: false,
  };
};

/**
 * Converts a permission string to a camelCase property name at the type level
 */
type ToCamelCase<T extends string> = T extends `${infer P1}:${infer P2}`
  ? `hasAccess${Capitalize<P1>}${Capitalize<P2>}`
  : never;

/**
 * Maps an array of permissions to an object type with dynamic property names
 */
type PermissionsAccessMap<T extends ReadonlyArray<string>> = {
  [K in T[number] as ToCamelCase<K>]: boolean;
} & {
  canAccess: (permission: (typeof existingPermissions)[number]) => {
    hasAccess: boolean;
    canAccess: boolean;
  };
  isLoading: boolean;
};

/**
 * Hook to check if the current user has access to specific permissions
 * @param {Array<typeof existingPermissions>} permissions - The permissions to check
 * @returns Type-safe object with dynamic property names for each permission
 *
 * @example
 * ```typescript
 * const { hasAccessUserCreate, hasAccessRoleRead, isLoading } = useCanAccessAll([
 *   Permissions.User.CREATE, // "user:create"
 *   Permissions.Role.READ    // "role:read"
 * ]);
 *
 * // TypeScript knows these properties exist:
 * if (hasAccessUserCreate) { ... }
 * if (hasAccessRoleRead) { ... }
 * ```
 */
export const useCanAccessAll = <T extends ReadonlyArray<(typeof existingPermissions)[number]>>(
  permissions: T,
): PermissionsAccessMap<T> => {
  const { user: clerkUser, isLoaded: isClerkLoaded } = useUser();
  const permissionsFromStore = usePermissionStore((s) => s.permissions);

  const {
    data: detailedUser,
    isLoading: isUserDetailsLoading,
    error: userDetailsError,
  } = useUserDetailsBySSOId(clerkUser?.id);

  // Handle empty permissions array
  if (!permissions || permissions.length === 0) {
    return {
      isLoading: false,
      canAccess: () => ({ hasAccess: false, canAccess: false }),
    } as unknown as PermissionsAccessMap<T>;
  }

  // Handle loading states and errors
  if (!isClerkLoaded || isUserDetailsLoading) {
    const loadingResult = permissions.reduce(
      (acc, permission) => {
        const propertyName = `hasAccess${toCamelCase(permission)}`;
        acc[propertyName] = false;
        return acc;
      },
      {} as Record<string, boolean>,
    );

    return {
      ...loadingResult,
      isLoading: true,
      canAccess: () => ({ hasAccess: false, canAccess: false }),
    } as unknown as PermissionsAccessMap<T>;
  }

  if (
    userDetailsError ||
    !detailedUser ||
    !detailedUser.role ||
    typeof detailedUser.role.name !== "string"
  ) {
    const errorResult = permissions.reduce(
      (acc, permission) => {
        const propertyName = `hasAccess${toCamelCase(permission)}`;
        acc[propertyName] = false;
        return acc;
      },
      {} as Record<string, boolean>,
    );

    return {
      ...errorResult,
      isLoading: false,
      canAccess: () => ({ hasAccess: false, canAccess: false }),
    } as unknown as PermissionsAccessMap<T>;
  }

  // Ensure permissions map is populated
  if (Object.keys(permissionsFromStore).length === 0) {
    const loadingResult = permissions.reduce(
      (acc, permission) => {
        const propertyName = `hasAccess${toCamelCase(permission)}`;
        acc[propertyName] = false;
        return acc;
      },
      {} as Record<string, boolean>,
    );

    return {
      ...loadingResult,
      isLoading: true,
      canAccess: () => ({ hasAccess: false, canAccess: false }),
    } as unknown as PermissionsAccessMap<T>;
  }

  const currentUserRoleName = detailedUser.role.name;

  // Generate the access checks for all permissions
  const accessResults = permissions.reduce(
    (acc, permission) => {
      const propertyName = `hasAccess${toCamelCase(permission)}`;
      const accessStatus = checkAccess({
        permissionsMap: permissionsFromStore,
        permission,
        roleKey: currentUserRoleName,
      });
      acc[propertyName] = accessStatus.hasAccess;
      return acc;
    },
    {} as Record<string, boolean>,
  );

  // Add the general canAccess function for convenience
  const canAccess = (checkPermission: (typeof existingPermissions)[number]) => {
    return checkAccess({
      permissionsMap: permissionsFromStore,
      permission: checkPermission,
      roleKey: currentUserRoleName,
    });
  };

  return {
    ...accessResults,
    canAccess,
    isLoading: false,
  } as unknown as PermissionsAccessMap<T>;
};

/**
 * Converts a permission string to camelCase for property naming
 * @param permission - Permission string like "user:create"
 * @returns Camelized string like "UserCreate"
 */
function toCamelCase(permission: string): string {
  return permission
    .split(":")
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
    .join("");
}
