"use client";

import { I<PERSON><PERSON>ortB<PERSON>, IListCohortsResponse } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";
import { ColumnDef } from "@tanstack/react-table";
import { useCallback, useMemo, useState } from "react";
import { Plus, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/table";
import { Button } from "@/components/ui/button";
import { StatusBadge } from "@/components/base/courses/status-badge";
import CreateCohortModal from "@/components/(cohorts)/CreateCohortModal";
import { useTable } from "@/hooks/use-table";
import { useCohortList } from "@/services/cohort";
import { TableProps } from "@/types/table";
import { Filter } from "./filter";

interface CourseCohortProps {
  id: number;
}

type TableStateType = {
  pageIndex: number;
  pageSize: number;
  searchQuery: string;
  sortBy?: {
    id: string;
    desc: boolean;
  };
};

// Type for the cohort data as it comes from the backend
interface CohortEntity extends ICohortBase {
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export function CourseCohorts({ id }: CourseCohortProps) {
  const t = useTranslations("cohorts");
  const router = useRouter();
  const [openCreateCohortModal, setOpenCreateCohortModal] = useState(false);

  // Table state management with proper typing
  const [tableState, setTableState] = useState<TableStateType>({
    pageIndex: 0,
    pageSize: 10,
    searchQuery: "",
    sortBy: {
      id: "startDate",
      desc: true,
    },
  });

  // Fetch cohorts data using the service
  const { data: apiResponse, isLoading } = useCohortList({
    page: tableState.pageIndex + 1,
    limit: tableState.pageSize,
    search: tableState.searchQuery,
    courseId: id,
  });

  // Extract cohorts from the API response and apply sorting
  const cohorts = useMemo(() => {
    if (!apiResponse) return [];

    let cohortData = (apiResponse as IListCohortsResponse).data as CohortEntity[];

    // Always sort by startDate in descending order (newest/upcoming first)
    cohortData = [...cohortData].sort((a, b) => {
      // Ensure we're working with Date objects
      const dateA = new Date(a.startDate).getTime();
      const dateB = new Date(b.startDate).getTime();

      // Sort in descending order (newest first)
      return dateB - dateA;
    });

    // Then apply any other sorting if specified
    if (tableState.sortBy && tableState.sortBy.id !== "startDate") {
      const { id: sortField, desc: isDescending } = tableState.sortBy;

      cohortData = [...cohortData].sort((a, b) => {
        // Handle date fields
        if (
          sortField === "createdAt" ||
          sortField === "updatedAt" ||
          sortField === "startDate" ||
          sortField === "endDate"
        ) {
          const dateA = new Date(a[sortField as keyof CohortEntity] as string | Date).getTime();
          const dateB = new Date(b[sortField as keyof CohortEntity] as string | Date).getTime();
          return isDescending ? dateB - dateA : dateA - dateB;
        }

        // Handle numeric fields
        if (sortField === "maxParticipants" || sortField === "currentParticipants") {
          const numA = Number(a[sortField as keyof CohortEntity]);
          const numB = Number(b[sortField as keyof CohortEntity]);
          return isDescending ? numB - numA : numA - numB;
        }

        // Handle string fields
        const valA = String(a[sortField as keyof CohortEntity]).toLowerCase();
        const valB = String(b[sortField as keyof CohortEntity]).toLowerCase();
        return isDescending ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    }

    return cohortData;
  }, [apiResponse, tableState.sortBy]);

  // Handle cohort details navigation
  const handleViewCohortDetails = useCallback(
    (cohortId: number) => {
      router.push(`/cohorts/${cohortId}`);
    },
    [router],
  );

  // Define table columns with proper typing
  const columns = useMemo<ColumnDef<CohortEntity>[]>(
    () => [
      {
        id: "name",
        header: t("table.name"),
        accessorKey: "name",
        cell: ({ row }) => <div className="font-medium">{row.original.name}</div>,
        enableSorting: true,
      },
      {
        id: "startDate",
        header: t("table.start_date"),
        accessorKey: "startDate",
        cell: ({ row }) => new Date(row.original.startDate).toLocaleDateString(),
        enableSorting: true,
      },
      {
        id: "endDate",
        header: t("table.end_date"),
        accessorKey: "endDate",
        cell: ({ row }) => new Date(row.original.endDate).toLocaleDateString(),
        enableSorting: true,
      },
      {
        id: "maxParticipants",
        header: t("table.max_participants"),
        accessorKey: "maxParticipants",
        cell: ({ row }) => row.original.maxParticipants,
        enableSorting: true,
      },
      {
        id: "currentParticipants",
        header: t("table.current_participants"),
        accessorKey: "currentParticipants",
        cell: ({ row }) => row.original.currentParticipants,
        enableSorting: true,
      },
      {
        id: "status",
        header: t("table.status.self"),
        accessorKey: "status",
        cell: ({ row }) => (
          <StatusBadge status={t(`table.status.${row.original.status.toLowerCase()}`)} />
        ),
        enableSorting: true,
      },
      {
        id: "createdAt",
        header: t("created"),
        accessorKey: "createdAt",
        cell: ({ row }) => {
          const date = row.original.createdAt;
          return (
            <span className="text-sm text-muted-foreground">
              {date instanceof Date
                ? date.toLocaleDateString()
                : new Date(date).toLocaleDateString()}
            </span>
          );
        },
        enableSorting: true,
      },
      {
        id: "actions",
        header: t("table.actions.header"),
        cell: ({ row }) => {
          const cohort = row.original;
          return (
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => handleViewCohortDetails(cohort.id)}
            >
              <span className="sr-only">
                {t("table.actions.view_details", { name: cohort.name })}
              </span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          );
        },
      },
    ],
    [t, handleViewCohortDetails],
  );

  // Handle table state changes
  const handleTableStateChange = useCallback((newState: TableStateType) => {
    setTableState(newState);
  }, []);

  // Table configuration with proper typing
  const tableConfig = useMemo(
    () => ({
      onStateChange: handleTableStateChange,
      data: cohorts,
      columns,
      initialState: {
        sortBy: tableState.sortBy,
      },
    }),
    [cohorts, columns, handleTableStateChange, tableState.sortBy],
  );

  // Use the table hook
  const { selectedRows, handleRowSelection } = useTable(tableConfig);

  const createCohort = useCallback(() => {
    setOpenCreateCohortModal(true);
  }, []);

  // Memoize the create button
  const CreateCohortButton = useCallback(
    () => (
      <Button onClick={createCohort} className="ml-auto">
        <Plus className="mr-2 h-4 w-4" />
        {t("create")}
      </Button>
    ),
    [t],
  );

  // Memoize DataTable props
  const dataTableProps = useMemo<TableProps<CohortEntity>>(
    () => ({
      columns,
      data: cohorts,
      isLoading,
      selectedRows,
      enableSearch: true,
      enablePagination: true,
      enableSorting: true,
      pageSize: tableState.pageSize,
      totalRows: (apiResponse as IListCohortsResponse)?.total || 0,
      noResults: t("errors.no-results"),
      onStateChange: handleTableStateChange,
      onSelectionChange: handleRowSelection,
      getRowId: (row: CohortEntity) => row.id.toString(),
      minSearchLength: 1,
      filters: <Filter onCreateClick={createCohort} />,
      title: `${t("table.title")} (${(apiResponse as IListCohortsResponse)?.total || 0})`,
    }),
    [
      cohorts,
      apiResponse,
      isLoading,
      selectedRows,
      tableState.pageSize,
      t,
      columns,
      handleRowSelection,
      handleTableStateChange,
      createCohort,
    ],
  );

  return (
    <>
      <div className="overflow-x-auto">
        <DataTable {...dataTableProps} />
      </div>
      {openCreateCohortModal && (
        <CreateCohortModal
          courseId={id}
          isModalOpen={openCreateCohortModal}
          setIsModalOpen={setOpenCreateCohortModal}
          onComplete={() => {
            setOpenCreateCohortModal(false);
          }}
        />
      )}
    </>
  );
}
