import {
  IUserBase,
  IListUsersResponse,
  IBulkUpdateRole,
  IUpdateUser,
  ICreateUser,
  ISearchUser,
  UpdateResultWithItemInfo,
  IPaginationParams,
} from "@px-shared-account/hermes";

/**
 * Type for user list parameters
 */
export type ListUsersParams = ISearchUser & IPaginationParams;

/**
 * Core API endpoints for user operations
 * These are the base paths and methods used by both client and server implementations
 */
export const USER_API_ENDPOINTS = {
  /**
   * List users endpoint
   * @param params Query parameters for filtering and pagination
   * @returns Formatted URL with query parameters
   */
  list: (params: ListUsersParams = {}) => {
    const { search, role, page = 1, limit = 10, sortBy, sortOrder } = params;
    const queryParams = new URLSearchParams();

    if (search) queryParams.append("search", search);
    if (role !== undefined) queryParams.append("role", String(role)); // Ensure role is a string
    if (sortBy) queryParams.append("sortBy", sortBy);
    if (sortOrder) queryParams.append("sortOrder", sortOrder);
    queryParams.append("page", String(page));
    queryParams.append("limit", String(limit));

    return {
      url: `/users/list?${queryParams.toString()}`,
      method: "GET",
    };
  },

  /**
   * Get user by ID endpoint
   * @param id User ID
   * @returns Endpoint configuration
   */
  getById: (id: number) => ({
    url: `/users/get-by-id/${id}`,
    method: "GET",
  }),

  /**
   * Get user by SSO ID endpoint
   * @param id User SSO ID
   * @returns Endpoint configuration
   */
  getBySSOId: (id: string) => ({
    url: `/users/get-by-sso-id/${id}`,
    method: "GET",
  }),
  /**
   * Create user endpoint
   * @returns Endpoint configuration
   */
  create: () => ({
    url: "/users",
    method: "POST",
  }),

  /**
   * Update user endpoint
   * @param id User ID
   * @returns Endpoint configuration
   */
  update: (id: number) => ({
    url: `/users/${id}`,
    method: "PATCH",
  }),

  /**
   * Bulk update users endpoint
   * @returns Endpoint configuration
   */
  bulkUpdate: () => ({
    url: "/users/bulk-update",
    method: "POST",
  }),

  createPhoneNumber: (id: string) => ({
    url: `/users/${id}/phone-number`,
    method: "POST",
  }),

  unlinkPhoneNumber: (id: string) => ({
    url: `/users/${id}/phone-number/unlink`,
    method: "POST",
  }),
};

/**
 * Type definitions for API responses
 */
export type UserApiTypes = {
  list: IListUsersResponse;
  getById: IUserBase;
  getBySSOId: IUserBase;
  create: IUserBase;
  update: UpdateResultWithItemInfo<IUserBase>;
  bulkUpdate: UpdateResultWithItemInfo<IUserBase[]>;
};

/**
 * Type definitions for API request payloads
 */
export type UserApiPayloads = {
  create: ICreateUser;
  update: IUpdateUser;
  bulkUpdate: IBulkUpdateRole;
  createPhoneNumber: { phoneNumber: string };
  unlinkPhoneNumber: { phoneNumberId: string };
};
