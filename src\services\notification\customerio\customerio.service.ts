import { Injectable } from '@nestjs/common';
import { APIClient, SendEmailRequest, RegionEU } from 'customerio-node';
import { ConfigService } from '@config';

@Injectable()
export class CustomerioService {
  private client: APIClient;

  constructor(private configService: ConfigService) {
    const customerioSecrets = this.configService.customerioSecrets;
    this.client = new APIClient(customerioSecrets.CUSTOMERIO_API_KEY, {
      region: RegionEU,
    });
  }

  /**
   * Send a discount email to a user
   * @param email User email
   * @param code Discount code
   * @param discount Discount amount
   * @param expirationDate Expiration date of the discount code
   */
  async sendDiscountEmail(
    email: string,
    code: string,
    discount: number,
    expirationDate: Date,
  ) {
    if (!email || !code || !discount || !expirationDate) {
      throw new Error('Missing required parameters');
    }
    const templateId = 'discount_issued';
    try {
      const expirationDateFormatted = expirationDate
        .toISOString()
        .split('T')[0];
      const formattedExpirationDate = new Date(
        expirationDateFormatted,
      ).toLocaleDateString('fr-FR', {
        timeZone: 'Europe/Paris',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });

      const request = new SendEmailRequest({
        to: email,
        transactional_message_id: templateId,
        identifiers: { email },
        message_data: {
          code,
          discount,
          expirationDate: formattedExpirationDate,
        },
        send_to_unsubscribed: true,
        tracked: true,
        from: '<EMAIL>',
      });
      const response = await this.client.sendEmail(request);
      return response;
    } catch (error) {
      console.error('Customer.io send error', error);
      throw error;
    }
  }
}
