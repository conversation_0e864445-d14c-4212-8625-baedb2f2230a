import {
  Brackets,
  DeleteResult,
  En<PERSON><PERSON>Target,
  FindOptionsOrder,
  FindOptionsWhere,
  Repository,
  UpdateResult,
} from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { UpdateResultWithItemInfo } from '@types';
import { IPaginationParams } from '@px-shared-account/hermes';
import { IGenericRepository } from '@abstracts';

export class PostgresGenericRepository<T> implements IGenericRepository<T> {
  private _repository: Repository<T>;
  private _relationsToLoad: string[] = [];

  constructor(repository: Repository<T>, relationsToLoad: string[] = []) {
    this._repository = repository;
    this._relationsToLoad = relationsToLoad;
  }

  /**
   * Gets all records for the specified repository
   */
  async getAll(
    limit: number,
    page: number,
    sortOptions?: FindOptionsOrder<T>,
  ): Promise<{ items: T[]; total: number }> {
    console.log('\n\n\n\ngetting all\n\n');
    const [items, total] = await this._repository.findAndCount({
      relations: this._relationsToLoad,
      take: limit || undefined,
      skip: (page - 1) * limit || 0,
      order: sortOptions,
    });

    return {
      items,
      total,
    };
  }

  async findAllWithRelations(
    whereOptions: FindOptionsWhere<T>,
    limit = 50,
    page = 1,
    sortOptions?: FindOptionsOrder<T>,
  ): Promise<{ items: T[]; total: number }> {
    const [items, total] = await this._repository.findAndCount({
      where: whereOptions,
      relations: this._relationsToLoad,
      take: limit,
      skip: (page - 1) * limit,
      order: sortOptions,
    });

    return {
      items,
      total,
    };
  }

  /**
   * Gets a specific record based on conditions
   * @param whereOptions Options used for filtering
   */
  getOneBy(whereOptions: FindOptionsWhere<T>): Promise<T> {
    return this._repository.findOne({
      where: whereOptions,
      relations: this._relationsToLoad,
    });
  }

  /**
   * Gets records based on `whereOptions`
   * @param whereOptions Options used for filtering
   */
  async getAllBy(
    whereOptions: FindOptionsWhere<T>,
    limit = 100,
    page = 1,
    sortOptions?: FindOptionsOrder<T>,
  ): Promise<{ items: T[]; total: number }> {
    const [items, total] = await this._repository.findAndCount({
      where: whereOptions,
      take: limit,
      skip: (page - 1) * limit,
      order: sortOptions,
      relations: this._relationsToLoad,
    });

    return {
      items,
      total,
    };
  }

  /**
   * Gets a specific record based on conditions
   * @param whereOptions Options used for filtering
   */
  getByUsingQueryBuilder(
    tableToSearch: string,
    columnToMatch: string,
    lookupValue: string | number | boolean,
  ): Promise<T> {
    return this._repository
      .createQueryBuilder(tableToSearch)
      .where(`${tableToSearch}.${columnToMatch} = :lookupValue`, {
        lookupValue,
      })
      .loadAllRelationIds()
      .getOne();
  }

  /**
   * Gets all records matching the criteria
   * @param whereOptions Options used for filtering
   */
  getAllMatchingWithRelationIds(
    tableToSearch: string,
    columnToMatch: string,
    lookupValue: string | number | boolean,
  ): Promise<T[]> {
    return this._repository
      .createQueryBuilder(tableToSearch)
      .where(`${tableToSearch}.${columnToMatch} = :lookupValue`, {
        lookupValue,
      })
      .loadAllRelationIds()
      .getMany();
  }

  getOneWithRelations(
    tableToSearch: string,
    columnToMatch: string,
    lookupValue: string | number | boolean,
  ): Promise<T> {
    const queryBuilder = this._repository
      .createQueryBuilder(tableToSearch)
      .where(`${tableToSearch}.${columnToMatch} = :lookupValue`, {
        lookupValue,
      });
    const relations = this.getTableRelations();
    relations.forEach((relationName) => {
      queryBuilder.leftJoinAndSelect(
        `${tableToSearch}.${relationName}`,
        relationName,
      );
    });

    return queryBuilder.getOne();
  }

  getOneWithLeftJoin(
    columnToMatch: string,
    lookupValue: string | number | boolean,
    tableToJoin: string,
    joiningColumnInParent: string,
  ): Promise<T> {
    const tableName = this.getTableName();
    return this._repository
      .createQueryBuilder(tableName)
      .where(`${tableName}.${columnToMatch} = :lookupValue`, {
        lookupValue,
      })
      .leftJoinAndSelect(`${tableName}.${joiningColumnInParent}`, tableToJoin)
      .getOne();
  }

  getAllWithMatchingIds(
    idsToSearch: number[],
    joiningTableName: string,
    joiningColumn: string,
  ): Promise<T[]> {
    const tableName = this.getTableName();
    return this._repository
      .createQueryBuilder(tableName)
      .leftJoinAndSelect(`${tableName}.${joiningColumn}`, joiningTableName)
      .andWhereInIds(idsToSearch)
      .getMany();
  }

  /**
   * Creates a record in the database
   * @param item Values for the record
   */
  create(item: T): Promise<T> {
    return this._repository.save(item, { reload: true });
  }

  /**
   * Updates an record
   * @param findOptions Options used for filtering
   * @param updates Updates for the record
   */
  update(
    findOptions: FindOptionsWhere<T>,
    updates: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult> {
    return this._repository.update(findOptions, updates);
  }

  /**
   * Updates an entity and returns the updated record
   * @param lookupColumnName Column name to match against
   * @param lookupColumnValue Column value to match against
   * @param updates Update info including updated entity
   */
  async updateAndReturnItem(
    lookupColumnName: string,
    lookupColumnValue: string,
    updates: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResultWithItemInfo<T>> {
    const result: UpdateResult = await this._repository
      .createQueryBuilder()
      .update(updates)
      .where({
        [lookupColumnName]: lookupColumnValue,
      })
      .returning('*')
      .execute();

    const success = result.affected !== 0;
    const updatedItem = result.raw[0];
    return {
      success,
      updatedItem,
    };
  }

  /**
   * Searches a table for a query for specific column combined with additional filters
   * @param entity The entity to search
   * @param query The search query
   * @param queryColumns Columns to search for the query
   * @param filters Additional filters, applied using AND
   * @returns A promise that resolves to query results, total count and pagination info
   */
  async search(
    entity: EntityTarget<T>,
    query: string,
    queryColumns: (keyof T)[],
    filters: FindOptionsWhere<T>[],
    loadRelations = false,
    paginationParams?: IPaginationParams,
  ): Promise<{ items: T[]; total: number }> {
    const entityName = typeof entity === 'function' ? entity.name : entity;
    const queryBuilder = this._repository.createQueryBuilder(
      entityName as string,
    );

    if (query && queryColumns.length > 0) {
      const conditions = queryColumns
        .map((column) => `${entityName}.${String(column)} ILIKE :query`)
        .join(' OR ');
      queryBuilder.andWhere(
        new Brackets((qb) => qb.where(conditions, { query: `%${query}%` })),
      );
    }

    if (!this.filtersHasRelations(filters) && loadRelations) {
      this._relationsToLoad.forEach((relation) => {
        queryBuilder.leftJoinAndSelect(`${entityName}.${relation}`, relation);
      });
    }

    if (filters && filters.length > 0) {
      filters.forEach((filter) => {
        Object.entries(filter).forEach(([key, value]) => {
          let filterName = `${entityName}.${key}`;

          if (this._relationsToLoad?.includes(key)) {
            queryBuilder.leftJoinAndSelect(`${entityName}.${key}`, key);
            filterName = key;
            const [relationColumn, relationColumnValue] =
              Object.entries(value)[0];

            const paramName = `${filterName}_${relationColumn}`;
            queryBuilder.andWhere(
              `${filterName}.${relationColumn} = :${paramName}`,
              {
                [paramName]: relationColumnValue,
              },
            );
          } else {
            queryBuilder.andWhere(`${filterName} = :${key}`, {
              [key]: value,
            });
          }
        });
      });
    }

    if (paginationParams?.limit) {
      queryBuilder.take(paginationParams?.limit);
      queryBuilder.skip((paginationParams?.page - 1) * paginationParams?.limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();
    return { items, total };
  }

  /**
   * Deletes a record
   * @param findOptions Options used for filtering
   */
  delete(findOptions: FindOptionsWhere<T>): Promise<DeleteResult> {
    return this._repository.softDelete(findOptions);
  }

  /**
   * Hard deletes a record
   * @param findOptions Options used for filtering
   */
  hardDelete(findOptions: FindOptionsWhere<T>): Promise<DeleteResult> {
    return this._repository.delete(findOptions);
  }

  /**
   * Gets the table name of the repository to be used in complex queries
   */
  getTableName(): string {
    return this._repository.metadata.tableName;
  }

  /**
   * Checks if the filters have relations
   * @param filters Filters to check
   * @returns True if the filters have relations, false otherwise
   */
  filtersHasRelations(filters: FindOptionsWhere<T>[]): boolean {
    return filters.some((filter) =>
      Object.keys(filter).some((key) => this._relationsToLoad.includes(key)),
    );
  }

  getTableRelations(): string[] {
    const repositoryMetadata = this._repository.metadata;
    return repositoryMetadata.relations.map(
      (relation) => relation.propertyName,
    );
  }

  getRepository(): Repository<T> {
    return this._repository;
  }
}
