"use client";

import { IUserBase } from "@px-shared-account/hermes";
import { useMemo, useState } from "react";

import EditUserModal from "./EditUserModal";
import UserTabs from "./UserTabs";
import Information from "./Informations";
import { useUserDetails } from "@/services/user";
import { useUserInitials } from "@/hooks/user";
import UserAvatar from "../../components/UserAvatar";

export default function UserDetails({ id }: { id: number }) {
  const { data: user, mutate: mutateUser } = useUserDetails(id);
  const { email, name } = useUserInitials(user);
  const [activeTab, setActiveTab] = useState<string>("information");

  const data = useMemo(() => {
    return {
      user,
      email,
      name,
    };
  }, [user, email, name]);

  const onTabChange = (value: string) => {
    setActiveTab(value);
  };

  const TabContent = useMemo(() => {
    switch (activeTab) {
      case "profile":
        return <div>Profile content</div>;
      case "calendar":
        return <div>Calendar content</div>;
      case "analytics":
        return <div>Analytics content</div>;
      case "notifications":
        return <div>Notifications content</div>;
      case "information":
        return <Information user={data.user as IUserBase} />;
      default:
        return <div>Default content</div>;
    }
  }, [activeTab, data]);

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center gap-4">
          <UserAvatar user={data.user} size={16} />
          <div className="flex flex-col gap-1 text-white">
            <div className="text-lg font-medium">{data.name}</div>
            <div className="text-xs">{data.email}</div>
            {/* <span className="flex justify-center rounded-md bg-gray-200 p-1 text-xs uppercase text-white">
              {user?.profile}
            </span> */}
          </div>
        </div>
        {data.user && <EditUserModal user={data.user as IUserBase} onUpdate={() => mutateUser()} />}
      </div>
      <div className="space-y-16">
        <UserTabs activeTab={activeTab} onTabChange={onTabChange} />
      </div>
      {TabContent}
    </div>
  );
}
