# Stage 1: Build and Install Dependencies
FROM node:20.11-alpine AS builder

# Setup pnpm globally
RUN npm i -g pnpm

# Set the working directory
WORKDIR /usr/src/app

# Copy package.json and pnpm-lock.yaml for dependency installation
COPY package.json pnpm-lock.yaml ./

# Use Docker secret to provide .npmrc with token and install dependencies
RUN --mount=type=secret,id=npmrc,target=/root/.npmrc \
    pnpm install --frozen-lockfile

# Copy the rest of the application code
COPY . .

# Build the application
RUN pnpm build

# Verify build output exists
RUN ls -la dist && [ -f dist/main.js ]

# Prune dev dependencies
RUN pnpm prune --prod

# Stage 2: Create Production Image
FROM node:20.11-alpine AS production

# Set the working directory
WORKDIR /usr/src/app

# Copy only the necessary files from the "builder" stage
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/package.json ./package.json

# Verify files are copied correctly
RUN ls -la && ls -la dist && [ -f dist/main.js ]

# Expose the application port
EXPOSE 3000

# Start the server using the production build
CMD ["npm", "run", "start:prod"]