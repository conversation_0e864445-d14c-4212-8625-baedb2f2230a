import { Injectable, OnApplicationBootstrap } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import {
  CreditNoteTable,
  CronLogTable,
  CustomerAccessTable,
  DiscountTable,
  InvoiceTable,
  ProductLineTable,
  ProductFamilyTable,
  ProductTable,
  ProductPlanTable,
  ProductPlanPriceTable,
  TransactionTable,
  CheckoutPageTable,
  ProductOfferTable,
  ProductOfferAccessTable,
  WarrantySectionTable,
  BankTransferTable,
  SubscriptionTable,
  ExchangeRateTable,
  WebhookTable,
  CohortTable,
  CourseTable,
  RoleTable,
  UserTable,
  UserProfileTable,
  TriadTable,
  TriadParticipantTable,
  WhatsNewVersionTable,
  WhatsNewReactionTable,
} from '@tables';
import { PostgresGenericRepository } from './postgres-generic-repository';
import { IDataServices } from '@abstracts';
@Injectable()
export class PostgresDataServices
  implements IDataServices, OnApplicationBootstrap {
  bankTransfer: PostgresGenericRepository<BankTransferTable>;
  checkoutPage: PostgresGenericRepository<CheckoutPageTable>;
  creditNote: PostgresGenericRepository<CreditNoteTable>;
  cronLog: PostgresGenericRepository<CronLogTable>;
  customerAccess: PostgresGenericRepository<CustomerAccessTable>;
  discount: PostgresGenericRepository<DiscountTable>;
  exchangeRate: PostgresGenericRepository<ExchangeRateTable>;
  invoice: PostgresGenericRepository<InvoiceTable>;
  productLine: PostgresGenericRepository<ProductLineTable>;
  productFamily: PostgresGenericRepository<ProductFamilyTable>;
  product: PostgresGenericRepository<ProductTable>;
  productOffer: PostgresGenericRepository<ProductOfferTable>;
  productOfferAccess: PostgresGenericRepository<ProductOfferAccessTable>;
  productPlan: PostgresGenericRepository<ProductPlanTable>;
  productPlanPrice: PostgresGenericRepository<ProductPlanPriceTable>;
  subscription: PostgresGenericRepository<SubscriptionTable>;
  transaction: PostgresGenericRepository<TransactionTable>;
  warrantySection: PostgresGenericRepository<WarrantySectionTable>;
  webhook: PostgresGenericRepository<WebhookTable>;
  whatsNewVersion: PostgresGenericRepository<WhatsNewVersionTable>;
  whatsNewReaction: PostgresGenericRepository<WhatsNewReactionTable>;

  cohort: PostgresGenericRepository<CohortTable>;
  course: PostgresGenericRepository<CourseTable>;
  role: PostgresGenericRepository<RoleTable>;
  user: PostgresGenericRepository<UserTable>;
  userProfile: PostgresGenericRepository<UserProfileTable>;
  triad: PostgresGenericRepository<TriadTable>;
  triadParticipant: PostgresGenericRepository<TriadParticipantTable>;

  constructor(
    private dataSource: DataSource,
    @InjectRepository(BankTransferTable)
    private BankTransferRepository: Repository<BankTransferTable>,
    @InjectRepository(CheckoutPageTable)
    private CheckoutPageRepository: Repository<CheckoutPageTable>,
    @InjectRepository(CreditNoteTable)
    private CreditNoteRepository: Repository<CreditNoteTable>,
    @InjectRepository(CronLogTable)
    private CronLogRepository: Repository<CronLogTable>,
    @InjectRepository(CustomerAccessTable)
    private CustomerAccessRepository: Repository<CustomerAccessTable>,
    @InjectRepository(DiscountTable)
    private DiscountRepository: Repository<DiscountTable>,
    @InjectRepository(ExchangeRateTable)
    private ExchangeRateRepository: Repository<ExchangeRateTable>,
    @InjectRepository(InvoiceTable)
    private InvoiceRepository: Repository<InvoiceTable>,
    @InjectRepository(ProductLineTable)
    private ProductLineRepository: Repository<ProductLineTable>,
    @InjectRepository(ProductFamilyTable)
    private ProductFamilyRepository: Repository<ProductFamilyTable>,
    @InjectRepository(ProductTable)
    private ProductRepository: Repository<ProductTable>,
    @InjectRepository(ProductPlanTable)
    private ProductPlanRepository: Repository<ProductPlanTable>,
    @InjectRepository(ProductPlanPriceTable)
    private ProductPlanPriceRepository: Repository<ProductPlanPriceTable>,
    @InjectRepository(ProductOfferTable)
    private ProductOfferRepository: Repository<ProductOfferTable>,
    @InjectRepository(ProductOfferAccessTable)
    private ProductOfferAccessRepository: Repository<ProductOfferAccessTable>,
    @InjectRepository(SubscriptionTable)
    private SubscriptionRepository: Repository<SubscriptionTable>,
    @InjectRepository(TransactionTable)
    private TransactionRepository: Repository<TransactionTable>,
    @InjectRepository(WarrantySectionTable)
    private WarrantySectionRepository: Repository<WarrantySectionTable>,
    @InjectRepository(WebhookTable)
    private WebhookRepository: Repository<WebhookTable>,
    @InjectRepository(WhatsNewVersionTable)
    private WhatsNewVersionRepository: Repository<WhatsNewVersionTable>,
    @InjectRepository(WhatsNewReactionTable)
    private WhatsNewReactionRepository: Repository<WhatsNewReactionTable>,
    @InjectRepository(CohortTable)
    private CohortRepository: Repository<CohortTable>,
    @InjectRepository(CourseTable)
    private CourseRepository: Repository<CourseTable>,
    @InjectRepository(RoleTable)
    private RoleRepository: Repository<RoleTable>,
    @InjectRepository(UserTable)
    private UserRepository: Repository<UserTable>,
    @InjectRepository(UserProfileTable)
    private UserProfileRepository: Repository<UserProfileTable>,
    @InjectRepository(TriadTable)
    private TriadRepository: Repository<TriadTable>,
    @InjectRepository(TriadParticipantTable)
    private TriadParticipantRepository: Repository<TriadParticipantTable>,
  ) { }

  /**
   * Registers all the repositories on app's startup
   */
  async onApplicationBootstrap() {
    this.bankTransfer = new PostgresGenericRepository<BankTransferTable>(
      this.BankTransferRepository,
    );
    this.checkoutPage = new PostgresGenericRepository<CheckoutPageTable>(
      this.CheckoutPageRepository,
    );
    this.creditNote = new PostgresGenericRepository<CreditNoteTable>(
      this.CreditNoteRepository,
    );
    this.cronLog = new PostgresGenericRepository<CronLogTable>(
      this.CronLogRepository,
    );
    this.customerAccess = new PostgresGenericRepository<CustomerAccessTable>(
      this.CustomerAccessRepository,
    );
    this.discount = new PostgresGenericRepository<DiscountTable>(
      this.DiscountRepository,
      ['pricesAttachedTo'],
    );
    this.exchangeRate = new PostgresGenericRepository<ExchangeRateTable>(
      this.ExchangeRateRepository,
    );
    this.invoice = new PostgresGenericRepository<InvoiceTable>(
      this.InvoiceRepository,
    );
    this.productLine = new PostgresGenericRepository<ProductLineTable>(
      this.ProductLineRepository,
    );
    this.productFamily = new PostgresGenericRepository<ProductFamilyTable>(
      this.ProductFamilyRepository,
    );
    this.product = new PostgresGenericRepository<ProductTable>(
      this.ProductRepository,
      ['productFamily', 'plans'],
    );
    this.productPlan = new PostgresGenericRepository<ProductPlanTable>(
      this.ProductPlanRepository,
      ['prices', 'product'],
    );
    this.productPlanPrice =
      new PostgresGenericRepository<ProductPlanPriceTable>(
        this.ProductPlanPriceRepository,
      );
    this.productOffer = new PostgresGenericRepository<ProductOfferTable>(
      this.ProductOfferRepository,
      ['checkoutPage'],
    );
    this.productOfferAccess =
      new PostgresGenericRepository<ProductOfferAccessTable>(
        this.ProductOfferAccessRepository,
      );
    this.subscription = new PostgresGenericRepository<SubscriptionTable>(
      this.SubscriptionRepository,
    );
    this.transaction = new PostgresGenericRepository<TransactionTable>(
      this.TransactionRepository,
    );
    this.warrantySection = new PostgresGenericRepository<WarrantySectionTable>(
      this.WarrantySectionRepository,
    );
    this.webhook = new PostgresGenericRepository<WebhookTable>(
      this.WebhookRepository,
    );
    this.whatsNewVersion = new PostgresGenericRepository<WhatsNewVersionTable>(
      this.WhatsNewVersionRepository,
    );
    this.whatsNewReaction = new PostgresGenericRepository<WhatsNewReactionTable>(
      this.WhatsNewReactionRepository,
      ['version'],
    );
    this.cohort = new PostgresGenericRepository<CohortTable>(
      this.CohortRepository,
      ['students', 'course'],
    );
    this.course = new PostgresGenericRepository<CourseTable>(
      this.CourseRepository,
      ['managers', 'offers'],
    );
    this.role = new PostgresGenericRepository<RoleTable>(this.RoleRepository);
    this.user = new PostgresGenericRepository<UserTable>(this.UserRepository, [
      'role',
    ]);
    this.userProfile = new PostgresGenericRepository<UserProfileTable>(
      this.UserProfileRepository,
    );
    this.triad = new PostgresGenericRepository<TriadTable>(
      this.TriadRepository,
      ['participants', 'participants.user'],
    );
    this.triadParticipant =
      new PostgresGenericRepository<TriadParticipantTable>(
        this.TriadParticipantRepository,
      );
  }

  /**
   * Resets staging database by deleting everything from all tables except `users` & `typeorm_migrations`
   */
  async resetDatabase(): Promise<any> {
    const entities = this.dataSource.entityMetadatas;
    const appName = this.dataSource.driver.options?.name;

    if (appName?.includes('production')) {
      console.error(
        '❌ Looks like you tried to clean production DB, check your \x1b[32m ENV \x1b[0m file OR \x1b[32m NODE_ENV \x1b[0m',
      );
      return null;
    }
    const tables = entities
      .map((entity) => `"${entity.tableName}"`)
      .filter((tableName) => {
        return tableName !== '"users"' && tableName !== '"typeorm_migrations"';
      })
      .join(', ');

    return this.dataSource.query(`TRUNCATE ${tables} CASCADE;`);
  }

  getDataSource(): DataSource {
    return this.dataSource;
  }
}
