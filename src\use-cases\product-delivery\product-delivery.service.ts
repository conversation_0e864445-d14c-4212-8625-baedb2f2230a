import { Injectable } from '@nestjs/common';
import { UserUseCases } from '../user';
import { LearnworldsService } from '@services/lms';
import { CustomerAccessUseCases } from '../customer-access';
import {
  CustomerAccessType,
  AccessTarget,
  CommunityType,
} from '@enums';
import {
  AccessDetails,
  CommunityAccess,
  CommunityInfo,
  CourseAccess,
  CourseInfo,
  CronLogResult,
  PXActionResult,
  SpaceAccess,
  SpaceInfo,
  SuspensionResult,
} from '@types';
import { CircleService } from '@services/community';
import { UserEntity, CustomerAccess } from '@entities';
import { CustomerAccessStatus } from '@px-shared-account/hermes';

@Injectable()
export class ProductDeliveryService {
  constructor(
    private readonly userService: UserUseCases,
    private readonly customerAccessService: CustomerAccessUseCases,
    private readonly lmsService: LearnworldsService,
    private readonly circleService: CircleService,
  ) {}

  /**
   * Initiates product delivery for a successful subscription
   * @param subscriptionId `id` of the subscription
   * @param offerId `id` of the offer linked to the subscription
   * @param offerName Name of the offer linked to the subscription
   * @param customerEmail email address of the customer
   * @param customerFirstName First name of the customer
   * @param customerLastName Last name of the customer
   * @param businessEntityId Chargebee business entity id this customer belongs to
   * @param customerChargebeeId Chargebee id of the customer
   * @param courses Courses that the subscription provides access to
   * @param communities Communities that the subscription provides access to
   */
  async initiateProductDelivery(
    subscriptionId: number,
    offerId: number,
    offerName: string,
    customerEmail: string,
    customerFirstName: string,
    customerLastName: string,
    businessEntityId?: string,
    customerChargebeeId?: string,
    courses?: CourseInfo[],
    communities?: CommunityInfo[],
  ): Promise<PXActionResult> {
    const { success, message, data } = await this.userService.createOrInvite(
      customerEmail,
      customerFirstName,
      customerLastName,
      businessEntityId,
      customerChargebeeId,
    );
    const user = data?.user as UserEntity;

    if (!success) {
      return {
        success,
        message,
        data,
      };
    }

    try {
      const courseAccessResult = await this.handleCourseAccess(
        user.id,
        courses,
        subscriptionId,
        offerName,
        offerId,
        customerEmail,
        customerFirstName,
      );

      const communityAccessResult = await this.handleCommunityAccess(
        user.id,
        communities,
        subscriptionId,
        offerId,
        customerEmail,
        `${customerFirstName} ${customerLastName || ''}`,
      );

      const { pxsSpaces, pxlSpaces } = this.extractSpaces(communities);

      let pxsSpaceAccessResult;
      let pxlSpaceAccessResult;
      if (pxsSpaces.length > 0) {
        pxsSpaceAccessResult = await this.handleSpaceAccess(
          user.id,
          subscriptionId,
          offerId,
          customerEmail,
          pxsSpaces,
          CommunityType.PXS,
        );
      }
      if (pxlSpaces.length > 0) {
        pxlSpaceAccessResult = await this.handleSpaceAccess(
          user.id,
          subscriptionId,
          offerId,
          customerEmail,
          pxlSpaces,
          CommunityType.PXL,
        );
      }

      return {
        success: true,
        message: 'Product delivery executed successfully',
        data: {
          courseAccessResult,
          communityAccessResult,
          pxsSpaceAccessResult,
          pxlSpaceAccessResult,
        },
      };
    } catch (err) {
      return {
        success: false,
        message: `Error occurred while handling product delivery: ${err}`,
      };
    }
  }

  /**
   * Handles course access for a given subscription
   * @param customerId Customer ID
   * @param courses Courses to be accessed
   * @param subscriptionId Subscription ID
   * @param offerName Offer name
   * @param offerId Offer ID
   * @param customerEmail Customer email
   * @param customerFirstName Customer first name
   */
  private async handleCourseAccess(
    customerId: number,
    courses: CourseInfo[],
    subscriptionId: number,
    offerName: string,
    offerId: number,
    customerEmail: string,
    customerFirstName: string,
  ): Promise<{
    successfulCourseAccesses: number;
    failedCourseAccesses: number;
  }> {
    let successfulCourseAccesses = 0;
    let failedCourseAccesses = 0;
    for (const course of courses) {
      const { name, id } = course;
      const accessDetails: AccessDetails = {
        target: AccessTarget.LW_COURSE,
        name,
        slug: id,
        link: `https://learning.paradox.io/course/${id}`,
      };

      const courseAccess = await this.customerAccessService.create(
        customerId,
        subscriptionId,
        offerId,
        CustomerAccessStatus.PENDING,
        CustomerAccessType.LMS,
        accessDetails,
      );

      try {
        const { success, message } = await this.lmsService.enrollUser(
          customerEmail,
          customerFirstName,
          course.id,
        );

        if (!success) {
          await this.customerAccessService.update(
            { id: courseAccess.id },
            {
              status: CustomerAccessStatus.ERROR,
              metaData: { actionResult: message },
            },
          );
          failedCourseAccesses++;
          continue;
        }

        if (success) {
          await this.lmsService.addTagsToUser(customerEmail, [offerName]);
          await this.customerAccessService.update(
            { id: courseAccess.id },
            { status: CustomerAccessStatus.GRANTED },
          );
          successfulCourseAccesses++;
        }
      } catch (error) {
        await this.customerAccessService.update(
          { id: courseAccess.id },
          {
            status: CustomerAccessStatus.ERROR,
            metaData: { error },
          },
        );
      }
    }
    return {
      successfulCourseAccesses,
      failedCourseAccesses,
    };
  }

  /**
   * Handles community access for a given subscription
   * @param customerId `id` of the customer to whom the subscription belongs
   * @param communities Communities to which the customer needs to be added
   * @param subscriptionId Subscription ID
   * @param offerId Offer ID
   * @param customerEmail Customer email
   * @param customerName Customer name
   */
  private async handleCommunityAccess(
    customerId: number,
    communities: CommunityInfo[],
    subscriptionId: number,
    offerId: number,
    customerEmail: string,
    customerName: string,
  ): Promise<{
    successfulCommunityAccesses: number;
    failedCommunityAccesses: number;
  }> {
    let successfulCommunityAccesses = 0;
    let failedCommunityAccesses = 0;
    for (const communityInfo of communities) {
      const { community } = communityInfo;
      const accessDetails: AccessDetails = {
        target: AccessTarget.CIRCLE_COMMUNITY,
        name: community,
        accessId: null,
        domain: community,
        link: this.circleService.generateCommunityLink(community),
      };

      const communityAccess = await this.customerAccessService.create(
        customerId,
        subscriptionId,
        offerId,
        CustomerAccessStatus.PENDING,
        CustomerAccessType.COMMUNITY,
        accessDetails,
      );
      try {
        const { success, error, accessId, message } =
          await this.circleService.addCommunityMemberV1(
            customerEmail,
            customerName,
            community,
          );

        if (error) {
          await this.customerAccessService.update(
            { id: communityAccess.id },
            {
              status: CustomerAccessStatus.ERROR,
              metaData: { error, messageFromCircle: message },
            },
          );
          failedCommunityAccesses++;
          continue;
        }

        if (success) {
          await this.customerAccessService.update(
            { id: communityAccess.id },
            {
              status: CustomerAccessStatus.GRANTED,
              details: { ...accessDetails, accessId },
              metaData: { messageFromCircle: message },
            },
          );
          successfulCommunityAccesses++;
        }
      } catch (error) {
        await this.customerAccessService.update(
          { id: communityAccess.id },
          {
            status: CustomerAccessStatus.ERROR,
            metaData: { error, message: error?.message },
          },
        );
      }
    }
    return {
      successfulCommunityAccesses,
      failedCommunityAccesses,
    };
  }

  /**
   * Handles space access for a given subscription
   * @param customerId `id` of the customer to whom the subscription belongs
   * @param subscriptionId `id` of the subscription to which the space access belongs
   * @param offerId `id` of the offer to which the space access belongs
   * @param customerEmail Customer email
   * @param spaces Spaces to which the customer needs to be added
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   */
  async handleSpaceAccess(
    customerId: number,
    subscriptionId: number,
    offerId: number,
    customerEmail: string,
    spaces: SpaceInfo[],
    domain: CommunityType,
  ): Promise<{
    successfulSpaceAccesses: number;
    failedSpaceAccesses: number;
  }> {
    let successfulSpaceAccesses = 0;
    let failedSpaceAccesses = 0;
    for (const space of spaces) {
      const accessDetails: AccessDetails = {
        target: AccessTarget.CIRCLE_SPACE,
        name: space.name,
        spaceId: space.id,
        domain,
        link: this.circleService.generateSpaceLink(domain, space.slug),
      };
      const communityAccess = await this.customerAccessService.create(
        customerId,
        subscriptionId,
        offerId,
        CustomerAccessStatus.PENDING,
        CustomerAccessType.COMMUNITY,
        accessDetails,
      );
      try {
        const { success, message } = await this.circleService.addSpaceMemberV1(
          customerEmail,
          space.id,
          domain,
        );

        if (!success) {
          await this.customerAccessService.update(
            { id: communityAccess.id },
            {
              status: CustomerAccessStatus.ERROR,
              metaData: { messageFromCircle: message },
            },
          );
          failedSpaceAccesses++;
          continue;
        }

        if (success) {
          await this.customerAccessService.update(
            { id: communityAccess.id },
            {
              status: CustomerAccessStatus.GRANTED,
              metaData: { messageFromCircle: message },
            },
          );
          successfulSpaceAccesses++;
        }
      } catch (error) {
        await this.customerAccessService.update(
          { id: communityAccess.id },
          {
            status: CustomerAccessStatus.ERROR,
            metaData: { error, message: error?.message },
          },
        );
      }
    }
    return {
      successfulSpaceAccesses,
      failedSpaceAccesses,
    };
  }

  /**
   * Initiates the suspension of all customer accesses for a given subscription
   * @param subscriptionId The subscription id to suspend the accesses for
   * @returns A CronLogResult object containing the success status and output
   */
  async initiateSuspension(subscriptionId: number): Promise<CronLogResult> {
    const queryResult = await this.customerAccessService.listBySubscriptionId(
      subscriptionId,
    );
    const accessesToSuspend = queryResult?.items;
    if (!accessesToSuspend || accessesToSuspend.length === 0) {
      return {
        output: null,
        success: false,
        message: `No accesses to suspend for subscription ${subscriptionId}`,
      };
    }

    const jobResult = {} as CronLogResult;
    jobResult.output = [];
    const accessesWithoutCommunities = accessesToSuspend.filter(
      (access) => access.details.target !== AccessTarget.CIRCLE_COMMUNITY,
    );
    for (const access of accessesWithoutCommunities) {
      const suspensionResult = await this.suspendAccess(access.id);
      const message = suspensionResult?.suspended
        ? `Suspended access for subscription ${access.subscriptionId}`
        : `Failed to suspend access for subscription ${access.subscriptionId}`;
      jobResult.output.push({
        message,
        success: suspensionResult?.suspended,
        output: suspensionResult,
      });
    }
    jobResult.success = jobResult.output.every(
      (result: PXActionResult) => result.success,
    );
    return jobResult;
  }

  /**
   * Suspends access to LearnWorlds & Circle based on `accessId`
   * @param accessId The `id` of the `CustomerAccess` to suspend access against
   * @returns A promise that resolves to update result of `CustomerAccess` entity
   */
  async suspendAccess(accessId: number): Promise<SuspensionResult> {
    const accessData = await this.customerAccessService.getById(accessId);
    const customer = await this.userService.getById(accessData.customerId);
    const customerEmail = customer?.email;
    const accessDetails = accessData.details;
    let actionResult: PXActionResult;

    switch (accessDetails.target) {
      case AccessTarget.LW_COURSE: {
        const { slug } = accessDetails as CourseAccess;
        actionResult = await this.lmsService.unEnrollUser(customerEmail, slug);
        break;
      }

      case AccessTarget.CIRCLE_SPACE: {
        const { spaceId, domain } = accessDetails as SpaceAccess;
        actionResult = await this.circleService.removeSpaceMemberV1(
          customerEmail,
          spaceId,
          domain,
        );
        break;
      }

      default:
        throw new Error(
          `Unsupported access target. ${JSON.stringify(accessDetails)}`,
        );
    }

    if (actionResult?.success) {
      await this.customerAccessService.update(
        { id: accessId },
        { status: CustomerAccessStatus.SUSPENDED },
      );
    } else {
      await this.customerAccessService.update(
        { id: accessId },
        {
          status: CustomerAccessStatus.ERROR,
          metaData: {
            message: actionResult?.message,
          },
        },
      );
    }

    return {
      suspended: actionResult?.success,
      accessDetails,
      customerEmail,
      customerName: `${customer?.firstName} ${customer?.lastName}`,
      serviceResponse: actionResult,
    };
  }

  /**
   * Unsuspends all accesses for a given subscription
   * @param subscriptionId The Chargebee `id` of the `Subscription` to unsuspend access against
   * @returns A promise that resolves when all accesses are unsuspended
   */
  async unSuspendAccessForSubscription(subscriptionId: number) {
    const { items: accesses } =
      await this.customerAccessService.listBySubscriptionId(subscriptionId);

    for (const access of accesses) {
      await this.unSuspendAccess(access.id);
    }
  }

  /**
   * unSuspends access to LearnWorlds & Circle based on `accessId`
   * @param accessId The `id` of the `CustomerAccess` to unsuspend access against
   * @returns A promise that resolves to update result of `CustomerAccess` entity
   */
  async unSuspendAccess(accessId: number): Promise<PXActionResult> {
    const accessData = await this.customerAccessService.getById(accessId);
    const customer = await this.userService.getById(accessData.customerId);
    const customerEmail = customer?.email;
    const accessDetails = accessData.details;
    let result: PXActionResult;

    switch (accessDetails.target) {
      case AccessTarget.LW_COURSE: {
        const { slug } = accessDetails as CourseAccess;
        result = await this.lmsService.enrollUser(
          customerEmail,
          customer?.firstName,
          slug,
        );
        break;
      }

      case AccessTarget.CIRCLE_COMMUNITY: {
        const { domain } = accessDetails as CommunityAccess;
        result = await this.circleService.addCommunityMember(
          customerEmail,
          `${customer.firstName} ${customer?.lastName || ''}`,
          domain,
        );
        break;
      }

      case AccessTarget.CIRCLE_SPACE: {
        const { spaceId, domain } = accessDetails as SpaceAccess;
        result = await this.circleService.addSpaceMember(
          customerEmail,
          spaceId,
          domain,
        );
        break;
      }

      default:
        throw new Error(
          `Unsupported access target. ${JSON.stringify(accessDetails)}`,
        );
    }

    if (result?.success) {
      return this.customerAccessService.update(
        { id: accessId },
        { status: CustomerAccessStatus.GRANTED },
      );
    } else {
      return this.customerAccessService.update(
        { id: accessId },
        {
          status: CustomerAccessStatus.ERROR,
          metaData: { message: result?.message },
        },
      );
    }
  }

  /**
   * Extracts PXL and PXS spaces from the given communities
   * @param communities Communities to extract spaces from
   * @returns An object containing PXL and PXS spaces
   */
  extractSpaces(communities: CommunityInfo[]): {
    pxlSpaces: SpaceInfo[];
    pxsSpaces: SpaceInfo[];
  } {
    const pxlSpaces = communities
      .map((communityInfo) => {
        if (communityInfo.community === 'PXL') {
          return communityInfo.spaces;
        }
        return [];
      })
      .flat();

    const pxsSpaces = communities
      .map((communityInfo) => {
        if (communityInfo.community === 'PXS') {
          return communityInfo.spaces;
        }
        return [];
      })
      .flat();

    return { pxlSpaces, pxsSpaces };
  }

  /**
   * Handles failed customer accesses using a cron job
   * @param customerAccess Customer access entity
   * @param customerEmail Customer email
   * @param customerName Customer name
   */
  async handleFailedCustomerAccess(
    customerAccess: CustomerAccess,
    customerEmail: string,
    customerName: string,
  ): Promise<CronLogResult> {
    let result: PXActionResult;

    switch (customerAccess.details.target) {
      case AccessTarget.LW_COURSE:
        const courseAccess = customerAccess.details as CourseAccess;
        result = await this.lmsService.enrollUser(
          customerEmail,
          customerName,
          courseAccess.slug,
        );
        break;
      case AccessTarget.CIRCLE_COMMUNITY:
        const communityAccess = customerAccess.details as CommunityAccess;
        result = await this.circleService.addCommunityMemberV1(
          customerEmail,
          customerName,
          communityAccess.domain,
        );
        break;
      case AccessTarget.CIRCLE_SPACE:
        const spaceAccess = customerAccess.details as SpaceAccess;
        result = await this.circleService.addSpaceMemberV1(
          customerEmail,
          spaceAccess.spaceId,
          spaceAccess.domain,
        );
        break;
      default:
        throw new Error(
          `Unsupported access target. ${JSON.stringify(customerAccess)}`,
        );
    }

    if (result?.success) {
      await this.customerAccessService.update(
        { id: customerAccess.id },
        {
          status: CustomerAccessStatus.GRANTED,
          metaData: { message: result?.message },
        },
      );
    } else {
      await this.customerAccessService.update(
        { id: customerAccess.id },
        {
          status: CustomerAccessStatus.ERROR,
          metaData: { message: result?.message },
        },
      );
    }

    return {
      success: result?.success,
      output: result,
      message: result?.message,
    };
  }
}
