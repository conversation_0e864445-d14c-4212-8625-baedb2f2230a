"use client";

import { ICohortBase, IListCohortsResponse, CohortStatus } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";
import { ColumnDef } from "@tanstack/react-table";
import { useCallback, useMemo, useState } from "react";
import { ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";

import { DataTable } from "@/components/table";
import { Button } from "@/components/ui/button";
import { StatusBadge } from "@/components/base/courses/status-badge";
import { useTable } from "@/hooks/use-table";
import { useCohortList } from "@/services/cohort";
import { TableProps, TableState } from "@/types/table";
import { Filter } from "@/app/[lang]/(auth)/cohorts/components/filter";

interface CohortEntity extends ICohortBase {
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export default function CohortsTable() {
  const t = useTranslations("cohorts");
  const router = useRouter();

  const [tableState, setTableState] = useState<TableState>({
    pageIndex: 0,
    pageSize: 10,
    searchQuery: "",
  });

  const [filterValues, setFilterValues] = useState<Record<string, string>>({});

  const { data: apiResponse, isLoading } = useCohortList({
    page: tableState.pageIndex + 1,
    limit: tableState.pageSize,
    search: tableState.searchQuery,
    status: filterValues.status as CohortStatus | undefined,
  });

  const cohorts = useMemo(() => {
    if (!apiResponse) return [];
    return (apiResponse as IListCohortsResponse).data as CohortEntity[];
  }, [apiResponse]);

  const handleViewCohortDetails = useCallback(
    (cohortId: number) => {
      router.push(`/cohorts/${cohortId}`);
    },
    [router],
  );

  const handleFilterChange = useCallback((values: Record<string, string>) => {
    setFilterValues(values);
    setTableState((prev) => ({
      ...prev,
      pageIndex: 0, // Reset to first page when filters change
    }));
  }, []);

  const columns = useMemo<ColumnDef<CohortEntity>[]>(
    () => [
      {
        id: "name",
        header: t("table.name"),
        accessorKey: "name",
        cell: ({ row }) => <div className="font-medium">{row.original.name}</div>,
        enableSorting: true,
      },
      {
        id: "startDate",
        header: t("table.start_date"),
        accessorKey: "startDate",
        cell: ({ row }) => new Date(row.original.startDate).toLocaleDateString(),
        enableSorting: true,
        sortingFn: "datetime",
      },
      {
        id: "endDate",
        header: t("table.end_date"),
        accessorKey: "endDate",
        cell: ({ row }) => new Date(row.original.endDate).toLocaleDateString(),
        enableSorting: true,
        sortingFn: "datetime",
      },
      {
        id: "maxParticipants",
        header: t("table.max_participants"),
        accessorKey: "maxParticipants",
        cell: ({ row }) => row.original.maxParticipants,
        enableSorting: true,
      },
      {
        id: "currentParticipants",
        header: t("table.current_participants"),
        accessorKey: "currentParticipants",
        cell: ({ row }) => row.original.currentParticipants,
        enableSorting: true,
      },
      {
        id: "status",
        header: t("table.status.self"),
        accessorKey: "status",
        cell: ({ row }) => (
          <StatusBadge status={t(`table.status.${row.original.status.toLowerCase()}`)} />
        ),
        enableSorting: true,
      },
      {
        id: "actions",
        header: t("table.actions.header"),
        cell: ({ row }) => {
          const cohort = row.original;
          return (
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => handleViewCohortDetails(cohort.id)}
            >
              <span className="sr-only">
                {t("table.actions.view_details", { name: cohort.name })}
              </span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          );
        },
      },
    ],
    [t, handleViewCohortDetails],
  );

  const handleTableStateChange = useCallback((newState: Partial<TableState>) => {
    setTableState((prev) => ({
      ...prev,
      ...newState,
    }));
  }, []);

  const tableConfig = useMemo(
    () => ({
      onStateChange: handleTableStateChange,
      data: cohorts,
      columns,
    }),
    [cohorts, columns, handleTableStateChange],
  );

  const { selectedRows, handleRowSelection } = useTable(tableConfig);

  const dataTableProps = useMemo<TableProps<CohortEntity>>(
    () => ({
      columns,
      data: cohorts,
      isLoading,
      selectedRows,
      enableSearch: true,
      enablePagination: true,
      enableSorting: true,
      pageSize: tableState.pageSize,
      totalRows: (apiResponse as IListCohortsResponse)?.total || 0,
      noResults: t("errors.no-results"),
      onStateChange: handleTableStateChange,
      onSelectionChange: handleRowSelection,
      getRowId: (row: CohortEntity) => row.id.toString(),
      minSearchLength: 1,
      filters: <Filter onChange={handleFilterChange} value={filterValues} />,
      title: `${t("table.title")} (${(apiResponse as IListCohortsResponse)?.total || 0})`,
    }),
    [
      cohorts,
      apiResponse,
      isLoading,
      selectedRows,
      tableState.pageSize,
      filterValues,
      t,
      columns,
      handleRowSelection,
      handleTableStateChange,
      handleFilterChange,
    ],
  );

  return (
    <div className="overflow-x-auto">
      <DataTable {...dataTableProps} />
    </div>
  );
}
