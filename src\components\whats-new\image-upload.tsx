"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { Upload, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import ImageCarousel from "./image-carousel";
import { useUploadImage } from "@/services/whats-new";
import { IThumbnailImage, IMediaItem } from "@px-shared-account/hermes";
import { MediaGalleryModal } from "./media-gallery-modal";

interface ImageUploadProps {
  thumbnails: IThumbnailImage[];
  onThumbnailsChange: (thumbnails: IThumbnailImage[]) => void;
  className?: string;
}

export function ImageUpload({ thumbnails, onThumbnailsChange, className }: ImageUploadProps) {
  const t = useTranslations("whats-new.admin");
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadImage, isLoading: isUploading, error: uploadError } = useUploadImage();

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleFileUpload = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      const newThumbnails: IThumbnailImage[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        try {
          const result = await uploadImage(file);
          if (result?.url) {
            // Create a new image to get the dimensions
            const img = new Image();
            img.src = result.url;
            await new Promise((resolve) => {
              img.onload = resolve;
            });

            newThumbnails.push({
              url: result.url,
              width: img.width,
              height: img.height,
              aspectRatio: img.width / img.height,
            });
          }
        } catch (error) {
          console.error("Upload error:", error);
        }
      }

      onThumbnailsChange([...thumbnails, ...newThumbnails]);
    },
    [thumbnails, onThumbnailsChange, uploadImage],
  );

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setIsDragging(false);
      handleFileUpload(e.dataTransfer.files);
    },
    [handleFileUpload],
  );

  const handleSelectMedia = useCallback(
    (media: IMediaItem) => {
      // Create a new thumbnail from the selected media
      const newThumbnail: IThumbnailImage = {
        url: media.url,
        width: 800, // Default values since we don't have dimensions in the media item
        height: 600,
        aspectRatio: 4 / 3,
      };

      // Add the new thumbnail to the existing ones
      onThumbnailsChange([...thumbnails, newThumbnail]);
    },
    [thumbnails, onThumbnailsChange],
  );

  const handleRemoveThumbnail = useCallback(
    (indexToRemove: number) => {
      const newThumbnails = thumbnails.filter((_, index) => index !== indexToRemove);
      onThumbnailsChange(newThumbnails);
    },
    [thumbnails, onThumbnailsChange],
  );

  // Handle paste from clipboard
  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;

      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf("image") !== -1) {
          const file = items[i].getAsFile();
          if (file) {
            const fileList = new DataTransfer();
            fileList.items.add(file);
            handleFileUpload(fileList.files);
            break;
          }
        }
      }
    };

    document.addEventListener("paste", handlePaste);
    return () => {
      document.removeEventListener("paste", handlePaste);
    };
  }, [handleFileUpload]);

  return (
    <div className={cn("space-y-4", className)}>
      {thumbnails.length > 0 && (
        <div className="relative rounded-lg border p-4">
          <ImageCarousel thumbnails={thumbnails} onRemove={handleRemoveThumbnail} />
        </div>
      )}

      <div className="flex flex-wrap gap-4">
        <div
          className={cn(
            "relative flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6 transition-colors",
            isDragging && "border-primary bg-primary/5",
            "hover:border-primary hover:bg-primary/5",
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            type="file"
            className="hidden"
            accept="image/*"
            multiple
            ref={fileInputRef}
            onChange={(e) => handleFileUpload(e.target.files)}
          />
          {isUploading ? (
            <div className="flex flex-col items-center gap-2">
              <div className="border-primary h-5 w-5 animate-spin rounded-full border-t-2"></div>
              <span className="text-sm">{t("uploading")}</span>
            </div>
          ) : (
            <>
              <Upload className="mb-2 h-6 w-6 text-gray-400" />
              <p className="text-center text-sm">
                {isDragging ? t("drop-here") : t("drag-images")}
                <br />
                {t("click-to-upload")}
              </p>
              <p className="mt-2 text-center text-xs text-gray-500">{t("paste-image")}</p>
            </>
          )}
        </div>

        <MediaGalleryModal onSelect={handleSelectMedia} triggerText={t("browse-media")} />
      </div>

      {uploadError && (
        <div className="mt-2 rounded-md bg-red-50 p-2 text-sm text-red-600">
          {t("error.upload")}
        </div>
      )}
    </div>
  );
}
