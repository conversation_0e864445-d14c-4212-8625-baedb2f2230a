import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

export default function ProgressPills({ currentStep }: { currentStep: number }) {
  const t = useTranslations();
  const steps = [
    t("whats-new.admin.steps.version-info"),
    t("whats-new.admin.steps.english-content"),
    t("whats-new.admin.steps.french-content"),
    t("whats-new.admin.steps.preview"),
  ];

  return (
    <div className="mb-8 flex items-center justify-between">
      {steps.map((step, index) => (
        <div key={index} className="flex items-center">
          <div
            className={cn(
              "flex aspect-square h-8 w-8 items-center justify-center rounded-full text-sm font-medium",
              currentStep === index
                ? "bg-primary text-foreground"
                : currentStep > index
                  ? "text-foreground bg-green-500"
                  : "bg-muted text-muted-foreground",
            )}
          >
            {currentStep > index ? "✓" : index + 1}
          </div>
          <span
            className={cn(
              "ml-2 text-sm font-medium",
              currentStep === index
                ? "text-foreground"
                : currentStep > index
                  ? "text-green-500"
                  : "text-muted-foreground",
            )}
          >
            {step}
          </span>
          {index < steps.length - 1 && (
            <div
              className={cn("mx-4 h-[2px] w-24", currentStep > index ? "bg-green-500" : "bg-muted")}
            />
          )}
        </div>
      ))}
    </div>
  );
}
