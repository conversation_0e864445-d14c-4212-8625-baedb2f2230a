const API_URL = process.env.NEXT_PUBLIC_API_URL;

interface FetcherConfig {
  method?: string;
  body?: any;
}

/**
 * Creates a fetch wrapper function for server-side API calls that automatically includes authentication
 * @param tokenFn - Function that returns a Promise resolving to an authentication token or null
 * @returns An async function that takes a URL and makes an authenticated API request
 * @throws Will throw an error if the API request fails
 */
const serverFetcher =
  (tokenFn: () => Promise<string | null>) =>
  async (url: string, config: FetcherConfig = {}) => {
    try {
      const token = await tokenFn();
      if (!token) {
        throw new Error("No authentication token available");
      }

      // Detect if body is FormData to set appropriate headers
      const isFormData = config.body instanceof FormData;

      const headers: Record<string, string> = {
        Authorization: `Bearer ${token}`,
        "client-name": "px-app",
      };

      // Only set Content-Type for JSON, let browser set it for FormData
      if (!isFormData) {
        headers["Content-Type"] = "application/json";
      }

      const response = await fetch(`${API_URL}${url}`, {
        method: config.method || "GET",
        headers,
        ...(config.body && {
          body: isFormData ? config.body : JSON.stringify(config.body),
        }),
        cache: "no-store", // Ensure we always get fresh data on the server
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`#(serverFetcher): ${url}`, { error });
      return null;
    }
  };

export default serverFetcher;
