import { FilterButton } from "./FilterButton";
import { CreateButton } from "./CreateButton";

interface FilterProps {
  onCreateClick?: () => void;
}

export const Filter = ({ onCreateClick }: FilterProps) => {
  return (
    <div className="flex items-center justify-end gap-4">
      <FilterButton />
      <CreateButton onOpenModal={onCreateClick} />
    </div>
  );
};

export { FilterButton, CreateButton };
