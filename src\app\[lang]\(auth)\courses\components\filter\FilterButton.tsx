"use client";

import { useTranslations } from "next-intl";
import { useCallback, useMemo } from "react";
import { FilterBar } from "@/components/base/filter-bar";
import type { Step, FilterStep } from "@/components/base/filter-bar/types";
import { useQueryParams } from "@/hooks/use-query-params";

type FilterValues = Record<string, string>;

export const FilterButton = () => {
  const t = useTranslations();
  const { params, updateParams } = useQueryParams();

  // Filter steps configuration
  const steps = useMemo<Step[]>(
    () => [
      {
        type: "filter",
        id: "status",
        label: t("courses.filters.status.label"),
        placeholder: t("courses.filters.status.placeholder"),
        options: [
          { value: "PUBLISHED", label: t("courses.status.published") },
          { value: "DRAFT", label: t("courses.status.draft") },
          { value: "ARCHIVED", label: t("courses.status.archived") },
        ],
      },
    ],
    [t],
  );

  // Extract only filter-related params
  const filterParams = useMemo(() => {
    const filterKeys = steps
      .filter((step): step is FilterStep => step.type === "filter")
      .map((step) => step.id);

    const result = Object.fromEntries(
      Object.entries(params)
        .filter(([key]) => filterKeys.includes(key))
        .map(([key, value]) => [key, value as string]),
    );

    return result;
  }, [params, steps]);

  // Handle filter change
  const handleChange = useCallback(
    (values: FilterValues) => {
      // If we're clearing all filters, just remove filter params
      if (Object.keys(values).length === 0) {
        const nonFilterParams = Object.fromEntries(
          Object.entries(params).filter(([key]) => key === "search" || key === "page"),
        );
        updateParams({ ...nonFilterParams, page: "1" });
        return;
      }

      // Preserve non-filter params
      const filterKeys = steps
        .filter((step): step is FilterStep => step.type === "filter")
        .map((step) => step.id);

      const nonFilterParams = Object.fromEntries(
        Object.entries(params)
          .filter(([key]) => !filterKeys.includes(key) && key !== "page")
          .map(([key, value]) => [key, value as string]),
      );

      // Combine everything
      const updates = {
        ...nonFilterParams,
        ...values,
        page: "1",
      };
      updateParams(updates);
    },
    [params, steps, updateParams],
  );

  return (
    <FilterBar
      steps={steps}
      value={filterParams}
      onChange={handleChange}
      forceModal
      triggerText={t("courses.filters.button")}
    />
  );
};
