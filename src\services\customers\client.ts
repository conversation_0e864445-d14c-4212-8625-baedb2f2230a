"use client";

import { useCallback } from "react";
import { useApi } from "@/hooks/store/use-api";
import {
  CustomerAccessApiTypes,
  CUSTOMER_ACCESS_API_ENDPOINTS,
  UserAccessDetailsResponse,
} from "./core";
import { useImpersonatedEmail } from "@/hooks/use-impersonated-email";

/**
 * Custom class for handling API errors
 */
class ApiError extends Error {
  info: any;
  status: number;

  constructor(message: string, status: number, info: any = {}) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.info = info;
  }
}

/**
 * Hook to get user access details for the current user or a specified email
 * @param email Optional email override to fetch details for a specific user
 * @param config Optional API hook configuration options
 * @returns API response with user access details
 */
export function useCustomerAccessDetails(
  email?: string | null,
  config?: Parameters<typeof useApi<CustomerAccessApiTypes["getAccessDetails"]>>[1],
) {
  // Get effective email (impersonated or actual)
  const impersonatedEmail = useImpersonatedEmail();

  // Determine which email to use
  const effectiveEmail = email !== undefined ? email : impersonatedEmail;

  // Construct the API URL
  const apiConfig = useCallback(() => {
    if (!effectiveEmail) return null;
    const { url } = CUSTOMER_ACCESS_API_ENDPOINTS.getAccessDetails(effectiveEmail);
    return url;
  }, [effectiveEmail]);

  // Use the centralized API hook
  const { data, error, isLoading, isValidating, mutate } = useApi<
    CustomerAccessApiTypes["getAccessDetails"]
  >(apiConfig(), config);

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    email: effectiveEmail,
    refreshData: mutate,
  };
}

/**
 * Helper function to check if a user has access to a specific product
 * @param data User access details response
 * @param productType Product type to check (e.g., 'PXS', 'PXL')
 * @returns Boolean indicating whether the user has access to the product
 */
export function hasProductAccess(
  data: UserAccessDetailsResponse | null | undefined,
  productType: "PXS" | "PXL",
): boolean {
  if (!data) return false;

  return productType === "PXS"
    ? data.accessInfo?.isPXS || data.accessInfo?.hasBothAccess || false
    : data.accessInfo?.isPXL || data.accessInfo?.hasBothAccess || false;
}

/**
 * Hook to get details for a specific subscription
 * @param id The ID of the subscription
 * @param config Optional API hook configuration options
 * @returns API response with subscription details
 */
export function useSubscription(
  id: string | null,
  config?: Parameters<typeof useApi<CustomerAccessApiTypes["getSubscription"]>>[1],
) {
  // Construct the API URL
  const apiConfig = useCallback(() => {
    if (!id) return null;
    const { url } = CUSTOMER_ACCESS_API_ENDPOINTS.getSubscription(id);
    return url;
  }, [id]);

  // Use the centralized API hook
  const { data, error, isLoading, isValidating, mutate } = useApi<
    CustomerAccessApiTypes["getSubscription"]
  >(apiConfig(), config);

  return {
    subscription: data,
    error,
    isLoading,
    isValidating,
    refreshData: mutate,
  };
}

/**
 * Hook to get invoices for a specific subscription
 * @param id The ID of the subscription
 * @param config Optional API hook configuration options
 * @returns API response with invoice list
 */
export function useInvoices(
  id: string | null,
  config?: Parameters<typeof useApi<CustomerAccessApiTypes["getInvoices"]>>[1],
) {
  // Construct the API URL
  const apiConfig = useCallback(() => {
    if (!id) return null;
    const { url } = CUSTOMER_ACCESS_API_ENDPOINTS.getInvoices(id);
    return url;
  }, [id]);

  // Use the centralized API hook
  const { data, error, isLoading, isValidating, mutate } = useApi<
    CustomerAccessApiTypes["getInvoices"]
  >(apiConfig(), config);

  return {
    invoices: data || [],
    error,
    isLoading,
    isValidating,
    refreshData: mutate,
  };
}

/**
 * Hook to get order status for a specific subscription
 * @param id The ID of the subscription
 * @param config Optional API hook configuration options
 * @returns API response with order status
 */
export function useOrderStatus(
  id: string | null,
  config?: Parameters<typeof useApi<CustomerAccessApiTypes["getOrderStatus"]>>[1],
) {
  // Construct the API URL
  const apiConfig = useCallback(() => {
    if (!id) return null;
    const { url } = CUSTOMER_ACCESS_API_ENDPOINTS.getOrderStatus(id);
    return url;
  }, [id]);

  // Use the centralized API hook
  const { data, error, isLoading, isValidating, mutate } = useApi<
    CustomerAccessApiTypes["getOrderStatus"]
  >(apiConfig(), config);

  return {
    orderStatus: data?.orderStatus,
    error,
    isLoading,
    isValidating,
    refreshData: mutate,
  };
}

/**
 * Hook to cancel a subscription
 * @param id The ID of the subscription
 * @returns A function to trigger the cancellation
 */
export function useCancelSubscription(id: string | null) {
  // Construct the API URL
  const apiConfig = useCallback(() => {
    if (!id) return null;
    const { url, method } = CUSTOMER_ACCESS_API_ENDPOINTS.cancelSubscription(id);
    return { url, method };
  }, [id]);

  // Use the centralized API hook
  const { trigger, error, isLoading } = useApi<CustomerAccessApiTypes["cancelSubscription"]>(
    apiConfig()?.url || null,
    { method: apiConfig()?.method as "POST" },
  );

  return {
    cancelSubscription: trigger,
    error,
    isLoading,
  };
}
