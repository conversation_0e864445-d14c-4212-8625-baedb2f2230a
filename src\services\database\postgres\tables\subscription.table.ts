import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  DeleteDateColumn,
  JoinTable,
  ManyToMany,
  Index,
} from 'typeorm';
import {
  Addon,
  ChargedItem,
  ContractTerm,
  Coupon,
  Discount,
  ItemTier,
  ReferralInfo,
  ShippingAddress,
  SubscriptionItem,
} from 'chargebee-typescript/lib/resources/subscription';
import {
  SubscriptionStatus,
  TrialEndAction,
  SubscriptionCancelReason,
  ChargeBeeChannel,
  ChargeBeeDurationPeriodUnit,
  SubscriptionOrderStatus,
} from '@enums';
import { Subscription } from '@entities';
import { ProductPlanPriceTable } from './product-plan-price.table';

@Entity({
  name: 'subscriptions',
})
export class SubscriptionTable implements Subscription {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', unique: true })
  chargebeeId: string;

  @Column({ type: 'varchar' })
  currencyCode: string;

  @Column({ type: 'integer', nullable: true })
  billingPeriod?: number;

  @Column({ type: 'enum', enum: ChargeBeeDurationPeriodUnit, nullable: true })
  billingPeriodUnit?: ChargeBeeDurationPeriodUnit;

  @Column({ type: 'integer', nullable: true })
  initialBillingCycles?: number;

  @Column({ type: 'integer', nullable: true })
  totalBillingCycles?: number;

  @Column({ type: 'integer', nullable: true })
  remainingBillingCycles?: number;

  @Column({ type: 'varchar', nullable: true })
  poNumber?: string;

  @Column({ type: 'varchar', nullable: true })
  planId?: string;

  @Column({ type: 'integer', nullable: true })
  planQuantity?: number;

  @Column({ type: 'varchar', nullable: true })
  planQuantityInDecimal?: string;

  @Column({ type: 'varchar', nullable: true })
  crmId?: string;

  @Column({ type: 'varchar', nullable: true })
  planUnitPriceInDecimal?: string;

  @Column({ type: 'varchar' })
  customerId: string;

  @Column({ type: 'varchar' })
  customerName: string;

  @Index('idx_subscriptions_customer_email')
  @Column({ type: 'varchar' })
  customerEmail: string;

  @Column({ type: 'enum', enum: SubscriptionStatus })
  status: SubscriptionStatus;

  @Column({ type: 'enum', enum: SubscriptionOrderStatus, nullable: true })
  orderStatus?: SubscriptionOrderStatus;

  @Column({ type: 'enum', enum: TrialEndAction, nullable: true })
  trialEndAction?: TrialEndAction;

  @Column({ type: 'integer', nullable: true })
  contractTermBillingCycleOnRenewal?: number;

  @Column({ type: 'boolean', nullable: true })
  overrideRelationship?: boolean;

  @Column({ type: 'enum', enum: SubscriptionCancelReason, nullable: true })
  cancelReason?: SubscriptionCancelReason;

  @Column({ type: 'varchar', nullable: true })
  createdFromIp?: string;

  @Column({ type: 'bigint', nullable: true })
  resourceVersion?: number;

  @Column({ type: 'boolean', nullable: true })
  hasScheduledAdvanceInvoices?: boolean;

  @Column({ type: 'boolean', nullable: true })
  hasScheduledChanges?: boolean;

  @Column({ type: 'varchar', nullable: true })
  paymentSourceId?: string;

  @Column({ type: 'varchar', nullable: true })
  planFreeQuantityInDecimal?: string;

  @Column({ type: 'varchar', nullable: true })
  planAmountInDecimal?: string;

  @Column({ type: 'enum', enum: ChargeBeeChannel, nullable: true })
  channel?: ChargeBeeChannel;

  @Column({ type: 'integer', nullable: true })
  netTermDays?: number;

  @Column({ type: 'varchar', nullable: true })
  activeId?: string;

  @Column({ type: 'integer', nullable: true })
  dueInvoicesCount?: number;

  @Column({ type: 'integer', nullable: true })
  totalDues?: number;

  @Column({ type: 'integer', nullable: true })
  amountPaid?: number;

  @Column({ type: 'integer', nullable: true })
  amountRemaining?: number;

  @Column({ type: 'integer', nullable: true })
  amountRefunded?: number;

  @Column({ type: 'integer', nullable: true })
  totalOrderAmount?: number;

  @Column({ type: 'integer', nullable: true })
  mrr?: number;

  @Column({ type: 'integer', nullable: true })
  exchangeRate?: number;

  @Column({ type: 'varchar', nullable: true })
  baseCurrencyCode?: string;

  @Column({ type: 'varchar', nullable: true })
  invoiceNotes?: string;

  @Column({ type: 'varchar', nullable: true })
  autoCollection?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: 'boolean', nullable: true })
  deleted?: boolean;

  @Column({ type: 'varchar', nullable: true })
  cancelReasonCode?: string;

  @Column({ type: 'integer', nullable: true })
  freePeriod?: number;

  @Column({ type: 'enum', enum: ChargeBeeDurationPeriodUnit, nullable: true })
  freePeriodUnit?: ChargeBeeDurationPeriodUnit;

  @Column({ type: 'boolean', nullable: true })
  createPendingInvoices?: boolean;

  @Column({ type: 'boolean', nullable: true })
  autoCloseInvoices?: boolean;

  @Column({ type: 'varchar' })
  businessEntityId: string;

  @Column({ type: 'jsonb', nullable: true })
  subscriptionItems?: SubscriptionItem[];

  @Column({ type: 'jsonb', nullable: true })
  itemTiers?: ItemTier[];

  @Column({ type: 'jsonb', nullable: true })
  chargedItems?: ChargedItem[];

  @Column({ type: 'jsonb', nullable: true })
  coupons?: Coupon[];

  @Column({ type: 'jsonb', nullable: true })
  shippingAddress?: ShippingAddress;

  @Column({ type: 'jsonb', nullable: true })
  referralInfo?: ReferralInfo;

  @Column({ type: 'jsonb', nullable: true })
  contractTerm?: ContractTerm;

  @Column({ type: 'jsonb', nullable: true })
  customFields?: any;

  @Column({ type: 'jsonb', nullable: true })
  discounts?: Discount[];

  @Column({ type: 'jsonb', nullable: true })
  addons?: Addon[];

  @Column({ type: 'integer', nullable: true })
  startedAt: number;

  @Column({ type: 'integer', nullable: true })
  activatedAt?: number;

  @Column({ type: 'integer', nullable: true })
  startDate?: number;

  @Column({ type: 'integer', nullable: true })
  pauseDate?: number;

  @Column({ type: 'integer', nullable: true })
  resumeDate?: number;

  @Column({ type: 'integer', nullable: true })
  currentTermStart?: number;

  @Column({ type: 'integer', nullable: true })
  currentTermEnd?: number;

  @Column({ type: 'integer', nullable: true })
  changesScheduledAt?: number;

  @Column({ type: 'integer', nullable: true })
  nextBillingAt?: number;

  @Column({ type: 'integer', nullable: true })
  cancelScheduleCreatedAt?: number;

  @Column({ type: 'integer', nullable: true })
  cancelledAt?: number;

  @Column({ type: 'integer', nullable: true })
  trialStart?: number;

  @Column({ type: 'integer', nullable: true })
  trialEnd?: number;

  @Column({ type: 'boolean', nullable: true })
  isForever?: boolean;

  @Column({ type: 'integer', nullable: true })
  dueSince?: number;

  @ManyToMany(() => ProductPlanPriceTable, { nullable: true })
  @JoinTable({ name: 'Subscription_ProductPlanPrice' })
  attachedPrices?: ProductPlanPriceTable[];

  @Column({ type: 'integer' })
  createdAt: number;

  @Column({ type: 'integer', nullable: true })
  updatedAt?: number;

  @DeleteDateColumn()
  deletedAt?: Date;
}
