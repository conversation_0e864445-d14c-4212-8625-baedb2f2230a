"use client";

import { useTranslations } from "next-intl";
import { useEffect, useMemo, useState, useRef } from "react";
import { z } from "zod";

import { useUpdateCohort } from "@/services/cohort";
import { useToast } from "@/hooks/use-toast";
import { ModalConfig, MultiStepFormModal } from "../base/multi-step-form-modal";
import { ICohortBase } from "@px-shared-account/hermes";

type EditCohortModalProps = {
  cohort: ICohortBase;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onComplete?: (data: any) => Promise<void>;
};

export const EditCohortModal = ({
  cohort,
  isOpen,
  onOpenChange,
  onComplete,
}: EditCohortModalProps) => {
  const t = useTranslations("cohorts");
  const { toast } = useToast();
  const { update } = useUpdateCohort(cohort.id);

  // Ensure dates are properly formatted as Date objects
  const startDate = useMemo(() => {
    // Make sure we have a valid Date object
    if (!cohort.startDate) return new Date();
    const date = new Date(cohort.startDate);
    return isNaN(date.getTime()) ? new Date() : date;
  }, [cohort.startDate]);

  const endDate = useMemo(() => {
    // Make sure we have a valid Date object
    if (!cohort.endDate) return new Date();
    const date = new Date(cohort.endDate);
    return isNaN(date.getTime()) ? new Date() : date;
  }, [cohort.endDate]);

  const config: ModalConfig = useMemo(
    () => ({
      key: "edit-cohort",
      title: t("edit"),
      confirmClose: true,
      confirmReset: true,
      initialData: {
        name: cohort.name,
        description: cohort.description,
        maxParticipants: cohort.maxParticipants,
        startDate: startDate,
        endDate: endDate,
        courseId: cohort.course?.id, // Use course.id instead of courseId
        status: cohort.status,
      },
      submitButtons: [
        {
          label: t("edit-submit"),
          variant: "default",
          className: "bg-burgundy hover:bg-burgundy/90",
          isPrimary: true,
        },
      ],
      steps: [
        {
          id: "details",
          title: t("name"),
          fields: [
            {
              id: "name",
              type: "text",
              label: t("name"),
              placeholder: t("name_description"),
              required: true,
              validation: z.string().min(3, { message: t("name_validation") }),
            },
            {
              id: "description",
              type: "text",
              label: t("description"),
              required: true,
              placeholder: t("description_description"),
              validation: z.string().min(3, { message: t("description_validation") }),
            },
            {
              id: "maxParticipants",
              type: "number",
              label: t("maxParticipants"),
              placeholder: t("maxParticipants_description"),
              required: true,
              validation: z.number().min(1, { message: t("maxParticipants_validation") }),
            },
            {
              id: "startDate",
              type: "date",
              label: t("startDate"),
              required: true,
              placeholder: t("startDate_description"),
            },
            {
              id: "endDate",
              type: "date",
              label: t("endDate"),
              required: true,
              placeholder: t("endDate_description"),
            },
          ],
        },
      ],
    }),
    [t, cohort, startDate, endDate],
  );

  const handleComplete = async (formData: any) => {
    try {
      // Ensure dates are valid Date objects
      const startDate =
        formData.startDate instanceof Date ? formData.startDate : new Date(formData.startDate);
      const endDate =
        formData.endDate instanceof Date ? formData.endDate : new Date(formData.endDate);

      // Format the cohort data according to the API spec
      const cohortData = {
        name: formData.name,
        description: formData.description,
        maxParticipants: formData.maxParticipants,
        startDate: startDate,
        endDate: endDate,
        courseId: cohort.course?.id, // Use course.id instead of courseId
        status: formData.status || cohort.status,
      };

      // Update the cohort using the API
      const updatedCohort = await update(cohort.id, cohortData);

      toast({
        title: t("updated"),
        description: t("updated-description"),
        variant: "default",
      });

      // Call the onComplete callback if provided
      // This will trigger router.refresh() in the parent component
      if (onComplete) {
        await onComplete(updatedCohort);
      }
    } catch (error) {
      toast({
        title: t("error"),
        description: error instanceof Error ? error.message : t("error-description"),
        variant: "destructive",
      });
      throw error;
    }
  };

  // Create a ref to track if the form has been initialized
  const formInitializedRef = useRef(false);

  // Add a render counter to track renders
  const renderCountRef = useRef(0);
  renderCountRef.current += 1;

  // Create a stable key for the modal
  const modalKey = useMemo(() => `edit-cohort-${cohort.id}`, [cohort.id]);

  // Single useEffect to handle modal open/close
  useEffect(() => {
    if (isOpen) {
      // Only initialize once per modal open
      if (!formInitializedRef.current) {
        formInitializedRef.current = true;
      }

      // Cleanup when modal closes
      return () => {
        formInitializedRef.current = false;
      };
    }
  }, [isOpen, cohort.id]);

  return (
    <MultiStepFormModal
      key={modalKey}
      config={config}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      events={{
        onComplete: handleComplete,
      }}
    />
  );
};

export default EditCohortModal;
