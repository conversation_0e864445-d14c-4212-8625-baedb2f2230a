import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CronLogType, CronLogStatus } from '@enums';
import { CronLogResult } from '@types';
import { CronLog } from '@entities';

@Entity({
  name: 'cronLogs',
})
export class CronLogTable implements CronLog {
  @PrimaryColumn({
    type: 'varchar',
    unique: true,
  })
  id: string;

  @Column({
    type: 'enum',
    enum: CronLogType,
  })
  type: CronLogType;

  @Column({
    type: 'enum',
    enum: CronLogStatus,
  })
  status: CronLogStatus;

  @Column({
    type: 'json',
  })
  result: CronLogResult;

  @Column({
    type: 'json',
    nullable: true,
  })
  error?: any;

  @Column({
    type: 'timestamp with time zone',
  })
  startTime: Date;

  @Column({
    type: 'timestamp with time zone',
  })
  endTime: Date;

  @Column({
    type: 'integer',
    default: 0,
    nullable: true,
  })
  retryCount: number;

  @Column({
    type: 'boolean',
    default: false,
  })
  slackNotified: boolean;

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
