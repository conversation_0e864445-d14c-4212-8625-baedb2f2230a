# Shared Types

This package contains shared TypeScript types for our multi-project system.

## Installation

For local development, you can reference this package directly from the file system in your `package.json`:

```json
{
  "dependencies": {
    "@px-shared-account/hermes": "^2.0.0"
  },
  "dev-dependencies": {
    "@px-shared-account/hermes": "file:../path/to/shared-types"
  }
}
```

For production, install from npm:

```bash
npm install @px-shared-account/hermes
```

## Usage

```typescript
import { User, UserRole, PaginationParams } from "@px-shared-account/hermes";

// Use the types in your code
const user: User = {
  id: "1",
  email: "<EMAIL>",
  username: "user1",
  role: UserRole.USER,
  createdAt: new Date(),
  updatedAt: new Date(),
};
```

## Development

1. Make changes to the types in the `src` directory
2. Run `npm run build` to compile
3. Commit changes and push

## Publishing

1. Go to the GitHub Actions tab in your repository
2. Select the "Publish Package to NPM" workflow
3. Click "Run workflow"
4. Select the release type:
   - `patch`: Bug fixes (0.0.x)
   - `minor`: New features (0.x.0)
   - `major`: Breaking changes (x.0.0)
   - `prepatch`, `preminor`, `premajor`: Pre-release versions
   - `prerelease`: Increment pre-release version
5. Click "Run workflow"

The action will automatically:

- Bump the version according to the release type
- Create a git tag
- Create a GitHub release
- Publish to npm
