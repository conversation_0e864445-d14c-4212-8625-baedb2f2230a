import { ChatListItem } from "./ChatList";
import { Message } from "./ChatConversation";

export const currentUserId = "user-1";

export const sampleConversations: ChatListItem[] = [
  {
    id: "conv-1",
    title: "<PERSON>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Alice",
    lastMessage: "Hey, how are you?",
    timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    unreadCount: 2,
    online: true,
  },
  {
    id: "conv-2",
    title: "<PERSON>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Bob",
    lastMessage: "See you tomorrow!",
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    online: false,
  },
  {
    id: "conv-3",
    title: "<PERSON>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Carol",
    lastMessage: "The meeting is scheduled.",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    unreadCount: 1,
    online: true,
  },
];

export const sampleMessages: Record<string, Message[]> = {
  "conv-1": [
    {
      id: "msg-1",
      content: "Hey there!",
      timestamp: new Date(Date.now() - 1000 * 60 * 10),
      sender: {
        id: "user-2",
        name: "Alice Johnson",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Alice",
      },
      status: "read",
    },
    {
      id: "msg-2",
      content: "Hi Alice! How are you?",
      timestamp: new Date(Date.now() - 1000 * 60 * 8),
      sender: {
        id: "user-1",
        name: "You",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=You",
      },
      status: "read",
    },
    {
      id: "msg-3",
      content: "I'm doing great! Just wanted to check in about the project.",
      timestamp: new Date(Date.now() - 1000 * 60 * 5),
      sender: {
        id: "user-2",
        name: "Alice Johnson",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Alice",
      },
    },
  ],
  "conv-2": [
    {
      id: "msg-4",
      content: "Are we still meeting tomorrow?",
      timestamp: new Date(Date.now() - 1000 * 60 * 45),
      sender: {
        id: "user-3",
        name: "Bob Smith",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Bob",
      },
      status: "read",
    },
    {
      id: "msg-5",
      content: "Yes, 2 PM works for me!",
      timestamp: new Date(Date.now() - 1000 * 60 * 30),
      sender: {
        id: "user-1",
        name: "You",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=You",
      },
      status: "delivered",
    },
  ],
};
