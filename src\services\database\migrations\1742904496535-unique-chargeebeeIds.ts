import { MigrationInterface, QueryRunner } from 'typeorm';

export class UniqueChargeebeeIds1742904496535 implements MigrationInterface {
  name = 'UniqueChargeebeeIds1742904496535';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD CONSTRAINT "UQ_8c7cc2340e9ea0fc5a246e63749" UNIQUE ("code")`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscriptions" ADD CONSTRAINT "UQ_50a0b3f9251bc279c99934ec73c" UNIQUE ("chargebeeId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "transactions" ADD CONSTRAINT "UQ_586f2c3d9e281b39b1672f0d5dd" UNIQUE ("chargebeeId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" ADD CONSTRAINT "UQ_ee312dae7e654eafae1a88dfdc8" UNIQUE ("chargebeeId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "creditNotes" ADD CONSTRAINT "UQ_e28500f2ebfc7a8bd0b18eba40a" UNIQUE ("chargebeeId")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "creditNotes" DROP CONSTRAINT "UQ_e28500f2ebfc7a8bd0b18eba40a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" DROP CONSTRAINT "UQ_ee312dae7e654eafae1a88dfdc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "transactions" DROP CONSTRAINT "UQ_586f2c3d9e281b39b1672f0d5dd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscriptions" DROP CONSTRAINT "UQ_50a0b3f9251bc279c99934ec73c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP CONSTRAINT "UQ_8c7cc2340e9ea0fc5a246e63749"`,
    );
  }
}
