"use client";

import { useMemo } from "react";
import { format } from "date-fns";

import useCalendarStore from "@/hooks/store/calendar";
import computeViewLayout from "@/lib/calendar/computeViewLayout";
import { cn } from "@/lib/utils";

type CalendarHeaderProps = {
  selectedWeekIndex: number;
  selectedWeek: Date[];
};

export default function CalendarHeader({ selectedWeek, selectedWeekIndex }: CalendarHeaderProps) {
  const { layout, selectedDate } = useCalendarStore();

  return useMemo(() => {
    const viewLayout = computeViewLayout(layout);

    return (
      <>
        <div
          data-group="header"
          className="relative flex h-[100px] w-full items-stretch justify-center"
        >
          <div className="mt-[-8px] w-[40px] flex-none">
            <div className="relative h-[60px] pr-[8px] text-right text-[12px] text-[#70757a]" />
          </div>
          <div className="relative flex flex-grow items-start justify-center">
            <div className="relative h-[100px] w-full">
              <div className="relative flex h-full min-w-full flex-none flex-row items-start justify-center overflow-hidden align-top">
                <div className="mt-[85px] h-[15px] pl-[8px]" />
                {viewLayout.map((index) => {
                  const day = layout === "week" ? selectedWeek[index] : selectedDate;
                  const isToday = format(day, "ddMMyyyy") === format(new Date(), "ddMMyyyy");
                  return (
                    <div
                      id={`headerDay${index}`}
                      key={index}
                      className={cn("relative mt-[85px] h-[15px] flex-1")}
                    >
                      <div className="absolute left-[-1px] top-[-75px] flex h-[20px] w-full items-center justify-center">
                        <span className="text-sm font-normal text-[#70757a]">
                          {format(day, "EEE")}
                        </span>
                      </div>
                      <div className="absolute left-[-1px] top-[-55px] flex h-[45px] w-full items-center justify-center text-[24px] text-[#70757a]">
                        <span
                          className={cn(
                            "line-height-[45px] flex h-[45px] w-[45px] items-center justify-center rounded-full text-center text-xs font-normal",
                            isToday ? "bg-burgundy text-white" : "bg-white text-black",
                          )}
                        >
                          {format(day, "d")}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }, [selectedDate, layout, selectedWeek, selectedWeekIndex]);
}
