"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { anton } from "@/lib/fonts/anton";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { motion, AnimatePresence } from "framer-motion";

interface FilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const filterVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
  exit: {
    opacity: 0,
    y: 20,
    transition: { duration: 0.2 },
  },
};

export function FilterDialog({ open, onOpenChange }: FilterDialogProps) {
  const t = useTranslations("triad");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-background border-[#2C2C2C] p-0 text-white">
        <AnimatePresence mode="wait">
          <motion.div
            variants={filterVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="p-6"
          >
            <DialogHeader>
              <DialogTitle className={cn("text-center text-2xl text-white", anton.className)}>
                {t("filter-bar.title")}
              </DialogTitle>
              <DialogDescription>{t("filter.description")}</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="h-10 animate-pulse rounded-lg bg-[#2C2C2C]" />
              <div className="h-10 animate-pulse rounded-lg bg-[#2C2C2C]" />
              <div className="h-10 animate-pulse rounded-lg bg-[#2C2C2C]" />
              <div className="h-10 animate-pulse rounded-lg bg-[#2C2C2C]" />
            </div>
          </motion.div>
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
