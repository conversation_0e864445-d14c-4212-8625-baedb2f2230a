"use client";

import React from "react";

const lineDivisor = `
.line-divisor:after {
    content: "";
    border-bottom: 1px solid #dadce0;
    position: absolute;
    width: 100%;
    margin-top: -1px;
    z-index: 3;
    pointer-events: none;
}
`;

export default function LineDivisor() {
  return (
    <div className="border-t border-gray-300">
      <style>{lineDivisor}</style>
      {Array.from(Array(24).keys()).map((_: any, ix: number) => (
        <div
          key={`time-line-divisor-${ix}`}
          className="line-divisor relative h-[60px]"
          data-group="time-line"
        />
      ))}
    </div>
  );
}
