import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ProductCatalogEntityStatus } from '@enums';

export class CreateProductLineDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  description: string;
}

export class UpdateProductLineDTO extends PartialType(CreateProductLineDTO) {
  @ApiProperty({ enum: ProductCatalogEntityStatus })
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ProductCatalogEntityStatus)
  status?: ProductCatalogEntityStatus;
}
