"use client";

import React from "react";
import { cn } from "@/lib/utils";

export interface DotIndicatorProps {
  count: number;
  current: number;
  onDotClick: (index: number) => void;
  className?: string;
}

export const DotIndicator: React.FC<DotIndicatorProps> = ({
  count,
  current,
  onDotClick,
  className,
}) => {
  if (count <= 1) return null;
  return (
    <div className={cn("flex justify-center gap-2 py-2", className)}>
      {Array.from({ length: count }).map((_, i) => (
        <button
          key={i}
          onClick={() => onDotClick(i)}
          className={cn(
            "h-2 w-2 rounded-full transition-all duration-150 ease-in-out",
            current === i + 1 ? "bg-primary w-5" : "bg-primary/40 hover:bg-primary/70",
          )}
          aria-label={`Go to slide ${i + 1}`}
        />
      ))}
    </div>
  );
};

DotIndicator.displayName = "DotIndicator";
