"use client";

import { IUserBase } from "@px-shared-account/hermes";
import { format } from "date-fns";
import { useTranslations } from "next-intl";

import Score from "../../components/Score";
import { permissionsBySection } from "@/lib/permissions/shared";

type InformationProps = {
  user: IUserBase;
};

export default function Information({ user }: InformationProps) {
  const t = useTranslations("gestion.users");
  const tPermissions = useTranslations("gestion.permissions");

  if (!user) return null;

  return (
    <div className="space-y-4">
      <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-2">
        <div>
          <h1 className="text-xl">{t("information.title")}</h1>
          <div className="bg-secondary mt-4 rounded-3xl p-8">
            <div className="flex flex-col gap-4">
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">{t("information.role")}</span>
                <span className="text-foreground text-xs font-normal">{user.role.name}</span>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">{t("information.profile")}</span>
                <span className="text-foreground text-xs font-normal">---</span>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">{t("information.job")}</span>
                <span className="text-foreground text-xs font-normal">---</span>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">
                  {t("information.description")}
                </span>
                <span className="text-foreground text-xs font-normal">---</span>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">
                  {t("information.date-creation")}
                </span>
                <span className="text-foreground text-xs font-normal">
                  {format(user.createdAt, "dd/MM/yyyy HH:mm")}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div>
          <h1 className="text-xl">{t("activities.title")}</h1>
          <div className="bg-secondary mt-4 rounded-3xl p-8">
            <div className="flex flex-col gap-4">
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">{t("activities.score-nps")}</span>
                <Score score={Math.floor(Math.random() * 10)} />
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">
                  {t("activities.cohorts-management")}
                </span>
                <span className="text-foreground text-xs font-normal">---</span>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">
                  {t("activities.last-connection")}
                </span>
                <span className="text-foreground text-xs font-normal">---</span>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-muted-foreground text-xs">
                  {t("activities.last-courses")}
                </span>
                <div className="flex flex-wrap gap-2">
                  <div className="text-muted-foreground rounded-md bg-gray-100 p-1 text-xs uppercase">
                    ---
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <h1 className="text-xl">{t("authorization.title")}</h1>
          <div className="bg-secondary mt-4 rounded-3xl p-8">
            <div className="flex flex-col gap-4">
              {Object.keys(permissionsBySection).map((section) => (
                <div key={section} className="flex flex-col gap-2">
                  <span className="text-muted-foreground text-xs">{tPermissions(section)}</span>
                  <ul className="list-disc pl-2">
                    {Object.keys(
                      permissionsBySection[section as keyof typeof permissionsBySection],
                    ).map(
                      (permission: string) =>
                        user.role.permissions.includes(permission) && (
                          <li key={permission} className="text-foreground text-xs font-normal">
                            {tPermissions(permission)}
                          </li>
                        ),
                    )}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
