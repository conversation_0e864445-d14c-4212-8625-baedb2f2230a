"use client";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useMultiStepModal } from "@/hooks/use-multistep-modal";
import { cn } from "@/lib/utils";

export type Step<T = any> = {
  title: string;
  description?: string;
  component: React.ComponentType<{
    data: T;
    onDataChange: (data: Partial<T>) => void;
  }>;
  onNext?: (data: T) => Promise<boolean | void> | boolean | void | any;
};

interface MultiStepModalProps<T> {
  steps: Step<T>[];
  initialData: T;
  isOpen: boolean;
  onClose: () => void;
  onComplete: (data: T) => void;
  className?: string;
}

export function MultiStepModal<T>({
  steps,
  initialData,
  isOpen,
  onClose,
  onComplete,
  className,
}: MultiStepModalProps<T>) {
  const { currentStep, isLoading, modalData, updateData, nextStep, prevStep, resetModal } =
    useMultiStepModal<T>({
      steps,
      initialData,
      onComplete,
    });

  const CurrentStepComponent = steps[currentStep]?.component;

  const handleClose = () => {
    resetModal();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={cn("max-w-2xl backdrop-blur-3xl", className)}>
        <DialogTitle>{steps[currentStep]?.title}</DialogTitle>

        <div className="mb-8">
          <div className="mb-4 flex items-center justify-between">
            <div>
              {steps[currentStep]?.description && (
                <p className="text-sm text-muted-foreground">{steps[currentStep].description}</p>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              Step {currentStep + 1} of {steps.length}
            </div>
          </div>
          <div className="h-2 w-full rounded-full bg-secondary">
            <div
              className="h-2 rounded-full bg-primary transition-all duration-300"
              style={{
                width: `${((currentStep + 1) / steps.length) * 100}%`,
              }}
            />
          </div>
        </div>

        <div className="min-h-[200px]">
          {CurrentStepComponent && (
            <CurrentStepComponent data={modalData} onDataChange={updateData} />
          )}
        </div>

        <div className="mt-8 flex justify-between">
          <Button variant="outline" onClick={prevStep} disabled={currentStep === 0 || isLoading}>
            Previous
          </Button>
          <Button onClick={nextStep} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : currentStep === steps.length - 1 ? (
              "Complete"
            ) : (
              "Next"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
