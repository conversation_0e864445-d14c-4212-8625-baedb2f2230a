"use client";

/**
 * A toolbar component for the DataTable that provides search and selection management
 * Features:
 * - Displays total number of items
 * - Search input with clear button
 * - Selection count and clear selection button
 * - Smooth animations for state changes
 */

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Search } from "lucide-react";
import { useTranslations } from "next-intl";
import { motion, AnimatePresence } from "framer-motion";

interface TableToolbarProps<T> {
  /** Array of currently selected rows */
  selectedRows: T[];
  /** Total number of rows in the table */
  totalRows: number;
  /** Current search input value */
  searchValue: string;
  /** Singular label for the data type (e.g., "student") */
  dataLabel: string;
  /** Plural label for the data type (e.g., "students") */
  dataLabelPlural: string;
  /** Callback when search input changes */
  onSearch: (value: string) => void;
  /** Callback when search is cleared */
  onClearSearch: () => void;
  /** Callback when selection is cleared */
  onClearSelection: () => void;
}

export function TableToolbar<T>({
  selectedRows,
  totalRows,
  searchValue,
  dataLabel,
  dataLabelPlural,
  onSearch,
  onClearSearch,
  onClearSelection,
}: TableToolbarProps<T>) {
  const selectedCount = selectedRows.length;
  const t = useTranslations("components.data-table");

  return (
    <div className="flex h-auto flex-wrap items-center justify-between gap-2">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex min-w-fit flex-1 items-center gap-4"
      >
        <span className="text-xl font-semibold">
          {totalRows} {totalRows === 1 ? dataLabel : dataLabelPlural}
        </span>
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={`${t("search")} ${dataLabelPlural}...`}
            value={searchValue}
            onChange={(e) => onSearch(e.target.value)}
            className="truncate pl-8"
          />
          <AnimatePresence>
            {searchValue && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.15 }}
              >
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-2 hover:bg-transparent"
                  onClick={onClearSearch}
                >
                  <X className="h-4 w-4" />
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      <AnimatePresence>
        {selectedCount > 0 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            className="flex flex-1 items-center justify-end gap-4"
          >
            <span className="text-sm">
              {selectedCount} {selectedCount === 1 ? dataLabel : dataLabelPlural} {t("selected")}
            </span>
            <Button
              variant="ghost"
              onClick={onClearSelection}
              className="h-8 px-3 text-sm font-medium"
            >
              {t("clear")}
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
