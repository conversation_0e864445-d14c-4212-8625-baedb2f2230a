import { Injectable, Logger } from '@nestjs/common';
import { PgBoss, ProcessQueue } from '@nestjs-enhanced/pg-boss';
import axios, { AxiosRequestConfig } from 'axios';
import {
  ScheduleHttpRequestParams,
  ScheduledJobResponse,
  HttpRequestJob,
} from './http-schedule.types';
import { ListJobsDto } from '@dtos';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class HttpScheduleService {
  private readonly logger = new Logger(HttpScheduleService.name);
  private readonly QUEUE_NAME = 'http-request-queue';

  constructor(
    private readonly pgBoss: PgBoss,
    @InjectDataSource() private readonly dataSource: DataSource,
  ) {}

  /**
   * List all scheduled HTTP requests from the queue
   */
  async listScheduledJobs(options: ListJobsDto) {
    const { status, limit = 20, page = 1, sort = 'desc' } = options;
    const offset = (page - 1) * limit;

    let baseQuery = `FROM pgboss.job WHERE name = $1`;
    const queryParams: any[] = [this.QUEUE_NAME];
    let paramIndex = 2;

    if (status) {
      baseQuery += ` AND state = $${paramIndex++}`;
      queryParams.push(status);
    }

    // Total count query
    const countQuery = `SELECT COUNT(*) ${baseQuery}`;

    // Data query
    let dataQuery = `SELECT id, name as queue, data, state, createdon, startedon, completedon ${baseQuery}`;
    dataQuery += ` ORDER BY createdon ${sort === 'desc' ? 'DESC' : 'ASC'}`;
    dataQuery += ` LIMIT $${paramIndex++}`;
    const dataQueryParams = [...queryParams, limit];
    dataQuery += ` OFFSET $${paramIndex++}`;
    dataQueryParams.push(offset);

    try {
      const totalResult = await this.dataSource.query(countQuery, queryParams);
      const total = parseInt(totalResult[0].count, 10);
      const rows = await this.dataSource.query(dataQuery, dataQueryParams);

      return {
        data: rows,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error('Failed to list scheduled jobs:', error);
      throw error;
    }
  }

  /**
   * Schedule an HTTP request to be executed at a specific time
   */
  async scheduleHttpRequest(
    params: ScheduleHttpRequestParams,
  ): Promise<ScheduledJobResponse> {
    try {
      const scheduledAt = new Date(params.at);

      // Create the job payload
      const jobData: HttpRequestJob = {
        id: this.generateJobId(),
        url: params.url,
        method: params.method,
        headers: params.headers,
        body: params.body,
        query: params.query,
        scheduledAt,
      };

      // Schedule the job
      const jobId = await this.pgBoss.send(this.QUEUE_NAME, jobData, {
        startAfter: scheduledAt,
        retryLimit: 3,
        retryDelay: 60000, // 1 minute
        retryBackoff: true,
      });

      this.logger.log(
        `Scheduled HTTP ${params.method} request to ${params.url} at ${scheduledAt.toISOString()} with job ID: ${jobId}`,
      );

      return {
        jobId,
        scheduledAt,
        url: params.url,
        method: params.method,
      };
    } catch (error) {
      this.logger.error('Failed to schedule HTTP request:', error);
      throw error;
    }
  }

  /**
   * Cancel a scheduled HTTP request
   */
  async cancelScheduledRequest(jobId: string): Promise<boolean> {
    try {
      await this.pgBoss.cancel(jobId);
      this.logger.log(`Successfully cancelled job: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error cancelling job ${jobId}:`, error);
      return false;
    }
  }

  /**
   * Process queued HTTP requests
   */
  @ProcessQueue('http-request-queue')
  async processHttpRequest(job: { data: HttpRequestJob }): Promise<{
    success: boolean;
    status: number;
    statusText: string;
    headers: any;
    data: any;
  }> {
    const { data: requestData } = job;

    this.logger.log(
      `Processing HTTP ${requestData.method} request to ${requestData.url}`,
    );

    try {
      // Prepare axios config
      const axiosConfig: AxiosRequestConfig = {
        method: requestData.method.toLowerCase() as any,
        url: requestData.url,
        headers: requestData.headers,
        timeout: 30000, // 30 seconds timeout
      };

      // Add query parameters if present
      if (requestData.query) {
        axiosConfig.params = requestData.query;
      }

      // Add body for non-GET requests
      if (requestData.body && requestData.method.toUpperCase() !== 'GET') {
        axiosConfig.data = requestData.body;
      }

      // Execute the HTTP request
      const response = await axios(axiosConfig);

      this.logger.log(
        `Successfully executed HTTP ${requestData.method} request to ${requestData.url}. Status: ${response.status}`,
      );

      return {
        success: true,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
      };
    } catch (error) {
      this.logger.error(
        `Failed to execute HTTP ${requestData.method} request to ${requestData.url}:`,
        error.response?.data || error.message,
      );

      // Re-throw to trigger retry mechanism
      throw error;
    }
  }

  /**
   * Generate a unique job ID
   */
  private generateJobId(): string {
    return `http-req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
