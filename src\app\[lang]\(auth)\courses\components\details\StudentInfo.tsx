"use client";

import { memo } from "react";
import { Row } from "@tanstack/react-table";
import { ICourseStudent } from "@px-shared-account/hermes";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useUserInitials } from "@/hooks/user";
import UserAvatar from "../../../user-management/users/components/UserAvatar";

interface StudentInfoProps {
  row: Row<ICourseStudent>;
}

function StudentInfo({ row }: StudentInfoProps) {
  const student = row.original;
  const initials = useUserInitials(student);

  return (
    <div className="flex items-center gap-2" role="cell">
      <UserAvatar user={student} />
      <div className="flex flex-col">
        <span className="text-sm font-medium" title={initials.name}>
          {initials.name}
        </span>
        <span className="text-xs text-muted-foreground" title={initials.email}>
          {initials.email}
        </span>
      </div>
    </div>
  );
}

export default memo(StudentInfo);
