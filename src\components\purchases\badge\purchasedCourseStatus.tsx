"use client";

import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { CustomerAccessStatus } from "@px-shared-account/hermes";

// Define colors directly or import from a central theme if available
const statusColors = {
  green: "bg-[#E5F7D1] text-[#296218]",
  red: "bg-[#FBE2D5] text-[#BE2E2D]",
  gray: "bg-[#BBBBBB] text-[#222222]",
  yellow: "bg-[#FBE2D5] text-[#BE2E2D]",
  black: "bg-[#222222] text-[#FFFFFF]",
  white: "bg-white text-[#222222]", // Assuming default/unknown status color
};

const computeStatusColorClasses = (status: CustomerAccessStatus): string => {
  switch (status) {
    case CustomerAccessStatus.GRANTED:
      return statusColors.green;
    case CustomerAccessStatus.PENDING:
      return statusColors.yellow;
    case CustomerAccessStatus.REVOKED:
      return statusColors.red;
    case CustomerAccessStatus.SUSPENDED:
      return statusColors.gray;
    case CustomerAccessStatus.ERROR:
      return statusColors.black;
    default:
      return statusColors.white;
  }
};

export default function BadgePurchasedCourseStatus({ status }: { status: CustomerAccessStatus }) {
  const t = useTranslations();
  const [localStatus, setStatus] = useState<CustomerAccessStatus>(status);

  useEffect(() => {
    setStatus(status);
  }, [status]);

  const commonBadgeStyles =
    "z-20 h-6 rounded-md px-2.5 py-0.5 text-xs font-light uppercase inline-flex items-center justify-center";

  return (
    <div className={cn(commonBadgeStyles, computeStatusColorClasses(localStatus))}>
      {t(`dashboard.purchases.status.${localStatus || "default"}`)}
    </div>
  );
}
