"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { PlusIcon } from "lucide-react";
import { useTranslations } from "next-intl";

export interface ChatHeaderProps {
  title: string;
  avatar?: string;
  online?: boolean;
  onNewMessage?: () => void;
  className?: string;
  beforeContent?: React.ReactNode;
}

export function ChatHeader({
  title,
  avatar,
  online,
  onNewMessage,
  className,
  beforeContent,
}: ChatHeaderProps) {
  const t = useTranslations("messages");

  return (
    <div
      className={cn(
        "flex h-16 items-center justify-between border-b bg-background px-4",
        className,
      )}
    >
      <div className="flex items-center gap-3">
        {beforeContent}
        <div className="relative">
          <Avatar>
            {avatar && <AvatarImage src={avatar} alt={title} />}
            <AvatarFallback>{title[0]}</AvatarFallback>
          </Avatar>
          {typeof online === "boolean" && (
            <span
              className={cn(
                "absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-background",
                online ? "bg-green-500" : "bg-muted",
              )}
            />
          )}
        </div>
        <div>
          <h3 className="font-semibold">{title}</h3>
          {typeof online === "boolean" && (
            <p className="text-sm text-muted-foreground">{online ? t("online") : t("offline")}</p>
          )}
        </div>
      </div>
      {onNewMessage && (
        <Button
          onClick={onNewMessage}
          className="rounded-full bg-black text-white hover:bg-black/90"
        >
          <PlusIcon className="h-4 w-4 md:mr-2" />
          <span className="hidden md:block">{t("new-message")}</span>
        </Button>
      )}
    </div>
  );
}
