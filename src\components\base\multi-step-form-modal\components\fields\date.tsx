import { useFormContext } from "react-hook-form";
import * as React from "react";
import { Label } from "@/components/ui/label";
import { DateField as DateFieldType } from "../../types";
import { useLocale, useTranslations } from "next-intl";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { enUS, fr } from "date-fns/locale";

interface DateFieldProps {
  field: DateFieldType;
}

// Define the DateField component
export const DateField = ({ field }: DateFieldProps) => {
  // Add a render counter to track renders
  const renderCountRef = React.useRef(0);
  renderCountRef.current += 1;

  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();
  const error = errors[field.id];
  const t = useTranslations("validation");
  const locale = useLocale();

  // Get the current value from the form
  const value = watch(field.id);

  // Use a ref to prevent infinite loops
  const valueRef = React.useRef(value);
  const isUpdatingRef = React.useRef(false);

  // Only update the ref when the value changes and is a valid date
  React.useEffect(() => {
    if (!isUpdatingRef.current && value instanceof Date && !isNaN(value.getTime())) {
      valueRef.current = value;
    }
  }, [field.id, value]);

  const renderError = () => {
    if (!error) return null;

    if (error.type === "custom" && error.message) {
      return error.message.toString();
    }

    switch (error.type) {
      case "required":
        return t("required");
      default:
        return error.message?.toString() || t("invalid");
    }
  };

  return (
    <div className="w-full space-y-2">
      {field.description && <p className="text-muted-foreground text-xs">{field.description}</p>}
      <DateTimePicker
        hourCycle={24}
        showOutsideDays={false}
        showWeekNumber={false}
        locale={locale === "fr" ? fr : enUS}
        weekStartsOn={1}
        value={valueRef.current instanceof Date ? valueRef.current : undefined}
        onChange={(date) => {
          if (date) {
            // Set the updating flag to prevent infinite loops
            isUpdatingRef.current = true;

            // Update the local ref immediately for UI responsiveness
            valueRef.current = date;

            try {
              // Call the field's onChange handler if provided
              if (field.onChange) {
                field.onChange(date);
              }

              // Update the form value using setValue
              // This is the primary way to update the form value
              setValue(field.id, date, {
                shouldValidate: false, // Don't validate immediately for better performance
                shouldDirty: true,
                shouldTouch: true,
              });

              // Force a re-render to show the updated value immediately
              setTimeout(() => {
                // Now validate the form
                setValue(field.id, date, {
                  shouldValidate: true,
                });
              }, 0);
            } catch (error) {
              console.error(`Error updating date field ${field.id}:`, error);
            }

            // Reset the updating flag after a short delay
            setTimeout(() => {
              isUpdatingRef.current = false;
            }, 50); // Reduced from 100ms to 50ms for better responsiveness
          }
        }}
        placeholder={field.placeholder}
        disabled={field.disabled}
        aria-invalid={!!error}
        className={error ? "border-destructive" : ""}
      />
      {error && <p className="text-destructive text-sm font-medium">{renderError()}</p>}
    </div>
  );
};

DateField.displayName = "DateField";
