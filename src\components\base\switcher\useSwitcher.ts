"use client";

import { useState, useCallback } from "react";
import { UseSwitcherProps } from "./types";

export const useSwitcher = ({
  defaultChecked = false,
  checked,
  onCheckedChange,
}: UseSwitcherProps = {}) => {
  const [internalChecked, setInternalChecked] = useState(defaultChecked);

  const isControlled = checked !== undefined;
  const currentChecked = isControlled ? checked : internalChecked;

  const handleChange = useCallback(
    (value: boolean) => {
      if (!isControlled) {
        setInternalChecked(value);
      }
      onCheckedChange?.(value);
    },
    [isControlled, onCheckedChange],
  );

  return {
    checked: currentChecked,
    onCheckedChange: handleChange,
  };
};
