import { z } from "zod";
import { UserBaseSchema } from "./user.interface";
import { CourseStatus, CustomerAccessStatus, ProductOfferStatus } from "../enums";
import { ProductFamilyBaseSchema } from "./product-family.interface";

export const DefaultCourseOfferSchema = z.object({
  id: z.number().positive(),
  name: z.string().nonempty(),
});
export const CourseOfferSchema = DefaultCourseOfferSchema.extend({
  createdAt: z.date(),
  updatedAt: z.date(),
  status: z.nativeEnum(ProductOfferStatus),
  image: z.string().url().nonempty(),
});

export type ICourseOffer = z.infer<typeof CourseOfferSchema>;
export type ICreateUpdateCourseOffer = z.infer<typeof DefaultCourseOfferSchema>;

export const CourseBaseSchema = z.object({
  id: z.number().positive(),
  name: z.string().max(100).nonempty(),
  description: z.string().max(1500).nonempty(),
  bannerImage: z.string().url().nonempty(),
  cardImage: z.string().url().nonempty(),
  thumbnail: z.string().url().nonempty(),
  managers: z.array(UserBaseSchema),
  lmsId: z.string().nonempty(),
  productFamily: ProductFamilyBaseSchema,
  ctaLink: z.string().url().nullable().optional(),
  status: z.nativeEnum(CourseStatus).nullable(),
  offers: z.array(CourseOfferSchema),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateUpdateCourseSchema = CourseBaseSchema.extend({
  offers: z.array(DefaultCourseOfferSchema),
});

export type IBaseCourse = z.infer<typeof CourseBaseSchema>;
export type ICreateUpdateCourse = z.infer<typeof CreateUpdateCourseSchema>;

export const CreateCourseSchema = CreateUpdateCourseSchema.omit({
  productFamily: true,
})
  .partial({
    id: true,
    status: true,
    createdAt: true,
    updatedAt: true,
  })
  .extend({
    managers: z.array(z.number().positive()),
    productFamilyId: z.number().positive(),
  });
export type ICreateCourse = z.infer<typeof CreateCourseSchema>;

export const UpdateCourseSchema = z.object({
  name: z.string().max(100).optional(),
  description: z.string().max(1500).optional(),
  bannerImage: z.string().url().optional(),
  cardImage: z.string().url().optional(),
  thumbnail: z.string().url().optional(),
  managers: z.array(z.number().positive()).optional(),
  status: z.nativeEnum(CourseStatus).optional(),
  offers: z.array(DefaultCourseOfferSchema).optional(),
  ctaLink: z.string().url().nullable().optional(),
  productFamilyId: z.number().positive().optional(),
});
export type IUpdateCourse = z.infer<typeof UpdateCourseSchema>;

// Course Catalog Types
export const OwnedCourseCatalogItemSchema = CourseBaseSchema.extend({
  accessStatus: z.nativeEnum(CustomerAccessStatus).nullable().optional(),
  lmsUrl: z.string().url().optional(),
});
export type IOwnedCourseCatalogItem = z.infer<typeof OwnedCourseCatalogItemSchema>;

export const PurchasableCourseCatalogItemSchema = CourseBaseSchema;
export type IPurchasableCourseCatalogItem = z.infer<typeof PurchasableCourseCatalogItemSchema>;

// For backward compatibility, keep the original schema but mark as deprecated
export const CourseCatalogItemSchema = CourseBaseSchema.extend({
  owned: z.boolean(),
  accessStatus: z.nativeEnum(CustomerAccessStatus).nullable().optional(),
  lmsUrl: z.string().url().optional(),
});
export type ICourseCatalogItem = z.infer<typeof CourseCatalogItemSchema>;

export const ListCourseCatalogParamsSchema = z.object({
  limit: z.coerce.number().min(1).max(100).default(50),
  cursor: z.coerce.number().optional(),
  search: z.string().optional(),
  status: z.nativeEnum(CourseStatus).optional(),
  owned: z.boolean().optional(),
});
export type IListCourseCatalogParams = z.infer<
  typeof ListCourseCatalogParamsSchema
>;

export const ListCourseCatalogResponseSchema = z.object({
  ownedCourses: z.array(OwnedCourseCatalogItemSchema),
  purchasableCourses: z.array(PurchasableCourseCatalogItemSchema),
  nextCursor: z.number().nullable(),
});
export type IListCourseCatalogResponse = z.infer<
  typeof ListCourseCatalogResponseSchema
>;

export const GetCourseByIdSchema = z.object({
  id: z.number().positive(),
});
export type IGetCourseById = z.infer<typeof GetCourseByIdSchema>;

export const ListCourseSchema = z.object({
  search: z.string().optional(),
  status: z.nativeEnum(CourseStatus).optional(),
});
export type IListCourse = z.infer<typeof ListCourseSchema>;

export const ListCoursesResponseSchema = z.object({
  data: z.array(CourseBaseSchema),
  page: z.number(),
  total: z.number(),
});
export type IListCoursesResponse = z.infer<typeof ListCoursesResponseSchema>;

export const CourseStudentSchema = UserBaseSchema.extend({
  cohortId: z.number().positive(),
  cohortName: z.string(),
});

export type ICourseStudent = z.infer<typeof CourseStudentSchema>;

export const GetStudentsForCourseSchema = z.object({
  search: z.string().optional(),
});
export type IGetStudentsForCourse = z.infer<typeof GetStudentsForCourseSchema>;

export const GetStudentsForCourseResponseSchema = z.object({
  data: z.array(CourseStudentSchema),
  page: z.number(),
  total: z.number(),
});

export type IGetStudentsForCourseResponse = z.infer<
  typeof GetStudentsForCourseResponseSchema
>;

export const GetCourseByOfferIdSchema = z.object({
  offerId: z.coerce.number().positive(),
});
export type IGetCourseByOfferId = z.infer<typeof GetCourseByOfferIdSchema>;
