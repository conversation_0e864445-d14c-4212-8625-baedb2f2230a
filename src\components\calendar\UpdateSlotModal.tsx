"use client";

import { addMinutes } from "date-fns";
import { useTranslations } from "next-intl";
import { useState } from "react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import useCalendarStore, { CalendarEvent } from "@/hooks/store/calendar";
import UpdateSlotForm from "./forms/update-slot";
import { CreateNewSlotFormType } from "./forms/types";
import createEvent from "@/lib/calendar/createEvent";
import isBeforeMidWeek from "@/lib/calendar/isBeforeMidWeek";

export default function UpdateSlotModal({
  children,
  type,
  event,
}: {
  children: React.ReactNode;
  type: "create" | "edit";
  event?: CalendarEvent | null;
}) {
  const { events, setEvents } = useCalendarStore();
  const t = useTranslations("calendar");
  const [open, setOpen] = useState(false);

  // TODO: handle edit

  function onSubmit(data: CreateNewSlotFormType) {
    if (type === "create") {
      const newEvent = createEvent({
        // Part 1 - Event Details
        id: events.length + 1,
        title: data.type + " by " + data.mainSpeaker,
        type: data.type,
        replay: data.replay,
        recurrence: data.recurrence,
        description:
          data.comment || `participants: ${data.participantMin} - ${data.participantMax}`,
        begin: new Date(data.date),
        end: addMinutes(data.date, Number(data.duration)),
        status: isBeforeMidWeek(data.date) ? "pending" : "confirmed",
        // Part 2 - Speakers
        mainSpeaker: data.mainSpeaker,
        otherSpeakers: data.otherSpeakers,
        technicalSupport: data.technicalSupport,
        // Part 3 - Audience
        cohort: data.cohort,
        participantMin: data.participantMin,
        participantMax: data.participantMax,
        program: data.program,
        course: data.course,
        module: data.module,
        // Part 4 - Comments
        comments: data.comment || "",
      });

      setEvents([...events, newEvent]);
      setOpen(false);
    } else {
      console.log("TODO: edit", data);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="flex h-[95svh] w-[90svw] flex-col overflow-y-auto !rounded-3xl">
        <DialogHeader>
          <DialogTitle>{t("new-slot")}</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <UpdateSlotForm onSubmit={onSubmit} data={event || undefined} type={type} />
      </DialogContent>
    </Dialog>
  );
}
