import { Type } from 'class-transformer';
import {
  IsString,
  IsEnum,
  IsUrl,
  IsNotEmpty,
  ValidateNested,
  IsEmpty,
  IsOptional,
  IsBoolean,
  IsNumber,
  Max,
  IsArray,
  ValidateIf,
  IsObject,
  Length,
  ArrayMinSize,
} from 'class-validator';
import {
  ApiExtraModels,
  ApiProperty,
  ApiPropertyOptional,
  PartialType,
} from '@nestjs/swagger';
import {
  CommunityInfoDTO,
  CourseInfo,
  CreateCheckoutPageDTO,
  UpdateCheckoutPageDTO,
} from '@dtos';
import { FiscalEntity } from '@enums';
import { ProductOfferStatus } from '@px-shared-account/hermes';
import { AttachedProduct, ProductQuantities } from '@types';

export class LMSAccesses {
  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CourseInfo)
  products: CourseInfo[];

  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CourseInfo)
  recommended: CourseInfo[];
}

export class CommunityAccesses {
  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CommunityInfoDTO)
  products: CommunityInfoDTO[];

  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CommunityInfoDTO)
  recommended: CommunityInfoDTO[];
}

export class CreateOfferDTO {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  externalName: string;

  @ApiProperty()
  @IsUrl()
  image: string;

  @ApiProperty()
  @IsUrl()
  @IsNotEmpty()
  cardImage: string;

  @ApiPropertyOptional()
  @IsUrl()
  @IsOptional()
  bannerImage?: string;

  @ApiProperty()
  @IsString()
  @Length(0, 800)
  description: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Length(0, 200)
  usp?: string;

  @ApiProperty()
  @IsString()
  currency: string;

  @ApiProperty()
  @IsEnum(FiscalEntity)
  fiscalEntity: FiscalEntity;

  @ApiProperty()
  @IsEnum(ProductOfferStatus)
  status: ProductOfferStatus;

  @ApiProperty()
  @IsUrl()
  redirectUrl: string;

  @ApiProperty()
  @IsString()
  slug: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  paymentGateway?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isForever?: boolean;

  @ApiPropertyOptional()
  @IsNumber()
  @Max(300, {
    message: 'Maximum number of trial days for annual subscription is 300',
  })
  @Max(300, {
    message: 'Maximum number of trial days for annual subscription is 300',
  })
  @IsOptional()
  trialDaysYearly?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @Max(28, {
    message: 'Maximum number of trial days for monthly subscription is 28',
  })
  @Max(28, {
    message: 'Maximum number of trial days for monthly subscription is 28',
  })
  @IsOptional()
  trialDaysMonthly?: number;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  withProductDelivery?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  suspendable?: boolean;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  delayPeriod?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => LMSAccesses)
  lmsIDs?: LMSAccesses;

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => CommunityAccesses)
  communityIds?: CommunityAccesses;
}

export class UpdateOfferDTO extends PartialType(CreateOfferDTO) {
  @ApiProperty()
  @IsNotEmpty()
  id: number;

  @IsEmpty()
  status?: ProductOfferStatus;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  trialDaysMonthly?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  trialDaysYearly?: number;
}

export class CreateOfferWithCheckoutDTO {
  @ApiProperty()
  @ValidateNested()
  @Type(() => CreateOfferDTO)
  offerInfo: CreateOfferDTO;

  @ApiProperty()
  @ValidateNested()
  @Type(() => CreateCheckoutPageDTO)
  checkoutInfo: CreateCheckoutPageDTO;
}

@ApiExtraModels(UpdateOfferDTO, UpdateCheckoutPageDTO)
export class UpdateOfferWithCheckoutDTO {
  @ApiProperty({ type: () => UpdateOfferDTO })
  @ValidateNested()
  @IsNotEmpty()
  @Type(() => UpdateOfferDTO)
  offerInfo: UpdateOfferDTO;

  @ApiProperty({ type: () => UpdateCheckoutPageDTO })
  @IsOptional()
  @ValidateNested()
  checkoutInfo?: UpdateCheckoutPageDTO;
}

export class CreateOfferConfigDTO {
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isForever?: boolean;

  @ApiPropertyOptional()
  @ValidateIf(
    (config: CreateOfferConfigDTO) =>
      config.isForever &&
      (config.yearly === undefined || config.yearly?.length === 0),
  )
  @ValidateIf(
    (config: CreateOfferConfigDTO) =>
      config.isForever &&
      (config.yearly === undefined || config.yearly?.length === 0),
  )
  @IsArray()
  @ArrayMinSize(1, {
    message: 'One of monthly or yearly must have at least 1 element',
  })
  @ArrayMinSize(1, {
    message: 'One of monthly or yearly must have at least 1 element',
  })
  @IsString({ each: true })
  @Length(15, 40, { each: true })
  @IsOptional()
  monthly?: string[];

  @ApiPropertyOptional()
  @ValidateIf(
    (config: CreateOfferConfigDTO) =>
      config.isForever &&
      (config.monthly === undefined || config.monthly?.length === 0),
  )
  @ValidateIf(
    (config: CreateOfferConfigDTO) =>
      config.isForever &&
      (config.monthly === undefined || config.monthly?.length === 0),
  )
  @IsArray()
  @ArrayMinSize(1, {
    message: 'One of monthly or yearly must have at least 1 element',
  })
  @ArrayMinSize(1, {
    message: 'One of monthly or yearly must have at least 1 element',
  })
  @IsString({ each: true })
  @Length(15, 40, { each: true })
  @IsOptional()
  yearly?: string[];

  @ApiProperty()
  @ValidateIf((config: CreateOfferConfigDTO) => !config.isForever)
  @IsNotEmpty()
  products: AttachedProduct;

  @ApiPropertyOptional()
  @ValidateIf((config: CreateOfferConfigDTO) => !config.isForever)
  recommended?: AttachedProduct | null;

  @ApiProperty()
  @ValidateIf((config: CreateOfferConfigDTO) => !config.isForever)
  @IsNotEmpty()
  quantityInfo: {
    products: ProductQuantities;
    recommended?: ProductQuantities | null;
  };

  @ApiProperty({ enum: ['products', 'offer'] })
  @IsNotEmpty()
  display: 'products' | 'offer';

  @ApiProperty()
  @ValidateIf((config: CreateOfferConfigDTO) => !config.isForever)
  @IsNotEmpty()
  defaultBillingCycle: number;
}

export class DisableOfferDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  redirectIfDisabled: string;
}

export class UpdateOfferAccessDTO {
  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  suspendable: boolean;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  delayPeriod: number;

  @ApiProperty()
  @IsObject()
  @IsNotEmpty()
  @Type(() => LMSAccesses)
  lmsIds: LMSAccesses;

  @ApiProperty()
  @IsObject()
  @IsNotEmpty()
  @Type(() => CommunityAccesses)
  communityIds: CommunityAccesses;
}
