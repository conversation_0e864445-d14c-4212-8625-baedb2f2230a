.masonry-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  width: 100%;
  position: relative;
  grid-auto-flow: dense; /* Allows items to fill gaps when possible */
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: 0;
  display: inline-block;
  width: 100%;
  position: relative;
  transition:
    transform 0.3s ease,
    opacity 0.4s ease;
  animation: fadeIn 0.5s ease forwards;
  opacity: 0;
  will-change: transform, opacity;
}

/* Images with landscape orientation can span 2 columns if enough space */
.masonry-item.landscape {
  grid-column: span 2;
}

/* Prevent super tall images from dominating the layout */
.masonry-item.portrait {
  max-height: 80vh;
  overflow: hidden;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fix for Safari */
@supports (-webkit-overflow-scrolling: touch) {
  .masonry-gallery {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .masonry-item {
    width: calc(50% - 12px);
    margin-bottom: 24px;
  }

  .masonry-item.landscape {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .masonry-gallery {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }

  .masonry-item.landscape {
    grid-column: span 1; /* Don't span on mobile */
  }

  @supports (-webkit-overflow-scrolling: touch) {
    .masonry-item {
      width: 100%;
      margin-bottom: 16px;
    }
  }
}

@media (min-width: 1200px) {
  .masonry-gallery {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  @supports (-webkit-overflow-scrolling: touch) {
    .masonry-item {
      width: calc(33.333% - 16px);
    }

    .masonry-item.landscape {
      width: calc(66.666% - 16px);
    }
  }
}
