import { CourseEntity } from '@entities';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CourseStatus } from '@px-shared-account/hermes';
import { UserTable } from './user.table';
import { CohortTable } from './cohort.table';
import { ProductFamilyTable } from './product-family.table';
import { ProductOfferTable } from './product-offers.table';

@Entity({
  name: 'courses',
})
export class CourseTable implements CourseEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 1500 })
  description: string;

  @Column({ type: 'varchar', length: 100 })
  bannerImage: string;

  @Column({ type: 'varchar', length: 100 })
  cardImage: string;

  @Column({ type: 'varchar', length: 100 })
  thumbnail: string;

  @Column({
    type: 'enum',
    enum: CourseStatus,
    default: CourseStatus.DRAFT,
    nullable: true,
  })
  status: CourseStatus;

  @ManyToMany(() => UserTable, (user) => user.coursesManaged)
  @JoinTable({
    name: 'Course_Managers',
    joinColumn: {
      name: 'courseId',
      foreignKeyConstraintName: 'FK_Course_Managers_courseId',
    },
    inverseJoinColumn: {
      name: 'userId',
      foreignKeyConstraintName: 'FK_Course_Managers_userId',
    },
  })
  managers: UserTable[];

  @OneToMany(() => CohortTable, (cohort) => cohort.course)
  cohorts: CohortTable[];

  @ManyToMany(() => ProductOfferTable)
  @JoinTable({
    name: 'Course_Offers',
    joinColumn: {
      name: 'courseId',
      foreignKeyConstraintName: 'FK_Course_Offers_courseId',
    },
    inverseJoinColumn: {
      name: 'offerId',
      foreignKeyConstraintName: 'FK_Course_Offers_offerId',
    },
  })
  offers: ProductOfferTable[];

  @Column({ type: 'varchar', length: 255 })
  lmsId: string;

  @ManyToOne(() => ProductFamilyTable, (productFamily) => productFamily.courses)
  productFamily: ProductFamilyTable;

  @Column({ type: 'varchar', length: 500, default: 'https://paradox.io' })
  ctaLink: string;

  @CreateDateColumn({ default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}
