"use client";

import { useUser } from "@clerk/nextjs";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useState } from "react";

import { Input } from "../input";
import { Label } from "@/components/ui/label";

export default function UpdateAvatar() {
  const { user } = useUser();
  const t = useTranslations("profile");
  const [avatar, setAvatar] = useState(user?.imageUrl);
  const [isLoading, setIsLoading] = useState(false);

  const handleAvatarChange = async (e: any) => {
    try {
      if (isLoading) return;
      setIsLoading(true);
      const file = e.target.files?.[0];
      await user?.setProfileImage({ file });
      setAvatar(URL.createObjectURL(file));
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="mt-8 flex flex-col items-center justify-center gap-4">
        <div className="relative h-24 w-24 sm:h-32 sm:w-32">
          <Image
            src={avatar || ""}
            alt="avatar"
            fill
            className="rounded-full object-cover"
            loading="lazy"
          />
        </div>
        <Label htmlFor="picture" className="cursor-pointer">
          {t("upload")}
        </Label>
        <Input id="picture" type="file" onChange={handleAvatarChange} className="hidden" />
      </div>
    </>
  );
}
