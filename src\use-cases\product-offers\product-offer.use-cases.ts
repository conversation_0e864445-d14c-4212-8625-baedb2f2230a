import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { FindOptionsWhere, In } from 'typeorm';
import {
  Product,
  ProductOffer,
  ProductPlan,
  ProductPlanPrice,
} from '@entities';
import {
  AttachmentMap,
  CycleWithPlan,
  OfferConfig,
  UpdateResultWithItemInfo,
  CheckoutConfig,
  PXActionResult,
} from '@types';
import {
  ProductOfferStatus,
  ProductOfferVersion,
} from '@px-shared-account/hermes';
import { ProductOfferFactoryService } from '.';
import { CheckoutPageUseCases } from '../checkout-pages';
import { FiscalEntity } from '@enums';
import { ChargebeeBillingService } from '@services/billing';
import { RedisPublisher } from '@services/event-publishers';
import { IDataServices } from '@abstracts';
import { UpdateOfferAccessDTO, UpdateOfferWithCheckoutDTO } from '@dtos';

@Injectable()
export class ProductOfferUseCases {
  private readonly logger = new Logger(ProductOfferUseCases.name);

  constructor(
    private readonly dataServices: IDataServices,
    private readonly chargebeeService: ChargebeeBillingService,
    private readonly checkoutPageUseCases: CheckoutPageUseCases,
    private readonly redisPublisher: RedisPublisher,
    private readonly offerFactoryService: ProductOfferFactoryService,
  ) {}

  /**
   * Creates a new `ProductOffer` in database
   * @param offer `ProductOffer` entity
   * @returns `ProductOffer` record from database
   */
  async create(offer: ProductOffer): Promise<ProductOffer> {
    const createdOffer = await this.dataServices.productOffer.create(offer);
    const chargebeePlan = await this.chargebeeService.createPlan(createdOffer);
    if (!chargebeePlan?.item?.id) {
      await this.dataServices.productOffer.hardDelete({ id: createdOffer.id });
      throw new HttpException(
        {
          message: 'Failed to create offer in Chargebee',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return createdOffer;
  }

  /**
   * Retrieves a `ProductOffer` matching provided `id``
   * @param findOptions `id` of `ProductOffer` to fetch
   */
  async getOneBy(
    findOptions: FindOptionsWhere<ProductOffer>,
  ): Promise<ProductOffer> {
    return this.dataServices.productOffer.getOneBy(findOptions);
  }

  /**
   * Gets all `ProductOffer` records from the database
   * @param limit Limit for the number of records to fetch
   * @param page `limit x page` defines how many records to skip
   * @returns `ProductOffer` records from database with optional pagination
   */
  async getAll(
    limit?: number,
    page?: number,
  ): Promise<{ items: ProductOffer[]; total: number }> {
    return this.dataServices.productOffer.getAll(limit, page);
  }

  /**
   * Updates the product offer specified by the `id`
   * @param id `id` of the `ProductOffer` to update
   * @param updates Updates for the `ProductOffer`
   */
  async update(
    id: number,
    updates: Partial<ProductOffer>,
  ): Promise<UpdateResultWithItemInfo<ProductOffer>> {
    const offerRepo = this.dataServices.productOffer.getRepository();
    const existingOffer = await offerRepo.findOne({
      where: { id },
      relations: ['checkoutPage'],
    });
    if (!existingOffer) {
      throw new HttpException(
        {
          message: `Offer with id: ${id} was not found`,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    const updatedOffer = Object.assign(existingOffer, updates);
    const updatedOfferFromDB = await offerRepo.save({
      ...updatedOffer,
      version: ProductOfferVersion.PREVIEW,
    });

    return {
      success: true,
      updatedItem: updatedOfferFromDB,
    };
  }

  /**
   * Updates the offer and checkout page & publishes the preview
   * @param updates - The updates to sync
   * @returns PXActionResult
   */
  async updateWithCheckout(
    updates: UpdateOfferWithCheckoutDTO,
  ): Promise<PXActionResult> {
    const { offerInfo: offerUpdates, checkoutInfo: checkoutUpdates } = updates;
    let updatedOfferFromDB: ProductOffer;

    try {
      if (offerUpdates) {
        const updateResult = await this.update(offerUpdates.id, offerUpdates);
        updatedOfferFromDB = updateResult.updatedItem;
        await this.syncOfferUpdatesWithChargebee(
          updatedOfferFromDB?.chargebeeId,
          offerUpdates,
          FiscalEntity[updatedOfferFromDB.fiscalEntity],
        );
      }
      if (checkoutUpdates) {
        await this.checkoutPageUseCases.update(
          checkoutUpdates.id,
          checkoutUpdates,
          offerUpdates?.id,
        );
      }

      if (!updatedOfferFromDB) {
        updatedOfferFromDB = await this.checkoutPageUseCases.getLinkedOffer(
          checkoutUpdates.id,
        );
      }

      await this.publishOfferPreview(updatedOfferFromDB);

      return {
        success: true,
        message:
          'Offer and checkout page updated successfully and preview published',
      };
    } catch (err) {
      return {
        success: false,
        message: err.message,
      };
    }
  }

  /**
   * Syncs the offer updates with Chargebee
   * @param chargebeeId - The Chargebee ID of the offer
   * @param updates - The updates to sync
   * @param fiscalEntity - The fiscal entity of the offer
   */
  async syncOfferUpdatesWithChargebee(
    chargebeeId: string,
    updates: Partial<ProductOffer>,
    fiscalEntity: FiscalEntity,
  ): Promise<void> {
    await this.chargebeeService.updatePlan(
      chargebeeId,
      updates,
      FiscalEntity[fiscalEntity],
    );
  }

  /**
   * Publishes the offer preview
   * @param offer - The offer to publish
   */
  async publishOfferPreview(offer: ProductOffer): Promise<void> {
    if (!offer?.config) {
      this.logger.warn(
        `Offer with id: ${offer.id} has no config, skipping preview publish`,
      );
      return;
    }
    const checkoutPayload = await this.checkoutPageUseCases.getCheckoutConfig(
      offer,
    );
    const checkoutPayloadString = JSON.stringify(checkoutPayload).replace(
      /\\"/g,
      '"',
    );
    await this.redisPublisher.publishMessage(
      `${offer.slug}-preview`,
      checkoutPayloadString,
    );
  }
  /**
   * Disables a product offer and updates its config from Vercel's KV store.
   * The new config has `status` = `disabled` and has a value for `redirectIfDisabled`
   * @param offerId - The ID of the offer to disable.
   * @throws HttpException if the offer with the specified ID is not found.
   */
  async disableOffer(offerId: number, redirectUrl: string): Promise<void> {
    const offer = await this.dataServices.productOffer.getOneBy({
      id: offerId,
    });
    if (!offer) {
      throw new HttpException(
        {
          message: `Offer with id: ${offerId} was not found`,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    await this.dataServices.productOffer.update(
      { id: offerId },
      {
        status: ProductOfferStatus.DISABLED,
        version: ProductOfferVersion.PREVIEW,
        redirectIfDisabled: redirectUrl,
      },
    );
    await this.dataServices.checkoutPage.update(
      {
        id: offer?.checkoutPage?.id,
      },
      {
        isLive: false,
      },
    );
    const redisKey = `${offer.slug}-live`;
    const liveOfferConfigRaw = await this.redisPublisher.getMessage(redisKey);
    if (!liveOfferConfigRaw) {
      throw new NotFoundException(
        `Live config for offer with slug: ${redisKey} was not found in KV store`,
      );
    }
    const liveOfferConfig: CheckoutConfig = JSON.parse(liveOfferConfigRaw);
    liveOfferConfig.redirectIfDisabled = redirectUrl;
    liveOfferConfig.status = ProductOfferStatus.DISABLED;
    const checkoutPayloadString = JSON.stringify(liveOfferConfig).replace(
      /\\"/g,
      '"',
    );
    await this.redisPublisher.publishMessage(redisKey, checkoutPayloadString);
  }

  /**
   * Activates a product offer and it's config in the Vercel's KV store.
   * @param offerId - The ID of the offer to activate.
   * @throws HttpException if the offer with the specified ID is not found.
   */
  async activateOffer(offerId: number): Promise<void> {
    const offer = await this.dataServices.productOffer.getOneBy({
      id: offerId,
    });
    if (!offer) {
      throw new HttpException(
        {
          message: `Offer with id: ${offerId} was not found`,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    await this.dataServices.productOffer.update(
      { id: offerId },
      {
        status: ProductOfferStatus.ACTIVE,
        version: ProductOfferVersion.LIVE,
      },
    );
    await this.dataServices.checkoutPage.update(
      {
        id: offer?.checkoutPage?.id,
      },
      {
        isLive: true,
      },
    );
    const liveOfferConfigRaw = await this.redisPublisher.getMessage(
      `${offer.slug}-live`,
    );
    const liveOfferConfig: CheckoutConfig = JSON.parse(liveOfferConfigRaw);
    liveOfferConfig.redirectIfDisabled = null;
    liveOfferConfig.status = ProductOfferStatus.ACTIVE;
    const checkoutPayloadString = JSON.stringify(liveOfferConfig).replace(
      /\\"/g,
      '"',
    );
    await this.redisPublisher.publishMessage(
      `${offer.slug}-live`,
      checkoutPayloadString,
    );
  }

  /**
   * Saves the history of a product offer by creating a new version and adding it to the history array.
   * If the history array exceeds the maximum number of entries, the oldest entry is removed.
   * @param offerToUpdate - The product offer to update.
   * @returns The updated product offer with the history saved.
   */
  private saveHistory(offerToUpdate: ProductOffer): ProductOffer {
    const clonedOffer: ProductOffer = { ...offerToUpdate };
    const versionToSave = { ...clonedOffer };
    delete versionToSave.id;
    delete versionToSave.history;

    const maxEntries = 20;
    if (clonedOffer.history === null) {
      clonedOffer.history = [];
    }

    clonedOffer.history.push({ ...versionToSave });
    if (clonedOffer.history && clonedOffer.history.length > maxEntries) {
      clonedOffer.history.shift();
    }

    return clonedOffer;
  }

  /**
   * Search all `ProductOffer` records that match certain criteria
   * @param name `name` of the `ProductOffer` to search for
   * @param lineId `id` of the `ProductLine` to search linked `ProductOffer`s for
   * @param familyId `id` of the `ProductFamily` to search linked `ProductOffer`s for
   * @param productId `id` of the `Product` to search linked `ProductOffer`s for
   * @param planId `id` of the `ProductPlan` to search linked `ProductOffer`s for
   * @param status `status` of the `ProductOffer`
   * @param limit Limit for the number of records to fetch, defaults to 50
   * @param page `limit x page` defines how many records to skip, defaults to 1
   * @returns `ProductOffer` records with `total` count
   */
  async searchAll(
    name?: string,
    lineId?: number,
    familyId?: number,
    productId?: number,
    planId?: number,
    status?: ProductOfferStatus,
    limit = 500,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: ProductOffer[]; total: number }> {
    const offersRepo = this.dataServices.productOffer.getRepository();
    const columnsToFetch = offersRepo.metadata.columns
      .filter((column) => {
        return column.propertyName !== 'history';
      })
      .map((column) => `offer.${column.propertyName}`);
    const queryBuilder = offersRepo.createQueryBuilder('offer');
    queryBuilder
      .select(columnsToFetch)
      .leftJoinAndSelect('offer.products', 'product')
      .leftJoin('product.plans', 'plan')
      .leftJoin('product.productFamily', 'family')
      .leftJoin('family.productLine', 'line');

    if (lineId) {
      queryBuilder.where('line.id = :lineId', { lineId });
    }

    if (familyId) {
      queryBuilder.andWhere('family.id = :familyId', { familyId });
    }

    if (productId) {
      queryBuilder.andWhere('product.id = :productId', { productId });
    }

    if (planId) {
      queryBuilder.andWhere('plan.id = :planId', { planId });
    }

    if (name) {
      queryBuilder.andWhere('offer.name ILIKE :name', { name: `%${name}%` });
    }

    if (status) {
      queryBuilder.andWhere('offer.status = :status', { status });
    }

    queryBuilder.orderBy('offer.createdAt', orderBy || 'DESC');
    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);

    const [items, total] = await queryBuilder.getManyAndCount();
    return {
      items,
      total,
    };
  }

  /**
   * Updates the product configuration for an offer
   * @param id `id` of the offer to update products config for
   * @param config Object containing info about products configuration
   */
  async attachProducts(id: number, config: OfferConfig): Promise<void> {
    const offerToUpdate = await this.getOneBy({ id });
    if (!offerToUpdate) {
      throw new NotFoundException(`Offer with id: ${id} was not found`);
    }
    const addonsNotSyncedWithChargeBee = this.getAddonsNotSyncedWithChargeBee(
      offerToUpdate,
      config,
    );
    const newAttachmentMap = await this.getNewAttachmentMap(
      offerToUpdate,
      addonsNotSyncedWithChargeBee,
    );
    const plansDetachedFromOffer = this.getPlansDetachedFromOffer(
      offerToUpdate,
      config,
    );
    const detachedAddonIds = await this.getDetachedAddonIds(
      offerToUpdate,
      plansDetachedFromOffer,
    );
    this.removeDetachedAddonsFromMap(detachedAddonIds, newAttachmentMap);
    const attachedPlansIds = new Set<number>();
    const attachedProductsIds = new Set<number>();
    if (addonsNotSyncedWithChargeBee.length > 0) {
      for (const addon of addonsNotSyncedWithChargeBee) {
        const planToRelate = await this.dataServices.productPlan.getOneBy({
          chargebeeId: addon,
        });
        if (planToRelate) {
          attachedPlansIds.add(planToRelate.id);
          attachedProductsIds.add(planToRelate.product?.id);
        }
      }

      const productsToLink: Product[] = [];
      const plansToLink: ProductPlan[] = [];
      for (const id of attachedProductsIds.values()) {
        const product = new Product();
        product.id = id;
        productsToLink.push(product);
      }

      for (const id of attachedPlansIds.values()) {
        const plan = new ProductPlan();
        plan.id = id;
        plansToLink.push(plan);
      }
      offerToUpdate.products = productsToLink;
      offerToUpdate.plans = plansToLink;
    }
    await this.updateOfferAndPublishMessage(
      id,
      offerToUpdate,
      config,
      newAttachmentMap,
      offerToUpdate,
    );
  }

  /**
   * Retrieves the list of addon IDs that are not synced with ChargeBee.
   *
   * @param linkedOffer - The product offer for which addons are not synced.
   * @param config - The offer's product configuration.
   * @returns An array of addon IDs that are not synced with ChargeBee.
   */
  getAddonsNotSyncedWithChargeBee(
    linkedOffer: ProductOffer,
    config: OfferConfig,
  ): string[] {
    if (linkedOffer.isForever) {
      const existingAddonIds = new Set(
        this.getAddonIdsForForeverConfig(linkedOffer.config),
      );
      const newAddonIds = [...config?.monthly, ...config?.yearly];
      return newAddonIds.filter((id) => !existingAddonIds.has(id));
    }
    const existingAddonIds = new Set(
      ProductOfferUseCases.getAddonIds(linkedOffer.config),
    );
    const newAddonIds = ProductOfferUseCases.getAddonIds(config);
    return newAddonIds.filter((item) => !existingAddonIds.has(item));
  }

  /**
   * Extracts and returns id's of add-ons from the config of
   * a forever offer
   * @param config Existing config of a forever offer
   * @returns Array of add-on IDs
   */
  getAddonIdsForForeverConfig(config: OfferConfig): string[] {
    if (!config) {
      return [];
    }
    const monthlyAddons = config?.monthly;
    const yearlyAddons = config?.yearly;
    return [...monthlyAddons, ...yearlyAddons];
  }

  /**
   * Attaches new addons to the plan in ChargeBee and retrieves the
   * new attachment map for a product offer.
   * @param linkedOffer - The product offer to attach addons to.
   * @param addonsNotSyncedWithChargeBee - An array of addons that are not synced with ChargeBee.
   * @returns A Promise that resolves to the new attachment map.
   */
  async getNewAttachmentMap(
    linkedOffer: ProductOffer,
    addonsNotSyncedWithChargeBee: string[],
  ): Promise<AttachmentMap> {
    let newAttachmentMap = { ...linkedOffer.config?.attachmentMap };
    if (addonsNotSyncedWithChargeBee.length > 0) {
      const newAddonsMap = await this.chargebeeService.attachAddonsToPlan(
        linkedOffer.chargebeeId,
        addonsNotSyncedWithChargeBee,
        linkedOffer.fiscalEntity,
      );
      newAttachmentMap = {
        ...linkedOffer.config?.attachmentMap,
        ...newAddonsMap,
      };
    }
    return newAttachmentMap;
  }

  /**
   * Retrieves the detached plan IDs from the given offer
   * @param linkedOffer - The offer from which plans are detached.
   * @param config - The offer configuration.
   * @returns An array of detached plan IDs.
   */
  getPlansDetachedFromOffer(
    linkedOffer: ProductOffer,
    config: OfferConfig,
  ): string[] {
    let existingAddonIds: Set<string>;
    let newAddonIds: string[];
    const oldAttachmentMap = { ...linkedOffer.config?.attachmentMap };

    if (linkedOffer.isForever) {
      existingAddonIds = new Set(
        this.getAddonIdsForForeverConfig(linkedOffer.config),
      );
      newAddonIds = [...config?.monthly, ...config?.yearly];
    } else {
      existingAddonIds = new Set(
        ProductOfferUseCases.getAddonIds(linkedOffer.config),
      );
      newAddonIds = ProductOfferUseCases.getAddonIds(config);
    }

    return Array.from(existingAddonIds).reduce((detachedAddonIds, addonId) => {
      if (!newAddonIds.includes(addonId) && oldAttachmentMap?.[addonId]) {
        detachedAddonIds.push(oldAttachmentMap[addonId]);
      }
      return detachedAddonIds;
    }, []);
  }

  /**
   * Retrieves the IDs of the detached add-ons from the given offer.
   * @param linkedOffer - The product offer from which add-ons are detached.
   * @param plansDetachedFromOffer - The list of plans detached from the offer.
   * @returns A promise that resolves to an array of detached addon IDs.
   */
  async getDetachedAddonIds(
    linkedOffer: ProductOffer,
    plansDetachedFromOffer: string[],
  ): Promise<string[]> {
    let detachedAddonIds: string[] = [];
    if (plansDetachedFromOffer.length > 0) {
      detachedAddonIds = await this.chargebeeService.removeAddonsFromPlan(
        linkedOffer.chargebeeId,
        plansDetachedFromOffer,
        linkedOffer.fiscalEntity,
      );
    }
    return detachedAddonIds;
  }

  /**
   * Removes detached addons from the attachment map.
   * @param detachedAddonIds - An array of detached addon IDs.
   * @param newAttachmentMap - The attachment map to remove the detached addons from.
   */
  removeDetachedAddonsFromMap(
    detachedAddonIds: string[],
    newAttachmentMap: AttachmentMap,
  ): void {
    for (const id of detachedAddonIds) {
      delete newAttachmentMap[id];
    }
  }

  /**
   * Updates an offer and publishes a message to Vercel KV store.
   *
   * @param id - The ID of the offer to update.
   * @param offerWithUpdatedHistory - The offer object with updated history.
   * @param config - The offer configuration.
   * @param newAttachmentMap - The new attachment map.
   * @param offerToUpdate - The offer object to update.
   * @returns A promise that resolves when the update and message publishing is complete.
   */
  async updateOfferAndPublishMessage(
    id: number,
    offerWithUpdatedHistory: ProductOffer,
    config: OfferConfig,
    newAttachmentMap: AttachmentMap,
    offerToUpdate: ProductOffer,
  ): Promise<void> {
    config.attachmentMap = newAttachmentMap;
    const updatedOffer = Object.assign(offerWithUpdatedHistory, { config });
    const productRepository = this.dataServices.productOffer.getRepository();
    await productRepository.save({
      id,
      ...updatedOffer,
      version: ProductOfferVersion.PREVIEW,
    });
    const checkoutPayload = await this.checkoutPageUseCases.getCheckoutConfig({
      ...offerToUpdate,
      config,
    });
    const checkoutPayloadString = JSON.stringify(checkoutPayload).replace(
      /\\"/g,
      '"',
    );
    await this.redisPublisher.publishMessage(
      `${offerToUpdate.slug}-preview`,
      checkoutPayloadString,
    );
  }

  /**
   * Extracts the `id`s of add-ons from an offer's configuration
   * @param config Config including IDs of the plans to extract
   * @returns An array of strings, which are `chargebeeId`s of plans (add-ons)
   */
  static getAddonIds(config: OfferConfig): string[] {
    if (!config) {
      return [];
    }
    const productsArray: CycleWithPlan[] = Object.values(
      config.products,
    ).flatMap((productCyclesWithPlans: any) => productCyclesWithPlans);

    let recommendedProductsArray: CycleWithPlan[] = [];
    if (!!config.recommended) {
      recommendedProductsArray = Object.values(config.recommended).flatMap(
        (productCyclesWithPlans: any) => productCyclesWithPlans,
      );
    }
    const productIds: string[] = productsArray
      .map((cycle: CycleWithPlan) => Object.values(cycle))
      .flat();
    const recommendedProductIds: string[] = recommendedProductsArray
      .map((cycle: CycleWithPlan) => Object.values(cycle))
      .flat();
    return [...productIds, ...recommendedProductIds];
  }

  /**
   * Publishes the offer to the live environment
   * @param id `id` of the offer to publish
   */
  async publishOffer(id: number): Promise<any> {
    const offer = await this.dataServices.productOffer.getOneBy({ id });
    const checkoutPayload = await this.checkoutPageUseCases.getCheckoutConfig(
      offer,
    );
    const checkoutPayloadString = JSON.stringify(checkoutPayload).replace(
      /\\"/g,
      '"',
    );
    await this.redisPublisher.publishMessage(
      `${offer.slug}-live`,
      checkoutPayloadString,
    );
    await this.dataServices.productOffer.update(
      { id: offer.id },
      { version: ProductOfferVersion.LIVE },
    );
    await this.dataServices.checkoutPage.update(
      {
        id: offer.checkoutPage.id,
      },
      {
        isLive: true,
      },
    );
  }

  /**
   * Retrieves the offer and its products for a downsell
   * @param offerId `id` of the offer to fetch products for
   * @returns Offer and its products
   */
  async getOfferProductsForDownsell(offerId: number): Promise<any> {
    try {
      const offer = await this.dataServices.productOffer.getOneBy({
        id: offerId,
      });
      if (!offer.config) {
        throw new NotFoundException(`Offer with id: ${offerId} has no config`);
      }
      const configuredBillingCycles =
        this.offerFactoryService.getOfferBillingCycles(offer);
      const offerProducts: any = [];
      const prodKeys = Object.keys(offer.config.products);
      const dbProds = await this.dataServices.product.getAllBy(
        {
          id: In(prodKeys),
        },
        100,
        1,
      );

      dbProds.items.forEach((product: Product) => {
        const configProduct = offer.config.products[product.id];
        const configProdKeys = Object.keys(configProduct);
        const prices: ProductPlanPrice[] = [];
        configProdKeys.forEach((key) => {
          const priceId = Object.values(configProduct[key]);
          prices.push(
            product.plans.find(
              (plan) => plan.chargebeeId === priceId.toString(),
            ).prices[0],
          );
        });
        offerProducts.push({
          id: product.id,
          name: product.externalName,
          description: product.description,
          image: product.image,
          prices: prices,
        });
      });
      return {
        offer,
        configuredBillingCycles,
        offerProducts,
        defaultBillingCycle: offer.config.defaultBillingCycle,
      };
    } catch (error) {
      throw new HttpException(
        {
          message: error.message || 'Failed to get offer details',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Syncs offer updates with Redis
   * @param offerId Offer ID
   * @param updates Offer updates to sync
   */
  async publishOfferUpdatesForVega(
    offerId: number,
    updates: Partial<ProductOffer>,
  ) {
    await this.redisPublisher.updateHash(
      'vega-offers',
      offerId.toString(),
      updates,
    );
    await this.redisPublisher.publishMessageToChannel(
      'vega-offers',
      JSON.stringify({ id: offerId, ...updates }),
    );
  }

  /**
   * Updates the access configuration of an offer.
   * @param id The id of the offer to update
   * @param config The updated access configuration
   * @returns A promise that resolves to a `PXActionResult` object
   */
  async updateAccessConfig(
    id: number,
    config: UpdateOfferAccessDTO,
  ): Promise<PXActionResult> {
    const withProductDelivery =
      config.lmsIds?.products?.length > 0 ||
      config.communityIds?.products?.length > 0;
    const { affected } = await this.dataServices.productOffer.update(
      { id },
      { ...config, withProductDelivery },
    );

    return {
      success: affected > 0,
      message:
        affected > 0
          ? 'Access configuration updated successfully'
          : 'Access configuration could not be updated',
    };
  }
}
