import { Module } from '@nestjs/common';
import { ProductPlanPriceFactory, ProductPlanPriceUseCases } from '.';
import { ChargebeeBillingModule } from '@services/billing';
import { DataServicesModule } from '@services/database';

@Module({
  imports: [DataServicesModule, ChargebeeBillingModule],
  providers: [ProductPlanPriceUseCases, ProductPlanPriceFactory],
  exports: [ProductPlanPriceUseCases, ProductPlanPriceFactory],
})
export class ProductPlanPriceUseCasesModule {}
