import { Module } from '@nestjs/common';
import { InvoiceFactoryService, InvoiceUseCases } from '.';
import { ChargebeeBillingModule } from '@services/billing';
import { DataServicesModule } from '@services/database';
import { HubspotModule } from '@services/crm';
import { ProductDeliveryModule } from '../product-delivery';

@Module({
  imports: [
    DataServicesModule,
    ChargebeeBillingModule,
    HubspotModule,
    ProductDeliveryModule,
  ],
  providers: [InvoiceFactoryService, InvoiceUseCases],
  exports: [InvoiceFactoryService, InvoiceUseCases],
})
export class InvoiceUseCasesModule {}
