import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UaeExchangeRatesCrawlerService } from '@services/crawlers';
import { ExchangeRateCurrencies } from '@enums';
import { CreateExchangeRateDTO } from '@dtos';
import { IDataServices } from '@abstracts';
import { ExchangeRate } from '@entities';
import { CronLogResult } from '@types';
import { ExchangeRateFactory } from './exchange-rate.factory';

@Injectable()
export class ExchangeRateService {
  constructor(
    private readonly factory: ExchangeRateFactory,
    private readonly dataServices: IDataServices,
    private readonly ratesCrawler: UaeExchangeRatesCrawlerService,
  ) {}

  /**
   * Crawls exchange rates from UAE Central Bank and saves them in database
   * @returns A promise which resolves to a `CronLogResult` object
   */
  async crawlAndSave(): Promise<CronLogResult> {
    const crawlResult = await this.ratesCrawler.getExchangeRates();
    if (crawlResult === null || !crawlResult.rates) {
      return {
        success: false,
        message: 'Failed to crawl exchange rates',
        output: crawlResult,
      };
    }
    const { rates, lastUpdated } = crawlResult;
    for (const [currency, rate] of Object.entries(rates) as [
      keyof typeof ExchangeRateCurrencies,
      string,
    ][]) {
      const exchangeRate = this.factory.generate({
        from: ExchangeRateCurrencies[currency],
        rate: rate,
        metaData: {
          lastUpdated,
        },
      });
      await this.dataServices.exchangeRate.create(exchangeRate);
    }
    return {
      success: true,
      message: 'Exchange rates crawled and saved',
    };
  }

  /**
   * Creates a new exchange rate in database
   * @param exchangeRateInfo Exchange rate entity
   * @returns Exchange rate record from database
   */
  async create(exchangeRateInfo: CreateExchangeRateDTO): Promise<ExchangeRate> {
    const exchangeRate = this.factory.generate(exchangeRateInfo);
    return this.dataServices.exchangeRate.create(exchangeRate);
  }

  /**
   * Gets all exchange rates for the specified date
   * @param date Date to get exchange rates for
   * @param limit Number of exchange rates to fetch
   * @param page Page number
   * @returns Exchange rates for the specified date
   */
  async getAllByDate(
    date: string,
    limit?: number,
    page?: number,
  ): Promise<{ items: ExchangeRate[]; total: number }> {
    const dateInMs = this.factory.generateExchangeRateTime(date);
    return this.dataServices.exchangeRate.getAllBy(
      {
        date: dateInMs,
      },
      limit,
      page,
    );
  }

  /**
   * Gets an exchange rate for the specified date
   * @param from Currency to convert from
   * @param to Currency to convert to
   * @param date Date to get exchange rate for
   * @returns Exchange rate for the specified date
   */
  async getOneByDate(
    from: ExchangeRateCurrencies,
    date: string,
  ): Promise<ExchangeRate> {
    const dateInMs = this.factory.generateExchangeRateTime(date);
    const rates = await this.dataServices.exchangeRate.getOneBy({
      from,
      to: ExchangeRateCurrencies.AED,
      date: dateInMs,
    });

    if (!rates) {
      throw new HttpException(
        {
          success: false,
          message: `Exchange rate not found for ${from} on ${date}`,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return rates;
  }
}
