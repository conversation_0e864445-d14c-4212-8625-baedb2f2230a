import { TriadGroup } from "@/types/triad";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface GroupBadgeProps {
  group: TriadGroup;
}

export function GroupBadge({ group }: GroupBadgeProps) {
  const t = useTranslations("triad");

  const getStyle = () => {
    switch (group) {
      case "A":
        return "bg-purple-500/10 text-purple-500";
      case "B":
        return "bg-blue-500/10 text-blue-500";
      case "C":
        return "bg-emerald-500/10 text-emerald-500";
    }
  };

  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full px-3 py-1 text-sm font-medium",
        getStyle(),
      )}
    >
      {t("group", { letter: group })}
    </span>
  );
}
