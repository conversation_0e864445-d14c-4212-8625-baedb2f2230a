import { But<PERSON> } from "@/components/base/button";
import { DataTable } from "@/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ProfilesMockData } from "@/mockdata/profiles";
import { Profile } from "@/types/user-management/users";
import { ColumnDef } from "@tanstack/react-table";
import { Copy, Edit, Ellipsis, PlusIcon, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useMemo } from "react";

export default function ProfilesTable() {
  const t = useTranslations("gestion.users");
  const data = useMemo(() => ProfilesMockData, []);

  const columnDefs: ColumnDef<Profile>[] = [
    {
      header: t("name"),
      accessorKey: "name",
    },
    {
      header: t("members"),
      accessorKey: "members",
    },
    {
      header: t("permissions"),
      accessorKey: "permissions",
      cell: ({ row }) => (
        <div className="flex text-xs uppercase">
          {row.original.permissions.map((permission) => (
            <span key={permission}>{permission};&nbsp;</span>
          ))}
        </div>
      ),
    },
    {
      header: t("actions-header"),
      id: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Ellipsis className="cursor-pointer" />
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem className="flex items-center gap-2">
                  <Edit className="mr-2 h-4 w-4" />
                  {t("edit")}
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Copy className="mr-2 h-4 w-4" />
                  {t("duplicate")}
                </DropdownMenuItem>
                <DropdownMenuItem className="text-destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t("delete")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  const columns = useMemo(() => columnDefs, []);

  return (
    <div>
      <div className="flex flex-row justify-between gap-4">
        <span className="text-md font-medium">4 {t("profile")}</span>
        <Button className="hidden rounded-full lg:flex">
          <PlusIcon className="mr-2 h-4 w-4" />
          <span>{t("profiles.create")}</span>
        </Button>
      </div>
      <div className="overflow-x-scroll bg-white p-4">
        <DataTable
          columns={columns}
          data={data}
          enableSelection={false}
          enableSearch={false}
          enablePagination={false}
          pageSize={10}
          totalRows={4}
          onStateChange={() => {}}
          getRowId={(row: any) => row.id}
        />
      </div>
    </div>
  );
}
