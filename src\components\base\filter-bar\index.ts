/**
 * Filter Bar System
 * ================
 *
 * A comprehensive filtering system that provides both desktop and mobile-friendly
 * interfaces for handling complex, interdependent filters.
 *
 * Key Features:
 * ------------
 * - Responsive design with modal view for mobile and bar view for desktop
 * - Support for dependent filters with automatic option fetching
 * - Error handling with retry mechanism
 * - Loading states and error propagation
 * - Group-based filter organization
 * - Customizable filter options and placeholders
 *
 * Core Components:
 * --------------
 * 1. FilterBar: Main component that renders either modal or bar view based on screen size
 * 2. FilterBarModal: Mobile-friendly modal interface for filters
 * 3. Combobox: Base dropdown component used for individual filters
 * 4. useFilterBar: Custom hook managing filter logic and state
 *
 * Usage Example:
 * ------------
 * ```tsx
 * import { FilterBar, FilterStep } from './filter-bar';
 *
 * const steps: FilterStep[] = [
 *   {
 *     type: 'filter',
 *     id: 'program',
 *     label: 'Program',
 *     options: [
 *       { value: 'prog1', label: 'Program 1' },
 *       { value: 'prog2', label: 'Program 2' },
 *     ],
 *   },
 *   {
 *     type: 'filter',
 *     id: 'course',
 *     label: 'Course',
 *     dependsOn: {
 *       program: { required: true },
 *     },
 *   },
 * ];
 *
 * function MyComponent() {
 *   return (
 *     <FilterBar
 *       steps={steps}
 *       onChange={(values) => console.log('Filter values:', values)}
 *       onOptionsNeeded={async (stepId, dependencies) => {
 *         // Fetch options based on dependencies
 *         return [];
 *       }}
 *     />
 *   );
 * }
 * ```
 *
 * Dependencies:
 * -----------
 * - The system relies on shadcn/ui components for the UI elements
 * - TailwindCSS for styling
 * - React hooks for state management
 *
 * Error Handling:
 * -------------
 * The system includes built-in error handling with:
 * - Automatic retries (up to 3 times) with exponential backoff
 * - Error propagation to dependent filters
 * - User-friendly error messages
 * - Loading states during option fetching
 *
 * State Management:
 * ---------------
 * The useFilterBar hook manages:
 * - Filter values
 * - Loading states
 * - Error states
 * - Option caching
 * - Dependency tracking
 *
 */

export { FilterBar } from "./FilterBar";
export { useFilterBar } from "./useFilterBar";
export type { FilterBarProps, FilterStep, FilterOption } from "./types";
