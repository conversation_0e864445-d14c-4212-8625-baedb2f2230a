import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsArray,
  ValidateNested,
  ValidateIf,
} from 'class-validator';
import { CommunityType, ProductCatalogEntityStatus } from '@enums';
import { Type } from 'class-transformer';

export class SpaceInfo {
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  slug: string;
}

export class CourseInfo {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}

export class CommunityInfoDTO {
  @IsString()
  @IsNotEmpty()
  @IsEnum(CommunityType)
  community: CommunityType;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SpaceInfo)
  spaces: SpaceInfo[];
}

export class CreateProductDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  internalName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  externalName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  productFamilyId: number;

  @ApiProperty({ enum: ProductCatalogEntityStatus })
  @IsEnum(ProductCatalogEntityStatus)
  @IsOptional()
  status: ProductCatalogEntityStatus;

  @ApiProperty()
  @IsOptional()
  @IsString()
  image?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  taxProfile?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isEvent?: boolean;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  eventYear?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  eventLocation?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isForever?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  withProductDelivery?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((dto) => Array.isArray(dto.lmsIds) && dto.lmsIds?.length > 0)
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CourseInfo)
  lmsIds?: CourseInfo[];

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((dto) => !hasEmptyCommunityIds(dto))
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CommunityInfoDTO)
  communityIds?: CommunityInfoDTO[];
}

export class UpdateProductDTO extends PartialType(CreateProductDTO) {
  @ApiPropertyOptional()
  @IsOptional()
  productFamilyId?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((dto) => Array.isArray(dto.lmsIds) && dto.lmsIds?.length > 0)
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CourseInfo)
  lmsIds?: CourseInfo[];

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((dto) => !hasEmptyCommunityIds(dto))
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CommunityInfoDTO)
  communityIds?: CommunityInfoDTO[];
}

/**
 * Check if the communityIds array is empty or has empty spaces
 * @param dto - The CreateProductDTO object
 * @returns true if the communityIds array is empty or has empty spaces, false otherwise
 */
function hasEmptyCommunityIds(dto: CreateProductDTO) {
  return (
    Array.isArray(dto?.communityIds) &&
    dto?.communityIds?.every((community) => community?.spaces?.length === 0)
  );
}
