import {
  startOfMonth,
  endOfMonth,
  startOfWeek,
  lastDayOfWeek,
  addDays,
  differenceInDays,
  eachDayOfInterval,
} from "date-fns";

/**
 * Generates an array of weeks, each containing a specified number of days,
 * starting from a given date. The weeks are split into groups of the given size.
 *
 * @param {Date} selectedDate - The starting date from which the weeks are calculated.
 * @param {number} size - The number of days in each week group (e.g., 7 for a standard week).
 * @returns {Date[][]} An array of weeks, where each week is an array of Date objects.
 */
export default function getWeekDays(selectedDate: Date, size: number): Date[][] {
  // Helper function to calculate weeks in a month
  function getMonthWeeks(date: Date, { forceSixWeeks = false } = {}) {
    // Get the first date of the month
    const monthFirstDate = startOfMonth(date);

    // Get the last date of the month
    const monthLastDate = endOfMonth(date);

    // If forceSixWeeks is true, adjust the start of the month to the beginning of the week
    // Otherwise, use the first date of the month
    const start = forceSixWeeks ? startOfWeek(monthFirstDate, { weekStartsOn: 1 }) : monthFirstDate;

    // If forceSixWeeks is true, adjust the end of the month to include the last day of the week
    // Otherwise, use the last date of the month
    let end = forceSixWeeks
      ? lastDayOfWeek(addDays(monthLastDate, 2)) // Shift the end by 2 days to make sure the last week is included
      : monthLastDate;

    // Calculate the total number of days between the start and end dates
    const totalOfDays = differenceInDays(end, start);

    // If the total days aren't equal to 41, adjust the end date to ensure there are exactly 6 weeks
    if (totalOfDays !== 41) {
      end = lastDayOfWeek(addDays(end, 2));
    }

    // Return each day within the interval (start to end), which gives all the days in the month
    return eachDayOfInterval({ start, end });
  }

  // Get all the days for the month, with forceSixWeeks set to true to ensure 6 weeks of data
  const days = getMonthWeeks(selectedDate, { forceSixWeeks: true }).map(
    (date: Date, index: number) => date,
  );

  // Helper function to group the days into weekly intervals based on the given size
  const weekly = (_month: any[], _size: number) =>
    _month.reduce(
      // Accumulate groups of days based on the provided size (7 days for a week)
      (a: any[], b: any[], index: number, group: any[]) =>
        !(index % _size) // Every `size` days, start a new group
          ? a.concat([group.slice(index, index + _size)]) // Add the new group to the result array
          : a, // If not, just continue
      [],
    );

  // Return the grouped weeks (days divided by the provided size)
  return weekly(days, size);
}
