"use client";

import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { format } from "date-fns";

export interface ChatMessageProps {
  content: string;
  timestamp: Date;
  sender: {
    name: string;
    avatar?: string;
  };
  isOwn?: boolean;
  status?: "sent" | "delivered" | "read";
  showAvatar?: boolean;
  showTimestamp?: boolean;
  isFirstInGroup?: boolean;
  isLastInGroup?: boolean;
}

export function ChatMessage({
  content,
  timestamp,
  sender,
  isOwn = false,
  status = "sent",
  showAvatar = true,
  showTimestamp = true,
  isFirstInGroup = true,
  isLastInGroup = true,
}: ChatMessageProps) {
  return (
    <div
      className={cn(
        "flex w-full gap-3",
        isOwn ? "flex-row-reverse" : "flex-row",
        !isFirstInGroup && !isLastInGroup && "my-0.5",
        isFirstInGroup ? "mt-3" : "mt-0.5",
        isLastInGroup ? "mb-3" : "mb-0.5",
      )}
    >
      <div className={cn("w-8 shrink-0", !showAvatar && "invisible")}>
        <Avatar className="h-8 w-8">
          {sender.avatar && <AvatarImage src={sender.avatar} alt={sender.name} />}
          <AvatarFallback>{sender.name[0]}</AvatarFallback>
        </Avatar>
      </div>
      <div className={cn("flex max-w-[70%] flex-col gap-1", isOwn ? "items-end" : "items-start")}>
        {isFirstInGroup && <div className="text-sm text-muted-foreground">{sender.name}</div>}
        <div
          style={{
            wordBreak: "break-word",
          }}
          className={cn(
            "break-words rounded-lg px-3 py-2",
            isOwn ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground",
            !isFirstInGroup && !isLastInGroup && "rounded-b-md rounded-t-md",
            isFirstInGroup && !isLastInGroup && "rounded-b-md",
            !isFirstInGroup && isLastInGroup && "rounded-t-md",
          )}
        >
          {content}
        </div>
        {isLastInGroup && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>{format(timestamp, "HH:mm")}</span>
            {isOwn && (
              <span className="text-xs">
                {status === "read" ? "✓✓" : status === "delivered" ? "✓✓" : "✓"}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
