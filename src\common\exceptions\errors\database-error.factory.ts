import { EntityNotFoundError, QueryFailedError } from 'typeorm';
import {
  DatabaseError,
  EntityNotFoundDbError,
  ConnectionFailedDbError,
  UniqueViolationDbError,
  ForeignKeyDbError,
  NotNullViolationDbError,
  CheckViolationDbError,
  IntegrityConstraintViolationDbError,
  SerializationFailureDbError,
  DeadlockDetectedDbError,
  InsufficientResourcesDbError,
  InvalidColumnReferenceDbError,
  SyntaxErrorDbError,
  DataExceptionDbError,
  InvalidTransactionStateDbError,
  QueryFailedDbError,
} from './database.errors';

export class DatabaseErrorFactory {
  /**
   * Main method to process TypeORM errors
   * @param error - The error to process
   * @returns A DatabaseError instance
   */
  static createFromError(error: Error): DatabaseError {
    if (error instanceof EntityNotFoundError) {
      const match = error.message.match(
        /Could not find any entity of type "([^"]+)"/,
      );
      const entityName = match ? match[1] : 'Unknown';
      return new EntityNotFoundDbError(entityName, undefined, error);
    }

    if (error instanceof QueryFailedError) {
      return this.handleQueryFailedError(error);
    }

    if (
      error.message.includes('ECONNREFUSED') ||
      error.message.includes('connection') ||
      error.message.includes('timeout')
    ) {
      return new ConnectionFailedDbError(error);
    }

    return new DatabaseError('Database operation failed', 500, error);
  }

  /**
   * Handles specific QueryFailedError cases based on PostgreSQL error codes
   * @param error - The QueryFailedError to process
   * @returns A DatabaseError instance
   */
  private static handleQueryFailedError(
    error: QueryFailedError,
  ): DatabaseError {
    const message = error.message || '';
    const driverError = error.driverError || {};
    const code = (driverError as { code?: string })?.code || '';
    const detail = (driverError as { detail?: string })?.detail || '';

    // Class 23 — Integrity Constraint Violation
    switch (code) {
      // Unique violations
      case '23505':
        const entityMatch = this.extractEntityFromError(message);
        const fieldMatch = this.extractFieldFromError(message);
        return new UniqueViolationDbError(
          entityMatch || 'record',
          fieldMatch || 'value',
          error,
        );

      // Foreign key violations
      case '23503':
        const fkEntityMatch = this.extractEntityFromError(message);
        const relationMatch = this.extractRelationFromError(message);
        return new ForeignKeyDbError(
          fkEntityMatch || 'record',
          relationMatch || 'relation',
          error,
        );

      // Not null violations
      case '23502':
        const nnEntityMatch = this.extractEntityFromError(message);
        const nnFieldMatch = this.extractFieldFromError(message);
        return new NotNullViolationDbError(
          nnEntityMatch || 'table',
          nnFieldMatch || 'column',
          error,
        );

      // Check constraint violations
      case '23514':
        const checkEntityMatch = this.extractEntityFromError(message);
        const constraintMatch = this.extractConstraintFromError(message);
        return new CheckViolationDbError(
          checkEntityMatch || 'table',
          constraintMatch || 'constraint',
          error,
        );

      // Generic integrity constraint violations
      case '23000':
        return new IntegrityConstraintViolationDbError(
          detail || message,
          error,
        );
    }

    // Class 40 — Transaction Rollback
    switch (code) {
      // Serialization failure
      case '40001':
        return new SerializationFailureDbError(error);

      // Deadlock detected
      case '40P01':
        return new DeadlockDetectedDbError(error);
    }

    // Class 53 — Insufficient Resources
    if (code.startsWith('53')) {
      return new InsufficientResourcesDbError(detail || message, error);
    }

    // Class 42 — Syntax Error or Access Rule Violation
    if (code.startsWith('42')) {
      // Column does not exist
      if (code === '42703') {
        const columnMatch = this.extractFieldFromError(message);
        return new InvalidColumnReferenceDbError(
          columnMatch || 'unknown column',
          error,
        );
      }

      // Generic syntax error
      return new SyntaxErrorDbError(detail || message, error);
    }

    // Class 22 — Data Exception
    if (code.startsWith('22')) {
      return new DataExceptionDbError(detail || message, error);
    }

    // Class 25 — Invalid Transaction State
    if (code.startsWith('25')) {
      return new InvalidTransactionStateDbError(detail || message, error);
    }

    // Handle any other query failures with a generic error
    return new QueryFailedDbError(error);
  }

  /**
   * Extracts the entity name from the error message
   * @param message - The error message
   * @returns The extracted entity name or null
   */
  private static extractEntityFromError(message: string): string | null {
    const tablePattern = /table "([^"]+)"/;
    const relationPattern = /relation "([^"]+)"/;
    const tableMatch = message.match(tablePattern);
    const relationMatch = message.match(relationPattern);

    return tableMatch ? tableMatch[1] : relationMatch ? relationMatch[1] : null;
  }

  /**
   * Extracts the field name from the error message
   * @param message - The error message
   * @returns The extracted field name or null
   */
  private static extractFieldFromError(message: string): string | null {
    const columnPattern = /column "([^"]+)"/;
    const keyPattern = /Key \(([^)]+)\)/;
    const detailPattern = /Detail: Key \(([^)]+)\)=/;
    const columnMatch = message.match(columnPattern);
    const keyMatch = message.match(keyPattern);
    const detailMatch = message.match(detailPattern);

    return columnMatch
      ? columnMatch[1]
      : keyMatch
      ? keyMatch[1]
      : detailMatch
      ? detailMatch[1]
      : null;
  }

  /**
   * Extracts the relation name from the error message
   * @param message - The error message
   * @returns The extracted relation name or null
   */
  private static extractRelationFromError(message: string): string | null {
    const foreignKeyPattern = /foreign key constraint "([^"]+)"/;
    const referencesPattern = /references "([^"]+)"/;
    const fkMatch = message.match(foreignKeyPattern);
    const refMatch = message.match(referencesPattern);

    return fkMatch ? fkMatch[1] : refMatch ? refMatch[1] : null;
  }

  /**
   * Extracts the constraint name from the error message
   * @param message - The error message
   * @returns The extracted constraint name or null
   */
  private static extractConstraintFromError(message: string): string | null {
    const constraintPattern = /constraint "([^"]+)"/;
    const checkPattern = /check constraint "([^"]+)"/;
    const constraintMatch = message.match(constraintPattern);
    const checkMatch = message.match(checkPattern);

    return checkMatch
      ? checkMatch[1]
      : constraintMatch
      ? constraintMatch[1]
      : null;
  }
}
