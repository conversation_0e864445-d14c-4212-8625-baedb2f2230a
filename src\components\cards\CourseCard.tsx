"use client";

import Link from "next/link";
import { useTranslations } from "next-intl";
import { ExternalLink } from "lucide-react";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import Button from "@/components/base/button";
import Image from "next/image";
import { DEFAULT_IMAGES } from "@/constants/images";

interface CourseCardProps {
  id: string;
  name: string;
  index?: number;
  className?: string;
}

export function CourseCard({ id, name, index, className }: CourseCardProps) {
  const t = useTranslations();

  // LearnWorlds base URL from environment or use a default
  const learnWorldsBaseUrl = process.env.NEXT_PUBLIC_LW_BASE_URL || "https://paradox-school.com";

  return (
    <Card
      className={cn(
        "bg-background-secondary relative flex aspect-square size-56 overflow-hidden rounded-xl border-none p-4 shadow-md transition-all hover:shadow-lg",
        className,
      )}
    >
      <Image
        src={DEFAULT_IMAGES.course}
        alt="Course image"
        width={224}
        height={224}
        className="absolute inset-0 size-full rounded-xl object-cover"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
      <div className="bg-background/60 absolute inset-0 z-1" />

      <div className={"z-2 flex size-full flex-col justify-between"}>
        {/* Course number badge */}
        {typeof index === "number" && (
          <div className="bg-burgundy flex h-8 w-8 items-center justify-center rounded-full font-medium text-white">
            {index + 1}
          </div>
        )}

        <div
          className={cn(
            "flex size-full flex-col justify-end text-center",
            typeof index !== "number" && "justify-between",
          )}
        >
          {typeof index !== "number" && <div className="h-8 w-8" />}
          <h3 className="mb-3 line-clamp-2 text-lg font-semibold text-white">{name}</h3>

          <Link href={`${learnWorldsBaseUrl}/path-player?courseid=${id}`} target="_blank">
            <Button className="w-full gap-2 font-medium">
              {t("product.modal.access-course")}
              <ExternalLink className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </Card>
  );
}
