"use client";

import { setDefaultOptions } from "date-fns";
import { enUS, fr } from "date-fns/locale";
import { create } from "zustand";

export type UserStore = {
  language: string;
  timezone: string;
  setLanguage: (language: UserStore["language"]) => void;
};

const useUserStore = create<UserStore>((set) => ({
  language: "",
  timezone: "",
  setLanguage: (language: UserStore["language"]) => {
    set({ language });
    const targetLocale = language === "en" ? enUS : fr;
    setDefaultOptions({ locale: targetLocale });
  },
}));

if (typeof window !== "undefined") {
  // @ts-ignore
  window.userStore = useUserStore;
}

export default useUserStore;
