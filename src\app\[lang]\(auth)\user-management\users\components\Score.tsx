"use client";

import { Badge } from "@/components/base/badge";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useMemo } from "react";

type ScoreProps = {
  score: number;
};

const MID_SCORE = 5;
const HIGH_SCORE = 6;

// CSS variables for better maintainability
const SCORE_COLORS = {
  high: "bg-[#296218]",
  mid: "bg-[#F59E0B]",
  low: "bg-[#B42318]",
} as const;

export default function Score({ score }: ScoreProps) {
  // Validate score range
  if (score < 0 || score > 10 || !Number.isInteger(score)) {
    throw new Error("Score must be an integer between 0 and 10");
  }

  const scoreIcon = useMemo(() => {
    if (score >= HIGH_SCORE) return <Smile className="h-4 w-4" />;
    if (score === MID_SCORE) return <Annoyed className="h-4 w-4" />;
    return <Frown className="h-4 w-4" />;
  }, [score]);

  const scoreColor = useMemo(() => {
    if (score >= HIGH_SCORE) return SCORE_COLORS.high;
    if (score === MID_SCORE) return SCORE_COLORS.mid;
    return SCORE_COLORS.low;
  }, [score]);

  return (
    <Badge className={`flex h-8 w-20 ${scoreColor}`}>
      <div className="flex items-center gap-1 p-2">
        {scoreIcon}
        <span className="ml-2">{score}/10</span>
      </div>
    </Badge>
  );
}
