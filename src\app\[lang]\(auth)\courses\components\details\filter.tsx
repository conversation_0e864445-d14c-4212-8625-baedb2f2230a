import { Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";

interface FilterProps {
  onCreateClick: () => void;
}

export function Filter({ onCreateClick }: FilterProps) {
  const t = useTranslations("cohorts");

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        {/* This space is reserved for additional filter controls if needed */}
      </div>
      <Button onClick={onCreateClick}>
        <Plus className="mr-2 h-4 w-4" />
        {t("create")}
      </Button>
    </div>
  );
}
