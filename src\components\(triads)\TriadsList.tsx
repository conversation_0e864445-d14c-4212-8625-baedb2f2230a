"use client";

import { useCallback, useEffect, useState, useMemo } from "react";
import { useTranslations, useLocale } from "next-intl";
import { useAuth } from "@clerk/nextjs";
import { motion, AnimatePresence } from "framer-motion";
import { AccessInfo } from "@/services/customers";
import { ITriadBase } from "@px-shared-account/hermes";
import { useTriadActions } from "@/hooks/useTriadActions";
import { useTriadDialogs } from "@/hooks/useTriadDialogs";
import { TriadListSkeleton } from "./shared/TriadSkeletons";
import { TriadCard } from "./TriadCard";

const ITEMS_PER_PAGE = 10;

interface TriadsListProps {
  triads: ITriadBase[];
  triadsReady: boolean;
  accessInfo: AccessInfo;
  userEmail: string;
  userId: string;
}

const listVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

export function TriadsList({ triads, triadsReady, accessInfo, userEmail }: TriadsListProps) {
  const t = useTranslations("triad");
  const locale = useLocale();
  const { userId } = useAuth();
  const [displayedTriads, setDisplayedTriads] = useState<ITriadBase[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);

  // Use the shared hooks for actions and dialogs
  const triadActions = useTriadActions();

  const {
    isTriadLoading,
    selectedTriadId,
    upcomingTriad,
    handleJoinAttempt,
    handleLeaveAttempt,
    handleDeleteAttempt,
    confirmJoin,
    confirmLeave,
    confirmDelete,
    cancelAction,
  } = triadActions;

  // Use the shared dialogs
  const triadDialogs = useTriadDialogs({
    upcomingTriad,
    locale,
    onJoinConfirm: confirmJoin,
    onLeaveConfirm: confirmLeave,
    onDeleteConfirm: confirmDelete,
    onCancel: cancelAction,
    isLoading: isTriadLoading,
    selectedTriadId,
  });

  // Derive a single isLoading state from multiple conditions
  const isLoading = useMemo(() => {
    return !initialLoadComplete || !triadsReady;
  }, [initialLoadComplete, triadsReady]);

  // Initialize with first page of data
  useEffect(() => {
    const initialTriads = triads.slice(0, ITEMS_PER_PAGE);
    setDisplayedTriads(initialTriads);
    setHasMore(triads.length > ITEMS_PER_PAGE);
    setPage(1);
    setInitialLoadComplete(true);
  }, [triads]);

  const loadMore = useCallback(() => {
    if (!hasMore || loading) return;

    setLoading(true);
    const start = page * ITEMS_PER_PAGE;
    const end = (page + 1) * ITEMS_PER_PAGE;
    const newTriads = triads.slice(start, end);

    if (end >= triads.length) {
      setHasMore(false);
    }

    setDisplayedTriads((prev) => {
      const existingIds = new Set(prev.map((t) => t.id));
      const uniqueNewTriads = newTriads.filter((t) => !existingIds.has(t.id));
      return [...prev, ...uniqueNewTriads];
    });

    setLoading(false);
  }, [triads, hasMore, loading, page]);

  // Load more data when page changes
  useEffect(() => {
    if (page > 1) {
      loadMore();
    }
  }, [page, loadMore]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !loading && hasMore) {
          setPage((prev) => prev + 1);
        }
      },
      { threshold: 1.0 },
    );

    const sentinel = document.getElementById("sentinel");
    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => observer.disconnect();
  }, [loading, hasMore]);

  const handleJoin = async (triadId: number) => {
    const result = await handleJoinAttempt(triadId);
    if (result.needsConfirmation) {
      triadDialogs.openJoinDialog();
    }
  };

  const handleLeave = (triadId: number) => {
    handleLeaveAttempt(triadId);
    triadDialogs.openLeaveDialog();
  };

  const handleDelete = (triadId: number, participantCount: number) => {
    const result = handleDeleteAttempt(triadId, participantCount);
    if (result.needsConfirmation) {
      triadDialogs.openDeleteDialog(participantCount);
    }
  };

  if (isLoading) {
    return <TriadListSkeleton />;
  }

  return (
    <>
      <motion.div
        variants={listVariants}
        initial="hidden"
        animate="visible"
        className="space-y-[1px]"
      >
        <AnimatePresence mode="wait">
          {displayedTriads.map((triad, index) => (
            <TriadCard
              key={triad.id}
              triad={triad}
              index={index}
              totalLength={displayedTriads.length}
              locale={locale}
              accessInfo={accessInfo}
              userId={userId || undefined}
              isLoading={isTriadLoading(triad.id)}
              onJoin={handleJoin}
              onLeave={handleLeave}
              onDelete={handleDelete}
            />
          ))}
        </AnimatePresence>

        {hasMore && <div id="sentinel" className="h-4" />}

        {loading && <div className="py-4 text-center">{t("list.loading")}</div>}
      </motion.div>

      {triadDialogs.dialogs}
    </>
  );
}
