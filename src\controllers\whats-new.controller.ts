import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Query,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  CreateWhatsNewDto,
  UpdateWhatsNewDto,
  GetWhatsNewByIdDto,
  GetReactionsDto,
  ToggleReactionDto,
  DeleteWhatsNewDto,
  UploadResponseDto,
} from '@dtos';
import { WhatsNewUseCases } from '@useCases';
import { CurrentUser, RequirePermissions } from 'src/auth/decorators';
import { JwtPermissionsGuard } from 'src/auth/guards';
import {
  Permissions,
  IMediaListResponse,
  IMediaDeleteResponse,
  IUploadResponse,
} from '@px-shared-account/hermes';
import { UserEntity } from '@entities';
import { CurrentUserInterceptor } from '@auth';
import { UploadedMulterFile } from '@types';

@ApiTags("What's New")
@ApiBearerAuth()
@Controller('whats-new')
@UseGuards(JwtPermissionsGuard)
export class WhatsNewController {
  constructor(private readonly whatsNewUseCases: WhatsNewUseCases) {}

  @Get()
  @RequirePermissions(Permissions.WhatsNew.READ)
  @ApiOperation({ summary: "List all What's New versions" })
  async listVersions() {
    return this.whatsNewUseCases.list();
  }

  @Post('upload')
  @HttpCode(HttpStatus.CREATED)
  @RequirePermissions(Permissions.WhatsNew.UPLOAD)
  @ApiOperation({ summary: "Upload an image for a What's New version" })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Image file to upload',
    schema: {
      type: 'object',
      properties: { file: { type: 'string', format: 'binary' } },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Image uploaded successfully',
    type: UploadResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request (e.g., no file, invalid file type)',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(
    @UploadedFile() file: UploadedMulterFile,
  ): Promise<IUploadResponse> {
    if (!file) {
      throw new Error('No file uploaded');
    }
    return this.whatsNewUseCases.uploadImage(file);
  }

  @Get('media')
  @RequirePermissions(Permissions.WhatsNew.UPLOAD)
  @ApiOperation({ summary: "List all media files for What's New" })
  @ApiQuery({
    name: 'prefix',
    required: false,
    description: 'Optional prefix to filter files',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Optional limit for number of files to return',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Media files retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden' })
  async listMedia(
    @Query('prefix') prefix?: string,
    @Query('limit') limit?: number,
  ): Promise<IMediaListResponse> {
    return this.whatsNewUseCases.listMedia(
      prefix,
      limit ? parseInt(limit.toString()) : undefined,
    );
  }

  @Delete('media/:pathname')
  @RequirePermissions(Permissions.WhatsNew.UPLOAD)
  @ApiOperation({ summary: 'Delete a media file' })
  @ApiParam({
    name: 'pathname',
    description: 'The pathname of the file to delete',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Media file deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request (e.g., invalid pathname)',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden' })
  async deleteMedia(
    @Param('pathname') pathname: string,
  ): Promise<IMediaDeleteResponse> {
    return this.whatsNewUseCases.deleteMedia(pathname);
  }

  @Get(':id')
  @RequirePermissions(Permissions.WhatsNew.READ)
  @ApiOperation({ summary: "Get a specific What's New version by ID" })
  async getVersion(@Param() params: GetWhatsNewByIdDto) {
    return this.whatsNewUseCases.getById(params.id);
  }

  @Post()
  @RequirePermissions(Permissions.WhatsNew.CREATE)
  @ApiOperation({ summary: "Create a new What's New version" })
  async createVersion(@Body() payload: CreateWhatsNewDto) {
    return this.whatsNewUseCases.create(payload);
  }

  @Patch(':id')
  @RequirePermissions(Permissions.WhatsNew.UPDATE)
  @ApiOperation({ summary: "Update an existing What's New version" })
  async updateVersion(
    @Param('id') id: number,
    @Body() payload: UpdateWhatsNewDto,
  ) {
    return this.whatsNewUseCases.update(id, payload);
  }

  @Delete(':id')
  @RequirePermissions(Permissions.WhatsNew.DELETE)
  @ApiOperation({ summary: "Delete a What's New version" })
  async deleteVersion(@Param() params: DeleteWhatsNewDto) {
    return this.whatsNewUseCases.delete(params.id);
  }

  @Get(':versionId/reactions')
  @RequirePermissions(Permissions.WhatsNew.READ)
  @ApiOperation({ summary: 'Get reactions for a specific version' })
  @UseInterceptors(CurrentUserInterceptor)
  async getReactions(
    @Param() params: GetReactionsDto,
    @CurrentUser() user: UserEntity,
  ) {
    return this.whatsNewUseCases.getReactions(params.versionId, user?.id);
  }

  @Post('reactions')
  @RequirePermissions(Permissions.WhatsNew.REACT)
  @ApiOperation({ summary: 'Toggle reaction for a version' })
  @UseInterceptors(CurrentUserInterceptor)
  async toggleReaction(
    @Body() payload: ToggleReactionDto,
    @CurrentUser() user: UserEntity,
  ) {
    return this.whatsNewUseCases.toggleReaction(payload, user?.id);
  }
}
