import { Modu<PERSON> } from '@nestjs/common';
import { ProductOfferFactoryService, ProductOfferUseCases } from '.';
import { ProductPlanUseCasesModule } from '../product-plan';
import { CheckoutPageUseCases } from '../checkout-pages';
import {
  RedisPublisher,
  RedisClientsService,
  RedisClientsModule,
} from '@services/event-publishers';
import { ChargebeeBillingModule } from '@services/billing';
import { DataServicesModule } from '@services/database';

@Module({
  imports: [
    DataServicesModule,
    ChargebeeBillingModule,
    ProductPlanUseCasesModule,
    RedisClientsModule,
  ],
  providers: [
    ProductOfferFactoryService,
    ProductOfferUseCases,
    CheckoutPageUseCases,
    {
      provide: RedisPublisher,
      useFactory: async (redisClientsService: RedisClientsService) => {
        const redisClient = await redisClientsService.getPxApiRedisClient();
        return new RedisPublisher(redisClient);
      },
      inject: [RedisClientsService],
    },
  ],
  exports: [ProductOfferFactoryService, ProductOfferUseCases],
})
export class ProductOfferUseCasesModule {}
