import { Injectable } from '@nestjs/common';
import { UpdateResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { ChargebeeBillingService } from '@services/billing';
import { PaymentMethod, TransactionStatus, TransactionType } from '@enums';
import { TransactionFactoryService } from '.';
import { ChargeBeeWebhookPayload, PXActionResult } from '@types';
import { IDataServices } from '@abstracts';
import { Transaction } from '@entities';

@Injectable()
export class TransactionUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly transactionFactory: TransactionFactoryService,
    private readonly chargebeeService: ChargebeeBillingService,
  ) {}

  /**
   * Finds a transaction or creates a new one if it doesn't exist
   * @param transaction Transaction entity
   * @returns Transaction item record from database
   */
  async findOrCreate(transaction: Transaction): Promise<Transaction> {
    const existingTransaction = await this.dataServices.transaction.getOneBy({
      chargebeeId: transaction.chargebeeId,
    });

    if (existingTransaction) {
      return existingTransaction;
    }

    return this.dataServices.transaction.create(transaction);
  }

  /**
   * Creates a transaction from a Chargebee event.
   *
   * @param chargebeeEventContent - The content of the Chargebee webhook payload.
   * @returns A Promise that resolves to the created Transaction.
   */
  async createOrUpdateFromChargebeeEvent(
    chargebeeEventContent: ChargeBeeWebhookPayload['content'],
    action: 'create' | 'update',
  ): Promise<PXActionResult & { transaction?: Transaction }> {
    try {
      const transaction = this.transactionFactory.generateFromChargebeeEvent(
        chargebeeEventContent.transaction,
      );

      const { first_name, last_name, email } =
        await this.chargebeeService.getCustomerInfo(transaction.customerId);
      transaction.customerEmail = email?.toLowerCase()?.trim();
      transaction.customerName = `${first_name} ${last_name}`;

      let success = false;
      let message = '';

      if (action === 'create') {
        const createResult = await this.findOrCreate(transaction);
        success = !!createResult?.id;
        message = success
          ? 'Transaction created successfully'
          : 'No Transaction created';
      } else {
        const updateResult = await this.update(
          transaction.chargebeeId,
          transaction,
        );
        success = !!updateResult?.affected;
        message = success
          ? 'Transaction updated successfully'
          : 'No Transaction updated';
      }

      return {
        data: { id: transaction.chargebeeId },
        success,
        message,
        transaction,
      };
    } catch (err) {
      return {
        data: { id: chargebeeEventContent?.transaction?.id },
        success: false,
        message: `Error occurred while updating transaction: ${err}`,
      };
    }
  }

  /**
   * Updates a transaction with the specified `chargebeeId`.
   *
   * @param chargebeeId - The `chargebeeId` of the transaction to update.
   * @param updates - The updates to apply to the transaction.
   * @returns A promise that resolves to the result of the update operation.
   */
  async update(
    chargebeeId: string,
    updates: QueryDeepPartialEntity<Transaction>,
  ): Promise<UpdateResult> {
    return this.dataServices.transaction.update(
      {
        chargebeeId,
      },
      updates,
    );
  }

  /**
   * Retrieves all transaction records matching provided filters
   * @param limit Number of `Transaction`s to fetch
   * @param page Page number
   */
  async list(
    limit?: number,
    page?: number,
  ): Promise<{ items: Transaction[]; total: number }> {
    return this.dataServices.transaction.getAll(limit, page, {
      createdAt: 'DESC',
    });
  }

  /**
   * Searches and returns a transaction record from database
   * @param id `id` of the Transaction to search for
   * @returns Transaction record from database OR null
   */
  async getOne(id: string): Promise<Transaction> {
    return this.dataServices.transaction.getOneBy({ chargebeeId: id });
  }

  /**
   * Searches for transactions based on the provided search criteria.
   *
   * @param paymentMethod - The payment method to filter transactions.
   * @param customer - The customer ID to filter transactions.
   * @param status - The transaction status to filter transactions.
   * @param limit - The maximum number of transactions to retrieve.
   * @param page - The page number of transactions to retrieve.
   * @param orderBy - The order in which to retrieve the transactions. Defaults to 'DESC'.
   * @returns A promise that resolves to an object containing the retrieved transactions and the total count.
   */
  async searchAll(
    searchQuery?: string,
    paymentMethod?: PaymentMethod,
    subscriptionId?: string,
    customer?: string,
    status?: TransactionStatus,
    type?: TransactionType,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: Transaction[]; total: number }> {
    const queryBuilder = this.dataServices.transaction
      .getRepository()
      .createQueryBuilder('transaction');

    if (searchQuery) {
      queryBuilder.where('transaction.customerEmail ILIKE :searchQuery', {
        searchQuery: `%${searchQuery}%`,
      });
      queryBuilder.orWhere('transaction.customerName ILIKE :searchQuery', {
        searchQuery: `%${searchQuery}%`,
      });
    }

    if (status) {
      queryBuilder.andWhere('transaction.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('transaction.type = :type', { type });
    }

    if (customer) {
      queryBuilder.andWhere('transaction.customerId = :customer', { customer });
    }

    if (subscriptionId) {
      queryBuilder.andWhere('transaction.subscriptionId = :subscriptionId', {
        subscriptionId,
      });
    }

    if (paymentMethod) {
      queryBuilder.andWhere('transaction.paymentMethod = :paymentMethod', {
        paymentMethod,
      });
    }

    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.orderBy('transaction.createdAt', orderBy || 'DESC');

    const [items, total] = await queryBuilder.getManyAndCount();
    return {
      items,
      total,
    };
  }
}
