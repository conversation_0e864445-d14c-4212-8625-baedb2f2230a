"use client";

import { ChatHeader } from "@/components/base/chat-header/ChatHeader";
import { ChatInput } from "@/components/base/chat-input/ChatInput";
import { ChatMessage } from "@/components/base/chat-message/ChatMessage";
import { TypingIndicator } from "@/components/base/chat-message/TypingIndicator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { useEffect, useRef, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";

export interface Message {
  id: string;
  content: string;
  timestamp: Date;
  sender: {
    id: string;
    name: string;
    avatar?: string;
  };
  status?: "sent" | "delivered" | "read";
}

export interface ChatConversationProps {
  title: string;
  avatar?: string;
  online?: boolean;
  messages: Message[];
  currentUserId: string;
  isTyping?: boolean;
  onSend: (message: string) => void;
  onAttachment?: (file: File) => void;
  onNewMessage?: () => void;
  className?: string;
  disabled?: boolean;
  chatListTrigger?: React.ReactNode;
}

const messageVariants = {
  initial: (isOwn: boolean) => ({
    opacity: 0,
    x: isOwn ? 10 : -10,
    scale: 0.98,
  }),
  animate: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      type: "tween",
      duration: 0.1,
      ease: "easeOut",
    },
  },
  exit: (isOwn: boolean) => ({
    opacity: 0,
    x: isOwn ? 10 : -10,
    transition: {
      duration: 0.1,
    },
  }),
};

export function ChatConversation({
  title,
  avatar,
  online,
  messages,
  currentUserId,
  isTyping,
  onSend,
  onAttachment,
  onNewMessage,
  className,
  disabled,
  chatListTrigger,
}: ChatConversationProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  const groupedMessages = useMemo(() => {
    return messages.map((message, index) => {
      const prevMessage = messages[index - 1];
      const nextMessage = messages[index + 1];

      const isFirstInGroup = !prevMessage || prevMessage.sender.id !== message.sender.id;
      const isLastInGroup = !nextMessage || nextMessage.sender.id !== message.sender.id;

      return {
        ...message,
        isFirstInGroup,
        isLastInGroup,
      };
    });
  }, [messages]);

  return (
    <div className={cn("flex h-full w-full flex-col justify-between", className)}>
      <ChatHeader
        title={title}
        avatar={avatar}
        online={online}
        onNewMessage={onNewMessage}
        beforeContent={chatListTrigger}
      />
      <ScrollArea className="flex-1">
        <motion.div className="flex flex-col p-4" initial={false} transition={{ duration: 0.1 }}>
          <AnimatePresence mode="popLayout">
            {groupedMessages.map((message) => (
              <motion.div
                key={message.id}
                custom={message.sender.id === currentUserId}
                variants={messageVariants}
                initial="initial"
                animate="animate"
                exit="exit"
                layout
                transition={{ duration: 0.1 }}
              >
                <ChatMessage
                  content={message.content}
                  timestamp={message.timestamp}
                  sender={message.sender}
                  isOwn={message.sender.id === currentUserId}
                  status={message.status}
                  showAvatar={message.isLastInGroup}
                  showTimestamp={message.isLastInGroup}
                  isFirstInGroup={message.isFirstInGroup}
                  isLastInGroup={message.isLastInGroup}
                />
              </motion.div>
            ))}
            {isTyping && (
              <motion.div
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 5 }}
                transition={{ duration: 0.1 }}
              >
                <TypingIndicator
                  sender={{
                    name: title,
                    avatar,
                  }}
                />
              </motion.div>
            )}
          </AnimatePresence>
          <div ref={messagesEndRef} />
        </motion.div>
      </ScrollArea>
      <ChatInput onSend={onSend} onAttachment={onAttachment} disabled={disabled} />
    </div>
  );
}
