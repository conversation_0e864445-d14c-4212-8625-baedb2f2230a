/**
 * Possible values for currencies
 */
export enum Currencies {
  USD = 'USD',
  EUR = 'EUR',
  AED = 'AED',
  GBP = 'GBP',
}

export enum ExchangeRateCurrencies {
  ZAR = 'ZAR',
  AUD = 'AUD',
  USD = 'USD',
  EUR = 'EUR',
  CAD = 'CAD',
  CHF = 'CHF',
  GBP = 'GBP',
  AED = 'AED',
}

export enum UserRoles {
  ADMIN = 'ADMIN',
  FINANCE = 'FINANCE',
  SALES = 'SALES',
}

export enum FiscalEntity {
  PG = 'PG',
  PI = 'PI',
  PP = 'PP',
}

export enum ProductCatalogEntityStatus {
  active = 'active',
  draft = 'draft',
}

export enum ChargeBeeItemStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

export enum PlanAndOfferStatus {
  ACTIVE = 'active',
  DISABLED = 'disabled',
}

export enum PaymentMethod {
  CARD = 'card',
  CHECK = 'check',
  CHARGEBACK = 'chargeback',
  BANK_TRANSFER = 'bank_transfer',
  APPLE_PAY = 'apple_pay',
  GOOGLE_PAY = 'google_pay',
}

export enum TransactionStatus {
  IN_PROGRESS = 'in_progress',
  SUCCESS = 'success',
  VOIDED = 'voided',
  FAILURE = 'failure',
  TIMEOUT = 'timeout',
  NEEDS_ATTENTION = 'needs_attention',
}

export enum TransactionType {
  AUTHORIZATION = 'authorization',
  PAYMENT = 'payment',
  REFUND = 'refund',
  PAYMENT_REVERSAL = 'payment_reversal',
}

export enum InvoiceStatus {
  PAID = 'paid',
  POSTED = 'posted',
  PAYMENT_DUE = 'payment_due',
  NOT_PAID = 'not_paid',
  VOIDED = 'voided',
  PENDING = 'pending',
}

export enum InvoicePriceType {
  TAX_EXCLUSIVE = 'tax_exclusive',
  TAX_INCLUSIVE = 'tax_inclusive',
}

export enum LineItemEntityType {
  ADHOC = 'adhoc',
  PLAN_ITEM_PRICE = 'plan_item_price',
  ADDON_ITEM_PRICE = 'addon_item_price',
  CHARGE_ITEM_PRICE = 'charge_item_price',
}

export enum LineItemPricingModel {
  FLAT_FEE = 'flat_fee',
  PER_UNIT = 'per_unit',
  TIERED = 'tiered',
  VOLUME = 'volume',
  STAIRSTEP = 'stairstep',
}

export enum CreditNoteStatus {
  ADJUSTED = 'adjusted',
  REFUNDED = 'refunded',
  REFUND_DUE = 'refund_due',
  VOIDED = 'voided',
}

export enum CreditNoteType {
  ADJUSTMENT = 'adjustment',
  REFUNDABLE = 'refundable',
}

export enum BankTransferStatus {
  MATCHED = 'matched',
  UNMATCHED = 'unmatched',
  IGNORED = 'ignored',
}

export enum SubscriptionStatus {
  FUTURE = 'future',
  IN_TRIAL = 'in_trial',
  ACTIVE = 'active',
  NON_RENEWING = 'non_renewing',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  TRANSFERRED = 'transferred',
}

export enum SubscriptionOrderStatus {
  PAID = 'paid',
  ACTIVE = 'active',
  REFUNDED = 'refunded',
  STOPPED = 'stopped',
  OVERDUE = 'overdue',
  IN_TRIAL = 'in trial',
}

export enum TrialEndAction {
  SITE_DEFAULT = 'site_default',
  PLAN_DEFAULT = 'plan_default',
  ACTIVATE_SUBSCRIPTION = 'activate_subscription',
  CANCEL_SUBSCRIPTION = 'cancel_subscription',
}

export enum SubscriptionCancelReason {
  NOT_PAID = 'not_paid',
  NO_CARD = 'no_card',
  FRAUD_REVIEW_FAILED = 'fraud_review_failed',
  NON_COMPLIANT_EU_CUSTOMER = 'non_compliant_eu_customer',
  TAX_CALCULATION_FAILED = 'tax_calculation_failed',
  CURRENCY_INCOMPATIBLE_WITH_GATEWAY = 'currency_incompatible_with_gateway',
  NON_COMPLIANT_CUSTOMER = 'non_compliant_customer',
}

export enum ChargeBeeChannel {
  WEB = 'web',
  APP_STORE = 'app_store',
  PLAY_STORE = 'play_store',
}

export enum ChargeBeeDurationPeriodUnit {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
}

export enum AccessTarget {
  LW_COURSE = 'lwCourse',
  CIRCLE_COMMUNITY = 'circleCommunity',
  CIRCLE_SPACE_GROUP = 'circleSpaceGroup',
  CIRCLE_SPACE = 'circleSpace',
  EXTERNAL_MEDIA = 'externalMedia',
}

export enum CustomerAccessType {
  LMS = 'lms',
  COMMUNITY = 'community',
}

export enum CustomerType {
  INTERNAL = 'internal',
  EXTERNAL = 'external',
  BETA_TESTER = 'beta_tester',
}

export enum CommunityType {
  PXL = 'PXL',
  PXS = 'PXS',
}

export enum WebhookStatus {
  PROCESSED = 'processed',
  FAILED = 'failed',
}
