import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { useTranslations } from "next-intl";

interface ExportProps {
  data: any;
  filename?: string;
}

export function ExportStats({ data, filename = "triads-stats" }: ExportProps) {
  const t = useTranslations("triad.stats");

  const downloadCSV = () => {
    // Convert data to CSV format
    const csvData = convertToCSV(data);

    // Create blob and download
    const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);

    link.setAttribute("href", url);
    link.setAttribute("download", `${filename}-${new Date().toISOString().split("T")[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className="bg-background gap-2 rounded-full border-[#2a2a2a] px-2 text-white transition-all duration-200 hover:bg-white hover:text-[#222222] md:px-3"
      onClick={downloadCSV}
    >
      <Download className="h-4 w-4" />
      <span className="hidden md:inline">{t("export")}</span>
    </Button>
  );
}

function convertToCSV(data: any): string {
  if (!data) return "";

  // Handle different data types
  if (Array.isArray(data)) {
    // If it's an array of objects
    if (data.length === 0) return "";
    const headers = Object.keys(data[0]);
    const rows = [
      headers.join(","),
      ...data.map((row) => headers.map((header) => JSON.stringify(row[header])).join(",")),
    ];
    return rows.join("\n");
  } else if (typeof data === "object") {
    // If it's a single object
    const entries = Object.entries(data);
    if (entries.length === 0) return "";

    // Handle nested objects/arrays
    const rows = entries.map(([key, value]) => {
      if (typeof value === "object" && value !== null) {
        return `${key},${JSON.stringify(value)}`;
      }
      return `${key},${value}`;
    });

    return rows.join("\n");
  }

  return String(data);
}
