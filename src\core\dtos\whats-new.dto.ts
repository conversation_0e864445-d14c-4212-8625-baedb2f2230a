import { IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { createZodDto } from 'nestjs-zod';
import {
  CreateWhatsNewPayloadSchema,
  UpdateWhatsNewPayloadSchema,
  WhatsNewVersionSchema,
  WhatsNewListResponseSchema,
  ToggleReactionPayloadSchema,
  ToggleReactionResponseSchema,
  ReactionsResponseSchema,
  UploadResponseSchema,
} from '@px-shared-account/hermes';

// Main DTOs from Hermes schemas
export class CreateWhatsNewDto extends createZodDto(
  CreateWhatsNewPayloadSchema,
) {}
export class UpdateWhatsNewDto extends createZodDto(
  UpdateWhatsNewPayloadSchema,
) {}
export class WhatsNewVersionDto extends createZodDto(WhatsNewVersionSchema) {}
export class WhatsNewListResponseDto extends createZodDto(
  WhatsNewListResponseSchema,
) {}
export class ToggleReactionDto extends createZodDto(
  ToggleReactionPayloadSchema,
) {}
export class ToggleReactionResponseDto extends createZodDto(
  ToggleReactionResponseSchema,
) {}
export class ReactionsResponseDto extends createZodDto(
  ReactionsResponseSchema,
) {}
export class UploadResponseDto extends createZodDto(UploadResponseSchema) {}

// Parameter DTOs for API endpoints
export class GetWhatsNewByIdDto {
  @Type(() => Number)
  @IsNumber()
  id: number;
}

export class GetReactionsDto {
  @Type(() => Number)
  @IsNumber()
  versionId: number;
}

export class DeleteWhatsNewDto {
  @Type(() => Number)
  @IsNumber()
  id: number;
}
