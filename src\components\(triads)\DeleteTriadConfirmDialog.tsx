"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

interface DeleteTriadConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
  participantCount: number;
  loading?: boolean;
}

export function DeleteTriadConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  participantCount,
  loading,
}: DeleteTriadConfirmDialogProps) {
  const t = useTranslations("triad");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="rounded-3xl border-none bg-[#111111] text-white sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-xl">{t("confirm_delete.title")}</DialogTitle>
          <div className="space-y-2 text-center text-gray-400">
            <p>
              {t("confirm_delete.description", {
                count: participantCount,
              })}
            </p>
            <p className="mx-auto mt-4 max-w-[90%] text-sm break-words italic">
              {t("confirm_delete.note")}
            </p>
          </div>
        </DialogHeader>
        <DialogFooter className="flex flex-row gap-4 sm:gap-4">
          <Button
            variant="outline"
            onClick={onCancel}
            className="flex-1 rounded-full border-white bg-transparent text-white hover:bg-white hover:text-black"
          >
            {t("confirm_delete.cancel")}
          </Button>
          <Button
            onClick={onConfirm}
            disabled={loading}
            className="flex-1 rounded-full bg-red-500 text-white hover:bg-red-600"
          >
            {loading ? t("list.loading") : t("confirm_delete.confirm")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
