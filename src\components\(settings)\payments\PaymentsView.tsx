"use client";

import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";

import { useBreakpoint } from "@/lib/hooks/tailwind-breakpoints";
import PaymentsTable from "./PaymentsTable";
import PaymentsList from "./PaymentsList";
import { BadgeProps } from "@/components/ui/badge";
import { usePaymentsList } from "@/services/payments";

function getStatusBadgeVariant(status: string): BadgeProps["variant"] {
  switch (status) {
    case "active":
    case "paid":
      return "success";
    case "stopped":
      return "destructive";
    case "refunded":
    case "overdue":
      return "warning";
    default:
      return "secondary";
  }
}

export default function PaymentsView() {
  const t = useTranslations("payments");
  const isDesktop = useBreakpoint("md");
  const PAGE_SIZE = 10;
  const [page, setPage] = useState(1);
  const [payments, setPayments] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const { data, isLoading } = usePaymentsList(page, PAGE_SIZE);

  useEffect(() => {
    if (data?.items) {
      setPayments((prev) => {
        // If page is 1, reset; otherwise, append
        if (page === 1) return data.items;
        // Avoid duplicates
        const ids = new Set(prev.map((p) => p.id));
        return [...prev, ...data.items.filter((item: any) => !ids.has(item.id))];
      });
      setHasMore(data.items.length === PAGE_SIZE);
    } else if (page === 1) {
      setPayments([]);
      setHasMore(false);
    }
  }, [data, page]);

  const handleLoadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      setPage((prev) => prev + 1);
    }
  }, [isLoading, hasMore]);

  if (isLoading && payments.length === 0) {
    return <div className="text-muted-foreground p-4 text-center">{t("loading")}</div>;
  }

  if (!payments.length) {
    return <div className="text-muted-foreground p-4 text-center">{t("errors.no-results")}</div>;
  }

  if (isDesktop) {
    return (
      <PaymentsTable
        payments={payments}
        isLoading={isLoading}
        getStatusBadgeVariant={getStatusBadgeVariant}
      />
    );
  } else {
    return (
      <PaymentsList
        payments={payments}
        isLoading={isLoading}
        hasMore={hasMore}
        onLoadMore={handleLoadMore}
        getStatusBadgeVariant={getStatusBadgeVariant}
      />
    );
  }
}
