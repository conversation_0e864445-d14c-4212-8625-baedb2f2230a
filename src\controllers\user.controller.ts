import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  BulkUpdateRoleDto,
  CreateUserDto,
  ListUsersDTO,
  UpdateUserDto,
  ListOSUsersDTO,
  GetOSUserByEmailDTO,
} from '@dtos';
import { UserUseCases } from '@useCases';
import { RequirePermissions } from 'src/auth/decorators';
import { JwtPermissionsGuard } from 'src/auth/guards';
import { Permissions } from '@px-shared-account/hermes';
import { ApiOperation } from '@nestjs/swagger';
import { UserAccessDetailsResponse } from '@types';

@Controller('users')
@UseGuards(JwtPermissionsGuard)
export class UserController {
  constructor(private readonly userUseCases: UserUseCases) {}

  @ApiOperation({ summary: 'List all customers for PX OS' })
  @Get('/list-for-os')
  async listForOS(@Query() query: ListOSUsersDTO) {
    return this.userUseCases.listForOS(
      query.query,
      query.fiscalEntity,
      query.type,
      query.limit,
      query.page,
      query.orderBy,
    );
  }

  @Get('/list')
  @RequirePermissions(Permissions.User.READ)
  async listUsers(@Query() query: ListUsersDTO) {
    return this.userUseCases.search(
      query.search,
      query.role,
      query.limit,
      query.page,
    );
  }

  @ApiOperation({ summary: 'Get a customer by `email`' })
  @Get('/email/:email')
  async getOSUserByEmail(@Param() params: GetOSUserByEmailDTO) {
    return this.userUseCases.getByEmail(params.email);
  }

  @ApiOperation({
    summary:
      'Get combined access flags and purchase details for a customer by email',
  })
  @Get('/:email/access-details')
  async getAccessDetails(
    @Param() params: GetOSUserByEmailDTO,
  ): Promise<UserAccessDetailsResponse> {
    return this.userUseCases.getUserAccessDetails(params.email);
  }

  @Post('/')
  @RequirePermissions(Permissions.User.CREATE)
  async createUser(@Body() payload: CreateUserDto) {
    return this.userUseCases.create(payload);
  }

  @Get('/get-by-id/:id')
  @RequirePermissions(Permissions.User.READ)
  async getUser(@Param() params: { id: number }) {
    return this.userUseCases.getById(params.id);
  }

  /*
   ** No permission required for this endpoint YET.
   ** We need to implement something to imitate RLS (Row Level Security) to allow only the user to access its own data.
   ** OR, we could create a dedicated endpoint for this, like `/me`
   ** that returns the current user's data, and protected with a `user:me` permission.
   */
  @Get('/get-by-sso-id/:id')
  async getUserBySSOId(@Param() params: { id: string }) {
    return this.userUseCases.getBySSOId(params.id);
  }

  @Post('/bulk-update')
  @RequirePermissions(Permissions.User.UPDATE)
  async bulkUpdateUsers(@Body() body: BulkUpdateRoleDto) {
    return this.userUseCases.bulkRoleUpdate(body);
  }

  @Patch('/:email')
  @RequirePermissions(Permissions.User.UPDATE)
  async updateUserByEmail(
    @Param('email') email: string,
    @Body() body: UpdateUserDto,
  ) {
    return this.userUseCases.updateUsingEmail(email, body);
  }

  @Patch('/:id')
  @RequirePermissions(Permissions.User.UPDATE)
  async updateUserById(
    @Param() params: { id: number },
    @Body() body: UpdateUserDto,
  ) {
    return this.userUseCases.updateUsingId(params.id, body);
  }

  @Post('/:id/phone-number')
  @RequirePermissions(Permissions.User.UPDATE)
  async createPhoneNumber(
    @Param() params: { id: string },
    @Body() body: { phoneNumber: string },
  ) {
    return this.userUseCases.createPhoneNumber(params.id, body.phoneNumber);
  }

  @Post('/:id/phone-number/unlink')
  @RequirePermissions(Permissions.User.UPDATE)
  async unlinkPhoneNumber(
    @Param() params: { id: string },
    @Body() body: { phoneNumberId: string },
  ) {
    return this.userUseCases.unlinkPhoneNumber(params.id, body.phoneNumberId);
  }
}
