"use client";

/**
 * A checkbox component specifically designed for table headers and rows
 * Supports indeterminate state for header checkboxes when some but not all rows are selected
 * Renders as either a th or td element based on its position in the table
 */

import * as React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";

interface TableCheckboxProps {
  /** Whether the checkbox is checked */
  checked?: boolean;
  /** Whether the checkbox should show an indeterminate state */
  indeterminate?: boolean;
  /** Callback when the checkbox state changes */
  onCheckedChange?: (checked: boolean) => void;
  /** Whether this checkbox is in a table header */
  isHeader?: boolean;
}

export function TableCheckbox({
  checked,
  indeterminate,
  onCheckedChange,
  isHeader = false,
}: TableCheckboxProps) {
  const checkboxRef = React.useRef<React.ElementRef<typeof CheckboxPrimitive.Root>>(null);
  const CellComponent = isHeader ? "th" : "td";

  React.useEffect(() => {
    if (checkboxRef.current) {
      (checkboxRef.current as any).indeterminate = indeterminate ?? false;
    }
  }, [indeterminate]);

  return (
    <CellComponent
      className={cn("h-12 w-12", isHeader && "px-2 text-xs font-medium text-muted-foreground")}
    >
      <div className="flex h-full items-center justify-center">
        <Checkbox ref={checkboxRef} checked={checked} onCheckedChange={onCheckedChange} />
      </div>
    </CellComponent>
  );
}
