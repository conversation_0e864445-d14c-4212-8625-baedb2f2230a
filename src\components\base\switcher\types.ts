import { LucideIcon } from "lucide-react";
import * as SwitchPrimitive from "@radix-ui/react-switch";

export interface SwitcherProps extends React.ComponentPropsWithoutRef<typeof SwitchPrimitive.Root> {
  activeIcon?: LucideIcon;
  inactiveIcon?: LucideIcon;
  label?: string;
  description?: string;
}

export interface UseSwitcherProps {
  defaultChecked?: boolean;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
}
