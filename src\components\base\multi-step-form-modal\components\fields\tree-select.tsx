"use client";

import * as React from "react";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";
import TreeView, { TreeViewItem } from "@/components/tree-view";
import type { TreeSelectField as TreeSelectFieldType } from "../../types";

interface ExtendedTreeViewItem extends TreeViewItem {
  [key: string]: any; // Allow any additional properties
}

export const TreeSelectField = ({ field }: { field: TreeSelectFieldType }) => {
  const { setValue, watch, getValues } = useFormContext();
  // Get the current value from the form
  const value = watch(field.id);
  // Also get the raw value directly to ensure we have the latest data
  const rawValue = getValues(field.id);
  // Use the value from watch or getValues, whichever is more complete
  const effectiveValue = value || rawValue;

  const t = useTranslations("components.tree-view");

  // Process tree items to apply checked state
  const applyCheckedState = (
    items: ExtendedTreeViewItem[],
    formValue: any,
  ): ExtendedTreeViewItem[] => {
    if (!items) return [];
    if (!formValue) return items;

    // Ensure formValue has the expected structure
    const safeFormValue = {
      PXS: formValue.PXS || [],
      PXL: formValue.PXL || [],
    };

    // Function to check if an item should be checked based on form value
    const isItemChecked = (item: ExtendedTreeViewItem) => {
      // Custom logic for checking items is implemented in the parent component
      // through the formValue structure
      if (item.type === "file") {
        // For file items, check if they're in PXS or PXL arrays
        const inPXS = safeFormValue.PXS?.some((space: any) => {
          const spaceId = typeof space === "object" ? Number(space.id) : Number(space);
          return spaceId === Number(item.id);
        });

        const inPXL = safeFormValue.PXL?.some((space: any) => {
          const spaceId = typeof space === "object" ? Number(space.id) : Number(space);
          return spaceId === Number(item.id);
        });

        // Return true if the item is in either PXS or PXL
        return inPXS || inPXL;
      }
      return item.checked || false;
    };

    // Process each item and its children recursively
    return items.map((item) => {
      // Create a new item with the checked state
      const newItem = {
        ...item,
        checked: isItemChecked(item),
      };

      // Process children if they exist
      if (item.children && item.children.length > 0) {
        // Process children recursively
        const processedChildren = applyCheckedState(item.children, safeFormValue);

        // A folder is checked if any of its children are checked
        const hasCheckedChildren = processedChildren.some((child) => child.checked);

        // Update the item's checked state based on its children
        newItem.checked = newItem.checked || hasCheckedChildren;
        newItem.children = processedChildren;
      }

      return newItem;
    });
  };

  // Function to update form value when items are checked/unchecked
  const handleCheckChange = (item: ExtendedTreeViewItem, checked: boolean) => {
    // Call the field's onChange handler for custom processing
    if (field.onChange) {
      // Get current form value to pass to the onChange handler
      const formValue = effectiveValue || { PXS: [], PXL: [] };

      // Pass the current form value to the onChange handler
      const updatedValue = field.onChange({ item, checked, value: formValue });

      // Set the updated value and trigger validation
      setValue(field.id, updatedValue, { shouldValidate: true });
    }
  };

  // Pass translations to the TreeView component
  const checkboxLabels = {
    check: t("checkbox_labels.check"),
    uncheck: t("checkbox_labels.uncheck"),
  };

  // Memoize the processed data to prevent unnecessary recalculations
  const processedData = React.useMemo(() => {
    return applyCheckedState(field.data as ExtendedTreeViewItem[], effectiveValue);
  }, [field.data, effectiveValue]);

  return (
    <TreeView
      data={processedData}
      showCheckboxes
      onCheckChange={handleCheckChange}
      className={field.meta?.className}
      searchPlaceholder={t("search_placeholder")}
      selectionText={t("selection_text")}
      checkboxLabels={checkboxLabels}
    />
  );
};

TreeSelectField.displayName = "TreeSelectField";
