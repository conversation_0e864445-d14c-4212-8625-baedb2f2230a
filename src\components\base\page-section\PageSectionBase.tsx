"use client";

import { cn } from "@/lib/utils";
import type { BaseProps } from "@/types";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { useTranslations } from "next-intl";
import React, { Children } from "react"; // Removed useState, useEffect

// Props for the Base PageSection, without carousel-specific ones
export interface PageSectionBaseProps extends BaseProps {
  title?: string;
  description?: string | React.ReactNode;
  actions?: React.ReactNode;
  actionButtonLabel?: string;
  actionButtonIcon?: React.ElementType;
  onActionButtonClick?: () => void;
  contentClassName?: string;
}

export const PageSectionBase = ({
  title,
  description,
  actions,
  actionButtonLabel,
  actionButtonIcon: ActionButtonIcon = ArrowRight,
  onActionButtonClick,
  children,
  className,
  contentClassName,
}: PageSectionBaseProps) => {
  const t = useTranslations("dashboard.purchases");

  const renderActions = () => {
    if (actions) {
      return <div className="flex items-center gap-2">{actions}</div>;
    }
    if (actionButtonLabel) {
      return (
        <>
          {/* Desktop: full label */}
          <Button
            variant="outline"
            onClick={onActionButtonClick || (() => console.log("PageSection action clicked"))}
            className="bg-background/40 hidden px-4 text-base sm:inline-flex"
          >
            {actionButtonLabel}
            <ActionButtonIcon className="h-4 w-4" />
          </Button>
          {/* Mobile: short label */}
          <Button
            variant="outline"
            onClick={onActionButtonClick || (() => console.log("PageSection action clicked"))}
            className="bg-background/40 inline-flex px-4 text-base sm:hidden"
          >
            {t("see-more")}
            <ActionButtonIcon className="h-4 w-4" />
          </Button>
        </>
      );
    }
    return null;
  };

  return (
    <section className={cn("flex flex-col space-y-6", className)}>
      {/* Header */}
      <div
        className={cn(
          "flex gap-1.5 sm:flex-row sm:items-center sm:justify-between",
          actions || actionButtonLabel ? "flex-row items-center justify-between" : "flex-col",
        )}
      >
        <div className="space-y-1">
          {title && <h2 className="text-xl font-semibold tracking-tight">{title}</h2>}
          {description && <p className="text-muted-foreground text-base">{description}</p>}
        </div>
        {renderActions()}
      </div>

      {/* Content - Standard rendering */}
      {children && (
        <div className={cn("flex min-w-0 flex-1 flex-col", contentClassName)}>{children}</div>
      )}
    </section>
  );
};

PageSectionBase.displayName = "PageSectionBase";
