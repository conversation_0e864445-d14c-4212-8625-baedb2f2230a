"use client";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";

type CourseTabsProps = {
  activeTab: string;
};

export function CourseTabs({ activeTab }: CourseTabsProps) {
  const t = useTranslations("courses.details");
  const router = useRouter();
  const searchParams = useSearchParams();

  const onTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("tab", value);
    router.push(`?${params.toString()}`);
  };

  const tabTriggerStyles = cn(
    "relative h-10 rounded-none border-b-[3px] border-b-transparent data-[state=active]:border-b-primary data-[state=active]:shadow-none",
    "px-4 pb-3 pt-2 font-medium text-muted-foreground data-[state=active]:text-foreground",
  );

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full overflow-auto">
      <TabsList className="h-auto w-full justify-start border-b bg-transparent p-0">
        <TabsTrigger value="information" className={tabTriggerStyles}>
          {t("tabs.information")}
        </TabsTrigger>
        <TabsTrigger value="students" className={tabTriggerStyles}>
          {t("tabs.students")}
        </TabsTrigger>
        <TabsTrigger value="offers" className={tabTriggerStyles}>
          {t("tabs.offers")}
        </TabsTrigger>
        <TabsTrigger value="cohorts" className={tabTriggerStyles}>
          {t("tabs.cohorts")}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
