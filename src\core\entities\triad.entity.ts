import { Column } from 'typeorm';
import { BaseEntity } from './base.entity';
import { TriadParticipantEntity } from './triad-participant.entity';
import { UserEntity } from './user.entity';
import { SessionType } from '@px-shared-account/hermes';

export class TriadEntity extends BaseEntity {
  title: string;
  organizer: UserEntity;
  participants: TriadParticipantEntity[];
  @Column({ type: 'timestamptz' })
  sessionTime: Date;
  duration: number;
  meetingLink: string;
  maxParticipants: number;
  sessions: string[];
  sessionType: SessionType;
}
