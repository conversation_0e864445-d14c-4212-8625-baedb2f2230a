"use client";

import { ChatContainer } from "@/components/composite/chat/ChatContainer";
import { useMessages } from "@/hooks/use-messages";
import { toast } from "sonner";

export function MessagesContainer() {
  const { messages, conversations, typingStates, sendMessage, createConversation } = useMessages();

  const handleSend = (conversationId: string, message: string) => {
    sendMessage(conversationId, message);
  };

  const handleAttachment = (conversationId: string, file: File) => {
    toast.info(`File "${file.name}" attached to conversation ${conversationId}`);
  };

  const handleNewMessage = () => {
    // This is just a demo implementation
    const names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
    const randomName = names[Math.floor(Math.random() * names.length)];
    const newConv = createConversation(
      randomName,
      `https://api.dicebear.com/7.x/avataaars/svg?seed=${randomName}`,
    );
    toast.success(`Started a new conversation with ${newConv.title}`);
  };

  return (
    <div className="h-full">
      <ChatContainer
        currentUserId="user-1"
        conversations={conversations}
        messages={messages}
        typingStates={typingStates}
        onSend={handleSend}
        onAttachment={handleAttachment}
        onNewMessage={handleNewMessage}
      />
    </div>
  );
}
