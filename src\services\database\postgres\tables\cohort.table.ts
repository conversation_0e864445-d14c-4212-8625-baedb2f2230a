import { CohortEntity } from '@entities';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CourseTable } from './course.table';
import { CohortStatus, ICommunityInfo } from '@px-shared-account/hermes';
import { UserTable } from './user.table';

@Entity({
  name: 'cohorts',
})
export class CohortTable implements CohortEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'varchar', length: 1500 })
  description: string;

  @Column({ type: 'integer' })
  maxParticipants: number;

  @Column({ type: 'integer', nullable: true })
  currentParticipants: number;

  @Column({ type: 'timestamp' })
  startDate: Date;

  @Column({ type: 'timestamp' })
  endDate: Date;

  @Column({ type: 'enum', enum: CohortStatus, default: CohortStatus.ACTIVE })
  status: CohortStatus;

  @ManyToOne(() => CourseTable, (course) => course.cohorts)
  course: CourseTable;

  @ManyToMany(() => UserTable, (user) => user.cohorts)
  @JoinTable({
    name: 'Cohort_Students',
    joinColumn: {
      name: 'cohortId',
      foreignKeyConstraintName: 'FK_Cohort_Students_cohortId',
    },
    inverseJoinColumn: {
      name: 'userId',
      foreignKeyConstraintName: 'FK_Cohort_Students_userId',
    },
  })
  students: UserTable[];

  @Column({ type: 'jsonb', nullable: true })
  communityInfo: ICommunityInfo;

  @CreateDateColumn({ default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}
