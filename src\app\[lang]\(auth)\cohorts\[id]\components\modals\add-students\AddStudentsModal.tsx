"use client";

import { useTranslations } from "next-intl";
import { useMemo } from "react";
import { useCohortStudents, useCohortDetails } from "@/services/cohort";
import { useUserList } from "@/services/user";
import { useRoleList } from "@/services/role";
import { MultiStepFormModal } from "@/components/base/multi-step-form-modal";
import { ModalConfig } from "@/components/base/multi-step-form-modal/types";
import { IUserBase, UpdateCohortStudentsSchema, RoleGroup } from "@px-shared-account/hermes";
import { Badge } from "@/components/ui/badge";
import { z } from "zod";
import { toast } from "sonner";

interface AddStudentsModalProps {
  cohortId: number;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onComplete?: () => void;
}

export function AddStudentsModal({
  cohortId,
  isOpen,
  onOpenChange,
  onComplete,
}: AddStudentsModalProps) {
  const t = useTranslations("cohorts");
  const { addStudents, isAddingStudents } = useCohortStudents(cohortId);

  // Get the current cohort details to access the list of students already in the cohort
  const { data: cohortData, isLoading: isLoadingCohort } = useCohortDetails(cohortId);

  // Get the IDs of students already in the cohort
  const existingStudentIds = useMemo(() => {
    return cohortData?.students?.map((student) => student.id) || [];
  }, [cohortData?.students]);

  // First, get the student role ID
  const { data: rolesData } = useRoleList({
    group: RoleGroup.STUDENT,
    limit: 1,
  });

  const studentRoleId = rolesData?.data?.[0]?.id;

  // Then, use the role ID to fetch users
  const { data: usersData, isLoading: isLoadingUsers } = useUserList({
    role: studentRoleId,
    limit: 100, // Adjust this value based on your needs
  });

  // Filter out students who are already in the cohort
  const studentOptions = useMemo(
    () =>
      usersData?.data
        ?.filter((student: IUserBase) => !existingStudentIds.includes(student.id))
        .map((student: IUserBase) => ({
          value: student.id.toString(),
          label: `${student.firstName || ""} ${student.lastName || ""}`,
          render: () => (
            <div className="flex w-full items-center justify-between">
              <span>{`${student.firstName || ""} ${student.lastName || ""}`}</span>
              <Badge variant="secondary" className="ml-2">
                {student.role.name}
              </Badge>
            </div>
          ),
        })) || [],
    [usersData?.data, existingStudentIds],
  );

  // Create a custom validation schema that checks if adding the selected students would exceed the max participants
  const customStudentIdsValidation = useMemo(() => {
    const currentStudentCount = existingStudentIds.length;
    const maxParticipants = cohortData?.maxParticipants || 0;
    const remainingSlots = maxParticipants - currentStudentCount;

    return z.array(z.string()).refine((studentIds) => studentIds.length <= remainingSlots, {
      message: t("students.max_participants_exceeded", {
        max: maxParticipants,
      }),
    });
  }, [cohortData?.maxParticipants, existingStudentIds.length, t]);

  const config = useMemo<ModalConfig>(
    () => ({
      key: "add-students",
      title: t("students.add_title"),
      confirmClose: true,
      confirmReset: true,
      submitButtons: [
        {
          label: t("students.add_submit"),
          variant: "default",
          className: "bg-burgundy hover:bg-burgundy/90",
          isPrimary: true,
          isLoading: isAddingStudents,
        },
      ],
      steps: [
        {
          id: "students",
          title: t("students.add_title"),
          fields: [
            {
              id: "studentIds",
              type: "combobox",
              label: t("students.select_label"),
              required: true,
              placeholder: t("students.select_placeholder"),
              description: cohortData
                ? t("students.select_description_with_limit", {
                    current: existingStudentIds.length,
                    max: cohortData.maxParticipants,
                    remaining: cohortData.maxParticipants - existingStudentIds.length,
                  })
                : t("students.select_description"),
              validation: customStudentIdsValidation,
              options: studentOptions,
              isMulti: true,
              isLoading: isLoadingUsers || isLoadingCohort || !studentRoleId,
            },
          ],
        },
      ],
    }),
    [
      t,
      isAddingStudents,
      studentOptions,
      isLoadingUsers,
      isLoadingCohort,
      studentRoleId,
      customStudentIdsValidation,
    ],
  );

  const handleComplete = async (data: { studentIds: string[] }) => {
    try {
      // Double-check validation on the client side before sending the request
      const currentStudentCount = existingStudentIds.length;
      const maxParticipants = cohortData?.maxParticipants || 0;
      const remainingSlots = maxParticipants - currentStudentCount;

      if (data.studentIds.length > remainingSlots) {
        toast.error(t("students.error_title"), {
          description: t("students.max_participants_exceeded", {
            max: maxParticipants,
          }),
        });
        return;
      }

      await addStudents(cohortId, data.studentIds.map(Number));

      // Trigger the onComplete callback to refresh the parent component
      if (onComplete) {
        onComplete();
      }

      // Close the modal
      if (onOpenChange) {
        onOpenChange(false);
      }
    } catch (error) {
      console.error("[AddStudentsModal] Error adding students:", error);
      toast.error(t("students.error_title"), {
        description: t("students.error_description"),
      });
    }
  };

  return (
    <MultiStepFormModal
      config={config}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      events={{
        onComplete: handleComplete,
      }}
    />
  );
}
