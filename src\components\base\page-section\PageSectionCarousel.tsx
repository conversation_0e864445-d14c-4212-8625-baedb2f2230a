"use client";

import { cn } from "@/lib/utils";
import React, { Children } from "react";
import type useEmblaCarousel from "embla-carousel-react";
import { PageSectionBase, type PageSectionBaseProps } from "./PageSectionBase";
import { CarouselWrapper } from "@/components/base/page-section/CarouselWrapper";

// Props for the Carousel PageSection
export interface PageSectionCarouselProps extends PageSectionBaseProps {
  carouselOpts?: Parameters<typeof useEmblaCarousel>[0];
  carouselItemsClassName?: string;
  showCarouselArrows?: boolean;
  showCarouselDots?: boolean;
  carouselArrowLeftClassName?: string;
  carouselArrowRightClassName?: string;
  carouselDotsContainerClassName?: string;
  carouselWrapperClassName?: string;
  onEnd?: () => void;
  isLoadingMore?: boolean;
  hasMore?: boolean;
}

export const PageSectionCarousel = ({
  title,
  description,
  actions,
  actionButtonLabel,
  actionButtonIcon,
  onActionButtonClick,
  children,
  className,
  contentClassName,
  carouselOpts,
  carouselItemsClassName = "basis-full sm:basis-1/2 md:basis-auto",
  showCarouselArrows,
  showCarouselDots,
  carouselArrowLeftClassName,
  carouselArrowRightClassName,
  carouselDotsContainerClassName,
  carouselWrapperClassName = "pt-2",
  onEnd,
  isLoadingMore,
  hasMore,
  ...baseProps
}: PageSectionCarouselProps) => {
  const numChildren = Children.count(children);

  return (
    <PageSectionBase
      title={title}
      description={description}
      actions={actions}
      actionButtonLabel={actionButtonLabel}
      actionButtonIcon={actionButtonIcon}
      onActionButtonClick={onActionButtonClick}
      className={className} // Pass className to the base section
      // contentClassName is NOT passed to PageSectionBase here,
      // because its content area is replaced by CarouselWrapper.
      // It IS passed to CarouselWrapper for its CarouselContent.
      {...baseProps} // Pass any other base props
    >
      {/* Carousel replaces the standard children rendering of PageSectionBase */}
      {numChildren > 0 ? (
        <CarouselWrapper
          opts={{ ...carouselOpts, slidesToScroll: "auto" }}
          itemClassName={carouselItemsClassName}
          contentClassName={contentClassName} // Pass PageSection's contentClassName to CarouselContent via wrapper
          showArrows={showCarouselArrows}
          showDots={showCarouselDots}
          arrowLeftClassName={carouselArrowLeftClassName}
          arrowRightClassName={carouselArrowRightClassName}
          dotsContainerClassName={carouselDotsContainerClassName}
          wrapperClassName={carouselWrapperClassName} // For the main Carousel div within PageSectionCarousel
          onEnd={onEnd}
          isLoadingMore={isLoadingMore}
          hasMore={hasMore}
        >
          {children}
        </CarouselWrapper>
      ) : null}
    </PageSectionBase>
  );
};

PageSectionCarousel.displayName = "PageSectionCarousel";
