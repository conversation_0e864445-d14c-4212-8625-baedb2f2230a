export const loadChargebeeScript = () => {
  return new Promise<void>((resolve, reject) => {
    // Check if script is already loaded
    if (window.Chargebee) {
      resolve();
      return;
    }

    const script = document.createElement("script");
    script.src = "https://js.chargebee.com/v2/chargebee.js";
    script.async = true;

    script.onload = () => {
      resolve();
    };

    script.onerror = () => {
      reject(new Error("Failed to load Chargebee script"));
    };

    document.head.appendChild(script);
  });
};

// Add TypeScript declaration for the global Chargebee object
declare global {
  interface Window {
    Chargebee: any;
  }
}
