"use client";

import { ChartConfig } from "@/components/ui/chart";

export const chartData1 = [
  { month: "January", desktop: 186 },
  { month: "February", desktop: 305 },
  { month: "March", desktop: 237 },
  { month: "April", desktop: 273 },
  { month: "May", desktop: 309 },
  { month: "June", desktop: 314 },
  { month: "July", desktop: 290 },
  { month: "August", desktop: 325 },
  { month: "September", desktop: 298 },
  { month: "October", desktop: 342 },
  { month: "November", desktop: 330 },
  { month: "December", desktop: 358 },
  { month: "January", desktop: 345 },
  { month: "February", desktop: 372 },
  { month: "March", desktop: 360 },
  { month: "April", desktop: 385 },
  { month: "May", desktop: 410 },
  { month: "June", desktop: 395 },
];

export const chartData2 = [
  { month: "January", desktop: 120 },
  { month: "February", desktop: 145 },
  { month: "March", desktop: 132 },
  { month: "April", desktop: 178 },
  { month: "May", desktop: 201 },
  { month: "June", desktop: 225 },
  { month: "July", desktop: 210 },
  { month: "August", desktop: 235 },
  { month: "September", desktop: 218 },
  { month: "October", desktop: 242 },
  { month: "November", desktop: 228 },
  { month: "December", desktop: 255 },
  { month: "January", desktop: 240 },
  { month: "February", desktop: 262 },
  { month: "March", desktop: 250 },
  { month: "April", desktop: 275 },
  { month: "May", desktop: 290 },
  { month: "June", desktop: 280 },
];

export const chartData3 = [
  { month: "January", desktop: 75 },
  { month: "February", desktop: 92 },
  { month: "March", desktop: 88 },
  { month: "April", desktop: 110 },
  { month: "May", desktop: 135 },
  { month: "June", desktop: 158 },
  { month: "July", desktop: 145 },
  { month: "August", desktop: 165 },
  { month: "September", desktop: 152 },
  { month: "October", desktop: 170 },
  { month: "November", desktop: 162 },
  { month: "December", desktop: 180 },
  { month: "January", desktop: 175 },
  { month: "February", desktop: 190 },
  { month: "March", desktop: 182 },
  { month: "April", desktop: 195 },
  { month: "May", desktop: 210 },
  { month: "June", desktop: 205 },
];

export const chartConfig = {
  desktop: {
    label: "Desktop",
    color: "hsl(var(--chart-5))",
  },
} satisfies ChartConfig;

export const radialChartConfig = {
  strategy: {
    label: "Stratégie",
    color: "#FFFFFF",
  },
} satisfies ChartConfig;

export const radialChartData = [
  { strategy: "BASE PROTECTRICE", visitors: 80, fill: "#8D2146" },
  { strategy: "CROISSANCE CONTRÔLÉE", visitors: 15, fill: "#5865F2" },
  { strategy: "RENDEMENTS AMBITIEUX", visitors: 5, fill: "#C6712B" },
];

export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.07,
      delayChildren: 0.05,
      ease: [0.25, 0.1, 0.25, 1.0], // Custom cubic bezier for snappy feel
    },
  },
};

export const itemVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.2,
      ease: [0.25, 0.1, 0.25, 1.0],
    },
  },
};

export const avatarVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.25,
      ease: "easeOut",
    },
  },
};

export const statsVariants = {
  hidden: { opacity: 0, x: -5 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.15,
      ease: "easeOut",
    },
  },
};

export const buttonVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  hover: {
    scale: 1.03,
    transition: {
      duration: 0.1,
      ease: "easeInOut",
    },
  },
};

export const cardVariants = {
  hidden: { opacity: 0, y: 15 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.25,
      ease: [0.25, 0.1, 0.25, 1.0],
    },
  },
};
