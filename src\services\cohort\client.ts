"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import { useApi } from "@/hooks/store/use-api";
import { useToast } from "@/hooks/use-toast";
import { COHORT_API_ENDPOINTS, CohortApiTypes, ListCohortsParams, CohortApiPayloads } from "./core";

/**
 * Hook for fetching and managing cohort lists
 * @param params Filter and pagination parameters
 * @returns SWR response with cohort list data
 */
export function useCohortList(params: ListCohortsParams = {}) {
  const apiConfig = useMemo(() => {
    const { url } = COHORT_API_ENDPOINTS.list(params);
    return url;
  }, [params]);

  return useApi<CohortApiTypes["list"]>(apiConfig);
}

/**
 * Hook for fetching a specific cohort by ID
 * @param id Cohort ID to fetch
 * @returns SWR response with cohort data
 */
export function useCohortDetails(id?: number | null) {
  const apiConfig = useMemo(() => {
    if (!id) return null;
    const { url } = COHORT_API_ENDPOINTS.getById(id);
    return url;
  }, [id]);

  return useApi<CohortApiTypes["getById"]>(apiConfig);
}

/**
 * Hook for creating a new cohort
 * @returns Functions and state for cohort creation
 */
export function useCreateCohort() {
  const t = useTranslations("cohorts");
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);

  const { url, method } = COHORT_API_ENDPOINTS.create();
  const { trigger, isLoading } = useApi<CohortApiTypes["create"]>(url, {
    method: method as "POST",
  });

  const create = useCallback(
    async (data: CohortApiPayloads["create"]) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);
      try {
        const result = await trigger(data);
        toast({
          title: t("created"),
          description: t("created-description"),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("error"),
          description: errorMessage || t("error-description"),
          variant: "destructive",
        });
        throw error;
      }
    },
    [toast, trigger, t],
  );

  return { create, isLoading, error };
}

/**
 * Hook for updating an existing cohort
 * @param id Optional initial cohort ID
 * @returns Functions and state for cohort updates
 */
export function useUpdateCohort(id?: number | null) {
  const t = useTranslations("cohorts");
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const [cohortId, setCohortId] = useState<number | null>(id || null);
  const [updateData, setUpdateData] = useState<CohortApiPayloads["update"] | null>(null);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  const { trigger, isLoading } = useApi<CohortApiTypes["update"]>(
    cohortId ? COHORT_API_ENDPOINTS.update(cohortId).url : null,
    { method: "PATCH" },
  );

  const performUpdate = async (data?: CohortApiPayloads["update"]) => {
    try {
      if (!trigger) throw new Error("API not initialized");
      if (!cohortId) throw new Error("Cohort ID is required");
      if (!updateData && !data) throw new Error("Update data is required");

      const result = await trigger(data || updateData);
      toast({
        title: t("updated"),
        description: t("updated-description"),
        variant: "default",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(error instanceof Error ? error : new Error(errorMessage));
      toast({
        title: t("error"),
        description: errorMessage || t("error-description"),
        variant: "destructive",
      });
      throw error;
    } finally {
      setShouldUpdate(false);
      setUpdateData(null);
    }
  };

  useEffect(() => {
    if (shouldUpdate && updateData) {
      performUpdate();
    }
  }, [shouldUpdate, cohortId, updateData]);

  const update = useCallback(
    async (overrideId: number | null, data: CohortApiPayloads["update"]) => {
      setError(null);
      setUpdateData(data);
      if (cohortId !== overrideId) {
        setCohortId(overrideId || id || null);
        setShouldUpdate(true);
      } else {
        await performUpdate(data);
      }
    },
    [cohortId, id],
  );

  return { update, isLoading, error };
}

/**
 * Hook for managing students in a cohort
 * @param id Optional initial cohort ID
 * @returns Functions and state for student management
 */
export function useCohortStudents(id?: number | null) {
  const t = useTranslations("cohorts.students");
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const [cohortId, setCohortId] = useState<number | null>(id || null);
  const [addStudentsData, setAddStudentsData] = useState<{ studentIds: number[] } | null>(null);
  const [removeStudentsData, setRemoveStudentsData] = useState<{ studentIds: number[] } | null>(
    null,
  );
  const [shouldAddStudents, setShouldAddStudents] = useState(false);
  const [shouldRemoveStudents, setShouldRemoveStudents] = useState(false);

  // Create API triggers based on the current cohort ID
  const addStudentsTrigger = useApi<CohortApiTypes["addStudents"]>(
    cohortId ? COHORT_API_ENDPOINTS.addStudents(cohortId).url : null,
    { method: "POST" },
  );

  const removeStudentsTrigger = useApi<CohortApiTypes["removeStudents"]>(
    cohortId ? COHORT_API_ENDPOINTS.removeStudents(cohortId).url : null,
    { method: "POST" },
  );

  // Function to perform the add students operation
  const performAddStudents = useCallback(
    async (data?: { studentIds: number[] }) => {
      try {
        if (!addStudentsTrigger.trigger) throw new Error("API not initialized");
        if (!cohortId) throw new Error("Cohort ID is required");
        if (!addStudentsData && !data) throw new Error("Student IDs are required");

        const result = await addStudentsTrigger.trigger(data || addStudentsData);
        toast({
          title: t("add_success_title", { defaultValue: "Students added" }),
          description: t("add_success_description", {
            defaultValue: "Students have been added to the cohort successfully",
          }),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("error_title"),
          description: errorMessage || t("error_description"),
          variant: "destructive",
        });
        throw error;
      } finally {
        setShouldAddStudents(false);
        setAddStudentsData(null);
      }
    },
    [addStudentsTrigger.trigger, cohortId, addStudentsData, toast, t],
  );

  // Function to perform the remove students operation
  const performRemoveStudents = useCallback(
    async (data?: { studentIds: number[] }) => {
      try {
        if (!removeStudentsTrigger.trigger) throw new Error("API not initialized");
        if (!cohortId) throw new Error("Cohort ID is required");
        if (!removeStudentsData && !data) throw new Error("Student IDs are required");

        const result = await removeStudentsTrigger.trigger(data || removeStudentsData);
        toast({
          title: t("remove_success_title", { defaultValue: "Students removed" }),
          description: t("remove_success_description", {
            defaultValue: "Students have been removed from the cohort successfully",
          }),
          variant: "default",
        });
        return result;
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : "An error occurred";
        setError(error instanceof Error ? error : new Error(errorMessage));
        toast({
          title: t("remove_error_title", { defaultValue: "Error removing students" }),
          description:
            errorMessage ||
            t("remove_error_description", {
              defaultValue: "An error occurred while removing students from the cohort",
            }),
          variant: "destructive",
        });
        throw error;
      } finally {
        setShouldRemoveStudents(false);
        setRemoveStudentsData(null);
      }
    },
    [removeStudentsTrigger.trigger, cohortId, removeStudentsData, toast, t],
  );

  // Effect to trigger add students when state changes
  useEffect(() => {
    if (shouldAddStudents && addStudentsData) {
      performAddStudents();
    }
  }, [shouldAddStudents, cohortId, addStudentsData, performAddStudents]);

  // Effect to trigger remove students when state changes
  useEffect(() => {
    if (shouldRemoveStudents && removeStudentsData) {
      performRemoveStudents();
    }
  }, [shouldRemoveStudents, cohortId, removeStudentsData, performRemoveStudents]);

  // Public function to add students
  const addStudents = useCallback(
    async (overrideId: number | null, studentIds: number[]) => {
      setError(null);
      setAddStudentsData({ studentIds });

      if (cohortId !== overrideId) {
        setCohortId(overrideId || id || null);
        setShouldAddStudents(true);
      } else {
        await performAddStudents({ studentIds });
      }
    },
    [
      cohortId,
      id,
      setError,
      setAddStudentsData,
      setCohortId,
      setShouldAddStudents,
      performAddStudents,
    ],
  );

  // Public function to remove students
  const removeStudents = useCallback(
    async (overrideId: number | null, studentIds: number[]) => {
      setError(null);
      setRemoveStudentsData({ studentIds });

      if (cohortId !== overrideId) {
        setCohortId(overrideId || id || null);
        setShouldRemoveStudents(true);
      } else {
        await performRemoveStudents({ studentIds });
      }
    },
    [
      cohortId,
      id,
      setError,
      setRemoveStudentsData,
      setCohortId,
      setShouldRemoveStudents,
      performRemoveStudents,
    ],
  );

  return {
    addStudents,
    removeStudents,
    isAddingStudents: addStudentsTrigger.isLoading || shouldAddStudents,
    isRemovingStudents: removeStudentsTrigger.isLoading || shouldRemoveStudents,
    error,
  };
}
