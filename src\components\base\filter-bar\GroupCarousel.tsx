"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { ProcessedGroup } from "./types";

/**
 * Props for the GroupCarousel component
 */
interface GroupCarouselProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Group configuration and state */
  group: ProcessedGroup;
  /** Callback to store reference to the scrollable container */
  groupRef: (el: HTMLDivElement | null) => void;
  /** Whether the content is wrapped to multiple lines */
  wrapped: boolean;
}

/**
 * Horizontal scrolling container for filter groups with gradient indicators
 *
 * @component
 * @example
 * ```tsx
 * <GroupCarousel
 *   group={group}
 *   groupRef={setGroupRef}
 *   wrapped={false}
 * >
 *   <FilterComponent />
 *   <FilterComponent />
 * </GroupCarousel>
 * ```
 *
 * @remarks
 * Features:
 * - Horizontal scrolling with gradient indicators
 * - Scroll position tracking
 * - Optional content wrapping
 * - Customizable width and spacing
 */
export function GroupCarousel({
  group,
  groupRef,
  wrapped,
  className,
  children,
  ...props
}: GroupCarouselProps) {
  const { state } = group;

  return (
    <div className={cn("relative", className)} {...props}>
      {/* Left gradient indicator */}
      <div
        className={cn(
          "pointer-events-none absolute inset-y-0 left-0 z-10 w-8 transition-opacity",
          "bg-gradient-to-r from-background to-transparent",
          state.gradientState.left ? "opacity-100" : "opacity-0",
        )}
      />
      {/* Right gradient indicator */}
      <div
        className={cn(
          "pointer-events-none absolute inset-y-0 right-0 z-10 w-8 transition-opacity",
          "bg-gradient-to-l from-background to-transparent",
          state.gradientState.right ? "opacity-100" : "opacity-0",
        )}
      />
      {/* Scrollable container */}
      <div
        ref={groupRef}
        className={cn(
          "scrollbar-none overflow-x-auto px-4",
          "[&::-webkit-scrollbar]:h-1.5",
          "[&::-webkit-scrollbar-thumb]:rounded-full",
          "[&::-webkit-scrollbar-thumb]:bg-border",
          "[&::-webkit-scrollbar-track]:bg-transparent",
        )}
      >
        <div className="flex w-full min-w-max items-center gap-2">
          {React.Children.map(children, (child) => (
            <div className="flex-1">{child}</div>
          ))}
        </div>
      </div>
    </div>
  );
}
