"use client";

/**
 * A cell component for displaying a chip with optional icon and variant styling
 * Used for status indicators or tags in the DataTable
 */

import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import { type ChipVariant } from "@/types/data-table";

interface ChipCellProps {
  /** The text or numeric value to display in the chip */
  value: string | number;
  /** Optional icon to display before the text */
  icon?: LucideIcon;
  /** Visual style variant for the chip */
  variant?: ChipVariant;
}

export function ChipCell({ value, icon: Icon, variant = "default" }: ChipCellProps) {
  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        {
          "border-transparent bg-primary/10 text-primary": variant === "default",
          "border-transparent bg-green-100 text-green-600": variant === "success",
          "border-transparent bg-yellow-100 text-yellow-600": variant === "warning",
          "border-transparent bg-red-100 text-red-600": variant === "danger",
        },
      )}
    >
      {Icon && <Icon className="mr-1 h-3 w-3" />}
      {value}
    </span>
  );
}
