import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import {
  useDeleteTriad,
  useLeaveTriad,
  useJoinTriad,
  useGetUpcomingTriad,
} from "@/services/triad/client";
import { getTriadErrorMessage } from "@/utils/triadHelpers";

interface UseTriadActionsProps {
  onActionComplete?: () => void;
}

export function useTriadActions({ onActionComplete }: UseTriadActionsProps = {}) {
  const t = useTranslations("triad");
  const [loadingTriads, setLoadingTriads] = useState<Record<number, boolean>>({});
  const [selectedTriadId, setSelectedTriadId] = useState<number>(0);
  const [selectedTriadIdToDelete, setSelectedTriadIdToDelete] = useState<number>(0);

  // Service hooks
  const { join } = useJoinTriad();
  const { leave } = useLeaveTriad();
  const { deleteTriad } = useDeleteTriad(selectedTriadIdToDelete);
  const { upcomingTriad } = useGetUpcomingTriad();

  // Loading state management
  const setTriadLoading = useCallback((triadId: number, isLoading: boolean) => {
    setLoadingTriads((prev) => ({ ...prev, [triadId]: isLoading }));
  }, []);

  const isTriadLoading = useCallback(
    (triadId: number) => {
      return !!loadingTriads[triadId];
    },
    [loadingTriads],
  );

  // Join triad action
  const handleJoinTriad = useCallback(
    async (triadId: number) => {
      try {
        setTriadLoading(triadId, true);
        await join({ triadId });
        toast.success(t("success.joined"));
        onActionComplete?.();
      } catch (error) {
        const err = error as Error;
        const translationKey = getTriadErrorMessage(err, "join");
        toast.error(t(translationKey), { duration: 5000 });
      } finally {
        setTriadLoading(triadId, false);
        setSelectedTriadId(0);
      }
    },
    [t, join, setTriadLoading, onActionComplete],
  );

  // Join attempt with upcoming triads check
  const handleJoinAttempt = useCallback(
    async (triadId: number) => {
      if (upcomingTriad) {
        setSelectedTriadId(triadId);
        return { needsConfirmation: true, triadId };
      } else {
        await handleJoinTriad(triadId);
        return { needsConfirmation: false, triadId };
      }
    },
    [upcomingTriad, handleJoinTriad],
  );

  // Leave triad action
  const handleLeaveTriad = useCallback(
    async (triadId: number) => {
      try {
        setTriadLoading(triadId, true);
        await leave({ triadId });
        toast.success(t("table.success.left"));
        onActionComplete?.();
      } catch (error) {
        const err = error as Error;
        const translationKey = getTriadErrorMessage(err, "leave");
        toast.error(t(translationKey), { duration: 5000 });
      } finally {
        setTriadLoading(triadId, false);
        setSelectedTriadId(0);
      }
    },
    [t, leave, setTriadLoading, onActionComplete],
  );

  // Leave attempt
  const handleLeaveAttempt = useCallback((triadId: number) => {
    setSelectedTriadId(triadId);
    return { needsConfirmation: true, triadId };
  }, []);

  // Delete triad action
  const handleDeleteTriad = useCallback(
    async (triadId: number) => {
      try {
        setSelectedTriadIdToDelete(triadId);
        setTriadLoading(triadId, true);
        await deleteTriad(triadId);
        toast.success(t("table.success.deleted"));
        onActionComplete?.();
      } catch (error) {
        const err = error as Error;
        const translationKey = getTriadErrorMessage(err, "delete");
        toast.error(t(translationKey), { duration: 5000 });
      } finally {
        setTriadLoading(triadId, false);
        setSelectedTriadId(0);
      }
    },
    [t, deleteTriad, setTriadLoading, onActionComplete],
  );

  // Delete attempt with participant check
  const handleDeleteAttempt = useCallback(
    (triadId: number, participantCount: number) => {
      setSelectedTriadId(triadId);
      if (participantCount > 0) {
        return { needsConfirmation: true, triadId, participantCount };
      } else {
        // Immediate delete for triads with no participants
        setTimeout(() => handleDeleteTriad(triadId), 0);
        return { needsConfirmation: false, triadId, participantCount };
      }
    },
    [handleDeleteTriad],
  );

  // Confirm actions (used by dialog components)
  const confirmJoin = useCallback(
    () => selectedTriadId && handleJoinTriad(selectedTriadId),
    [selectedTriadId, handleJoinTriad],
  );

  const confirmLeave = useCallback(
    () => selectedTriadId && handleLeaveTriad(selectedTriadId),
    [selectedTriadId, handleLeaveTriad],
  );

  const confirmDelete = useCallback(
    () => selectedTriadId && handleDeleteTriad(selectedTriadId),
    [selectedTriadId, handleDeleteTriad],
  );

  // Cancel actions
  const cancelAction = useCallback(() => {
    setSelectedTriadId(0);
  }, []);

  return {
    // State
    loadingTriads,
    selectedTriadId,
    isTriadLoading,
    upcomingTriad,

    // Actions
    handleJoinAttempt,
    handleLeaveAttempt,
    handleDeleteAttempt,

    // Confirmations
    confirmJoin,
    confirmLeave,
    confirmDelete,
    cancelAction,

    // Direct actions (for immediate execution)
    handleJoinTriad,
    handleLeaveTriad,
    handleDeleteTriad,

    // Utilities
    setTriadLoading,
  };
}
