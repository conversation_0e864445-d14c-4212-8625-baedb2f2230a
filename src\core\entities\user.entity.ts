import { BaseEntity } from './base.entity';
import { CohortEntity } from './cohort.entity';
import { CourseEntity } from './course.entity';
import { RoleEntity } from './role.entity';
import { UserProfileEntity } from './user.profile.entity';
import { UserType, IUserChargebeeIds } from '@px-shared-account/hermes';
import { TriadEntity } from './triad.entity';
import { TriadParticipantEntity } from './triad-participant.entity';
export class UserEntity extends BaseEntity {
  firstName: string;
  lastName: string;
  email: string;
  role: RoleEntity;
  ssoId?: string;
  profile: UserProfileEntity;
  coursesManaged: CourseEntity[];
  triadsManaged: TriadEntity[];
  triadParticipations: TriadParticipantEntity[];
  cohorts: CohortEntity[];
  metaData?: Record<string, any>;
  type: UserType;
  chargebeeIds?: IUserChargebeeIds;
  lmsId?: string;
  communityId?: string;
  crmId?: string;
  createdAt: Date;
}
