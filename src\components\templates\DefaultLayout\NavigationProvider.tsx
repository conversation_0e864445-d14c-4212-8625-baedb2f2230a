"use client";

import React, { createContext, useContext, useState } from "react";
import { usePathname } from "next/navigation";

interface NavigationContextType {
  currentPath: string;
  isSidebarOpen: boolean;
  openSidebar: () => void;
  closeSidebar: () => void;
  toggleSidebar: () => void;
  isCurrentPath: (path: string) => boolean;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error("useNavigation must be used within a NavigationProvider");
  }
  return context;
};

interface NavigationProviderProps {
  children: React.ReactNode;
}

export const NavigationProvider = ({ children }: NavigationProviderProps) => {
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const openSidebar = () => setIsSidebarOpen(true);
  const closeSidebar = () => setIsSidebarOpen(false);
  const toggleSidebar = () => setIsSidebarOpen((prev) => !prev);

  const isCurrentPath = (path: string) => {
    if (path === "/") {
      return pathname === path;
    }
    return pathname.startsWith(path);
  };

  const value = {
    currentPath: pathname,
    isSidebarOpen,
    openSidebar,
    closeSidebar,
    toggleSidebar,
    isCurrentPath,
  };

  return <NavigationContext.Provider value={value}>{children}</NavigationContext.Provider>;
};

NavigationProvider.displayName = "NavigationProvider";
