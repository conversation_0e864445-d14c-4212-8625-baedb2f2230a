import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserProfileEntity } from '@entities';
import { Gender } from '@px-shared-account/hermes';

@Entity({
  name: 'userProfiles',
})
export class UserProfileTable implements UserProfileEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column('date', { nullable: true })
  dateOfBirth?: Date;

  @Column('varchar', { array: true, nullable: true })
  interests?: string[];

  @Column('varchar', { nullable: true })
  city?: string;

  @Column('varchar', { nullable: true })
  country?: string;

  @Column('enum', { enum: Gender, nullable: true })
  gender?: Gender;

  @CreateDateColumn({ default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}
