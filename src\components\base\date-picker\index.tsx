"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { useTranslations } from "next-intl";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

export function DatePicker({
  className,
  value,
  onChange,
  placeholder,
  buttonClassName,
  disablePastDates = true,
}: {
  className?: string;
  buttonClassName?: string;
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  placeholder?: string;
  disablePastDates?: boolean;
}) {
  const t = useTranslations();
  const [date, setDate] = React.useState<Date | undefined>(value);

  // Sync internal state with external value
  React.useEffect(() => {
    setDate(value);
  }, [value]);

  return (
    <div className={cn("group grid gap-2", className)}>
      <Popover modal>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "justify-start text-left font-normal",
              "bg-background rounded-full border-none font-medium",
              buttonClassName,
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? (
              format(date, "dd/MM/yyyy")
            ) : (
              <span>{placeholder || t("date_range_picker.select_date")}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="group bg-background pointer-events-auto w-auto rounded-2xl border-none p-0"
          align="start"
        >
          <Calendar
            mode="single"
            selected={date}
            onSelect={(newDate: Date | undefined) => {
              setDate(newDate);
              onChange?.(newDate);
            }}
            disabled={disablePastDates ? { before: new Date() } : undefined}
            initialFocus
            className="border-none"
            classNames={{
              months: "space-y-4",
              month: "space-y-4",
              caption: "flex justify-center pt-1 relative items-center ",
              caption_label: "text-sm font-medium ",
              nav: "space-x-1 flex items-center",
              nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 ",
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex",
              head_cell: "text-muted-foreground rounded-md w-9 font-normal text-xs",
              row: "flex w-full mt-2",
              day_today: "",
              cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-primary first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-foreground/10 rounded-md",
              day_selected: "bg-primary hover:bg-primary/50",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_hidden: "invisible",
            }}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
