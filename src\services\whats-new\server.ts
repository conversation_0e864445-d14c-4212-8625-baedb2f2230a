import { auth } from "@clerk/nextjs/server";
import serverFetcher from "@/lib/server/server-fetcher";
import { WHATS_NEW_API_ENDPOINTS, WhatsNewApiTypes, WhatsNewApiPayloads } from "./core";

/**
 * Creates a server-side token getter for the fetcher
 * @returns Promise with authentication token or null
 */
const getServerToken = async () => {
  const session = await auth();
  return session?.getToken() || null;
};

// Initialize the fetcher with server-side token getter
const apiFetcher = serverFetcher(getServerToken);

/**
 * Server-side function for fetching What's New versions list
 * @returns Promise with versions list data
 */
export async function getWhatsNewVersions(): Promise<WhatsNewApiTypes["list"]> {
  const { url } = WHATS_NEW_API_ENDPOINTS.list();
  return apiFetcher(url);
}

/**
 * Server-side function for fetching a specific What's New version by ID
 * @param id Version ID to fetch
 * @returns Promise with version data
 */
export async function getWhatsNewVersion(id: number): Promise<WhatsNewApiTypes["getById"]> {
  const { url } = WHATS_NEW_API_ENDPOINTS.getById(id);
  return apiFetcher(url);
}

/**
 * Server-side function for creating a new What's New version
 * @param data Version creation data
 * @returns Promise with created version data
 */
export async function createWhatsNewVersion(
  data: WhatsNewApiPayloads["create"],
): Promise<WhatsNewApiTypes["create"]> {
  const { url, method } = WHATS_NEW_API_ENDPOINTS.create();
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for updating an existing What's New version
 * @param data Version update data (including ID)
 * @returns Promise with updated version data
 */
export async function updateWhatsNewVersion(
  data: WhatsNewApiPayloads["update"],
): Promise<WhatsNewApiTypes["update"]> {
  const { url, method } = WHATS_NEW_API_ENDPOINTS.update(data.id);
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for deleting a What's New version
 * @param id Version ID to delete
 * @returns Promise with deletion result
 */
export async function deleteWhatsNewVersion(id: number): Promise<WhatsNewApiTypes["delete"]> {
  const { url, method } = WHATS_NEW_API_ENDPOINTS.delete(id);
  return apiFetcher(url, {
    method,
  });
}

/**
 * Server-side function for fetching reactions for a specific version
 * @param versionId Version ID to fetch reactions for
 * @returns Promise with reactions data
 */
export async function getWhatsNewReactions(
  versionId: number,
): Promise<WhatsNewApiTypes["getReactions"]> {
  const { url } = WHATS_NEW_API_ENDPOINTS.getReactions(versionId);
  return apiFetcher(url);
}

/**
 * Server-side function for toggling a reaction on a version
 * @param data Reaction toggle data
 * @returns Promise with toggle result
 */
export async function toggleWhatsNewReaction(
  data: WhatsNewApiPayloads["toggleReaction"],
): Promise<WhatsNewApiTypes["toggleReaction"]> {
  const { url, method } = WHATS_NEW_API_ENDPOINTS.toggleReaction();
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for uploading an image
 * @param file File to upload
 * @returns Promise with upload result
 */
export async function uploadWhatsNewImage(file: File): Promise<WhatsNewApiTypes["uploadImage"]> {
  const formData = new FormData();
  formData.append("file", file);

  const { url, method } = WHATS_NEW_API_ENDPOINTS.uploadImage();

  return apiFetcher(url, {
    method,
    body: formData,
  });
}
