/**
 * Core type definitions for the Filter Bar system
 */

/** Option type for filter dropdowns */
export interface Option {
  value: string;
  label: string;
  labelEl?: React.ReactNode;
}

/** Base step interface */
interface BaseStep {
  id: string;
  label: string;
  placeholder?: string;
}

/** Filter step type */
export interface FilterStep extends BaseStep {
  type: "filter";
  options?: Option[];
  defaultValue?: string;
  dependsOn?: Record<string, { required?: boolean }>;
  multiple?: boolean;
}

/** Date range step type */
export interface DateRangeStep extends BaseStep {
  type: "dateRange";
  defaultValue?: string;
}

/** Time range step type */
export interface TimeRangeStep extends BaseStep {
  type: "timeRange";
  defaultValue?: string;
}

/** Separator step type */
export interface SeparatorStep {
  type: "separator";
}

/** Union type of all step types */
export type Step = FilterStep | DateRangeStep | TimeRangeStep | SeparatorStep;

/** State for a single filter step */
export interface StepState {
  isLoading: boolean;
  error: string | null;
  retryCount: number;
  options: Option[];
}

/** Group state for carousel */
export interface GroupState {
  scrollable: boolean;
  gradientState: {
    left: boolean;
    right: boolean;
  };
  wrapped: boolean;
}

/** Processed group with items and state */
export interface ProcessedGroup {
  items: Step[];
  state: GroupState;
}

/** Complete state for the filter bar */
export interface FilterBarState {
  values: Record<string, string | string[]>;
  stepsState: Record<string, StepState>;
  groups: ProcessedGroup[];
}

/** Props for the useFilterBar hook */
export interface UseFilterBarProps {
  steps: Step[];
  value?: Record<string, string | string[]>;
  onChange?: (values: Record<string, string | string[]>) => void;
  onOptionsNeeded?: (
    stepId: string,
    dependencies: Record<string, string | string[]>,
  ) => Promise<Option[]>;
}

/** Props for the FilterBar component */
export interface FilterBarProps {
  steps: Step[];
  value?: Record<string, string | string[]>;
  onChange?: (stepId: string, value: string | string[]) => void;
  onOptionsNeeded?: (
    stepId: string,
    dependencies: Record<string, string | string[]>,
  ) => Promise<Option[]>;
  className?: string;
  forceModal?: boolean;
  forceBar?: boolean;
  /** Unique identifier for this filter bar instance */
  instanceId?: string;
}

/** Props for the FilterBarModal component */
export interface FilterBarModalProps extends Omit<FilterBarProps, "onChange"> {
  /** Whether the modal is in a loading state */
  loading?: boolean;
  /** Callback fired when the Apply button is clicked */
  onApply?: () => void;
  /** Custom trigger element */
  triggerEl?: (activeFiltersCount: number) => React.ReactNode;
  /** Filter bar state from useFilterBar hook */
  values: Record<string, string>;
  stepsState: Record<string, StepState>;
  handleChange: (stepId: string, value: string | string[]) => void;
  isStepEnabled: (stepId: string) => boolean;
  clearAll: () => void;
  /** Unique identifier for this filter bar instance */
  instanceId?: string;
}
