import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { Permissions } from '@px-shared-account/hermes';
import {
  CreateCourseDto,
  GetCourseByIdDto,
  GetCoursesByOfferIdDTO,
  GetStudentsForCourseDto,
  ListCourseCatalogDto,
  ListCoursesDTO,
  UpdateCourseDto,
} from '@dtos';
import { CourseUseCases } from '@useCases';
import { CurrentUser, RequirePermissions } from 'src/auth/decorators';
import { JwtPermissionsGuard } from 'src/auth/guards';
import { UserEntity } from '@entities';
import { CurrentUserInterceptor } from 'src/auth/interceptors';

@Controller('course')
@UseGuards(JwtPermissionsGuard)
export class CourseController {
  constructor(private readonly courseUseCases: CourseUseCases) { }

  @Post('/')
  @RequirePermissions(Permissions.Course.CREATE)
  async createCourse(@Body() payload: CreateCourseDto) {
    return this.courseUseCases.create(payload);
  }

  @Patch('/:id')
  @RequirePermissions(Permissions.Course.UPDATE)
  async updateCourse(
    @Param() params: GetCourseByIdDto,
    @Body() payload: UpdateCourseDto,
  ) {
    return this.courseUseCases.update(params.id, payload);
  }

  @Get('/list')
  @RequirePermissions(Permissions.Course.READ)
  async listCourses(@Query() payload: ListCoursesDTO): Promise<any> {
    return this.courseUseCases.list(
      payload.search,
      payload.status,
      payload.limit,
      payload.page,
    );
  }

  @Get('/catalog')
  @RequirePermissions(Permissions.Course.READ)
  @UseInterceptors(CurrentUserInterceptor)
  async listCourseCatalog(
    @Query() payload: ListCourseCatalogDto,
    @CurrentUser() user: UserEntity,
  ) {
    return this.courseUseCases.listCatalog(payload, user);
  }

  @Post('/:id/publish')
  @RequirePermissions(Permissions.Course.UPDATE)
  async publishCourse(@Param() params: GetCourseByIdDto) {
    return this.courseUseCases.publish(params.id);
  }

  @Post('/:id/archive')
  @RequirePermissions(Permissions.Course.ARCHIVE)
  async archiveCourse(@Param() params: GetCourseByIdDto) {
    return this.courseUseCases.archive(params.id);
  }

  @Get('/:id')
  @RequirePermissions(Permissions.Course.READ)
  async getCourse(@Param() params: GetCourseByIdDto) {
    return this.courseUseCases.getById(params.id);
  }

  @Get('/offer/:offerId')
  @RequirePermissions(Permissions.Course.READ)
  async getCoursesByOffer(@Param() params: GetCoursesByOfferIdDTO) {
    return this.courseUseCases.getByOfferId(params.offerId);
  }

  @Get('/:id/students')
  @RequirePermissions(Permissions.Course.READ)
  async getStudentsForCourse(
    @Param() params: GetCourseByIdDto,
    @Query() query: GetStudentsForCourseDto,
  ) {
    return this.courseUseCases.getStudentsForCourse(
      params.id,
      query.search,
      query.limit,
      query.page,
    );
  }
}
