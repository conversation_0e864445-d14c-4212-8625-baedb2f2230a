import { MigrationInterface, QueryRunner } from 'typeorm';

export class ProductDelivery1728565667292 implements MigrationInterface {
  name = 'ProductDelivery1728565667292';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."customers_type_enum" AS ENUM('internal', 'external', 'beta_tester')`,
    );
    await queryRunner.query(
      `CREATE TABLE "customers" ("id" SERIAL NOT NULL, "email" character varying NOT NULL, "ssoId" character varying, "firstName" character varying NOT NULL, "lastName" character varying, "type" "public"."customers_type_enum" NOT NULL DEFAULT 'external', "chargebeeIds" jsonb NOT NULL, "lmsId" character varying, "communityId" character varying, "crmId" character varying, "metaData" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "UQ_8536b8b85c06969f84f0c098b03" UNIQUE ("email"), CONSTRAINT "PK_133ec679a801fab5e070f73d3ea" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."customerAccesses_type_enum" AS ENUM('lms', 'community')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."customerAccesses_status_enum" AS ENUM('error', 'granted', 'revoked', 'suspended', 'pending')`,
    );
    await queryRunner.query(
      `CREATE TABLE "customerAccesses" ("id" SERIAL NOT NULL, "customerId" integer NOT NULL, "subscriptionId" integer, "offerId" integer, "type" "public"."customerAccesses_type_enum" NOT NULL, "status" "public"."customerAccesses_status_enum" NOT NULL, "details" jsonb NOT NULL, "expiresAt" TIMESTAMP, "metaData" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_b4f111323246436abed8a471cd9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "productOfferAccesses" ("id" SERIAL NOT NULL, "offerId" uuid NOT NULL, "lmsIds" jsonb, "communityIds" jsonb DEFAULT '[]', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_b71bb65286ea39d35ce36816aab" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" ADD "withProductDelivery" boolean DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "products" ADD "lmsIds" jsonb`);
    await queryRunner.query(
      `ALTER TABLE "products" ADD "communityIds" jsonb DEFAULT '[]'`,
    );
    await queryRunner.query(
      `ALTER TABLE "productOffers" ADD "withProductDelivery" boolean DEFAULT false, ADD "externalName" CHARACTER VARYING(255) NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "productOffers" ADD "lmsIds" jsonb`);
    await queryRunner.query(
      `ALTER TABLE "productOffers" ADD "communityIds" jsonb DEFAULT '[]'`,
    );
    await queryRunner.query(
      `ALTER TABLE "productOffers" ADD "suspendable" boolean DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "productOffers" ADD "delayPeriod" integer`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."cronLogs_type_enum" RENAME TO "cronLogs_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum" AS ENUM('OVERDUE_INVOICE_CHECK', 'ACCESS_SUSPENSION_CHECK')`,
    );
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ALTER COLUMN "type" TYPE "public"."cronLogs_type_enum" USING "type"::"text"::"public"."cronLogs_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum_old" AS ENUM('OVERDUE_INVOICE_CHECK')`,
    );
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ALTER COLUMN "type" TYPE "public"."cronLogs_type_enum_old" USING "type"::"text"::"public"."cronLogs_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."cronLogs_type_enum_old" RENAME TO "cronLogs_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "productOffers" DROP COLUMN "delayPeriod"`,
    );
    await queryRunner.query(
      `ALTER TABLE "productOffers" DROP COLUMN "suspendable"`,
    );
    await queryRunner.query(
      `ALTER TABLE "productOffers" DROP COLUMN "communityIds"`,
    );
    await queryRunner.query(`ALTER TABLE "productOffers" DROP COLUMN "lmsIds"`);
    await queryRunner.query(
      `ALTER TABLE "productOffers" DROP COLUMN "withProductDelivery"`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" DROP COLUMN "communityIds"`,
    );
    await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "lmsIds"`);
    await queryRunner.query(
      `ALTER TABLE "products" DROP COLUMN "withProductDelivery"`,
    );
    await queryRunner.query(`DROP TABLE "productOfferAccesses"`);
    await queryRunner.query(`DROP TABLE "customerAccesses"`);
    await queryRunner.query(
      `DROP TYPE "public"."customerAccesses_status_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."customerAccesses_type_enum"`);
    await queryRunner.query(`DROP TABLE "customers"`);
    await queryRunner.query(`DROP TYPE "public"."customers_type_enum"`);
  }
}
