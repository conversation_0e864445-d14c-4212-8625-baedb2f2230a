"use client";

import { Message } from "@/components/composite/chat/ChatConversation";
import { ChatListItem } from "@/components/composite/chat/ChatList";
import {
  currentUserId,
  sampleConversations,
  sampleMessages,
} from "@/components/composite/chat/sample-data";
import { useState, useCallback, useEffect } from "react";

export interface UseMessagesOptions {
  initialMessages?: Record<string, Message[]>;
  initialConversations?: ChatListItem[];
  userId?: string;
  simulateRealTime?: boolean;
}

interface TypingState {
  [conversationId: string]: boolean;
}

const AUTO_REPLIES = [
  "I'll get back to you soon!",
  "Thanks for your message!",
  "Got it, working on it now.",
  "Interesting point! Let me think about it.",
  "That's a great idea!",
  "Thanks for sharing that with me.",
];

export function useMessages({
  initialMessages = sampleMessages,
  initialConversations = sampleConversations,
  userId = currentUserId,
  simulateRealTime = true,
}: UseMessagesOptions = {}) {
  const [messages, setMessages] = useState(initialMessages);
  const [conversations, setConversations] = useState(initialConversations);
  const [typingStates, setTypingStates] = useState<TypingState>({});

  // Simulate other users going online/offline randomly
  useEffect(() => {
    if (!simulateRealTime) return;

    const interval = setInterval(() => {
      setConversations((prev) =>
        prev.map((conv) => ({
          ...conv,
          online: Math.random() > 0.3, // 70% chance of being online
        })),
      );
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, [simulateRealTime]);

  const simulateTyping = useCallback(
    (conversationId: string) => {
      if (!simulateRealTime) return;

      setTypingStates((prev) => ({ ...prev, [conversationId]: true }));
      return setTimeout(
        () => {
          setTypingStates((prev) => ({ ...prev, [conversationId]: false }));
        },
        2000 + Math.random() * 2000,
      ); // Random typing duration between 2-4 seconds
    },
    [simulateRealTime],
  );

  const simulateReply = useCallback(
    (conversationId: string, replyToMessage: Message) => {
      if (!simulateRealTime) return;

      const conversation = conversations.find((c) => c.id === conversationId);
      if (!conversation?.online) return; // Don't reply if user is offline

      const typingTimeout = simulateTyping(conversationId);

      // Random reply delay between 2-5 seconds
      setTimeout(
        () => {
          clearTimeout(typingTimeout);
          setTypingStates((prev) => ({ ...prev, [conversationId]: false }));

          const reply: Message = {
            id: `msg-${Date.now()}`,
            content: AUTO_REPLIES[Math.floor(Math.random() * AUTO_REPLIES.length)],
            timestamp: new Date(),
            sender: {
              id: conversation.id,
              name: conversation.title,
              avatar: conversation.avatar,
            },
            status: "sent",
          };

          setMessages((prev) => ({
            ...prev,
            [conversationId]: [...(prev[conversationId] || []), reply],
          }));

          setConversations((prev) =>
            prev.map((conv) =>
              conv.id === conversationId
                ? {
                    ...conv,
                    lastMessage: reply.content,
                    timestamp: reply.timestamp,
                    unreadCount: (conv.unreadCount || 0) + 1,
                  }
                : conv,
            ),
          );
        },
        2000 + Math.random() * 3000,
      );
    },
    [conversations, simulateRealTime, simulateTyping],
  );

  const sendMessage = useCallback(
    (conversationId: string, content: string) => {
      const newMessage: Message = {
        id: `msg-${Date.now()}`,
        content,
        timestamp: new Date(),
        sender: {
          id: userId,
          name: "You",
          avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`,
        },
        status: "sent",
      };

      // Update messages
      setMessages((prev) => ({
        ...prev,
        [conversationId]: [...(prev[conversationId] || []), newMessage],
      }));

      // Update conversation last message
      setConversations((prev) =>
        prev.map((conv) =>
          conv.id === conversationId
            ? {
                ...conv,
                lastMessage: content,
                timestamp: new Date(),
                unreadCount: 0,
              }
            : conv,
        ),
      );

      // Simulate message delivery
      setTimeout(() => {
        setMessages((prev) => ({
          ...prev,
          [conversationId]: prev[conversationId].map((msg) =>
            msg.id === newMessage.id ? { ...msg, status: "delivered" } : msg,
          ),
        }));

        // Simulate reply from the other user
        simulateReply(conversationId, newMessage);
      }, 1000);

      return newMessage;
    },
    [userId, simulateReply],
  );

  const markMessageAsRead = useCallback((conversationId: string, messageId: string) => {
    setMessages((prev) => ({
      ...prev,
      [conversationId]: prev[conversationId].map((msg) =>
        msg.id === messageId ? { ...msg, status: "read" } : msg,
      ),
    }));
  }, []);

  const clearUnreadCount = useCallback((conversationId: string) => {
    setConversations((prev) =>
      prev.map((conv) => (conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv)),
    );
  }, []);

  const createConversation = useCallback((title: string, avatar?: string) => {
    const newConversation: ChatListItem = {
      id: `conv-${Date.now()}`,
      title,
      avatar,
      timestamp: new Date(),
      online: true,
    };

    setConversations((prev) => [...prev, newConversation]);
    return newConversation;
  }, []);

  const deleteConversation = useCallback((conversationId: string) => {
    setConversations((prev) => prev.filter((conv) => conv.id !== conversationId));
    setMessages((prev) => {
      const newMessages = { ...prev };
      delete newMessages[conversationId];
      return newMessages;
    });
  }, []);

  return {
    messages,
    conversations,
    typingStates,
    sendMessage,
    markMessageAsRead,
    clearUnreadCount,
    createConversation,
    deleteConversation,
  };
}
