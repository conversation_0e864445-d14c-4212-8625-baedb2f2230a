import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Merges class names with tailwind
 * @param inputs Class names to merge
 * @returns Merged class names
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Formats a date in a human-readable format
 * @param timestamp Unix timestamp in seconds or milliseconds
 * @param locale Locale to use for formatting (default: 'fr-FR')
 * @returns Formatted date string
 */
export function formatDate(timestamp: number | undefined, locale = "fr-FR"): string {
  if (!timestamp) return "";

  // If the timestamp is in seconds (Unix timestamp), convert to milliseconds
  const milliseconds = timestamp < 10000000000 ? timestamp * 1000 : timestamp;

  return new Date(milliseconds).toLocaleDateString(locale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

/**
 * Formats a currency value
 * @param amount Amount to format
 * @param currency Currency code (default: 'EUR')
 * @param locale Locale to use for formatting (default: 'fr-FR')
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number | undefined,
  currency = "EUR",
  locale = "fr-FR",
): string {
  if (amount === undefined || amount === null) return "";

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
  }).format(amount);
}

/**
 * Formats a file size in bytes to a human-readable format
 * @param bytes File size in bytes
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const units = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
}

/**
 * Groups an array of items by a key
 * @param array Array to group
 * @param key Key to group by
 * @returns Object with grouped items
 */
export function groupBy<T>(array: T[], key: keyof T): { [key: string]: T[] } {
  return array.reduce(
    (result, item) => {
      const groupKey = String(item[key]);
      result[groupKey] = result[groupKey] || [];
      result[groupKey].push(item);
      return result;
    },
    {} as { [key: string]: T[] },
  );
}

/**
 * Generates a Google Calendar URL from an event object
 * @param event Event object containing title, description, start time, end time, and location
 * @returns Google Calendar URL
 */
export function generateGoogleCalendarUrl(event: {
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  location: string;
}) {
  const params = new URLSearchParams({
    action: "TEMPLATE",
    text: event.title,
    details: event.description,
    dates: `${event.startTime.replace(/[-:]/g, "")}/${event.endTime.replace(/[-:]/g, "")}`,
  });

  // Check if the location is a Google Meet link
  try {
    const url = new URL(event.location);
    if (url.hostname === "meet.google.com") {
      // For Google Meet links, use the conferenceData parameter
      params.append("conferenceData", "true");
      params.append("add", event.location);
    } else {
      // For other links (like Zoom), use the location parameter
      params.append("location", event.location);
    }
  } catch {
    // If the URL is invalid, just use it as location
    params.append("location", event.location);
  }

  return `https://calendar.google.com/calendar/render?${params.toString()}`;
}

/**
 * Checks if the given email belongs to a Paradox domain.
 * @param email - The email address to check.
 * @returns A boolean indicating whether the email belongs to a Paradox domain.
 */
export function isParadoxEmail(email: string) {
  const domain = email.split("@")[1];
  const allowedDomains = [
    "paradoxgroup.com",
    "paradoxinstitute.com",
    "paradox.media",
    "paradox-ext.com",
    "paradoxfoundations.com",
    "davidlaroche.fr",
    "larocheproduction.com",
    "davidlarocheworld.com",
  ];
  return allowedDomains.includes(domain);
}
