import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  JoinColumn,
} from 'typeorm';
import { WarrantySection } from '@entities';
import { CheckoutPageTable } from './checkout-page.table';

@Entity({
  name: 'warrantySection',
})
export class WarrantySectionTable implements WarrantySection {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar' })
  icon: string;

  @Column({ type: 'varchar' })
  message: string;

  @JoinColumn({
    name: 'checkoutPageId',
    foreignKeyConstraintName: 'FK_checkoutPageWarrantySection',
  })
  @ManyToOne(
    () => CheckoutPageTable,
    (checkoutPage) => checkoutPage.warrantySections,
  )
  checkoutPage: CheckoutPageTable;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;
}
