"use client";
import { Card, CardContent } from "@/components/ui/card";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ComposedChart,
  Bar,
  Line,
} from "recharts";
import { Loader2 } from "lucide-react";
import useSWR from "swr";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import { anton } from "@/lib/fonts/anton";
import { useTranslations } from "next-intl";
import { PlatformTriadStats } from "@/types/triad";
import { usePlatformTriadStats } from "@/services/triad/client";

interface TimeSeriesData {
  triadsCreated: Array<{ date: string; value: number }>;
  participantsJoined: Array<{ date: string; value: number }>;
}

interface StatsResponse {
  timeSeriesData: TimeSeriesData;
}

interface TimeRange {
  start: Date;
  end: Date;
  type: string;
}

interface TriadsGrowthStatsProps {
  timeRange: TimeRange;
  onDataChange?: (data: PlatformTriadStats | null) => void;
}

export default function TriadsGrowthStats({ timeRange, onDataChange }: TriadsGrowthStatsProps) {
  const t = useTranslations("triad.stats");
  const {
    stats: triadStats,
    isLoading,
    error,
  } = usePlatformTriadStats(timeRange.start.toISOString(), timeRange.end.toISOString());

  useEffect(() => {
    if (triadStats && onDataChange) {
      onDataChange(triadStats);
    }
  }, [triadStats, onDataChange]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-white/50" />
      </div>
    );
  }

  if (error) {
    return <div className="text-center text-red-400">{t("growth.error")}</div>;
  }

  if (!triadStats) return null;

  const { timeSeriesData } = triadStats;

  // Calculate cumulative data
  const cumulativeData = timeSeriesData.triadsCreated.reduce(
    (acc, curr, index) => {
      const prevCumulative = index > 0 ? acc[index - 1].cumulative : 0;
      return [
        ...acc,
        {
          date: curr.date,
          value: curr.value,
          cumulative: prevCumulative + curr.value,
          participants: timeSeriesData.participantsJoined[index]?.value || 0,
        },
      ];
    },
    [] as Array<{
      date: string;
      value: number;
      cumulative: number;
      participants: number;
    }>,
  );

  // Calculate month-over-month growth
  const monthlyGrowth = cumulativeData.map((curr, index) => {
    const prevMonth = index > 0 ? cumulativeData[index - 1].value : 0;
    const growthRate = prevMonth === 0 ? 0 : ((curr.value - prevMonth) / prevMonth) * 100;
    return {
      ...curr,
      growthRate: isFinite(growthRate) ? growthRate : 0,
    };
  });

  // Helper function to get translated time period
  const getTranslatedTimePeriod = (type: string) => {
    return t(`time_periods.${type.toLowerCase()}`);
  };

  return (
    <div className="space-y-6">
      <Card className="overflow-hidden border-0 bg-[#1a1a1a]">
        <CardContent className="p-6">
          <div className="mb-6 flex items-center justify-between">
            <h3 className={cn("text-xl font-semibold text-white", anton.className)}>
              {t("growth.charts.cumulative_growth.title")}
            </h3>
            <div className="rounded-full bg-violet-500/20 px-2.5 py-1 text-xs font-medium text-violet-400">
              {t("time_periods.last", {
                period: getTranslatedTimePeriod(timeRange.type),
              })}
            </div>
          </div>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={cumulativeData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#2a2a2a" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(date) => new Date(date).toLocaleDateString()}
                  stroke="#666"
                  fontSize={12}
                />
                <YAxis stroke="#666" fontSize={12} />
                <Tooltip
                  labelFormatter={(date) => new Date(date).toLocaleDateString()}
                  contentStyle={{
                    backgroundColor: "#1a1a1a",
                    border: "1px solid #2a2a2a",
                    borderRadius: "6px",
                  }}
                  labelStyle={{ color: "#fff" }}
                />
                <Area
                  type="monotone"
                  dataKey="cumulative"
                  name={t("growth.charts.cumulative_growth.total_triads")}
                  stroke="#8b5cf6"
                  fill="#8b5cf6"
                  fillOpacity={0.2}
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border-0 bg-[#1a1a1a]">
        <CardContent className="p-6">
          <div className="mb-6 flex items-center justify-between">
            <h3 className={cn("text-xl font-semibold text-white", anton.className)}>
              {t("growth.charts.monthly_growth.title")}
            </h3>
            <div className="rounded-full bg-fuchsia-500/20 px-2.5 py-1 text-xs font-medium text-fuchsia-400">
              {t("growth.charts.monthly_growth.subtitle")}
            </div>
          </div>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={monthlyGrowth} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#2a2a2a" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(date) => new Date(date).toLocaleDateString()}
                  stroke="#666"
                  fontSize={12}
                />
                <YAxis yAxisId="left" stroke="#666" fontSize={12} />
                <YAxis yAxisId="right" orientation="right" stroke="#666" fontSize={12} />
                <Tooltip
                  labelFormatter={(date) => new Date(date).toLocaleDateString()}
                  contentStyle={{
                    backgroundColor: "#1a1a1a",
                    border: "1px solid #2a2a2a",
                    borderRadius: "6px",
                  }}
                  labelStyle={{ color: "#fff" }}
                />
                <Bar
                  yAxisId="left"
                  dataKey="value"
                  name={t("growth.charts.monthly_growth.new_triads")}
                  fill="#d946ef"
                  fillOpacity={0.7}
                  radius={[4, 4, 0, 0]}
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="growthRate"
                  name={t("growth.charts.monthly_growth.growth_rate")}
                  stroke="#f0abfc"
                  strokeWidth={2}
                  dot={false}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border-0 bg-[#1a1a1a]">
        <CardContent className="p-6">
          <div className="mb-6 flex items-center justify-between">
            <h3 className={cn("text-xl font-semibold text-white", anton.className)}>
              {t("growth.charts.comparison.title")}
            </h3>
            <div className="rounded-full bg-rose-500/20 px-2.5 py-1 text-xs font-medium text-rose-400">
              {t("growth.charts.comparison.subtitle")}
            </div>
          </div>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart
                data={cumulativeData}
                margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#2a2a2a" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(date) => new Date(date).toLocaleDateString()}
                  stroke="#666"
                  fontSize={12}
                />
                <YAxis stroke="#666" fontSize={12} />
                <Tooltip
                  labelFormatter={(date) => new Date(date).toLocaleDateString()}
                  contentStyle={{
                    backgroundColor: "#1a1a1a",
                    border: "1px solid #2a2a2a",
                    borderRadius: "6px",
                  }}
                  labelStyle={{ color: "#fff" }}
                />
                <Bar
                  dataKey="value"
                  name={t("growth.charts.comparison.new_triads")}
                  fill="#f43f5e"
                  fillOpacity={0.7}
                  radius={[4, 4, 0, 0]}
                />
                <Line
                  type="monotone"
                  dataKey="participants"
                  name={t("growth.charts.comparison.new_participants")}
                  stroke="#fda4af"
                  strokeWidth={2}
                  dot={false}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
