"use client";

import { useTranslations } from "next-intl";

const KeyValue = ({ label, value }: { label: string; value: string }) => {
  return (
    <div className="flex flex-col">
      <div className="text-sm font-bold text-[#6e6e6e]">{label}</div>
      <div className="text-sm">{value}</div>
    </div>
  );
};

export default function CohortDetailsContent() {
  const t = useTranslations("cohorts.details-page");

  return (
    <div className="w-full">
      <div className="pb-4 text-2xl font-bold">{t("info.content-title")}</div>
      <div className="rounded-2xl bg-[#171717] px-8 pt-8">
        <div className="flex flex-row gap-4 pb-8">
          <div className="flex flex-col">
            <KeyValue label="Cohorte" value="Cohorte 1" />
          </div>
          <div className="flex flex-col">
            <KeyValue label="Cohorte" value="Cohorte 1" />
          </div>
        </div>
        <div className="flex flex-row gap-4 pb-8">
          <div className="flex flex-col">
            <KeyValue label="Cohorte" value="Cohorte 1" />
          </div>
        </div>
        <div className="flex flex-row gap-4 pb-8">
          <div className="flex flex-col">
            <KeyValue label="Cohorte" value="Cohorte 1" />
          </div>
        </div>
      </div>
    </div>
  );
}
