import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import { Permissions } from "@px-shared-account/hermes";
import { hasServerPermission } from "@/lib/permissions/server";
import RolesTable from "./components/RolesTable";

export default async function RolesPage() {
  const canReadRoles = await hasServerPermission(Permissions.Role.READ);

  if (!canReadRoles) {
    redirect("/dashboard");
  }

  return (
    <div className="space-y-4 py-4">
      <div className="space-y-4 border-b pb-4">
        <div className="flex flex-wrap items-center justify-between"></div>
      </div>
      <RolesTable />
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "navigation.user-management" });

  return {
    title: t("roles"),
  };
}
