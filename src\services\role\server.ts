import { auth } from "@clerk/nextjs/server";
import server<PERSON>etcher from "@/lib/server/server-fetcher";
import { ROLE_API_ENDPOINTS, RoleApiTypes, ListRolesParams, RoleApiPayloads } from "./core";

/**
 * Creates a server-side token getter for the fetcher
 * @returns Promise with authentication token or null
 */
const getServerToken = async () => {
  const session = await auth();
  return session?.getToken() || null;
};

// Initialize the fetcher with server-side token getter
const apiFetcher = serverFetcher(getServerToken);

/**
 * Server-side function for fetching role list
 * @param params Filter and pagination parameters
 * @returns Promise with role list data
 */
export async function getRoleList(params: ListRolesParams = {}): Promise<RoleApiTypes["list"]> {
  const { url } = ROLE_API_ENDPOINTS.list(params);
  return apiFetcher(url);
}

/**
 * Server-side function for fetching a specific role by ID
 * @param id Role ID to fetch
 * @returns Promise with role data
 */
export async function getRoleById(id: number): Promise<RoleApiTypes["getById"]> {
  const { url } = ROLE_API_ENDPOINTS.getById(id);
  return apiFetcher(url);
}

/**
 * Alias for getRoleById to maintain consistency with other services
 * @param id Role ID to fetch
 * @returns Promise with role data
 */
export const getRoleDetails = getRoleById;

/**
 * Server-side function for creating a new role
 * @param data Role creation data
 * @returns Promise with created role data
 */
export async function createRole(data: RoleApiPayloads["create"]): Promise<RoleApiTypes["create"]> {
  const { url, method } = ROLE_API_ENDPOINTS.create();
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for updating an existing role
 * @param id Role ID to update
 * @param data Role update data
 * @returns Promise with update result
 */
export async function updateRole(
  id: number,
  data: RoleApiPayloads["update"],
): Promise<RoleApiTypes["update"]> {
  const { url, method } = ROLE_API_ENDPOINTS.update(id);
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for deleting a role
 * @param id Role ID to delete
 * @returns Promise with delete result
 */
export async function deleteRole(id: number): Promise<RoleApiTypes["delete"]> {
  const { url, method } = ROLE_API_ENDPOINTS.delete(id);
  return apiFetcher(url, { method });
}
