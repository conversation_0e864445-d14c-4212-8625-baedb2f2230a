import { getTranslations } from "next-intl/server";
import { Permissions } from "@px-shared-account/hermes";
import { hasServerPermission } from "@/lib/permissions/server";

import UsersTable from "./components/UsersTable";
import { redirect } from "next/navigation";

export default async function Page() {
  const canReadUsers = await hasServerPermission(Permissions.User.READ);

  if (!canReadUsers) {
    redirect("/dashboard");
  }

  return (
    <div className="space-y-4 py-4">
      <div className="space-y-4 border-b pb-4">
        <div className="flex flex-wrap items-center justify-between"></div>
      </div>
      <UsersTable />
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "navigation.user-management" });

  return {
    title: t("users"),
  };
}
