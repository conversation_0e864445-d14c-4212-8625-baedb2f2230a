import { DateRange } from "react-day-picker";
import { ITriadBase } from "@px-shared-account/hermes";

interface FilterValues {
  period?: string;
  timeWindow?: string;
  sessionType?: string;
  status?: string;
  registered?: string;
}

export function filterTriads(
  triads: ITriadBase[],
  searchQuery: string,
  filterValues: FilterValues,
  userId?: string,
): ITriadBase[] {
  return (
    triads?.filter((triad) => {
      // Apply search filter
      if (!matchesSearch(triad, searchQuery)) return false;

      // Apply date range filter
      if (!matchesDateRange(triad, filterValues.period)) return false;

      // Apply time window filter
      if (!matchesTimeWindow(triad, filterValues.timeWindow)) return false;

      // Apply session type filter
      if (!matchesSessionType(triad, filterValues.sessionType)) return false;

      // Apply status filter
      if (!matchesStatus(triad, filterValues.status)) return false;

      // Apply registration filter
      if (!matchesRegistration(triad, filterValues.registered, userId)) return false;

      return true;
    }) || []
  );
}

function matchesSearch(triad: ITriadBase, searchQuery: string): boolean {
  if (!searchQuery) return true;

  const searchLower = searchQuery.toLowerCase();
  const organizerName = triad.organizer?.firstName + " " + triad.organizer?.lastName;

  return (
    triad.title.toLowerCase().includes(searchLower) ||
    organizerName.toLowerCase().startsWith(searchLower) ||
    triad.participants?.some((p) => {
      const participantName = p.user.firstName + " " + p.user.lastName;
      return participantName.toLowerCase().startsWith(searchLower);
    }) ||
    false
  );
}

function matchesDateRange(triad: ITriadBase, periodValue?: string): boolean {
  if (!periodValue) return true;

  try {
    const dateRange = JSON.parse(periodValue) as DateRange;
    if (!dateRange?.from || !dateRange?.to) return true;

    const sessionDate = new Date(triad.sessionTime);
    const fromDate = new Date(dateRange.from);
    const toDate = new Date(dateRange.to);
    // Set the end date to the end of the day (23:59:59.999)
    toDate.setHours(23, 59, 59, 999);

    return sessionDate >= fromDate && sessionDate <= toDate;
  } catch {
    return true;
  }
}

function matchesTimeWindow(triad: ITriadBase, timeWindowValue?: string): boolean {
  if (!timeWindowValue) return true;

  try {
    const timeWindow = JSON.parse(timeWindowValue) as {
      from?: Date;
      to?: Date;
    };

    if (!timeWindow?.from || !timeWindow?.to) return true;

    const sessionDate = new Date(triad.sessionTime);
    const sessionHours = sessionDate.getHours();
    const sessionMinutes = sessionDate.getMinutes();
    const sessionTime = sessionHours * 60 + sessionMinutes;

    const fromTime = new Date(timeWindow.from);
    const toTime = new Date(timeWindow.to);
    const fromMinutes = fromTime.getHours() * 60 + fromTime.getMinutes();
    const toMinutes = toTime.getHours() * 60 + toTime.getMinutes();

    return sessionTime >= fromMinutes && sessionTime <= toMinutes;
  } catch {
    return true;
  }
}

function matchesSessionType(triad: ITriadBase, sessionType?: string): boolean {
  if (!sessionType || sessionType === "all") return true;
  return triad.sessionType === sessionType;
}

function matchesStatus(triad: ITriadBase, status?: string): boolean {
  if (!status) return true;

  const availableSpots = triad.maxParticipants - (triad.participants?.length || 0) - 1; // -1 for organizer
  console.log({
    title: triad.title,
    availableSpots,
    participants: triad.participants?.length,
    maxParticipants: triad.maxParticipants,
    status,
  });

  switch (status) {
    case "2slotsandmore":
      return availableSpots >= 2;
    case "1slot":
      return availableSpots === 1;
    case "full":
      return availableSpots === 0;
    default:
      return true;
  }
}

function matchesRegistration(
  triad: ITriadBase,
  registeredFilter?: string,
  userId?: string,
): boolean {
  if (!registeredFilter || !userId) return true;

  const isRegistered =
    triad.organizer?.ssoId === userId || triad.participants?.some((p) => p.user.ssoId === userId);

  switch (registeredFilter) {
    case "registered":
      return isRegistered || false;
    case "not_registered":
      return !isRegistered;
    default:
      return true;
  }
}
