"use client";

import { useTranslations } from "next-intl";
import { useRouter, usePathname } from "next/navigation";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface CohortTabsProps {
  activeTab: string;
}

export function CohortTabs({ activeTab }: CohortTabsProps) {
  const t = useTranslations("cohorts");
  const router = useRouter();
  const pathname = usePathname();

  const tabs = [
    { value: "information", label: t("tabs.information") },
    { value: "students", label: t("tabs.students") },
    { value: "offers", label: t("tabs.offers") },
  ];

  const handleTabChange = (value: string) => {
    router.push(`${pathname}?tab=${value}`);
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange}>
      <TabsList>
        {tabs.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value}>
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
