"use client";

import { useState, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format, subDays, startOfDay, endOfDay, subMonths, subWeeks, subYears } from "date-fns";
import { cn } from "@/lib/utils";
import { ExportStats } from "./ExportStats";
import { Combobox } from "@/components/base/combobox";
import { useTranslations } from "next-intl";

export type TimeRangeType = "day" | "week" | "month" | "year" | "custom";

interface TimeRangeSelectorProps {
  onRangeChange: (range: { start: Date; end: Date; type: TimeRangeType }) => void;
  data?: any;
}

export function TimeRangeSelector({ onRangeChange, data }: TimeRangeSelectorProps) {
  const t = useTranslations("triad.stats");
  const [range, setRange] = useState<TimeRangeType>("month");
  const [date, setDate] = useState<Date | undefined>(new Date());

  const timeRangeOptions = [
    {
      label: t("time_periods.day"),
      value: "day",
    },
    {
      label: t("time_periods.week"),
      value: "week",
    },
    {
      label: t("time_periods.month"),
      value: "month",
    },
    {
      label: t("time_periods.year"),
      value: "year",
    },
    {
      label: t("time_periods.custom"),
      value: "custom",
    },
  ];

  const updateDateRange = useCallback(
    (type: TimeRangeType, selectedDate?: Date) => {
      const end = endOfDay(selectedDate || new Date());
      let start: Date;

      switch (type) {
        case "day":
          start = startOfDay(end);
          break;
        case "week":
          start = startOfDay(subWeeks(end, 1));
          break;
        case "month":
          start = startOfDay(subMonths(end, 1));
          break;
        case "year":
          start = startOfDay(subYears(end, 1));
          break;
        case "custom":
          start = startOfDay(selectedDate || new Date());
          break;
        default:
          start = startOfDay(subDays(end, 30));
      }

      onRangeChange({ start, end, type });
    },
    [onRangeChange],
  );

  const onRangeTypeChange = (value: string | string[]) => {
    // Ensure we're working with a single string value
    const selectedValue = Array.isArray(value) ? value[0] : value;
    if (selectedValue && timeRangeOptions.some((opt) => opt.value === selectedValue)) {
      setRange(selectedValue as TimeRangeType);
      if (selectedValue !== "custom") {
        updateDateRange(selectedValue as TimeRangeType);
      }
    }
  };

  const onDateSelect = (selectedDate: Date | undefined) => {
    setDate(selectedDate);
    if (selectedDate) {
      updateDateRange("custom", selectedDate);
    }
  };

  return (
    <div className="flex items-center justify-between gap-4">
      <div className="min-w-[180px]">
        <Combobox
          value={range}
          onChange={onRangeTypeChange}
          options={timeRangeOptions}
          label={t("time_range.label")}
          placeholder={t("time_range.placeholder")}
          triggerClassName="min-w-40 bg-background text-white rounded-full transition-all duration-200"
          contentClassName="z-[60]"
        />
      </div>

      {range === "custom" && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "min-w-[240px] justify-start text-left font-normal",
                "bg-background rounded-full border-[#2a2a2a] text-white transition-all duration-200 hover:bg-white hover:text-[#222222]",
                !date && "text-muted-foreground",
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "PPP") : <span>{t("time_range.pick_date")}</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar mode="single" selected={date} onSelect={onDateSelect} initialFocus />
          </PopoverContent>
        </Popover>
      )}

      {data && <ExportStats data={data} />}
    </div>
  );
}
