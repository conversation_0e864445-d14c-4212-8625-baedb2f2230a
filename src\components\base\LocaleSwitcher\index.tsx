"use client";

import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { useTransition } from "react";

import { Locale, usePathname, useRouter, routing } from "@/i18n/routing";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import useUserStore from "@/hooks/store/user";
import { cn } from "@/lib/utils";

export default function LocaleSwitcher({ className = "" }: { className?: string }) {
  const router = useRouter();
  const t = useTranslations("locale-switcher");
  const [_, startTransition] = useTransition();
  const pathname = usePathname();
  const params = useParams();
  const { language, setLanguage } = useUserStore();

  function onSelectChange(value: Locale) {
    const nextLocale = value;

    startTransition(() => {
      router.replace(
        // @ts-expect-error -- TypeScript will validate that only known `params`
        // are used in combination with a given `pathname`. Since the two will
        // always match for the current route, we can skip runtime checks.
        { pathname, params },
        { locale: nextLocale },
      );
    });
    router.refresh();
    setLanguage(nextLocale);
  }

  const FrenchFlag = <span className="fi fi-fr"></span>;

  const BritishFlag = <span className="fi fi-us"></span>;

  return (
    <Select value={language} onValueChange={onSelectChange}>
      <SelectTrigger
        className={cn(
          "bg-background/40 hover:bg-accent/50 hover:text-accent-foreground border-input w-fit border",
          className,
        )}
      >
        <SelectValue placeholder={language === "fr" ? FrenchFlag : BritishFlag} />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="fr">{FrenchFlag}</SelectItem>
        <SelectItem value="en">{BritishFlag}</SelectItem>
      </SelectContent>
    </Select>
  );
}
