import { useCallback, useState } from "react";
import { CohortStatus } from "@px-shared-account/hermes";
import { useFilterBar } from "@/components/base/filter-bar";

interface FilterState {
  status?: CohortStatus;
}

export const useCohortsFilter = () => {
  const [filters, setFilters] = useState<FilterState>({});

  const handleFilterChange = useCallback((filters: Record<string, any>) => {
    setFilters(filters);
  }, []);

  const { values: selectedFilters, clearAll: clearFilters } = useFilterBar({
    steps: [
      {
        id: "status",
        type: "filter",
        label: "Status",
        options: Object.values(CohortStatus).map((status) => ({
          label: status,
          value: status,
        })),
      },
    ],
    onChange: handleFilterChange,
  });

  return {
    filters,
    selectedFilters,
    clearFilters,
  };
};
