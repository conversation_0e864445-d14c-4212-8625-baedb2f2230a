@config "../../tailwind.config.ts";
@import "tailwindcss";
@import "tw-animate-css";
@import "flag-icons/css/flag-icons.min.css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: hsl(204 33% 98%); /* #F8FAFB */
  --foreground: hsl(0 0% 3.9%);
  --background-secondary: hsl(0, 0%, 96.1%);
  --background-secondary-foreground: hsl(0 0% 9%);
  --background-tertiary: hsl(0, 0%, 13%);
  --background-tertiary-foreground: hsl(240, 4.8%, 97.9%);
  --background-quaternary: hsl(0, 0%, 22%);
  --background-quaternary-foreground: hsl(240, 4.8%, 99.9%);
  --card: hsl(0 0% 100%); /* Pure white */
  --card-foreground: hsl(0 0% 3.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 3.9%);
  --primary: hsl(339 62% 34%); /* Pure black for buttons */
  --primary-foreground: hsl(0 0% 100%);
  --burgundy: hsl(339 62% 34%); /* #8D2146 */
  --burgundy-foreground: hsl(0 0% 100%);
  --secondary: hsl(0 0% 96.1%);
  --secondary-foreground: hsl(0 0% 9%);
  --muted: hsl(0 0% 96.1%);
  --muted-foreground: hsl(0 0% 45.1%);
  --accent: hsl(0 0% 96.1%);
  --accent-foreground: hsl(0 0% 9%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(240 5.9% 90%);
  --input: hsl(240 5.9% 90%); /* Pure white for form fields */
  --ring: hsl(240 5% 64.9%);
  --radius: 9999px; /* Fully rounded for buttons and search */
  --radius-card: 0.5rem; /* Keep normal radius for cards */
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar-background: hsl(0 0% 100%); /* Pure white sidebar */
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
  --sidebar: hsl(0 0% 98%);
}

.dark {
  --background: hsl(0 0% 4.7%);
  --foreground: hsl(0 0% 98%);
  --background-secondary: hsl(0, 0%, 7%);
  --background-secondary-foreground: hsl(240 4.8% 95.9%);
  --background-tertiary: hsl(0, 0%, 13%);
  --background-tertiary-foreground: hsl(240, 4.8%, 97.9%);
  --background-quaternary: hsl(0, 0%, 22%);
  --background-quaternary-foreground: hsl(240, 4.8%, 99.9%);
  --card: hsl(0 0% 3.9%);
  --card-foreground: hsl(0 0% 98%);
  --popover: hsl(0 0% 3.9%);
  --popover-foreground: hsl(0 0% 98%);
  --primary: hsl(339 62% 34%);
  --primary-foreground: hsl(0 0% 100%);
  --burgundy: hsl(339 62% 34%); /* #8D2146 */
  --burgundy-foreground: hsl(0 0% 100%);
  --secondary: hsl(0 0% 14.9%);
  --secondary-foreground: hsl(0 0% 98%);
  --muted: hsl(0 0% 14.9%);
  --muted-foreground: hsl(0 0% 63.9%);
  --accent: hsl(0 0% 14.9%);
  --accent-foreground: hsl(0 0% 98%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(0 0% 14.9%);
  --input: hsl(0 0% 14.9%);
  --ring: hsl(0 0% 83.1%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar-background: hsl(0 0% 9%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
  --sidebar: hsl(240 5.9% 10%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-background-secondary: var(--background-secondary);
  --color-background-secondary-foreground: var(--background-secondary-foreground);
  --color-background-tertiary: var(--background-tertiary);
  --color-background-tertiary-foreground: var(--background-tertiary-foreground);
  --color-background-quaternary: var(--background-quaternary);
  --color-background-quaternary-foreground: var(--background-quaternary-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-burgundy: var(--burgundy);
  --color-burgundy-foreground: var(--burgundy-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar-background: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar: var(--sidebar);
  /* Non-color variables like radius are not included here */
}

body {
  font-family: var(--font-clash), Helvetica, Arial, sans-serif;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
