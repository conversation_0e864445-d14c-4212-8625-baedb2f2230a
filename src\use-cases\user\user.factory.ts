import { RoleEntity, UserEntity } from '@entities';
import { Injectable } from '@nestjs/common';
import { CreateUserDto } from '@dtos';
import { ConfigService, ChargebeeSecrets } from '@config';
import { UserType, IUserChargebeeIds } from '@px-shared-account/hermes';
import { FiscalEntity } from '@enums';
import { User } from '@clerk/backend';

@Injectable()
export class UserFactory {
  private readonly PG_ENTITY_ID: string;
  private readonly PI_ENTITY_ID: string;
  private readonly PT_ENTITY_ID: string;
  private readonly pxDomains: string[];
  private readonly secrets: ChargebeeSecrets;

  constructor(private readonly configService: ConfigService) {
    this.secrets = this.configService.chargebeeSecrets;
    this.PG_ENTITY_ID = this.secrets.CHARGEBEE_BUSINESS_ID_PG;
    this.PI_ENTITY_ID = this.secrets.CHARGEBEE_BUSINESS_ID_PI;
    this.PT_ENTITY_ID = this.secrets.CHARGEBEE_BUSINESS_ID_PP;
    this.pxDomains = [
      'paradoxgroup.com',
      'paradox-ext.com',
      'paradoxinstitute.com',
      'paradoxfoundation.com',
      'paradox.io',
      'paradox.media',
    ];
  }

  /**
   * Generates a new UserEntity object based on the provided CreateUserDto
   * @param userDTO The CreateUserDto object containing user information
   * @returns A new UserEntity object
   */
  generate(userDTO: CreateUserDto): UserEntity {
    const user = new UserEntity();
    user.firstName = userDTO.firstName;
    user.lastName = userDTO.lastName;
    user.email = userDTO.email?.toLowerCase()?.trim();
    user.ssoId = userDTO.ssoId;
    const userRole = new RoleEntity();
    userRole.id = userDTO.role?.id;
    user.role = userRole;
    user.metaData = userDTO.metaData;
    user.type = this.isParadoxUser(userDTO.email)
      ? UserType.INTERNAL
      : UserType.EXTERNAL;
    user.chargebeeIds = this.generateChargebeeIds(userDTO.chargebeeIds);
    user.crmId = userDTO.crmId;
    user.ssoId = userDTO.ssoId;
    return user;
  }

  /**
   * Gets the business entity name based on business entity ID
   * @param entityId
   * @returns
   */
  getBusinessEntityName(entityId: string): FiscalEntity {
    switch (entityId) {
      case this.PG_ENTITY_ID:
        return FiscalEntity.PG;
      case this.PI_ENTITY_ID:
        return FiscalEntity.PI;
      case this.PT_ENTITY_ID:
        return FiscalEntity.PP;
      default:
        throw new Error(
          `Invalid business entity provided for customer: ${entityId}`,
        );
    }
  }

  /**
   * Checks if the provided email is a Paradox email
   * @param email Email address to check
   * @returns A boolean indicating whether the email is a Paradox email
   */
  isParadoxUser(email: string): boolean {
    return this.pxDomains.includes(email.split('@')[1]);
  }

  /**
   * Generates a Chargebee ID for each business entity
   * @param idsMap Map of business entity IDs to customer's Chargebee IDs
   * @returns A `CustomerChargebeeInfo` object containing the generated Chargebee IDs
   */
  generateChargebeeIds(idsMap: Record<string, string>): IUserChargebeeIds {
    return Object.entries(idsMap).reduce(
      (chargebeeIds, [businessEntityId, chargebeeId]) => {
        if (businessEntityId === '') {
          return chargebeeIds;
        }
        const businessEntityName = this.getBusinessEntityName(businessEntityId);
        chargebeeIds[businessEntityName] = chargebeeId;
        return chargebeeIds;
      },
      { PG: '', PP: '', PI: '' } as IUserChargebeeIds,
    );
  }

  /**
   * Generates a customer entity from a Clerk user
   * @param clerkUser Clerk user to generate the customer entity from
   * @param chargebeeId Chargebee ID of the customer
   * @param businessEntityId Business entity ID of the customer
   * @returns A customer entity
   */
  generateFromClerkUser(
    clerkUser: User,
    chargebeeId: string,
    businessEntityId: string,
  ): UserEntity {
    const user = new UserEntity();
    user.firstName = clerkUser.firstName;
    user.lastName = clerkUser.lastName;
    user.email = clerkUser.primaryEmailAddress?.emailAddress?.toLowerCase()?.trim();
    user.type = this.isParadoxUser(user.email)
      ? UserType.INTERNAL
      : UserType.EXTERNAL;
    user.ssoId = clerkUser.id;
    user.chargebeeIds = this.generateChargebeeIds({
      [businessEntityId]: chargebeeId,
    });
    return user;
  }
}
