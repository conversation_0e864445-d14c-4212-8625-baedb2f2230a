import { MigrationInterface, QueryRunner } from 'typeorm';

export class VegaMigration1747045317314 implements MigrationInterface {
  name = 'VegaMigration1747045317314';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."roles_group_enum" AS ENUM('ADMIN', 'PROGRAM_MANAGER', 'STUDENT', 'EXTERNAL')`,
    );
    await queryRunner.query(
      `CREATE TABLE "roles" ("id" SERIAL NOT NULL, "group" "public"."roles_group_enum" NOT NULL DEFAULT 'STUDENT', "name" character varying NOT NULL, "isDefault" boolean NOT NULL DEFAULT false, "permissions" text array NOT NULL DEFAULT '{}', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "UQ_648e3f5447f725579d7d4ffdfb7" UNIQUE ("name"), CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."userProfiles_gender_enum" AS ENUM('MALE', 'FEMALE')`,
    );
    await queryRunner.query(
      `CREATE TABLE "userProfiles" ("id" SERIAL NOT NULL, "dateOfBirth" date, "interests" character varying array, "city" character varying, "country" character varying, "gender" "public"."userProfiles_gender_enum", "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_94a08f7c0b98f9607a0ce0ebf4c" PRIMARY KEY ("id"))`,
    );
    /**
     * Migrating customers to users
     */
    await queryRunner.query(`ALTER TABLE "customers" RENAME TO "users"`);
    await queryRunner.query(
      `ALTER INDEX "idx_customers_email" RENAME TO "idx_users_email"`,
    );
    await queryRunner.query(`
      ALTER TYPE "public"."customers_type_enum"
      RENAME TO "customers_type_enum_to_be_dropped"
    `);
    await queryRunner.query(`
      CREATE TYPE "public"."users_type_enum" AS ENUM('internal', 'external', 'beta_tester')
    `);
    await queryRunner.query(`
      ALTER TABLE "users"
      ALTER COLUMN "type" DROP DEFAULT,
      ALTER COLUMN "type" TYPE "public"."users_type_enum"
        USING "type"::text::"public"."users_type_enum",
      ALTER COLUMN "type" SET DEFAULT 'external'
    `);
    await queryRunner.query(`
      DROP TYPE "public"."customers_type_enum_to_be_dropped"
    `);
    await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "roleId" integer`);
    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN "profileId" integer`,
    );
    await queryRunner.query(`
      ALTER TABLE "users"
      ADD CONSTRAINT "FK_users_role"
      FOREIGN KEY ("roleId")
      REFERENCES "roles"("id")
      ON DELETE SET NULL
    `);
    await queryRunner.query(`
      ALTER TABLE "users"
      ADD CONSTRAINT "FK_users_profile"
      FOREIGN KEY ("profileId")
      REFERENCES "userProfiles"("id")
      ON DELETE SET NULL
    `);
    await queryRunner.query(
      `CREATE TYPE "public"."cohorts_status_enum" AS ENUM('ACTIVE', 'PAST', 'FUTURE')`,
    );
    await queryRunner.query(
      `CREATE TABLE "cohorts" ("id" SERIAL NOT NULL, "name" character varying(100) NOT NULL, "description" character varying(1500) NOT NULL, "maxParticipants" integer NOT NULL, "currentParticipants" integer, "startDate" TIMESTAMP NOT NULL, "endDate" TIMESTAMP NOT NULL, "status" "public"."cohorts_status_enum" NOT NULL DEFAULT 'ACTIVE', "communityInfo" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "courseId" integer, CONSTRAINT "PK_fd38f76b135e907b834fda1e752" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."courses_status_enum" AS ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED')`,
    );
    await queryRunner.query(
      `CREATE TABLE "courses" ("id" SERIAL NOT NULL, "name" character varying(255) NOT NULL, "description" character varying(1500) NOT NULL, "bannerImage" character varying(100) NOT NULL, "cardImage" character varying(100) NOT NULL, "thumbnail" character varying(100) NOT NULL, "status" "public"."courses_status_enum" DEFAULT 'DRAFT', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_3f70a487cc718ad8eda4e6d58c9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "Cohort_Students" ("cohortId" integer NOT NULL, "userId" integer NOT NULL, CONSTRAINT "PK_ffc85af15ac7c05642e3a28339f" PRIMARY KEY ("cohortId", "userId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cohort_students_cohortId" ON "Cohort_Students" ("cohortId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cohort_students_userId" ON "Cohort_Students" ("userId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "Course_Managers" ("courseId" integer NOT NULL, "userId" integer NOT NULL, CONSTRAINT "PK_38a03fb73bfa5ec19cdf27a9865" PRIMARY KEY ("courseId", "userId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_course_managers_courseId" ON "Course_Managers" ("courseId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_course_managers_userId" ON "Course_Managers" ("userId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "Course_Offers" ("courseId" integer NOT NULL, "offerId" integer NOT NULL, CONSTRAINT "PK_44d1ca782eb4d8d95034b2b7832" PRIMARY KEY ("courseId", "offerId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_course_offers_courseId" ON "Course_Offers" ("courseId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_course_offers_offerId" ON "Course_Offers" ("offerId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "cohorts" ADD CONSTRAINT "FK_16678415d388dd072c6640a77d3" FOREIGN KEY ("courseId") REFERENCES "courses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_368e146b785b574f42ae9e53d5e" FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_b1bda35cdb9a2c1b777f5541d87" FOREIGN KEY ("profileId") REFERENCES "userProfiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "Cohort_Students" ADD CONSTRAINT "FK_Cohort_Students_cohortId" FOREIGN KEY ("cohortId") REFERENCES "cohorts"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "Cohort_Students" ADD CONSTRAINT "FK_Cohort_Students_userId" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "Course_Managers" ADD CONSTRAINT "FK_Course_Managers_courseId" FOREIGN KEY ("courseId") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "Course_Managers" ADD CONSTRAINT "FK_Course_Managers_userId" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "Course_Offers" ADD CONSTRAINT "FK_Course_Offers_courseId" FOREIGN KEY ("courseId") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "Course_Offers" ADD CONSTRAINT "FK_Course_Offers_offerId" FOREIGN KEY ("offerId") REFERENCES "productOffers"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "Course_Offers" DROP CONSTRAINT "FK_Course_Offers_offerId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "Course_Offers" DROP CONSTRAINT "FK_Course_Offers_courseId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "Course_Managers" DROP CONSTRAINT "FK_Course_Managers_userId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "Course_Managers" DROP CONSTRAINT "FK_Course_Managers_courseId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "Cohort_Students" DROP CONSTRAINT "FK_Cohort_Students_userId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "Cohort_Students" DROP CONSTRAINT "FK_Cohort_Students_cohortId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_b1bda35cdb9a2c1b777f5541d87"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_368e146b785b574f42ae9e53d5e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cohorts" DROP CONSTRAINT "FK_16678415d388dd072c6640a77d3"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_course_offers_offerId"`);
    await queryRunner.query(`DROP INDEX "public"."idx_course_offers_courseId"`);
    await queryRunner.query(`DROP TABLE "Course_Offers"`);
    await queryRunner.query(`DROP INDEX "public"."idx_course_managers_userId"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_course_managers_courseId"`,
    );
    await queryRunner.query(`DROP TABLE "Course_Managers"`);
    await queryRunner.query(`DROP INDEX "public"."idx_cohort_students_userId"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_cohort_students_cohortId"`,
    );
    await queryRunner.query(`DROP TABLE "Cohort_Students"`);
    await queryRunner.query(`DROP TABLE "courses"`);
    await queryRunner.query(`DROP TYPE "public"."courses_status_enum"`);
    await queryRunner.query(`DROP TABLE "cohorts"`);
    await queryRunner.query(`DROP TYPE "public"."cohorts_status_enum"`);
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_users_profile"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_users_role"`,
    );
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "profileId"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "roleId"`);
    await queryRunner.query(`
      CREATE TYPE "public"."customers_type_enum_to_be_dropped" AS ENUM('internal', 'external', 'beta_tester')
    `);
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "type" TYPE "public"."customers_type_enum_to_be_dropped" USING "type"::text::"public"."customers_type_enum_to_be_dropped"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "type" SET DEFAULT 'external'`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."customers_type_enum_to_be_dropped" RENAME TO "customers_type_enum"`,
    );
    await queryRunner.query(
      `ALTER INDEX "idx_users_email" RENAME TO "idx_customers_email"`,
    );
    await queryRunner.query(`ALTER TABLE "users" RENAME TO "customers"`);
    await queryRunner.query(`DROP TABLE "userProfiles"`);
    await queryRunner.query(`DROP TYPE "public"."userProfiles_gender_enum"`);
    await queryRunner.query(`DROP TABLE "roles"`);
    await queryRunner.query(`DROP TYPE "public"."roles_group_enum"`);
  }
}
