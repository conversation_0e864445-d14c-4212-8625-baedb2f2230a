import FAQSelector from "@/components/(settings)/faq/FAQSelector";
import IframePlayer from "@/components/(settings)/faq/IframePlayer";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

export default function Page() {
  return (
    <>
      <div className="px-4">
        <BreadcrumbSettings active="/faq/pxl" />
      </div>
      <div className="px-4">
        <FAQSelector isActive="pxl" />
        <div className="h-12" />
        <IframePlayer src="https://paradoxlearning.frontkb.com/" title="PXL FAQ" />
      </div>
    </>
  );
}

export async function generateMetadata() {
  return {
    title: "PXL FAQ",
  };
}
