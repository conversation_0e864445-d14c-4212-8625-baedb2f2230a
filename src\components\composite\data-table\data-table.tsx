"use client";

import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TableCheckbox } from "@/components/base/data-table/table-checkbox";
import { TextCell } from "@/components/base/data-table/cell-content/text-cell";
import { ChipCell } from "@/components/base/data-table/cell-content/chip-cell";
import { BadgeCell } from "@/components/base/data-table/cell-content/badge-cell";
import { ThumbnailCell } from "@/components/base/data-table/cell-content/thumbnail-cell";
import { ThumbTextCell } from "@/components/base/data-table/cell-content/thumb-text-cell";
import { TableToolbar } from "./table-toolbar";
import { ChevronRight } from "lucide-react";
import { useState } from "react";
import {
  type ColumnDef,
  type DataTableProps,
  type ChipVariant,
  type BadgeVariant,
} from "@/types/data-table";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

const tableRowVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: (i: number) => ({
    opacity: 1,
    x: 0,
    transition: {
      delay: i * 0.05,
    },
  }),
  removed: { opacity: 0, x: 20 },
};

/**
 * A versatile data table component with rich features and smooth animations.
 *
 * Features:
 * - Row selection with checkboxes
 * - Custom cell renderers for different data types
 * - Search functionality with instant feedback
 * - Row actions with click handlers
 * - Smooth animations for all state changes
 * - Responsive design with proper mobile handling
 * - Internationalization support
 *
 * Cell Types:
 * - text: Simple text display with alignment options
 * - number: Numeric values with optional formatting
 * - chip: Pill-shaped indicators with optional icons
 * - badge: Status indicators with variants and consistent sizing
 * - thumbnails: Stack of images with overflow handling
 * - thumb-text: Thumbnail with text, ideal for items with images
 *
 * @template T - The type of data being displayed. Must include an 'id' property.
 * @param {Object} props - Component props
 * @param {T[]} props.data - Array of data items to display in the table
 * @param {ColumnDef<T>[]} props.columns - Array of column definitions specifying how to render each column
 * @param {boolean} [props.selectable=false] - Whether to enable row selection with checkboxes
 * @param {string} [props.dataLabel="item"] - Singular label for the data type (e.g., "student")
 * @param {string} [props.dataLabelPlural="items"] - Plural label for the data type (e.g., "students")
 * @param {(selectedRows: T[]) => void} [props.onRowSelect] - Callback fired when row selection changes
 * @param {(row: T) => void} [props.onRowAction] - Callback fired when a row action is triggered
 *
 * @example
 * ```tsx
 * <DataTable
 *   data={students}
 *   columns={[
 *     { id: 'name', header: 'Name', accessorKey: 'name', type: 'text' },
 *     { id: 'status', header: 'Status', accessorKey: 'status', type: 'badge' }
 *   ]}
 *   selectable
 *   dataLabel="student"
 *   dataLabelPlural="students"
 *   onRowSelect={handleSelect}
 *   onRowAction={handleAction}
 * />
 * ```
 */
export function DataTable<T extends { id: string }>({
  data,
  columns,
  selectable = false,
  dataLabel = "item",
  dataLabelPlural = "items",
  onRowSelect,
  onRowAction,
}: DataTableProps<T>) {
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [searchValue, setSearchValue] = useState("");

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(new Set(data.map((row) => row.id)));
      onRowSelect?.(data);
    } else {
      setSelectedRows(new Set());
      onRowSelect?.([]);
    }
  };

  const handleSelectRow = (checked: boolean, rowId: string) => {
    const newSelectedRows = new Set(selectedRows);
    if (checked) {
      newSelectedRows.add(rowId);
    } else {
      newSelectedRows.delete(rowId);
    }
    setSelectedRows(newSelectedRows);
    onRowSelect?.(data.filter((row) => newSelectedRows.has(row.id)));
  };

  const renderCell = (column: ColumnDef<T>, row: T) => {
    const value = row[column.accessorKey];
    const stringValue = String(value);

    switch (column.type) {
      case "text":
      case "number":
        return <TextCell value={stringValue} align={column.meta?.align} />;
      case "chip":
        return (
          <ChipCell
            value={stringValue}
            variant={column.meta?.variant as ChipVariant}
            icon={column.meta?.icon}
          />
        );
      case "badge":
        return <BadgeCell value={stringValue} variant={column.meta?.variant as BadgeVariant} />;
      case "thumbnails":
        if (!Array.isArray(value)) return null;
        return <ThumbnailCell images={value} maxDisplay={column.meta?.maxThumbnails} />;
      case "thumb-text":
        const imageUrl = column.meta?.imageKey ? String(row[column.meta.imageKey]) : "";
        return <ThumbTextCell text={stringValue} imageUrl={imageUrl} />;
      default:
        return stringValue;
    }
  };

  return (
    <div className="space-y-4">
      <TableToolbar
        selectedRows={data.filter((row) => selectedRows.has(row.id))}
        totalRows={data.length}
        searchValue={searchValue}
        onSearch={setSearchValue}
        onClearSearch={() => setSearchValue("")}
        onClearSelection={() => {
          setSelectedRows(new Set());
          onRowSelect?.([]);
        }}
        dataLabel={dataLabel}
        dataLabelPlural={dataLabelPlural}
      />
      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="rounded-md border">
        <Table>
          <TableHeader className="bg-muted/50">
            <TableRow className="hover:bg-transparent">
              {selectable && (
                <TableCheckbox
                  checked={selectedRows.size === data.length}
                  indeterminate={selectedRows.size > 0 && selectedRows.size < data.length}
                  onCheckedChange={handleSelectAll}
                  isHeader
                />
              )}
              {columns.map((column) => (
                <TableHead
                  key={column.id}
                  className={cn(
                    "h-12 text-xs font-medium uppercase tracking-wide text-muted-foreground",
                    column.className,
                  )}
                  style={{
                    textAlign: column.meta?.align || "left",
                    width: column.meta?.width,
                  }}
                >
                  {column.header}
                </TableHead>
              ))}
              {onRowAction && <TableHead className="h-12 w-12" />}
            </TableRow>
          </TableHeader>
          <TableBody>
            <AnimatePresence>
              {data.map((row, index) => (
                <motion.tr
                  key={row.id}
                  custom={index}
                  variants={tableRowVariants}
                  initial="hidden"
                  animate="visible"
                  exit="removed"
                  className={cn(
                    "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
                    selectedRows.has(row.id) && "bg-muted",
                    onRowAction && "cursor-pointer",
                  )}
                  onClick={(e) => {
                    // Don't trigger row click if clicking on interactive elements
                    if (
                      e.target instanceof HTMLElement &&
                      (e.target.closest("button") ||
                        e.target.closest("a") ||
                        e.target.closest('[role="checkbox"]') ||
                        e.target.closest("input"))
                    ) {
                      return;
                    }

                    // If we have an action column with a chevron, click it directly
                    if (onRowAction) {
                      // Find the action button in the DOM
                      const rowElement = e.currentTarget;
                      const actionIcon = rowElement.querySelector(".h-4.w-4.cursor-pointer");

                      if (actionIcon && actionIcon instanceof HTMLElement) {
                        // Use the icon's click handler
                        actionIcon.click();
                      } else {
                        // Fallback to the provided handler
                        onRowAction(row);
                      }
                    }
                  }}
                >
                  {selectable && (
                    <TableCheckbox
                      checked={selectedRows.has(row.id)}
                      onCheckedChange={(checked) => handleSelectRow(checked, row.id)}
                    />
                  )}
                  {columns.map((column) => (
                    <TableCell
                      key={column.id}
                      className={column.className}
                      style={{
                        textAlign: column.meta?.align || "left",
                        width: column.meta?.width,
                      }}
                    >
                      {renderCell(column, row)}
                    </TableCell>
                  ))}
                  {onRowAction && (
                    <TableCell className="w-12">
                      <ChevronRight
                        className="h-4 w-4 cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent double triggering
                          onRowAction(row);
                        }}
                      />
                    </TableCell>
                  )}
                </motion.tr>
              ))}
            </AnimatePresence>
          </TableBody>
        </Table>
      </motion.div>
    </div>
  );
}
