import { Download, Loader2 } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";
import { formatCurrency } from "@/lib/utils";
import { Combobox } from "@/components/base/combobox";
import { ListItemCard } from "@/components/ui/ListItemCard";
import { Badge, BadgeProps } from "@/components/ui/badge";

interface PaymentInvoicesListProps {
  invoices: any[];
  invoiceYears: string[];
  selectedYears: string[];
  setSelectedYears: (years: string[]) => void;
  isLoading: boolean;
  getStatusBadgeVariant: (status: string) => BadgeProps["variant"];
}

export default function PaymentInvoicesList({
  invoices,
  invoiceYears,
  selectedYears,
  setSelectedYears,
  isLoading,
  getStatusBadgeVariant,
}: PaymentInvoicesListProps) {
  const t = useTranslations("payments.details.invoices");
  const locale = useLocale();

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-row gap-2 md:flex-row md:items-center md:justify-between">
        <div className="text-2xl font-bold">{t("list.title", { count: invoices.length })}</div>
        <div className="ml-auto flex items-center gap-2">
          <Combobox
            multiple
            value={selectedYears}
            onChange={(val) => setSelectedYears(Array.isArray(val) ? val : [val])}
            options={invoiceYears.map((year: string) => ({ value: year, label: year }))}
            label={t("filters.year")}
            placeholder={t("filters.year-placeholder")}
            triggerClassName="w-40"
            contentClassName="max-h-60 overflow-auto"
          />
        </div>
      </div>
      {isLoading ? (
        <div className="flex h-full items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div className="flex flex-col gap-4">
          {invoices.map((invoice: any) => {
            const formattedDate = new Date(invoice.date).toLocaleDateString(locale, {
              year: "numeric",
              month: "long",
              day: "numeric",
            });
            return (
              <ListItemCard
                key={invoice.id}
                title={t("list.title-number", { id: invoice.id })}
                badge={
                  <Badge variant={getStatusBadgeVariant(invoice.status)}>
                    {invoice.status?.toUpperCase?.()}
                  </Badge>
                }
                infos={{
                  [t("list.date")]: formattedDate,
                  [t("list.amount")]: formatCurrency(invoice.amountPaid),
                  [t("list.total")]: formatCurrency(invoice.total),
                }}
                icon={<Download className="h-5 w-5 cursor-pointer" />}
                iconClick={() => {
                  window.open(invoice.downloadLink, "_blank");
                }}
              />
            );
          })}
        </div>
      )}
    </div>
  );
}
