"use client";

import { useTranslations } from "next-intl";
import { IUserBase } from "@px-shared-account/hermes";
import { useCohortStudents } from "@/services/cohort";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface RemoveStudentsConfirmationProps {
  cohortId: number;
  students: IUserBase[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete?: () => void;
}

export function RemoveStudentsConfirmation({
  cohortId,
  students,
  isOpen,
  onOpenChange,
  onComplete,
}: RemoveStudentsConfirmationProps) {
  const t = useTranslations("cohorts");
  const { removeStudents, isRemovingStudents } = useCohortStudents(cohortId);

  const handleConfirm = async () => {
    try {
      const studentIds = students.map((student) => student.id);
      await removeStudents(cohortId, studentIds);

      if (onComplete) {
        onComplete();
      }

      onOpenChange(false);
    } catch (error) {
      console.error("[RemoveStudentsConfirmation] Error removing students:", error);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t("students.remove_title")}</AlertDialogTitle>
          <AlertDialogDescription>
            {students.length === 1
              ? t("students.remove_description_single", {
                  name: `${students[0].firstName || ""} ${students[0].lastName || ""}`,
                })
              : t("students.remove_description_multiple", { count: students.length })}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isRemovingStudents}>
            {t("students.remove_cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isRemovingStudents}
            className="bg-secondary text-secondary-foreground hover:bg-accent"
          >
            {isRemovingStudents ? t("students.removing") : t("students.remove_confirm")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
