import { DataSource } from 'typeorm';

const typeOrmMigrationConfig = new DataSource({
  type: 'postgres',
  host: process.env.HOST,
  port: +process.env.PORT,
  username: process.env.USERNAME,
  password: process.env.PASSWORD,
  database: process.env.DATABASE,
  entities: ['./src/**/**/*.table.{ts,js}'],
  logging: true,
  connectTimeoutMS: 10000,
  ssl: {
    rejectUnauthorized: false,
  },
  synchronize: false,
  applicationName: `${process.env.NODE_ENV}-vega`,
  migrations: ['./src/services/database/migrations/*.ts'],
  migrationsTableName: 'typeorm_migrations',
});

export default typeOrmMigrationConfig;
