"use client";

import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { usePaymentDetails } from "@/services/payments";
import PaymentInformation from "./PaymentInformation";
import PaymentInvoicesTable from "./PaymentInvoicesTable";
import { useBreakpoint } from "@/lib/hooks/tailwind-breakpoints";
import PaymentInvoicesList from "./PaymentInvoicesList";
import Link from "next/link";
import { useFilteredInvoices } from "@/services/payments/client";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

export default function PaymentDetailsPage() {
  const t = useTranslations("payments.details");
  const params = useParams();
  const id = params.id as string;
  const { data: payment, isLoading } = usePaymentDetails(id);
  const { invoices, invoiceYears, selectedYears, setSelectedYears } = useFilteredInvoices(payment);
  const isDesktop = useBreakpoint("md");

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "paid":
        return "success";
      case "overdue":
        return "destructive";
      case "refunded":
        return "warning";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-8 text-white">
      <BreadcrumbSettings active="/payments/details" />
      {/* Info Section */}
      <PaymentInformation payment={payment} t={t} />
      {/* Table/List Section with shared filter */}
      {isDesktop ? (
        <PaymentInvoicesTable
          invoices={invoices}
          invoiceYears={invoiceYears}
          selectedYears={selectedYears}
          setSelectedYears={setSelectedYears}
          isLoading={isLoading}
          t={t}
          getStatusBadgeVariant={getStatusBadgeVariant}
        />
      ) : (
        <PaymentInvoicesList
          invoices={invoices}
          invoiceYears={invoiceYears}
          selectedYears={selectedYears}
          setSelectedYears={setSelectedYears}
          isLoading={isLoading}
          getStatusBadgeVariant={getStatusBadgeVariant}
        />
      )}
    </div>
  );
}
