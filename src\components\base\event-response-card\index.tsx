import { cn } from "@/lib/utils";
import { BaseProps } from "@/types";
import { Avatar } from "@/components/base/avatar";
import { Check, X } from "lucide-react";
import { Button } from "@/components/base/button";

export interface EventResponseCardProps extends BaseProps {
  title: string;
  timestamp: string;
  avatar?: {
    src?: string;
    fallback: string;
  };
  onAccept?: () => void;
  onDecline?: () => void;
  isLoading?: boolean;
}

export const EventResponseCard = ({
  title,
  timestamp,
  avatar,
  onAccept,
  onDecline,
  isLoading = false,
  className,
}: EventResponseCardProps) => {
  return (
    <div className={cn("flex items-center gap-4 rounded-lg p-4", className)}>
      {/* Avatar */}
      <Avatar
        src={avatar?.src}
        fallback={avatar?.fallback || "E"}
        className="size-16 rounded-lg bg-muted md:size-10 xl:size-16"
      />

      {/* Content */}
      <div className="flex min-w-0 flex-1 flex-col">
        <p className="text-base font-medium text-foreground lg:text-xs xl:text-base">{title}</p>
        <p className="text-xs text-muted-foreground xl:text-sm">{timestamp}</p>
      </div>

      {/* Actions */}
      <div className="flex flex-col items-center gap-2 sm:flex-row lg:flex-col xl:flex-row">
        <Button
          variant="outline"
          className="h-auto w-6 rounded-full border-2 border-black bg-transparent xl:w-8"
          onClick={onDecline}
          disabled={isLoading}
        >
          <X className="h-auto w-4 xl:w-6" />
          <span className="sr-only">Refuser</span>
        </Button>
        <Button
          variant="outline"
          className="h-auto w-6 rounded-full border-2 border-black bg-transparent xl:w-8"
          onClick={onAccept}
          disabled={isLoading}
        >
          <Check className="h-auto w-4 xl:w-6" />
          <span className="sr-only">Accepter</span>
        </Button>
      </div>
    </div>
  );
};

EventResponseCard.displayName = "EventResponseCard";
