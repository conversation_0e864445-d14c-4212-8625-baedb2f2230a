"use client";

import { CalendarStore } from "@/hooks/store/calendar";

/**
 * Computes the view layout based on the given calendar layout type.
 *
 * @param {CalendarStore["layout"]} layout - The layout type, which can be "week", "day", or "month"
 * @returns {number[]} An array representing the indices of days to display in the calendar view
 */
export default function computeViewLayout(layout: CalendarStore["layout"]) {
  return Array.from(
    {
      // Determine the number of days based on the layout type
      length: layout === "week" ? 6 : layout === "day" ? 1 : 0,
    },
    // Generate an array with indices from 0 to (length - 1)
    (_, i) => i,
  );
}
