"use client";

import { CreditCard } from "lucide-react";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface PaymentMethodProps {
  paymentMethod?: {
    brand?: string;
    last4?: string;
    expMonth?: number;
    expYear?: number;
  } | null;
  cardholderName?: string;
}

export function PaymentMethod({ paymentMethod, cardholderName }: PaymentMethodProps) {
  const t = useTranslations("subscription.payment-method");

  if (!paymentMethod) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("title")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 text-center text-gray-500">{t("no-payment-method")}</div>
        </CardContent>
      </Card>
    );
  }

  const { brand, last4, expMonth, expYear } = paymentMethod;

  // Calculate if the card is expired
  const isExpired = expMonth && expYear ? new Date() > new Date(expYear, expMonth) : false;

  // Format expiration date
  const formattedExpiration =
    expMonth && expYear
      ? `${expMonth.toString().padStart(2, "0")}/${expYear.toString().slice(-2)}`
      : t("unknown");

  // Capitalize card brand
  const cardBrand = brand ? brand.charAt(0).toUpperCase() + brand.slice(1) : t("credit-card");

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-start space-x-4">
          <div className="rounded-md bg-primary/10 p-2">
            <CreditCard className="h-6 w-6 text-primary" />
          </div>
          <div className="space-y-1">
            <p className="font-medium">
              {cardBrand} {isExpired && `(${t("expired")})`}
            </p>
            {last4 && <p className="text-sm text-gray-500">•••• •••• •••• {last4}</p>}
            <div className="text-sm text-gray-500">
              {cardholderName && <p>{cardholderName}</p>}
              <p>
                {t("expires")}: {formattedExpiration}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
