"use client";

import { IListRolesResponse, IRoleBase, IUserBase } from "@px-shared-account/hermes";
import { PlusIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Control, ControllerRenderProps, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

import { useUpdateUser } from "@/services/user";
import { useApi } from "@/hooks/store/use-api";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import SelectRole from "../../components/SelectRole";

type EditUserModalProps = {
  user: IUserBase;
  onUpdate?: () => void;
};

const editUserSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  role: z.string().min(1, "Role is required"),
});

type EditUserFormType = z.infer<typeof editUserSchema>;

export default function EditUserModal({ user, onUpdate }: EditUserModalProps) {
  const t = useTranslations("gestion.users.user");
  const [isOpen, setIsOpen] = useState(false);
  const { data: roles, isLoading: isLoadingRoles } = useApi<IListRolesResponse>("/role");
  const { toast } = useToast();
  const { update, isLoading: isUpdating } = useUpdateUser(user.id);
  const form = useForm<EditUserFormType>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      firstName: user.firstName || "",
      lastName: user.lastName || "",
      role: user.role.name || "",
    },
  });

  const onSubmit = async (data: EditUserFormType) => {
    try {
      await update(user.id, data);
      toast({
        title: t("updated"),
        description: t("updated-description"),
      });
      setIsOpen(false);
      onUpdate?.();
      form.reset();
    } catch (error) {
      toast({
        title: t("error"),
        description: t("error-description"),
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="hidden rounded-full lg:flex">
          <PlusIcon className="mr-2 h-4 w-4" />
          <span>{t("modal-title")}</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] lg:min-w-[800px]">
        <DialogHeader>
          <DialogTitle>{t("modal-title")}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 p-8">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <UserFormField
                  control={form.control}
                  name="role"
                  label={t("role")}
                  render={(field) => (
                    <SelectRole
                      forceUpdate={false}
                      roles={roles?.data as IRoleBase[]}
                      isLoading={isLoadingRoles}
                      userData={user}
                      onChange={(value) => field.onChange(value)}
                    />
                  )}
                />
                <UserFormField
                  control={form.control}
                  name="firstName"
                  label={t("firstName")}
                  render={(field) => (
                    <Input
                      placeholder="John"
                      className="w-full border-2 border-gray-200"
                      {...field}
                    />
                  )}
                />
                <UserFormField
                  control={form.control}
                  name="lastName"
                  label={t("lastName")}
                  render={(field) => (
                    <Input
                      placeholder="Doe"
                      className="w-full border-2 border-gray-200"
                      {...field}
                    />
                  )}
                />
              </div>
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={!form.formState.isDirty || form.formState.isSubmitting}
                >
                  {form.formState.isSubmitting ? t("saving") : t("save")}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

type UserFormFieldProps = {
  control: Control<EditUserFormType>;
  name: keyof EditUserFormType;
  label: string;
  render: (field: ControllerRenderProps) => React.ReactNode;
};

function UserFormField({ control, name, label, render }: UserFormFieldProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>{render(field as any)}</FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
