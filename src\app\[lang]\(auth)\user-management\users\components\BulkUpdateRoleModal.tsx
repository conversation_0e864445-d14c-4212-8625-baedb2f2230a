"use client";

import { IRoleBase } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";

import { Modal } from "@/components/base/modal";
import { useToast } from "@/hooks/use-toast";
import SelectRole from "./SelectRole";

type BulkUpdateRoleModalProps = {
  isOpen: boolean;
  roles: IRoleBase[];
  onUpdate: (payload: { targetRole: string }) => void;
  onClose: () => void;
};

export default function BulkUpdateRoleModal({
  isOpen,
  roles,
  onClose,
  onUpdate,
}: BulkUpdateRoleModalProps) {
  const t = useTranslations("gestion.users");
  const { toast } = useToast();
  const [selectedRole, setSelectedRole] = useState<string>("");

  const handleRoleChange = useCallback((role: string) => {
    setSelectedRole(role);
  }, []);

  const handleOk = useCallback(() => {
    if (!selectedRole) {
      toast({
        title: t("bulk-update-role-error"),
        description: t("bulk-update-role-error-description"),
        variant: "destructive",
      });
      return;
    }
    onUpdate({ targetRole: selectedRole });
  }, [selectedRole, onUpdate]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} onOk={handleOk} title={t("bulk-update-role")}>
      <div className="flex flex-col">
        <SelectRole
          isLoading={false}
          forceUpdate={false}
          roles={roles}
          showLabel={false}
          onChange={handleRoleChange}
        />
      </div>
    </Modal>
  );
}
