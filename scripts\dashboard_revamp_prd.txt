<context>
# Overview
The current dashboard user interface (UI) for Sirius is basic and needs a significant refresh to align with modern UI/UX standards and better present information to users. This project aims to revamp the dashboard UI based on provided design specifications, introducing several new, well-defined UI sections and components. This will improve user engagement, provide a clearer overview of their products, courses, events, and tools, and enhance the overall aesthetic appeal of the platform. This revamp is for all users of the Sirius platform. The primary value is a significantly improved user experience, making the platform more intuitive and visually engaging.

# Core Features
1.  **Dynamic Main Product Display:**
    *   **What it does:** Prominently showcases the customer's primary subscribed product or offering at the top of the dashboard, including a title, subtitle, and configurable action buttons.
    *   **Why it's important:** Immediately highlights the most relevant product/service to the user.
    *   **How it works at a high level:** A dedicated `HeroCard` component will fetch and display dynamic product information.

2.  **Standardized Content Sections:**
    *   **What it does:** Introduces a reusable section layout consisting of a section title on the left and an optional outlined action button (e.g., "View More") on the right. The content within these sections will be horizontally scrollable using a carousel if it overflows.
    *   **Why it's important:** Provides a consistent and organized way to present various types of content like courses, events, and tools.
    *   **How it works at a high level:** A new `Section` component will be created. This component will house a `Carousel` (from the existing `shadcn/ui` library) populated with specific content cards.

3.  **Product Display Cards (for Courses, etc.):**
    *   **What it does:** Displays individual products (like courses) in a 9:16 aspect ratio card. Each card will feature a background cover image, a title, a description, an action button at the bottom, and an optional burgundy-colored "New" badge at the top-right.
    *   **Why it's important:** Offers a visually appealing and informative way to list multiple products or courses.
    *   **How it works at a high level:** A `ProductCard` component will be developed.

4.  **Event Display Cards:**
    *   **What it does:** Showcases upcoming events in a 16:9 aspect ratio, full-width card designed for a carousel that displays one event at a time. The card includes a background cover image, title, description, a bulleted list of details, and an action button. All content within the card is vertically centered and left-aligned.
    *   **Why it's important:** Provides an engaging and detailed view for featured events.
    *   **How it works at a high level:** An `EventCard` component will be developed and used within a full-width carousel.

5.  **Tool Display Cards:**
    *   **What it does:** Presents available tools in a 1:1 aspect ratio, dark-themed card.
        *   Accessible State: Shows a title, subtitle, and a fully rounded button with a right arrow icon at the bottom-right.
        *   Disabled State: Text is muted, the action button is hidden, a "LOCKED" badge (brand color) appears at the top-right, and a message like "Tool accessible only with the {requiredCourseName} course" is displayed at the bottom.
    *   **Why it's important:** Clearly communicates the availability and access requirements for different tools.
    *   **How it works at a high level:** A `ToolCard` component with a `disabled` prop and `requiredCourseName` prop to manage its state and display.

6.  **"Wall of Dreams" Display Area:**
    *   **What it does:** A full-width section featuring a background cover image, a main title, a subtitle, and a group of action buttons (configurable for primary branded and secondary dark styles). Content is centered both vertically and horizontally.
    *   **Why it's important:** Creates an engaging call-to-action or community highlight section.
    *   **How it works at a high level:** The `HeroCard` component will be reused and configured for this specific content and layout.
</context>
<PRD>
# Technical Architecture
*   **System Components:**
    *   Frontend Framework: Next.js (existing)
    *   UI Library: React (existing)
    *   New UI Components:
        *   `Section.tsx` (Reusable container for content blocks)
        *   `HeroCard.tsx` (For main product display and "Dreams Wall")
        *   `ProductCard.tsx` (For courses/products)
        *   `EventCard.tsx` (For upcoming events)
        *   `ToolCard.tsx` (For tools with locked/unlocked states)
        *   `Badge.tsx` (If a suitable one doesn't exist, for "New" and "LOCKED" labels)
    *   Base UI Elements: `shadcn/ui` components (Carousel, Button, Card, etc. - existing)
    *   Styling: TailwindCSS (existing)
    *   Internationalization: `next-intl` (existing)
*   **Data Models:** Component data will be passed via props. Initial data fetching will occur in the main dashboard page component (`page.tsx`) or through dedicated service functions (e.g., `getCustomerAccessDetails`). Props will include image URLs, titles, descriptions, lists, button configurations, and status flags (e.g., `isNew`, `disabled`).
*   **APIs and Integrations:** Leverage existing customer data APIs (`getCustomerAccessDetails`). Potentially new or modified API endpoints might be needed to supply specific data for courses, events, and tools if not already available.
*   **Infrastructure Requirements:** No changes to the existing infrastructure are anticipated for this UI-focused revamp.

# Development Roadmap
*   **Phase 1: Foundational Component Development**
    *   Develop the reusable `Section` component (title, optional action button, carousel-ready content area).
    *   Develop or verify an existing `Badge` component for use on cards.
*   **Phase 2: Individual Card Component Development**
    *   Implement `ProductCard` (9:16, image, title, description, button, optional badge).
    *   Implement `EventCard` (16:9, full-width for carousel, image, title, description, bullet points, button).
    *   Implement `ToolCard` (1:1, dark theme, title, subtitle, conditional button/locked state with badge and message).
    *   Implement `HeroCard` (16:9, full-width, image, title, subtitle, configurable button group).
*   **Phase 3: Dashboard Page Assembly & Integration**
    *   Refactor `src/app/[lang]/(auth)/dashboard/page.tsx` to incorporate the new `Section` and card components.
    *   Integrate `shadcn/ui Carousel` within `Section` components for courses, events (full-width, 1-by-1), and tools.
    *   Connect all static text to `next-intl` for internationalization using appropriate JSON keys.
    *   Use placeholder data for dynamic content initially.
*   **Phase 4: Styling, Refinement & Finalization**
    *   Apply detailed TailwindCSS styling to all new components and sections to precisely match the provided design images.
    *   Ensure full responsiveness across various screen sizes (desktop, tablet, mobile).
    *   Conduct thorough testing for UI consistency, functionality, and internationalization.
    *   Address any linting or TypeScript errors.

# Logical Dependency Chain
1.  **Base Component (`Badge.tsx`):** If a new one is needed, it should be created first or an existing one verified.
2.  **Layout Component (`Section.tsx`):** This structural component is a prerequisite for organizing content sections.
3.  **Individual Card Components (`ProductCard.tsx`, `EventCard.tsx`, `ToolCard.tsx`, `HeroCard.tsx`):** These can be developed in parallel after the `Badge` is ready.
4.  **Carousel Integration:** Once cards are available, they can be integrated into carousels within the `Section` component.
5.  **Page Composition (`dashboard/page.tsx`):** Assembled using the `Section` and card components.
6.  **Styling & Internationalization:** Applied iteratively throughout component development and page assembly.
7.  **Data Integration:** While props are defined early, full data fetching logic can be integrated once components are structurally complete.

# Risks and Mitigations
*   **Risk:** Achieving precise visual fidelity with the complex, image-rich design mockups.
    *   **Mitigation:** Iterative development with frequent visual checks. Leverage TailwindCSS's flexibility. Break down complex styles into smaller, manageable pieces.
*   **Risk:** Ensuring the `shadcn/ui Carousel` behaves as expected for different content types (e.g., standard scroll for products/tools, full-width single item display for events).
    *   **Mitigation:** Thoroughly test carousel configurations and props. Refer to `shadcn/ui` documentation and examples.
*   **Risk:** Managing dynamic data flow and states for various components (e.g., locked/unlocked tools, dynamic hero product name).
    *   **Mitigation:** Define clear and comprehensive prop interfaces for each component early on. Use placeholder data during initial UI construction. Plan data fetching and state management strategy for the page component.
*   **Risk:** Scope creep if additional UI elements or functionalities not in the initial designs are requested during development.
    *   **Mitigation:** Adhere strictly to this PRD and the provided visual designs. New requests should go through a change request process.

# Appendix
*   Original design images provided by the user will be the primary visual reference.
*   Internationalization will utilize `next-intl`, with translations stored in `public/locales/[lang]/dashboard.json` or `common.json` under relevant namespaces and keys.
*   The existing `FloatingActionButton` in the dashboard layout is out of scope for this revamp and should not be altered.
*   The footer displayed in some overview images is out of scope for this revamp.
</PRD> 