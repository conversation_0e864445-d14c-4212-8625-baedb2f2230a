// Navigation
export interface NavigationItem {
  id: string;
  label: string;
  icon?: React.ElementType | string;
  href?: string;
  children?: NavigationItem[];
  external?: boolean;
  hasCustomIcon?: boolean;
  target?: string;
}

// Common Props
export interface BaseProps {
  className?: string;
  children?: React.ReactNode;
}

// Layout
export interface LayoutProps extends BaseProps {
  title?: string;
}

// User
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

// Notification
export interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  type?: "info" | "warning" | "error" | "success";
}
