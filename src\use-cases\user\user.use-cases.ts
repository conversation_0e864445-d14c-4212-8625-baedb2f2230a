import { Injectable } from '@nestjs/common';
import { UserFactory } from './user.factory';
import { RoleEntity, UserEntity } from '@entities';
import {
  IListUsersResponse,
  UpdateResultWithItemInfo,
  UserType,
} from '@px-shared-account/hermes';
import {
  Brackets,
  DataSource,
  FindOptionsOrder,
  FindOptionsWhere,
  In,
} from 'typeorm';
import { RoleTable, UserTable } from '@tables';
import { BulkUpdateRoleDto, CreateUserDto, UpdateUserDto } from '@dtos';
import { RoleUseCases } from '../role';
import { User, UserJSON } from '@clerk/backend';
import { ClerkService } from '@services/sso';
import { IDataServices } from '@abstracts';
import {
  AccessInfo,
  PurchaseRecord,
  PXActionResult,
  UserAccessDetailsResponse,
} from '@types';
import { FiscalEntity } from '@enums';
import { HubspotService } from '@services/crm';
import { ChargebeeBillingService } from '@services/billing';
import { GoliathService } from '../goliaths';

@Injectable()
export class UserUseCases {
  constructor(
    private readonly databaseService: IDataServices,
    private readonly factory: UserFactory,
    private readonly roleUseCases: RoleUseCases,
    private readonly clerkService: ClerkService,
    private readonly hubspotService: HubspotService,
    private readonly chargebeeService: ChargebeeBillingService,
    private readonly goliathService: GoliathService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Creates a user or invites a new user to Clerk.
   * @param email Email address of the user.
   * @param firstName First name of the user.
   * @param lastName Last name of the user.
   * @param businessEntityId Business entity to which the user belongs.
   * @param chargebeeId Chargebee ID of the user.
   * @returns A promise that resolves to an object containing the success status and the user.
   */
  async createOrInvite(
    email: string,
    firstName: string,
    lastName: string,
    businessEntityId: string,
    chargebeeId: string,
  ): Promise<PXActionResult> {
    try {
      const existingUser = await this.getByEmail(email?.toLowerCase()?.trim());
      if (existingUser) {
        return this.handleExistingUser(existingUser);
      }

      const { success, message, data } = await this.clerkService.inviteUser(
        email,
      );
      if (!success) {
        return { success: false, message, data: { user: null } };
      }

      if (data?.alreadyInvited && data?.existingUserInfo) {
        const result = await this.createUserFromExistingClerkUser(
          data?.existingUserInfo,
          chargebeeId,
          businessEntityId,
        );
        return result;
      }

      if (data?.invitation) {
        return this.createUserFromInvitedClerkUser(
          email,
          firstName,
          lastName,
          businessEntityId,
          chargebeeId,
          data?.invitation.id,
        );
      }

      return { success: false, message: 'Unexpected Error' };
    } catch (error) {
      return {
        success: false,
        message: error?.message,
        data: {
          error: error?.stack,
        },
      };
    }
  }

  /**
   * Handles Clerk invitation for an existing user.
   * @param existingUser Existing user.
   * @returns A promise that resolves to an object containing the success status and the user.
   */
  async handleExistingUser(existingUser: UserEntity): Promise<PXActionResult> {
    if (existingUser.ssoId) {
      return {
        success: true,
        message: 'User already exists and has an SSO ID',
        data: { user: existingUser },
      };
    }

    const inviteResult = await this.clerkService.inviteUser(existingUser.email);
    if (!inviteResult?.success) {
      return {
        success: false,
        message: inviteResult?.message,
      };
    }

    return {
      success: true,
      message: 'Existing user invited to Clerk',
      data: {
        user: existingUser,
      },
    };
  }

  /**
   * Creates a user from an existing Clerk user.
   * @param existingClerkUser Clerk's existing user information.
   * @param chargebeeId Chargebee ID.
   * @param businessEntityId Business entity ID.
   * @returns A promise that resolves to an object containing the success status and the new user.
   */
  private async createUserFromExistingClerkUser(
    existingClerkUser: User,
    chargebeeId: string,
    businessEntityId: string,
  ): Promise<PXActionResult> {
    try {
      const userInfo = this.factory.generateFromClerkUser(
        existingClerkUser,
        chargebeeId,
        businessEntityId,
      );
      const crmId = await this.getOrCreateCrmContact(
        userInfo.email,
        userInfo.firstName,
        userInfo.lastName,
      );
      const newUser = await this.databaseService.user.create({
        ...userInfo,
        crmId,
      });
      return {
        success: true,
        message: 'Existing Clerk user created in DB',
        data: { user: newUser },
      };
    } catch (err) {
      return {
        success: false,
        message: err?.message,
        data: {
          error: err?.stack,
        },
      };
    }
  }

  /**
   * Creates a user from an already invited Clerk user.
   * @param email Email address of the user.
   * @param firstName First name of the user.
   * @param lastName Last name of the user.
   * @param businessEntityId Business entity ID.
   * @param chargebeeId Chargebee ID.
   * @param invitationId Invitation ID.
   * @returns A promise that resolves to an object containing the success status and the new user.
   */
  private async createUserFromInvitedClerkUser(
    email: string,
    firstName: string,
    lastName: string,
    businessEntityId: string,
    chargebeeId: string,
    invitationId: string,
  ): Promise<PXActionResult> {
    const crmId = await this.getOrCreateCrmContact(email, firstName, lastName);
    const role = await this.roleUseCases.getDefaultStudentRole();
    const userInfo = this.factory.generate({
      email,
      firstName,
      lastName,
      metaData: {
        invitationId,
        createdFrom: 'Product Delivery Flow',
      },
      chargebeeIds: { [businessEntityId]: chargebeeId },
      crmId,
      role,
    });
    const newUser = await this.databaseService.user.create(userInfo);
    return {
      success: true,
      message: 'New user created',
      data: { user: newUser },
    };
  }

  /**
   * Creates a user from Clerk's `user.created` event
   * @param eventData Event data containing user info
   * @returns Created user record
   */
  async createUserOrUpdateSSOId(eventData: UserJSON): Promise<PXActionResult> {
    const email = this.clerkService.getPrimaryEmail(eventData);
    const userExists = await this.getByEmail(email);

    if (userExists) {
      return this.updateUsingEmail(email, { ssoId: eventData.id });
    }
    const firstName = eventData.first_name;
    const lastName = eventData.last_name;
    const ssoId = eventData.id;
    const metadata = eventData.unsafe_metadata;
    const roleFromClerk = this.clerkService.getUserRole(eventData);
    const role = await this.roleUseCases.getByRoleName(roleFromClerk);
    return this.createFromClerkEvent(
      email,
      firstName,
      lastName,
      ssoId,
      role,
      metadata,
    );
  }

  /**
   * Fetches users for PX OS based on filters like `searchQuery`, `fiscalEntity`, and `type`
   * @param searchQuery Optional search term for user's email or full name
   * @param fiscalEntity Optional filter to match users with valid chargebeeIds for `PG`, `PP`, or `PI`
   * @param type Optional user type filter
   * @param limit Maximum number of results to return
   * @param page Page number for pagination
   * @param orderBy Sort order for the results
   * @returns A promise that resolves to the list of users and the total count
   */
  async listForOS(
    searchQuery?: string,
    fiscalEntity?: FiscalEntity,
    type?: UserType,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: UserEntity[]; total: number }> {
    const queryBuilder = this.databaseService.user
      .getRepository()
      .createQueryBuilder('user');

    if (type) {
      queryBuilder.andWhere('user.type = :type', { type });
    }

    if (fiscalEntity) {
      queryBuilder.andWhere(`user."chargebeeIds"->>:fiscalEntity IS NOT NULL`, {
        fiscalEntity,
      });
      queryBuilder.andWhere(`user."chargebeeIds"->>:fiscalEntity != ''`, {
        fiscalEntity,
      });
    }

    if (searchQuery) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('user.email ILIKE :searchQuery', {
            searchQuery: `%${searchQuery}%`,
          }).orWhere('user.fullName ILIKE :searchQuery', {
            searchQuery: `%${searchQuery}%`,
          });
        }),
      );
    }

    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.orderBy('user.createdAt', orderBy || 'DESC');

    const [items, total] = await queryBuilder.getManyAndCount();
    return {
      items,
      total,
    };
  }

  /**
   * Creates a new user
   * @param userInfo User information
   * @returns UserEntity
   */
  async create(userInfo: CreateUserDto): Promise<UserEntity> {
    try {
      const newUser = this.factory.generate(userInfo);
      const user = await this.databaseService.user.create(newUser);
      return user;
    } catch (err) {
      console.error('error creating user', err);
    }
  }

  /**
   * Gets a user by ID
   * @param id ID of the user
   * @returns UserEntity
   */
  async getById(id: number): Promise<UserEntity> {
    return this.databaseService.user.getOneBy({
      id,
    });
  }

  /**
   * Searches for users based on query and role where
   * query is matched against firstName, lastName, or email
   * @param query Query to search for
   * @param role `uuid` of the Role to search for
   * @param limit Limit of results
   * @param page Page number
   * @returns UserEntity[]
   */
  async search(
    query?: string,
    role?: number,
    limit?: number,
    page?: number,
  ): Promise<IListUsersResponse> {
    const columnsToQuery = [
      'firstName',
      'lastName',
      'email',
    ] as unknown as (keyof UserEntity)[];
    const filters: FindOptionsWhere<UserTable>[] = [];

    if (role) {
      const roleGroup = new RoleTable();
      roleGroup.id = role;
      filters.push({
        role: roleGroup,
      });
    }
    const searchResult = await this.databaseService.user.search(
      UserTable,
      query,
      columnsToQuery,
      filters,
      true,
      { limit, page },
    );

    return {
      data: searchResult.items,
      page,
      total: searchResult.total,
    };
  }

  /**
   * Gets all users
   * @param limit Number of users to return
   * @param page Page number
   * @param sortOptions Sort options
   * @returns UserEntity[]
   */
  async getAll(
    limit?: number,
    page?: number,
    sortOptions?: FindOptionsOrder<UserEntity>,
  ): Promise<{ items: UserEntity[]; total: number }> {
    return this.databaseService.user.getAll(limit, page, sortOptions);
  }

  /**
   * Fetches a user based on email address
   * @param email User's `email`
   * @returns A promise that resolves to the fetched user if found, null otherwise
   */
  async getByEmail(email: string): Promise<UserEntity> {
    return this.databaseService.user.getOneBy({ email });
  }

  /**
   * Fetches a user based on the Learnworlds id
   * @param id User's Learnworlds `id`
   * @returns A promise that resolves to the fetched user if found, null otherwise
   */
  async getByLMSId(id: string): Promise<UserEntity> {
    return this.databaseService.user.getOneBy({ lmsId: id });
  }

  /**
   * Fetches a user based on the Hubspot id
   * @param id User's Hubspot `id`
   * @returns A promise that resolves to the fetched user if found, null otherwise
   */
  async getByCRMId(id: string): Promise<UserEntity> {
    return this.databaseService.user.getOneBy({ crmId: id });
  }

  /**
   * Fetches a user based on the Circle id
   * @param id User's Circle `id`
   * @returns A promise that resolves to the fetched user if found, null otherwise
   */
  async getByCommunityId(id: string): Promise<UserEntity> {
    return this.databaseService.user.getOneBy({ communityId: id });
  }

  /**
   * Fetches a user based on the Chargebee id
   * @param id User's Chargebee `id`
   * @param businessEntity Business entity in Chargebee to which this user belongs
   * @returns A promise that resolves to the fetched user if found, null otherwise
   */
  async getByChargebeeId(
    id: string,
    businessEntity: FiscalEntity,
  ): Promise<UserEntity> {
    return this.databaseService.user.getOneBy({
      chargebeeIds: {
        [businessEntity]: id,
      },
    });
  }

  /**
   * Fetches a user based on the Clerk id
   * @param id User's Clerk `id`
   * @returns A promise that resolves to the fetched user if found, null otherwise
   */
  async getBySSOId(id: string): Promise<UserEntity> {
    const user = await this.databaseService.user.getOneBy({ ssoId: id });
    if (!user) {
      throw new Error('User not found');
    }
    return user;
  }

  /**
   * Gets a CRM ID for a user by either creating a CRM contact
   * or fetching an existing contact
   * @param email User's email address
   * @param firstName User's first name
   * @param lastName User's last name
   * @returns A promise that resolves to a CRM ID
   */
  async getOrCreateCrmContact(
    email: string,
    firstName: string,
    lastName: string,
    // The metadata is used to pass additional data to the CRM, like UTMs and the project where the user was created.
    metadata?: UserUnsafeMetadata,
  ): Promise<string> {
    const crmContact = await this.hubspotService.findByEmailOrCreateContact(
      email,
      {
        first_name: firstName,
        last_name: lastName,
        email,
      },
      metadata,
    );
    return crmContact?.id;
  }

  /**
   * Gets a list of Chargebee user IDs for a given email address
   * @param email Email address of the user
   * @returns A promise that resolves to a list of Chargebee user IDs
   */
  async getChargebeeCustomerIds(
    email: string,
  ): Promise<Record<string, string>> {
    const chargebeeCustomers = await this.chargebeeService.getCustomersByEmail(
      email,
    );
    return chargebeeCustomers.reduce((chargebeeIds, customer) => {
      chargebeeIds[customer.businessEntityId] = customer.id;
      return chargebeeIds;
    }, {} as Record<string, string>);
  }

  /**
   * Gets combined access information (flags) and purchase history for a user.
   * @param email User's email address
   * @returns A promise that resolves to the UserAccessDetailsResponse object.
   */
  async getUserAccessDetails(
    email: string,
  ): Promise<UserAccessDetailsResponse> {
    // 1. Query the view (case insensitive using lower() for safety)
    const purchaseViewResults = await this.dataSource.query<any[]>(
      `SELECT * FROM user_access_purchases_view WHERE lower("userEmail") = lower($1)`,
      [email],
    );

    let isPXS = false;
    let isPXL = false;
    const purchases: PurchaseRecord[] = [];

    // 2. Process purchase records and determine PXS/PXL access
    for (const row of purchaseViewResults) {
      // Check if this subscription contributes to PXS/PXL access
      // Use the calculated flags from the view
      if (row.hasPxs) {
        isPXS = true;
      }
      if (row.hasPxl) {
        isPXL = true;
      }

      // Process lmsIds based on includesRecommended flag
      const lmsIds = { ...row.offerLmsIds };

      // Process communityIds based on includesRecommended flag
      const communityIds = { ...row.offerCommunityIds };

      // Check if subscription includes recommended products
      const includesRecommended =
        row.subscriptionMetaData?.includesRecommended === 'true';

      // If it includes recommended products, merge recommended courses into main products
      if (includesRecommended) {
        // Merge LMS IDs if they exist
        if (lmsIds && lmsIds.products && lmsIds.recommended) {
          lmsIds.products = [...lmsIds.products, ...lmsIds.recommended];
        }

        // Merge community IDs if they exist
        if (communityIds && communityIds.products && communityIds.recommended) {
          communityIds.products = [
            ...communityIds.products,
            ...communityIds.recommended,
          ];
        }
      }

      // Map raw row to PurchaseRecord structure
      purchases.push({
        subscription: {
          id: row.subscriptionId,
          status: row.subscriptionStatus,
          cfIsForever: row.subscriptionCfIsForever,
          nextBillingAt: row.subscriptionNextBillingAt,
          cancelledAt: row.subscriptionCancelledAt,
          cancelScheduleCreatedAt: row.subscriptionCancelScheduleCreatedAt,
          mrr: row.subscriptionMrr,
          metaData: row.subscriptionMetaData,
          lastPaymentDate: row.lastPaymentDate,
        },
        plan: {
          // Use subscription-derived values for plan information
          id: row.subscriptionId,
          externalName: row.offerName,
          itemId: row.subscriptionId,
          name: row.offerName,
        },
        item: {
          // Use subscription-derived values for item information
          id: row.subscriptionId,
          cfProductLine: row.cfProductLine || '',
          name: row.offerName,
        },
        offer: {
          orderStatus: row.offerOrderStatus,
          bannerImage: row.offerBannerImage,
          cardImage: row.offerCardImage,
          chargebeeId: row.offerChargebeeId,
          description: row.offerDescription,
          lmsIds: lmsIds, // Use the potentially modified lmsIds object
          name: row.offerName,
          usp: row.offerUsp,
          communityIds: communityIds, // Use the potentially modified communityIds object
        },
      });
    }

    // 3. Get Hubspot Data
    let hubspotSession: string | null = null;
    try {
      const hubspotContact = await this.hubspotService.getHubspotContact(
        email,
        true,
      );
      hubspotSession = hubspotContact?.session_edec || null;
    } catch (error) {
      // Log error appropriately, but don't fail the whole request if Hubspot contact not found
      console.error(`Error fetching Hubspot contact for ${email}:`, error);
    }

    // 4. Get POM Status
    let isPOM = false;
    try {
      const pomResult = await this.goliathService.isPOMStudent(email);
      isPOM = pomResult?.isPOMStudent || false;
    } catch (error) {
      // Log error appropriately
      console.error(`Error fetching POM status for ${email}:`, error);
    }

    // 5. Get Paradox Status
    const isParadox = this.factory.isParadoxUser(email);

    // 6. Calculate hasBothAccess
    const hasBothAccess = (isPXS && isPXL) || isParadox;

    // 7. Construct AccessInfo
    const accessInfo: AccessInfo = {
      isPXS,
      hubspotSession,
      isPXL,
      isParadox,
      hasBothAccess,
      isPOM,
    };

    // 8. Return combined response
    return {
      email,
      accessInfo,
      purchases,
    };
  }

  /**
   * Updates a user using their email address
   * @param email User email address
   * @param payload Properties to update
   * @returns Updated user
   */
  async updateUsingEmail(
    email: string,
    payload: UpdateUserDto,
  ): Promise<UpdateResultWithItemInfo<UserEntity>> {
    const { role, ...otherUpdates } = payload;
    const userUpdates: Partial<UserEntity> = { ...otherUpdates };
    if (role) {
      const updatedRole = await this.roleUseCases.getByRoleName(role);
      userUpdates.role = updatedRole;
    }

    return this.databaseService.user.updateAndReturnItem(
      'email',
      email,
      userUpdates,
    );
  }

  /**
   * Updates a user using their ID
   * @param id User ID
   * @param payload Properties to update
   * @returns Updated user
   */
  async updateUsingId(
    id: number,
    payload: UpdateUserDto,
  ): Promise<UpdateResultWithItemInfo<UserEntity>> {
    const { role, ...otherUpdates } = payload;
    const userUpdates: Partial<UserEntity> = { ...otherUpdates };
    if (role) {
      const updatedRole = await this.roleUseCases.getByRoleName(role);
      userUpdates.role = updatedRole;
    }

    return this.databaseService.user.updateAndReturnItem(
      'id',
      id.toString(),
      userUpdates,
    );
  }

  /**
   * Creates a user from a Clerk event
   * @param email Email address of the user
   * @param firstName First name of the user
   * @param lastName Last name of the user
   * @param ssoId SSO ID of the user
   * @param metadata Metadata of the user
   * @param role Role of the user
   * @returns A promise that resolves to the created user
   */
  async createFromClerkEvent(
    email: string,
    firstName: string,
    lastName: string,
    ssoId: string,
    role: RoleEntity,
    metadata?: UserUnsafeMetadata,
  ): Promise<PXActionResult> {
    const crmId = await this.getOrCreateCrmContact(
      email,
      firstName,
      lastName,
      metadata,
    );
    const userChargebeeIds = await this.getChargebeeCustomerIds(email);
    const userInfo = this.factory.generate({
      email,
      firstName,
      lastName,
      role,
      ssoId,
      metaData: metadata,
      chargebeeIds: userChargebeeIds,
      crmId,
    });
    const user = await this.databaseService.user.create(userInfo);
    if (user) {
      await this.goliathService.generateCodes(1);
      await this.goliathService.issueAndPush(email);
    }
    return { success: true, message: 'User created' };
  }

  /**
   * Bulk updates roles for users
   * @param updateInfo Bulk update info
   * @returns A promise that resolves to a PXActionResult
   */
  async bulkRoleUpdate(updateInfo: BulkUpdateRoleDto): Promise<PXActionResult> {
    const { targetRole, users, filters } = updateInfo;

    const updatedRole = await this.roleUseCases.getById(targetRole);
    if (!updatedRole) {
      return {
        success: false,
        message: `Target role ${targetRole} not found.`,
      };
    }

    let userIds: number[] = [];

    if (users === 'all' && filters) {
      const searchResults = await this.search(filters?.search, filters.role);
      if (searchResults.total === 0) {
        return {
          success: false,
          message: `No users found for the given filters.`,
        };
      }
      userIds = searchResults.data.map((user) => user.id);
    } else if (Array.isArray(users) && users.length > 0) {
      userIds = users;
    }

    if (userIds.length === 0) {
      return {
        success: false,
        message: 'No users provided for role update.',
      };
    }

    const result = await this.databaseService.user.update(
      { id: In(userIds) },
      { role: updatedRole },
    );

    if (result?.affected === 0) {
      return { success: false, message: `No users were updated.` };
    }

    return {
      success: true,
      message: `Roles were updated for ${result.affected} users.`,
    };
  }

  /**
   * Creates a phone number for a user
   * @param id ID of the user
   * @param phoneNumber Phone number of the user
   * @returns A promise that resolves to a PXActionResult
   */
  async createPhoneNumber(
    id: string,
    phoneNumber: string,
  ): Promise<PXActionResult> {
    if (!id || !phoneNumber) {
      return { success: false, message: 'Invalid request.' };
    }
    const user = await this.getBySSOId(id);
    if (!user) {
      return { success: false, message: 'User not found.' };
    }
    return {
      success: true,
      message: 'Phone number created',
      data: {
        clerkResponse: await this.clerkService.createPhoneNumber(
          user.ssoId,
          phoneNumber,
        ),
      },
    };
  }

  /**
   * Unlinks a phone number from a user
   * @param id ID of the user
   * @param phoneNumberId ID of the phone number to unlink
   */
  async unlinkPhoneNumber(
    id: string,
    phoneNumberId: string,
  ): Promise<PXActionResult> {
    if (!id || !phoneNumberId) {
      return { success: false, message: 'Invalid request.' };
    }
    const user = await this.getBySSOId(id);
    if (!user) {
      return { success: false, message: 'User not found.' };
    }
    return {
      success: true,
      message: 'Phone number unlinked',
      data: {
        clerkResponse: await this.clerkService.unlinkPhoneNumber(phoneNumberId),
      },
    };
  }
}
