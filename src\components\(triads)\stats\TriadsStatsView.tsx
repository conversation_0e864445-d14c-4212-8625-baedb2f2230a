"use client";
import { redirect } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import TriadsOverviewStats from "@/components/(triads)/stats/TriadsOverviewStats";
import TriadsEngagementStats from "@/components/(triads)/stats/TriadsEngagementStats";
import TriadsGrowthStats from "@/components/(triads)/stats/TriadsGrowthStats";
import TriadsOrganizerStats from "@/components/(triads)/stats/TriadsOrganizerStats";
import { useState } from "react";
import { cn, isParadoxEmail } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { BarChart, Users, TrendingUp, UserCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import { TimeRangeSelector, TimeRangeType } from "@/components/(triads)/stats/TimeRangeSelector";
import { clash } from "@/lib/fonts/clash";

export default function TriadsStatsView() {
  const t = useTranslations("triad");
  const { user } = useUser();

  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-[#111111]">
        <div className="animate-pulse text-white/50">{t("loading")}</div>
      </div>
    );
  }
  const email = user?.emailAddresses[0]?.emailAddress;

  if (!email || !isParadoxEmail(email)) {
    redirect("/triads");
  }

  return (
    <div className="min-h-screen bg-[#111111]">
      <section className={cn(clash.className, "container py-12")}>
        <div className="mb-12 flex flex-col items-center text-center">
          <h1 className="mb-3 text-4xl font-bold text-white">{t("stats.title")}</h1>
          <p className="max-w-2xl text-lg text-gray-400">{t("stats.description")}</p>
        </div>
        <StatsContent />
      </section>
    </div>
  );
}

function StatsContent() {
  const t = useTranslations("triad");
  const [timeRange, setTimeRange] = useState({
    start: new Date(new Date().setMonth(new Date().getMonth() - 1)),
    end: new Date(),
    type: "month" as TimeRangeType,
  });

  const [activeTab, setActiveTab] = useState("overview");
  const [statsData, setStatsData] = useState<any>(null);

  const handleRangeChange = (range: { start: Date; end: Date; type: TimeRangeType }) => {
    setTimeRange(range);
  };

  const tabs = [
    { id: "overview", icon: BarChart, label: t("stats.tabs.overview") },
    { id: "engagement", icon: Users, label: t("stats.tabs.engagement") },
    { id: "growth", icon: TrendingUp, label: t("stats.tabs.growth") },
    {
      id: "organizers",
      icon: UserCircle,
      label: t("stats.tabs.organizers"),
    },
  ];

  return (
    <div className="space-y-8">
      <Card className="border-[#2a2a2a] bg-[#1a1a1a]">
        <CardContent className="p-6">
          <TimeRangeSelector onRangeChange={handleRangeChange} data={statsData} />
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid h-auto w-full grid-cols-4 gap-2 bg-transparent p-0 md:gap-4">
          {tabs.map(({ id, icon: Icon, label }) => (
            <TabsTrigger
              key={id}
              value={id}
              className={cn(
                "relative rounded-lg border border-[#2a2a2a] bg-[#1a1a1a]",
                "data-[state=active]:border-[#8D2146] data-[state=active]:bg-[#8D2146]",
                "data-[state=active]:text-white",
                "transition-all duration-200 hover:bg-[#2a2a2a]",
                "h-auto px-3 py-4 md:px-6",
                "text-white",
                clash.className,
              )}
            >
              <div className="flex items-center justify-center md:gap-3">
                <Icon className="h-5 w-5 text-white" />
                <span className="hidden text-sm font-medium text-white md:inline-block">
                  {label}
                </span>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>

        <div className="mt-8">
          <TabsContent value="overview" className="m-0">
            <Card className="border-[#2a2a2a] bg-[#1a1a1a]">
              <CardContent className="p-6">
                <TriadsOverviewStats timeRange={timeRange} onDataChange={setStatsData} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="engagement" className="m-0">
            <Card className="border-[#2a2a2a] bg-[#1a1a1a]">
              <CardContent className="p-6">
                <TriadsEngagementStats timeRange={timeRange} onDataChange={setStatsData} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="growth" className="m-0">
            <Card className="border-[#2a2a2a] bg-[#1a1a1a]">
              <CardContent className="p-6">
                <TriadsGrowthStats timeRange={timeRange} onDataChange={setStatsData} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="organizers" className="m-0">
            <Card className="border-[#2a2a2a] bg-[#1a1a1a]">
              <CardContent className="p-6">
                <TriadsOrganizerStats timeRange={timeRange} onDataChange={setStatsData} />
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
