import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, ILike, UpdateResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { HubspotService } from '@services/crm';
import { IDataServices } from '@abstracts';
import { ProductLine } from '@entities';

@Injectable()
export class ProductLineUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly hubspotService: HubspotService,
  ) {}

  /**
   * Creates a product line
   * @param lineInfo Info of the product line
   */
  async create(lineInfo: Partial<ProductLine>): Promise<ProductLine> {
    const productLine = new ProductLine();
    productLine.name = lineInfo.name;
    productLine.description = lineInfo.description;
    productLine.status = 'active';
    productLine.chargebeeId = productLine.name;
    await this.hubspotService.createProductLine(
      productLine.name,
      productLine.description,
    );
    return this.dataServices.productLine.create(productLine);
  }

  /**
   * Returns all product line records from database
   * @returns Product line records from database
   */
  async getAll(
    limit: number,
    page: number,
  ): Promise<{ items: ProductLine[]; total: number }> {
    return this.dataServices.productLine.getAll(limit, page);
  }

  /**
   * Updates a product line
   * @param id ID of the line to update
   * @param updates Updates for the product line
   */
  async updateById(
    id: number,
    updates: QueryDeepPartialEntity<ProductLine>,
  ): Promise<UpdateResult> {
    return this.dataServices.productLine.update(
      {
        id,
      },
      updates,
    );
  }

  /**
   * Retrieves a product line matching provided filters
   * @param filter Properties of line to search for
   */
  async searchOne(filter: Partial<ProductLine>): Promise<ProductLine> {
    return this.dataServices.productLine.getOneBy(filter);
  }

  /**
   * Retrieves a product line matching provided `id``
   * @param id `id` of line to fetch
   */
  async getOne(id: number): Promise<ProductLine> {
    return this.dataServices.productLine.getOneBy({
      id,
    });
  }

  /**
   * Retrieves all product lines matching provided `status`
   * @param name `name` of the `ProductLine` to search for
   * @param status `status` of `ProductLine` to search for
   */
  async searchByNameOrStatus(
    name: string,
    status: string,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: ProductLine[]; total: number }> {
    const filters: FindOptionsWhere<ProductLine> = {};

    if (status) {
      filters.status = status;
    }

    if (name) {
      filters.name = ILike(`%${name}%`);
    }

    return this.dataServices.productLine.getAllBy(filters, limit, page, {
      createdAt: orderBy || 'DESC',
    });
  }
}
