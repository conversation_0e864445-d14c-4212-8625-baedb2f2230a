"use client";

import { useCallback, useMemo } from "react";
import { useTranslations } from "next-intl";
import { ColumnDef } from "@tanstack/react-table";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { DataTable } from "@/components/table";
import { useTable } from "@/hooks/use-table";
import { useTableActions } from "./actions";
import { useCourseList } from "@/services/course";
import { useCoursesTable } from "./use-courses-table";
import { Filter } from "../filter";
import { IBaseCourse } from "@px-shared-account/hermes";
import { useRouter } from "next/navigation";
import { StatusBadge } from "@/components/base/courses/status-badge";
const createColumns = (
  t: (key: string, params?: Record<string, any>) => string,
  router: any,
): ColumnDef<IBaseCourse>[] => [
  {
    header: t("courses.table.name"),
    accessorKey: "name",
    cell: ({ row }) => {
      const course = row.original;
      return (
        <div className="flex items-center gap-4">
          {course.thumbnail && (
            <div className="relative aspect-square size-10 flex-shrink-0 overflow-hidden rounded-xl">
              <Image
                src={course.thumbnail}
                alt={course.name}
                fill
                sizes="40px"
                className="object-cover"
                priority={false}
              />
            </div>
          )}
          <span className="font-medium">{course.name}</span>
        </div>
      );
    },
  },
  {
    header: t("courses.table.description"),
    accessorKey: "description",
    cell: ({ row }) => (
      <div className="max-w-[500px]">
        <span className="text-muted-foreground line-clamp-2 text-sm">
          {row.original.description || t("courses.table.no_description")}
        </span>
      </div>
    ),
  },
  {
    header: t("courses.table.status"),
    accessorKey: "status",
    cell: ({ row }) => (
      <StatusBadge status={t(`courses.status.${row.original.status?.toLowerCase() || "draft"}`)} />
    ),
  },
  {
    header: t("courses.table.created_at"),
    accessorKey: "createdAt",
    cell: ({ row }) => {
      const date = row.original.createdAt;
      return (
        <span className="text-muted-foreground text-sm">
          {date instanceof Date ? date.toLocaleDateString() : new Date(date).toLocaleDateString()}
        </span>
      );
    },
    enableSorting: true,
  },
  {
    id: "actions",
    header: t("courses.table.actions.header"),
    cell: ({ row }) => {
      const course = row.original;
      return (
        <Button
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => router.push(`/courses/${course.id}`)}
        >
          <span className="sr-only">{t("courses.table.view_details", { name: course.name })}</span>
          <ArrowRight className="h-4 w-4" />
        </Button>
      );
    },
  },
];

interface ModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: (data: any) => Promise<void>;
}

interface CoursesTableProps {
  modalProps: ModalProps;
}

export const CoursesTable = ({ modalProps }: CoursesTableProps) => {
  const t = useTranslations();
  const router = useRouter();
  const { DeleteConfirmationDialog } = useTableActions();

  // Use the custom hook for table state and configuration
  const { tableConfig, getRowId, itemsPerPage, tableState } = useCoursesTable();

  // Use the course list hook for API data fetching
  const { data, isLoading } = useCourseList(tableState);

  // Memoize table instance
  const { selectedRows, handleRowSelection } = useTable(tableConfig);

  // Memoize create course handler
  const handleCreateCourse = useCallback(() => {
    modalProps.onOpenChange(true);
  }, [modalProps]);

  // Memoize all table-related computations together
  const tableProps = useMemo(() => {
    // Format dates helper
    const ensureDate = (date: Date | string): Date => {
      if (!date) return new Date();
      return typeof date === "string" ? new Date(date) : date;
    };

    // Format data
    let formattedData =
      data?.data?.map((course) => ({
        ...course,
        createdAt: ensureDate(course.createdAt),
        updatedAt: ensureDate(course.updatedAt),
      })) ?? [];

    // Apply client-side sorting by createdAt in descending order (newest first)
    if (tableState.sortBy === "createdAt") {
      formattedData = [...formattedData].sort((a, b) => {
        const dateA = a.createdAt.getTime();
        const dateB = b.createdAt.getTime();
        return tableState.sortOrder === "asc" ? dateA - dateB : dateB - dateA;
      });
    }

    // Create columns with router
    const columns = createColumns(t, router);

    // Calculate total
    const total = data?.total ?? 0;

    return {
      // Static props
      enableSelection: true,
      enableSearch: true,
      enablePagination: true,
      enableSorting: true,
      pageSize: itemsPerPage,
      noResults: t("courses.table.no_results"),
      filters: <Filter onCreateClick={handleCreateCourse} />,

      // Dynamic props
      columns,
      data: formattedData,
      totalRows: total,
      title: `${t("courses.title")} (${total})`,
    };
  }, [data, t, router, itemsPerPage, handleCreateCourse, tableState]);

  // Memoize final DataTable props
  const dataTableProps = useMemo(
    () => ({
      ...tableProps,
      isLoading,
      selectedRows,
      getRowId,
      onStateChange: tableConfig.onStateChange,
      onSelectionChange: handleRowSelection,
      minSearchLength: 1,
    }),
    [tableProps, isLoading, selectedRows, getRowId, tableConfig.onStateChange, handleRowSelection],
  );

  return (
    <>
      <div className="overflow-x-auto">
        <DataTable {...dataTableProps} />
      </div>
      <DeleteConfirmationDialog />
    </>
  );
};
