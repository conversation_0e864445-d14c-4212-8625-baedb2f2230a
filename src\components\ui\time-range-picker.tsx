import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useTranslations } from "next-intl";
import { motion } from "framer-motion";
import * as React from "react";
import { ChevronsUpDown } from "lucide-react";

interface TimeRange {
  from?: Date;
  to?: Date;
}

interface TimeRangePickerProps {
  value?: TimeRange;
  onChange?: (value: TimeRange | undefined) => void;
  className?: string;
  placeholder?: string;
}

export function TimeRangePicker({ value, onChange, className, placeholder }: TimeRangePickerProps) {
  const t = useTranslations();
  const [timeRange, setTimeRange] = React.useState<TimeRange | undefined>(value);
  const [open, setOpen] = React.useState(false);

  // Sync internal state with external value
  React.useEffect(() => {
    setTimeRange(value);
  }, [value]);

  const hours = Array.from({ length: 24 }, (_, i) => i);

  const handleTimeClick = (type: "from" | "to", hour: number) => {
    const newDate = new Date();
    newDate.setHours(hour, 0, 0, 0);

    const newRange = {
      ...timeRange,
      [type]: newDate,
    };

    // If "to" time is before "from" time, swap them
    if (newRange.from && newRange.to && newRange.to < newRange.from) {
      const temp = newRange.from;
      newRange.from = newRange.to;
      newRange.to = temp;
    }

    setTimeRange(newRange);
    // Only trigger onChange when both times are set
    if (newRange.from && newRange.to) {
      onChange?.(newRange);
    }
  };

  const formatTime = (date?: Date) => {
    if (!date) return "";
    return format(date, "HH:00");
  };

  const getSelectedHour = (date?: Date) => {
    if (!date || !(date instanceof Date)) return undefined;
    return date.getHours();
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={open} onOpenChange={setOpen} modal={true}>
        <PopoverTrigger asChild>
          <Button variant="outline" className={"w-full justify-between"}>
            {timeRange?.from ? (
              <>
                {formatTime(timeRange.from)}
                {timeRange.to ? ` - ${formatTime(timeRange.to)}` : ""}
              </>
            ) : (
              placeholder || t("time_range_picker.select_time")
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="bg-background pointer-events-auto rounded-2xl border-none p-4 text-white"
          style={{ width: "var(--radix-popover-trigger-width)" }}
        >
          <div className="grid grid-cols-2 gap-4">
            {/* Start Time Column */}
            <div className="space-y-2">
              <Button
                variant={timeRange?.from ? "default" : "link"}
                className="!w-full justify-center rounded-full text-white hover:text-gray-300"
              >
                {timeRange?.from ? formatTime(timeRange.from) : t("time_range_picker.start_time")}
              </Button>
              <ScrollArea className="h-52">
                <div className="flex flex-col gap-1">
                  {hours.map((hour) => {
                    const selectedHour = getSelectedHour(timeRange?.from);
                    return (
                      <Button
                        key={`from-${hour}`}
                        variant="ghost"
                        className={cn(
                          "h-8 w-full px-3 py-1 hover:bg-white/10 hover:text-white",
                          selectedHour === hour && "bg-primary hover:bg-primary/50",
                        )}
                        onClick={() => handleTimeClick("from", hour)}
                      >
                        {hour.toString().padStart(2, "0")}:00
                      </Button>
                    );
                  })}
                </div>
              </ScrollArea>
            </div>

            {/* End Time Column */}
            <div className="space-y-2">
              <Button
                variant={timeRange?.to ? "default" : "link"}
                className="!w-full justify-center rounded-full text-white hover:text-gray-300"
              >
                {timeRange?.to ? formatTime(timeRange.to) : t("time_range_picker.end_time")}
              </Button>
              <ScrollArea className="h-52">
                <div className="flex flex-col gap-1">
                  {hours.map((hour) => {
                    const selectedHour = getSelectedHour(timeRange?.to);
                    return (
                      <Button
                        key={`to-${hour}`}
                        variant="ghost"
                        className={cn(
                          "h-8 w-full px-3 py-1 hover:bg-white/10 hover:text-white",
                          selectedHour === hour && "bg-primary hover:bg-primary/50",
                        )}
                        onClick={() => handleTimeClick("to", hour)}
                      >
                        {hour.toString().padStart(2, "0")}:00
                      </Button>
                    );
                  })}
                </div>
              </ScrollArea>
            </div>
          </div>

          <div className="mt-4 flex items-center justify-between border-t border-white/10 pt-4">
            <Button
              variant="default"
              onClick={() => {
                if (timeRange?.from && timeRange?.to) {
                  onChange?.(timeRange);
                  setOpen(false);
                }
              }}
              className="!w-3/4 text-sm"
            >
              {t("time_range_picker.apply")}
            </Button>
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                variant="link"
                onClick={() => {
                  setTimeRange(undefined);
                  onChange?.(undefined);
                }}
                className="text-sm text-white underline hover:text-gray-300"
              >
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.1 }}
                >
                  {t("time_range_picker.clear")}
                </motion.span>
              </Button>
            </motion.div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
