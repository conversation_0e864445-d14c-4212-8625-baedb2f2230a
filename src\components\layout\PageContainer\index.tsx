"use client";

import { useMemo } from "react";

import { Sidebar } from "@/components/base/sidebar";
import { Header } from "@/components/layout/Header";
import { SidebarProvider } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { NavigationItem, BaseProps } from "@/types";

export interface PageContainerProps extends BaseProps {
  title?: string;
  navigation: NavigationItem[];
  notificationCount?: number;
}

export const PageContainer = ({
  children,
  className,
  title,
  navigation,
  notificationCount,
}: PageContainerProps) => {
  // Memoize the sidebar to prevent unnecessary re-renders
  const sidebarMemo = useMemo(
    () => <Sidebar items={navigation} className="hidden md:flex md:w-[180px]" hideFooterOnMobile />,
    [navigation],
  );

  // Memoize the header to prevent unnecessary re-renders
  const headerMemo = useMemo(
    () => <Header title={title} notificationCount={notificationCount} />,
    [title, notificationCount],
  );

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        {sidebarMemo}
        <div className="flex min-w-[1px] flex-1 flex-col">
          {headerMemo}
          <main className={cn("mb-16 space-y-6 pt-14 md:mb-0 md:pt-0", className)}>{children}</main>
        </div>
      </div>
    </SidebarProvider>
  );
};

PageContainer.displayName = "PageContainer";
