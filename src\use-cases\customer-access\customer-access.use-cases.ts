import { IDataServices } from '@abstracts';
import { CustomerAccess } from '@entities';
import { AccessTarget, CustomerAccessType } from '@enums';
import { Injectable, Logger } from '@nestjs/common';
import { CustomerAccessFactory } from './customer-access.factory';
import {
  AccessDetails,
  AccessInfoWithCustomerInfo,
  CourseAccess,
  PXActionResult,
  SpaceAccess,
  SubscriptionAccesses,
} from '@types';
import { UserTable, SubscriptionTable } from '@tables';
import { CircleService } from '@services/community';
import { LearnworldsService } from '@services/lms';
import { GoliathService } from '../goliaths';
import { CustomerAccessStatus } from '@px-shared-account/hermes';

@Injectable()
export class CustomerAccessUseCases {
  private readonly logger = new Logger(CustomerAccessUseCases.name);

  constructor(
    private readonly dataServices: IDataServices,
    private readonly factory: CustomerAccessFactory,
    private readonly circleService: CircleService,
    private readonly learnworldsService: LearnworldsService,
    private readonly goliathService: GoliathService,
  ) {}

  /**
   * Creates a customer access record in DB
   * @param customerId `id` of the customer to create access for
   * @param subscriptionId `id` of the subscription to create access for
   * @param offerId `id` of the offer to create access for
   * @param status Status of the customer access
   * @param type Type of the customer access
   * @param details Details of the customer access
   * @returns A promise that resolves to the created customer access record or null
   */
  async create(
    customerId: number,
    subscriptionId: number,
    offerId: number,
    status: CustomerAccessStatus,
    type: CustomerAccessType,
    details: AccessDetails,
  ): Promise<CustomerAccess> {
    const customerAccess = this.factory.generate(
      customerId,
      subscriptionId,
      offerId,
      status,
      type,
      details,
    );
    return this.dataServices.customerAccess.create(customerAccess);
  }

  /**
   * Fetches a customer access record based on id
   * @param id Customer access record's `id`
   * @returns A promise that resolves to the fetched customer access record if found, null otherwise
   */
  async getById(id: number): Promise<CustomerAccess> {
    return this.dataServices.customerAccess.getOneBy({ id });
  }

  /**
   * Fetches a customer access record based on customer id
   * @param customerId Customer's `id`
   * @returns A promise that resolves to the fetched customer access record if found, null otherwise
   */
  async listByCustomerId(customerId: number): Promise<CustomerAccess> {
    return this.dataServices.customerAccess.getOneBy({ customerId });
  }

  /**
   * Fetches a customer access record based on customer email
   * @param email Customer's `email`
   * @returns A promise that resolves to the fetched customer access record if found, null otherwise
   */
  async getAllByCustomerEmailWithSubscriptionInfo(
    email: string,
  ): Promise<
    Array<CustomerAccess & { chargebeeId: string; offerName: string }>
  > {
    return this.dataServices.customerAccess
      .getRepository()
      .createQueryBuilder('customerAccess')
      .innerJoin(UserTable, 'user', 'user.id = customerAccess.customerId')
      .innerJoin(
        SubscriptionTable,
        'subscription',
        'subscription.id = customerAccess.subscriptionId',
      )
      .where('user.email ILIKE :email', { email })
      .select([
        'customerAccess.id AS id',
        'customerAccess.status AS status',
        'customerAccess.type AS type',
        'customerAccess.details AS details',
        'subscription.chargebeeId AS "chargebeeId"',
        'subscription.metadata->>\'offerName\' AS "offerName"',
      ])
      .getRawMany();
  }

  /**
   * Fetches all customer accesses for a given customer email and groups them by subscription
   * @param email Customer's email
   * @returns A promise that resolves to an object containing the grouped customer accesses
   */
  async listForSubscriptionsByCustomerEmail(
    email: string,
  ): Promise<Record<string, SubscriptionAccesses>> {
    const accesses = await this.getAllByCustomerEmailWithSubscriptionInfo(
      email,
    );
    console.log('accesses', accesses);
    const discountCode = await this.goliathService.getUserDiscountCode(email);

    const groupedData = accesses.reduce((acc, record) => {
      const chargebeeId = record.chargebeeId;
      const offerName = record.offerName;

      if (!acc[chargebeeId]) {
        acc[chargebeeId] = {
          offerName,
          communityAccesses: [],
          lmsAccesses: [],
        };
      }

      if (record?.details?.target === 'circleCommunity') {
        return acc;
      }

      if (record.type === 'community') {
        const details = record.details as SpaceAccess;
        acc[chargebeeId].communityAccesses.push({
          spaceName: details?.name ?? 'Unknown',
          community: details?.domain,
          status: record.status,
          id: record.id,
        });
      } else if (record.type === 'lms') {
        acc[chargebeeId].lmsAccesses.push({
          courseName: record.details.name,
          status: record.status,
          id: record.id,
        });
      }

      return acc;
    }, {});

    console.log('groupedData', JSON.stringify(groupedData, null, 2));

    groupedData['discountCode'] = discountCode;
    return groupedData;
  }

  /**
   * Fetches a customer access record based on subscription id
   * @param subscriptionId Subscription's `id`
   * @param limit Number of records to fetch
   * @param page Page number
   * @param orderBy Sort order (ASC or DESC) for the createdAt field
   * @returns A promise that resolves to the fetched customer access record if found, null otherwise
   */
  async listBySubscriptionId(
    subscriptionId: number,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: CustomerAccess[]; total: number }> {
    return this.dataServices.customerAccess.getAllBy(
      { subscriptionId },
      limit,
      page,
      {
        createdAt: orderBy,
      },
    );
  }

  /**
   * Fetches a customer access record based on offer id
   * @param offerId Offer's `id`
   * @param limit Number of records to fetch
   * @param page Page number
   * @param orderBy Sort order (ASC or DESC) for the createdAt field
   * @returns A promise that resolves to the fetched customer access record if found, null otherwise
   */
  async listByOfferId(
    offerId: number,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: CustomerAccess[]; total: number }> {
    return this.dataServices.customerAccess.getAllBy({ offerId }, limit, page, {
      createdAt: orderBy,
    });
  }

  /**
   * Filters and returns customer access records based on status
   * @param status `status` of the customer access records to filter by
   * @param limit Number of records to return
   * @param page Page number to return
   * @param sortOrder Sort order of the records to return
   * @returns A promise that resolves to an object containing the filtered customer access records and the total count
   */
  async filterByStatus(
    status: CustomerAccessStatus,
    limit = 1000,
    page = 1,
    sortOrder?: 'DESC' | 'ASC',
  ): Promise<{ items: CustomerAccess[]; total: number }> {
    return this.dataServices.customerAccess.getAllBy({ status }, limit, page, {
      createdAt: sortOrder,
    });
  }

  /**
   * Updates a customer access record
   * @param lookupColum Column to match the record to update
   * @param updates Updates for the customer access record
   * @returns A promise that resolves to `PXActionResult`
   */
  async update(
    lookupColum: Partial<CustomerAccess>,
    updates: Partial<CustomerAccess>,
  ): Promise<PXActionResult> {
    const result = await this.dataServices.customerAccess.update(
      lookupColum,
      updates,
    );
    if (result?.affected > 0) {
      return {
        success: true,
        message: `Access updated successfully`,
        data: { id: result?.affected },
      };
    }
    return {
      success: false,
      message: `Access not updated`,
      data: null,
    };
  }
  /**
   * Grants access to a customer by their access ID.
   * Delegates to `handleAccessChange` with action type "grant".
   *
   * @param accessId - The ID of the access record to grant.
   * @returns A promise that resolves to a `PXActionResult` object indicating success or failure.
   */
  async grantAccess(accessId: number): Promise<PXActionResult> {
    return this.handleAccessChange(accessId, 'grant');
  }

  /**
   * Revokes access from a customer by their access ID.
   * Delegates to `handleAccessChange` with action type "revoke".
   *
   * @param accessId - The ID of the access record to revoke.
   * @returns A promise that resolves to a `PXActionResult` object indicating success or failure.
   */
  async revokeAccess(accessId: number): Promise<PXActionResult> {
    return this.handleAccessChange(accessId, 'revoke');
  }

  /**
   * Handles access changes (granting or revoking) for a customer based on access target.
   *
   * @param accessId - The ID of the access record to update.
   * @param action - The action to perform: "grant" or "revoke".
   * @returns A promise that resolves to a `PXActionResult` indicating the result of the operation.
   */
  private async handleAccessChange(
    accessId: number,
    action: 'grant' | 'revoke',
  ): Promise<PXActionResult> {
    const access = await this.getAccessAndCustomerInfo(accessId);
    if (!access?.details?.target) {
      return {
        success: false,
        message: 'Invalid access target',
        data: null,
      };
    }

    const { target } = access.details;
    const isGranting = action === 'grant';
    const newStatus = isGranting
      ? CustomerAccessStatus.GRANTED
      : CustomerAccessStatus.REVOKED;

    try {
      let changeResult: PXActionResult;
      if (target === AccessTarget.CIRCLE_SPACE) {
        const { spaceId, domain } = access.details as SpaceAccess;
        if (isGranting) {
          changeResult = await this.circleService.addSpaceMemberV1(
            access.email,
            spaceId,
            domain,
          );
        } else {
          changeResult = await this.circleService.removeSpaceMemberV1(
            access.email,
            spaceId,
            domain,
          );
        }
      } else if (target === AccessTarget.LW_COURSE) {
        const { slug } = access.details as CourseAccess;
        if (isGranting) {
          changeResult = await this.learnworldsService.enrollUser(
            access.email,
            access.customerName,
            slug,
          );
        } else {
          changeResult = await this.learnworldsService.unEnrollUser(
            access.email,
            slug,
          );
        }
      } else {
        return {
          success: false,
          message: 'Unsupported access target',
          data: null,
        };
      }

      if (changeResult.success) {
        return this.update(
          { id: access.id },
          { status: newStatus, metaData: { message: changeResult.message } },
        );
      } else {
        await this.update(
          { id: access.id },
          { metaData: { message: changeResult.message } },
        );
        return {
          success: false,
          message: `Failed to ${action} access: ${changeResult.message}`,
          data: null,
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to ${action} access: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Retrieves access and customer info for a given access ID
   * @param accessId Access ID
   * @returns A promise that resolves to `AccessInfoWithCustomerInfo` object
   */
  async getAccessAndCustomerInfo(
    accessId: number,
  ): Promise<AccessInfoWithCustomerInfo> {
    return this.dataServices.customerAccess
      .getRepository()
      .createQueryBuilder('customerAccess')
      .innerJoin(UserTable, 'user', 'user.id = customerAccess.customerId')
      .where('customerAccess.id = :accessId', { accessId })
      .select([
        'customerAccess.id AS id',
        'customerAccess.status AS status',
        'customerAccess.type AS type',
        'customerAccess.details AS details',
        'customer.email AS email',
        'customer.firstName AS customerName',
      ])
      .getRawOne<AccessInfoWithCustomerInfo>();
  }
}
