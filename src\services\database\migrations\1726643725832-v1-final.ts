import { MigrationInterface, QueryRunner } from 'typeorm';

export class V1Final1726643725832 implements MigrationInterface {
  name = 'V1Final1726643725832';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."bankTransfers_currency_enum" AS ENUM('USD', 'EUR', 'AED', 'GBP')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."bankTransfers_status_enum" AS ENUM('matched', 'unmatched', 'ignored')`,
    );
    await queryRunner.query(
      `CREATE TABLE "bankTransfers" ("id" SERIAL NOT NULL, "bankName" character varying NOT NULL, "currency" "public"."bankTransfers_currency_enum" NOT NULL, "paymentId" character varying NOT NULL, "paymentDate" TIMESTAMP NOT NULL, "senderEmail" character varying NOT NULL, "paymentAmount" numeric NOT NULL, "senderAddress" character varying NOT NULL, "senderLastName" character varying NOT NULL, "senderFirstName" character varying NOT NULL, "paymentDescription" character varying NOT NULL, "status" "public"."bankTransfers_status_enum" DEFAULT 'unmatched' NOT NULL, "entityId" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "UQ_c5b05928690fcb452ef19ce5fc6" UNIQUE ("paymentId"), CONSTRAINT "PK_6940f30f2b8c3d581eee3f20436" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."creditNotes_status_enum" AS ENUM('adjusted', 'refunded', 'refund_due', 'voided')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."creditNotes_currencycode_enum" AS ENUM('USD', 'EUR', 'AED', 'GBP')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."creditNotes_type_enum" AS ENUM('adjustment', 'refundable')`,
    );
    await queryRunner.query(
      `CREATE TABLE "creditNotes" ("id" SERIAL NOT NULL, "chargebeeId" character varying NOT NULL, "status" "public"."creditNotes_status_enum" NOT NULL, "date" integer NOT NULL, "generatedAt" integer NOT NULL, "refundedAt" integer, "currencyCode" "public"."creditNotes_currencycode_enum" NOT NULL, "total" integer NOT NULL, "exchangeRate" numeric NOT NULL, "customerId" character varying NOT NULL, "customerEmail" character varying NOT NULL, "customerName" character varying NOT NULL, "subscriptionId" character varying, "referenceInvoiceId" character varying, "type" "public"."creditNotes_type_enum" NOT NULL, "businessEntityId" character varying NOT NULL, "amountAvailable" integer NOT NULL, "amountAllocated" integer NOT NULL, "amountRefunded" integer NOT NULL, "createReason" character varying(100) NOT NULL, "channel" character varying NOT NULL, "priceType" character varying NOT NULL, "allocations" jsonb DEFAULT '[]', "lineItems" jsonb DEFAULT '[]', "linkedRefunds" jsonb DEFAULT '[]', "billingAddress" jsonb DEFAULT '{}', "shippingAddress" jsonb DEFAULT '{}', "customerNotes" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_c5e5d1d68c8474d9c114f9a8084" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum" AS ENUM('OVERDUE_INVOICE_CHECK')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_status_enum" AS ENUM('SUCCESS', 'FAILED', 'RUNNING')`,
    );
    await queryRunner.query(
      `CREATE TABLE "cronLogs" ("id" character varying NOT NULL, "type" "public"."cronLogs_type_enum" NOT NULL, "status" "public"."cronLogs_status_enum" NOT NULL, "result" json NOT NULL, "error" json, "startTime" TIMESTAMP WITH TIME ZONE NOT NULL, "endTime" TIMESTAMP WITH TIME ZONE NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_29a5f964f13277fa4f097826b23" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."productPlanPrices_fiscalentity_enum" AS ENUM('PG', 'PI', 'PP')`,
    );
    await queryRunner.query(
      `CREATE TABLE "productPlanPrices" ("id" SERIAL NOT NULL, "internalName" character varying(255) NOT NULL, "externalName" character varying(255) NOT NULL, "description" character varying(500) NOT NULL, "status" character varying(8) NOT NULL, "pricingModel" character varying(10) NOT NULL DEFAULT 'per_unit', "amountPerBillingCycle" numeric NOT NULL, "amount" numeric NOT NULL, "period" integer, "periodUnit" character varying(15) NOT NULL DEFAULT 'month', "currencyCode" character varying(5) NOT NULL DEFAULT 'EUR', "totalBillingCycles" integer, "chargebeeId" character varying NOT NULL, "fiscalEntity" "public"."productPlanPrices_fiscalentity_enum", "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "productPlanId" integer, CONSTRAINT "UQ_b3e4e1b329e03ba74dd8fad49a0" UNIQUE ("internalName"), CONSTRAINT "PK_6adaf6bbc425b21644256142973" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."discounts_status_enum" AS ENUM('active', 'disabled', 'expired')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."discounts_type_enum" AS ENUM('percentage', 'fixed_amount')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."discounts_applyon_enum" AS ENUM('invoice_amount', 'each_specified_item')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."discounts_durationtype_enum" AS ENUM('one_time', 'limited_period', 'forever')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."discounts_durationperiodunit_enum" AS ENUM('day', 'week', 'month', 'year')`,
    );
    await queryRunner.query(
      `CREATE TABLE "discounts" ("id" SERIAL NOT NULL, "name" character varying(100) NOT NULL, "code" character varying(50) NOT NULL, "status" "public"."discounts_status_enum" NOT NULL DEFAULT 'active', "invoiceName" character varying(100), "type" "public"."discounts_type_enum" NOT NULL, "currency" character varying(5) DEFAULT 'EUR', "amount" numeric NOT NULL, "applyOn" "public"."discounts_applyon_enum" NOT NULL, "durationType" "public"."discounts_durationtype_enum" NOT NULL, "durationPeriodUnit" "public"."discounts_durationperiodunit_enum", "durationPeriodAmount" integer, "validUntil" TIMESTAMP WITH TIME ZONE, "maxRedemptions" integer, "attachedTo" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_66c522004212dc814d6e2f14ecc" PRIMARY KEY ("id")); COMMENT ON COLUMN "discounts"."currency" IS 'Only if type === fixed'`,
    );
    await queryRunner.query(
      `CREATE TABLE "invoices" ("id" SERIAL NOT NULL, "chargebeeId" character varying NOT NULL, "customerId" character varying NOT NULL, "customerEmail" character varying NOT NULL, "customerName" character varying NOT NULL, "subscriptionId" character varying NOT NULL, "status" character varying NOT NULL, "priceType" character varying NOT NULL, "date" integer NOT NULL, "dueDate" integer NOT NULL, "paidAt" integer, "netTermDays" integer NOT NULL, "exchangeRate" numeric, "total" integer NOT NULL, "amountPaid" integer NOT NULL, "amountAdjusted" integer NOT NULL, "writeOffAmount" integer NOT NULL, "amountDue" integer NOT NULL, "amountToCollect" integer NOT NULL, "currencyCode" character varying NOT NULL, "baseCurrencyCode" character varying, "generatedAt" bigint NOT NULL, "channel" character varying NOT NULL, "tax" integer NOT NULL, "lineItems" jsonb NOT NULL, "discounts" jsonb, "lineItemDiscounts" jsonb, "taxes" jsonb, "lineItemTaxes" jsonb, "subTotal" integer NOT NULL, "linkedPayments" jsonb, "adjustmentCreditNotes" jsonb, "issuedCreditNotes" jsonb, "dunningAttempts" jsonb, "billingAddress" jsonb DEFAULT '{}', "shippingAddress" jsonb DEFAULT '{}', "businessEntityId" character varying NOT NULL, "crmId" character varying, "overdueCheckPassed" boolean DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_668cef7c22a427fd822cc1be3ce" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "productLines" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "status" character varying NOT NULL, "chargebeeId" character varying(100) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "UQ_0a8af4ef74125b23e29075ba7af" UNIQUE ("name"), CONSTRAINT "PK_2f446b2acf10433c233841eef5c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "productFamilies" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "status" character varying NOT NULL, "chargebeeId" character varying(100) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "productLineId" integer, CONSTRAINT "UQ_783e2f36c2326a01e6ba41e3363" UNIQUE ("name"), CONSTRAINT "PK_0fae9fb71c8571d473deaa49cbd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."productOffers_fiscalentity_enum" AS ENUM('PG', 'PI', 'PP')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."productOffers_status_enum" AS ENUM('active', 'disabled')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."productOffers_version_enum" AS ENUM('live', 'preview')`,
    );
    await queryRunner.query(
      `CREATE TABLE "productOffers" ("id" SERIAL NOT NULL, "name" character varying(255) NOT NULL, "image" character varying(255) NOT NULL, "cardImage" character varying(255) NOT NULL, "bannerImage" character varying(255), "description" character varying(800), "usp" character varying(200), "currency" character varying(3) NOT NULL, "fiscalEntity" "public"."productOffers_fiscalentity_enum" NOT NULL, "status" "public"."productOffers_status_enum" NOT NULL, "redirectUrl" character varying(100) NOT NULL, "redirectIfDisabled" character varying(100), "chargebeeId" character varying(100) NOT NULL, "history" jsonb, "version" "public"."productOffers_version_enum" NOT NULL DEFAULT 'preview', "config" jsonb, "paymentGateway" character varying(100), "slug" character varying(100) NOT NULL, "isForever" boolean DEFAULT false, "trialDaysMonthly" integer DEFAULT '28', "trialDaysYearly" integer DEFAULT '300', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "checkoutPage" integer, CONSTRAINT "REL_fd09a76f9d55073f73b3f2b5a3" UNIQUE ("checkoutPage"), CONSTRAINT "PK_1a14b12ee482698fdc24ec48ea7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."productPlans_fiscalentity_enum" AS ENUM('PG', 'PI', 'PP')`,
    );
    await queryRunner.query(
      `CREATE TABLE "productPlans" ("id" SERIAL NOT NULL, "internalName" character varying(255) NOT NULL, "externalName" character varying(255) NOT NULL, "description" character varying(500) NOT NULL, "status" character varying(8) NOT NULL, "fiscalEntity" "public"."productPlans_fiscalentity_enum", "chargebeeId" character varying(100) NOT NULL, "crmId" character varying(50), "crmSku" character varying(99), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "productId" integer, CONSTRAINT "UQ_52d6c3e49a74b87dfbf333602ba" UNIQUE ("internalName"), CONSTRAINT "PK_08e39f4edba73df7ed72d77845d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."products_fiscalentity_enum" AS ENUM('PG', 'PI', 'PP')`,
    );
    await queryRunner.query(
      `CREATE TABLE "products" ("id" SERIAL NOT NULL, "internalName" character varying(255) NOT NULL, "externalName" character varying(255) NOT NULL, "description" character varying(500) NOT NULL, "code" character varying(255) NOT NULL, "fiscalEntity" "public"."products_fiscalentity_enum", "image" character varying(100), "status" character varying(10) NOT NULL, "taxProfile" character varying(100), "isEvent" boolean DEFAULT false, "isForever" boolean DEFAULT false, "eventYear" character varying(10), "eventLocation" character varying(255), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "productFamilyId" integer, CONSTRAINT "UQ_7cfc24d6c24f0ec91294003d6b8" UNIQUE ("code"), CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."subscriptions_status_enum" AS ENUM('future', 'in_trial', 'active', 'non_renewing', 'paused', 'cancelled', 'transferred')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."subscriptions_orderstatus_enum" AS ENUM('paid', 'active', 'refunded', 'stopped', 'overdue', 'in trial')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."subscriptions_trialendaction_enum" AS ENUM('site_default', 'plan_default', 'activate_subscription', 'cancel_subscription')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."subscriptions_cancelreason_enum" AS ENUM('not_paid', 'no_card', 'fraud_review_failed', 'non_compliant_eu_customer', 'tax_calculation_failed', 'currency_incompatible_with_gateway', 'non_compliant_customer')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."subscriptions_channel_enum" AS ENUM('web', 'app_store', 'play_store')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."subscriptions_freeperiodunit_enum" AS ENUM('day', 'week', 'month', 'year')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."subscriptions_billingperiodunit_enum" AS ENUM('day', 'week', 'month', 'year')`,
    );
    await queryRunner.query(
      `CREATE TABLE "subscriptions" ("id" SERIAL NOT NULL, "chargebeeId" character varying NOT NULL, "currencyCode" character varying NOT NULL, "billingPeriod" integer, "initialBillingCycles" integer, "totalBillingCycles" integer, "remainingBillingCycles" integer, "poNumber" character varying, "planId" character varying, "planQuantity" integer, "planQuantityInDecimal" character varying, "crmId" character varying, "planUnitPriceInDecimal" character varying, "customerId" character varying NOT NULL, "customerName" character varying NOT NULL, "customerEmail" character varying NOT NULL, "status" "public"."subscriptions_status_enum" NOT NULL, "orderStatus" "public"."subscriptions_orderstatus_enum", "trialEndAction" "public"."subscriptions_trialendaction_enum", "contractTermBillingCycleOnRenewal" integer, "overrideRelationship" boolean, "cancelReason" "public"."subscriptions_cancelreason_enum", "createdFromIp" character varying, "resourceVersion" bigint, "hasScheduledAdvanceInvoices" boolean, "hasScheduledChanges" boolean, "paymentSourceId" character varying, "planFreeQuantityInDecimal" character varying, "planAmountInDecimal" character varying, "channel" "public"."subscriptions_channel_enum", "netTermDays" integer, "activeId" character varying, "dueInvoicesCount" integer, "totalDues" integer, "amountPaid" integer, "amountRemaining" integer, "amountRefunded" integer, "totalOrderAmount" integer, "mrr" integer, "exchangeRate" integer, "baseCurrencyCode" character varying, "invoiceNotes" character varying, "autoCollection" character varying, "metadata" jsonb, "deleted" boolean, "cancelReasonCode" character varying, "freePeriod" integer, "freePeriodUnit" "public"."subscriptions_freeperiodunit_enum", "billingPeriodUnit" "public"."subscriptions_billingperiodunit_enum", "createPendingInvoices" boolean, "autoCloseInvoices" boolean, "businessEntityId" character varying NOT NULL, "subscriptionItems" jsonb, "itemTiers" jsonb, "chargedItems" jsonb, "coupons" jsonb, "shippingAddress" jsonb, "referralInfo" jsonb, "contractTerm" jsonb, "customFields" jsonb, "discounts" jsonb, "addons" jsonb, "startedAt" integer, "activatedAt" integer, "startDate" integer, "pauseDate" integer, "resumeDate" integer, "currentTermStart" integer, "currentTermEnd" integer, "changesScheduledAt" integer, "nextBillingAt" integer, "cancelScheduleCreatedAt" integer, "cancelledAt" integer, "trialStart" integer, "trialEnd" integer, "isForever" boolean, "dueSince" integer, "createdAt" integer NOT NULL, "updatedAt" integer, "deletedAt" TIMESTAMP, CONSTRAINT "PK_a87248d73155605cf782be9ee5e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."transactions_status_enum" AS ENUM('in_progress', 'success', 'voided', 'failure', 'timeout', 'needs_attention')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."transactions_type_enum" AS ENUM('authorization', 'payment', 'refund', 'payment_reversal')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."transactions_currency_enum" AS ENUM('USD', 'EUR', 'AED', 'GBP')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."transactions_paymentmethod_enum" AS ENUM('card', 'check', 'chargeback', 'bank_transfer', 'apple_pay', 'google_pay')`,
    );
    await queryRunner.query(
      `CREATE TABLE "transactions" ("id" SERIAL NOT NULL, "chargebeeId" character varying NOT NULL, "status" "public"."transactions_status_enum" NOT NULL, "date" integer NOT NULL, "type" "public"."transactions_type_enum" NOT NULL, "currency" "public"."transactions_currency_enum" NOT NULL, "amount" integer NOT NULL, "amountUnused" integer NOT NULL DEFAULT 0, "subscriptionId" character varying, "customerId" character varying NOT NULL, "customerEmail" character varying NOT NULL, "customerName" character varying NOT NULL, "exchangeRate" numeric, "referenceNumber" character varying, "linkedInvoices" jsonb DEFAULT '[]', "linkedCreditNotes" jsonb DEFAULT '[]', "linkedRefunds" jsonb DEFAULT '[]', "paymentMethod" "public"."transactions_paymentmethod_enum", "maskedCardNumber" character varying, "paymentMethodDetails" json, "gateway" character varying NOT NULL, "idAtGateway" character varying, "businessEntityId" character varying NOT NULL, "errorCode" character varying, "errorText" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_a219afd8dd77ed80f5a862f1db9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."users_role_enum" AS ENUM('ADMIN', 'FINANCE', 'SALES')`,
    );
    await queryRunner.query(
      `CREATE TABLE "users" ("id" SERIAL NOT NULL, "name" character varying(50) NOT NULL, "email" character varying(50) NOT NULL, "password" character varying(200) NOT NULL, "role" "public"."users_role_enum" NOT NULL DEFAULT 'ADMIN', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "warrantySection" ("id" SERIAL NOT NULL, "icon" character varying NOT NULL, "message" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP DEFAULT now(), "deletedAt" TIMESTAMP, "checkoutPageId" integer, CONSTRAINT "PK_34baa81dc13365a10eb03caa690" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "checkoutPages" ("id" SERIAL NOT NULL, "bannerImage" character varying(100) NOT NULL, "mainColor" character varying(10) NOT NULL, "isLive" boolean DEFAULT false, "url" character varying(100), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_8c14c1dad356aa9864acfc6a624" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "Discount_ProductPlanPrice" ("discountsId" integer NOT NULL, "productPlanPricesId" integer NOT NULL, CONSTRAINT "PK_2be403d0fc2d7c332ee557af058" PRIMARY KEY ("discountsId", "productPlanPricesId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bdb7203e41d570362cf138de77" ON "Discount_ProductPlanPrice" ("discountsId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3022623ed410cebab2324037c7" ON "Discount_ProductPlanPrice" ("productPlanPricesId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "ProductOffer_Product" ("offerId" integer NOT NULL, "productId" integer NOT NULL, CONSTRAINT "PK_f32e2fd56a2ab08c410e7d460ba" PRIMARY KEY ("offerId", "productId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ffa06cd9f2b89717db549a904c" ON "ProductOffer_Product" ("offerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_39bbd4874b26b210ec4c26cf7d" ON "ProductOffer_Product" ("productId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "ProductOffer_Plans" ("offerId" integer NOT NULL, "planId" integer NOT NULL, CONSTRAINT "PK_74eda2825ebc4202e84415ed84b" PRIMARY KEY ("offerId", "planId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_091b307ac4349bf74df4c7f463" ON "ProductOffer_Plans" ("offerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7a16a31913fa520242ca3c7301" ON "ProductOffer_Plans" ("planId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "Subscription_ProductPlanPrice" ("subscriptionsId" integer NOT NULL, "productPlanPricesId" integer NOT NULL, CONSTRAINT "PK_4b94ad205e5d1fcc626de6f96d0" PRIMARY KEY ("subscriptionsId", "productPlanPricesId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_053e0fac8a1cd307c70536a230" ON "Subscription_ProductPlanPrice" ("subscriptionsId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_15693a64cfe30957b896f63140" ON "Subscription_ProductPlanPrice" ("productPlanPricesId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "productPlanPrices" ADD CONSTRAINT "FK_f00e575fd4e3322be1b610d5006" FOREIGN KEY ("productPlanId") REFERENCES "productPlans"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "productFamilies" ADD CONSTRAINT "FK_4b961e4d6902f759329dffbfa7a" FOREIGN KEY ("productLineId") REFERENCES "productLines"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "productOffers" ADD CONSTRAINT "FK_fd09a76f9d55073f73b3f2b5a3c" FOREIGN KEY ("checkoutPage") REFERENCES "checkoutPages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "productPlans" ADD CONSTRAINT "FK_f6b823f8f4ddb027d6b18b4fd1d" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" ADD CONSTRAINT "FK_6cc12940073ebd8958669cc94b6" FOREIGN KEY ("productFamilyId") REFERENCES "productFamilies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "warrantySection" ADD CONSTRAINT "FK_checkoutPageWarrantySection" FOREIGN KEY ("checkoutPageId") REFERENCES "checkoutPages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "Discount_ProductPlanPrice" ADD CONSTRAINT "FK_bdb7203e41d570362cf138de77c" FOREIGN KEY ("discountsId") REFERENCES "discounts"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "Discount_ProductPlanPrice" ADD CONSTRAINT "FK_3022623ed410cebab2324037c71" FOREIGN KEY ("productPlanPricesId") REFERENCES "productPlanPrices"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "ProductOffer_Product" ADD CONSTRAINT "FK_ProductOffer_Product_offerId" FOREIGN KEY ("offerId") REFERENCES "productOffers"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "ProductOffer_Product" ADD CONSTRAINT "FK_ProductOffer_Product_productId" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "ProductOffer_Plans" ADD CONSTRAINT "FK_ProductOffer_Plans_offerId" FOREIGN KEY ("offerId") REFERENCES "productOffers"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "ProductOffer_Plans" ADD CONSTRAINT "FK_ProductOffer_Plans_planId" FOREIGN KEY ("planId") REFERENCES "productPlans"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "Subscription_ProductPlanPrice" ADD CONSTRAINT "FK_053e0fac8a1cd307c70536a2308" FOREIGN KEY ("subscriptionsId") REFERENCES "subscriptions"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "Subscription_ProductPlanPrice" ADD CONSTRAINT "FK_15693a64cfe30957b896f63140d" FOREIGN KEY ("productPlanPricesId") REFERENCES "productPlanPrices"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "Subscription_ProductPlanPrice" DROP CONSTRAINT "FK_15693a64cfe30957b896f63140d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "Subscription_ProductPlanPrice" DROP CONSTRAINT "FK_053e0fac8a1cd307c70536a2308"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ProductOffer_Plans" DROP CONSTRAINT "FK_ProductOffer_Plans_planId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ProductOffer_Plans" DROP CONSTRAINT "FK_ProductOffer_Plans_offerId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ProductOffer_Product" DROP CONSTRAINT "FK_ProductOffer_Product_productId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ProductOffer_Product" DROP CONSTRAINT "FK_ProductOffer_Product_offerId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "Discount_ProductPlanPrice" DROP CONSTRAINT "FK_3022623ed410cebab2324037c71"`,
    );
    await queryRunner.query(
      `ALTER TABLE "Discount_ProductPlanPrice" DROP CONSTRAINT "FK_bdb7203e41d570362cf138de77c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "warrantySection" DROP CONSTRAINT "FK_checkoutPageWarrantySection"`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" DROP CONSTRAINT "FK_6cc12940073ebd8958669cc94b6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "productPlans" DROP CONSTRAINT "FK_f6b823f8f4ddb027d6b18b4fd1d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "productOffers" DROP CONSTRAINT "FK_fd09a76f9d55073f73b3f2b5a3c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "productFamilies" DROP CONSTRAINT "FK_4b961e4d6902f759329dffbfa7a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "productPlanPrices" DROP CONSTRAINT "FK_f00e575fd4e3322be1b610d5006"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_15693a64cfe30957b896f63140"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_053e0fac8a1cd307c70536a230"`,
    );
    await queryRunner.query(`DROP TABLE "Subscription_ProductPlanPrice"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7a16a31913fa520242ca3c7301"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_091b307ac4349bf74df4c7f463"`,
    );
    await queryRunner.query(`DROP TABLE "ProductOffer_Plans"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_39bbd4874b26b210ec4c26cf7d"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ffa06cd9f2b89717db549a904c"`,
    );
    await queryRunner.query(`DROP TABLE "ProductOffer_Product"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3022623ed410cebab2324037c7"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_bdb7203e41d570362cf138de77"`,
    );
    await queryRunner.query(`DROP TABLE "Discount_ProductPlanPrice"`);
    await queryRunner.query(`DROP TABLE "checkoutPages"`);
    await queryRunner.query(`DROP TABLE "warrantySection"`);
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
    await queryRunner.query(`DROP TABLE "transactions"`);
    await queryRunner.query(
      `DROP TYPE "public"."transactions_paymentmethod_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."transactions_currency_enum"`);
    await queryRunner.query(`DROP TYPE "public"."transactions_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."transactions_status_enum"`);
    await queryRunner.query(`DROP TABLE "subscriptions"`);
    await queryRunner.query(
      `DROP TYPE "public"."subscriptions_freeperiodunit_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."subscriptions_billingperiodunit_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."subscriptions_channel_enum"`);
    await queryRunner.query(
      `DROP TYPE "public"."subscriptions_cancelreason_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."subscriptions_trialendaction_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."subscriptions_orderstatus_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."subscriptions_status_enum"`);
    await queryRunner.query(`DROP TABLE "products"`);
    await queryRunner.query(`DROP TYPE "public"."products_fiscalentity_enum"`);
    await queryRunner.query(`DROP TABLE "productPlans"`);
    await queryRunner.query(
      `DROP TYPE "public"."productPlans_fiscalentity_enum"`,
    );
    await queryRunner.query(`DROP TABLE "productOffers"`);
    await queryRunner.query(`DROP TYPE "public"."productOffers_version_enum"`);
    await queryRunner.query(`DROP TYPE "public"."productOffers_status_enum"`);
    await queryRunner.query(
      `DROP TYPE "public"."productOffers_fiscalentity_enum"`,
    );
    await queryRunner.query(`DROP TABLE "productFamilies"`);
    await queryRunner.query(`DROP TABLE "productLines"`);
    await queryRunner.query(`DROP TABLE "invoices"`);
    await queryRunner.query(`DROP TABLE "discounts"`);
    await queryRunner.query(
      `DROP TYPE "public"."discounts_durationperiodunit_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."discounts_durationtype_enum"`);
    await queryRunner.query(`DROP TYPE "public"."discounts_applyon_enum"`);
    await queryRunner.query(`DROP TYPE "public"."discounts_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."discounts_status_enum"`);
    await queryRunner.query(`DROP TABLE "productPlanPrices"`);
    await queryRunner.query(
      `DROP TYPE "public"."productPlanPrices_fiscalentity_enum"`,
    );
    await queryRunner.query(`DROP TABLE "cronLogs"`);
    await queryRunner.query(`DROP TYPE "public"."cronLogs_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum"`);
    await queryRunner.query(`DROP TABLE "creditNotes"`);
    await queryRunner.query(`DROP TYPE "public"."creditNotes_type_enum"`);
    await queryRunner.query(
      `DROP TYPE "public"."creditNotes_currencycode_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."creditNotes_status_enum"`);
    await queryRunner.query(`DROP TABLE "bankTransfers"`);
    await queryRunner.query(`DROP TYPE "public"."bankTransfers_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."bankTransfers_currency_enum"`);
  }
}
