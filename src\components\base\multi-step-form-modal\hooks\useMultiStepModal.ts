import { useEffect, useCallback, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ModalConfig, ModalEvents, FieldConfig } from "../types";
import { useModalStore } from "./useModalStore";
import * as z from "zod";
import { useTranslations } from "next-intl";

/**
 * Checks if a value should be considered empty
 * @param value - The value to check
 * @returns True if the value is considered empty
 */
const isEmptyValue = (value: any): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === "string") return value.trim() === "";
  if (typeof value === "boolean") return false;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === "object") return Object.keys(value).length === 0;
  return false;
};

/**
 * Checks if form data has any non-empty values compared to initial data
 * @param data - Current form data
 * @param initialData - Initial form data to compare against
 * @returns True if there are any non-empty values that differ from initial data
 */
const hasNonEmptyValues = (
  data: Record<string, any>,
  initialData: Record<string, any>,
): boolean => {
  return Object.entries(data).some(([key, value]) => {
    const initialValue = initialData[key];
    return !isEmptyValue(value) && value !== initialValue;
  });
};

/**
 * Custom hook for managing multi-step modal state and behavior
 *
 * This hook provides form handling, validation, step navigation, and state management
 * for multi-step modals. It integrates with react-hook-form for form handling and
 * zod for validation.
 *
 * @param config - Configuration object for the modal
 * @param events - Optional event handlers for modal lifecycle events
 * @returns Object containing form instance, state, and control functions
 *
 * @example
 * ```tsx
 * const {
 *   form,
 *   isOpen,
 *   currentStep,
 *   nextStep,
 *   previousStep,
 * } = useMultiStepModal({
 *   key: "example",
 *   title: "Example Modal",
 *   steps: [...]
 * });
 * ```
 */
export const useMultiStepModal = (config: ModalConfig, events?: ModalEvents) => {
  const {
    modals,
    registerModal,
    unregisterModal,
    nextStep: nextStepStore,
    previousStep: previousStepStore,
    setFormData,
    setLoading,
    resetForm: resetFormStore,
    setDirty,
  } = useModalStore();

  const t = useTranslations("validation");
  const isUpdatingRef = useRef(false);

  /**
   * Creates a Zod validation schema based on field configurations
   * @returns Zod object schema for form validation
   */
  const createValidationSchema = useCallback(() => {
    const schema: Record<string, z.ZodTypeAny> = {};

    const addFieldToSchema = (field: FieldConfig) => {
      if (field.type === "accordion-select") {
        field.options?.forEach((option) => {
          option.fields?.forEach((nestedField) => {
            addFieldToSchema(nestedField);
          });
        });
        return;
      } else if (field.type === "layout") {
        field.meta.layout.fields.forEach((nestedField) => {
          addFieldToSchema(nestedField);
        });
        return;
      }
      if (field.validation) {
        schema[field.id] = field.validation;
        return;
      }

      schema[field.id] = z.any().optional();
    };

    config.steps.forEach((step) => {
      step.fields.forEach((field) => {
        addFieldToSchema(field);
      });
    });

    return z.object(schema);
  }, [config.steps]);

  // Get or create initial modal state
  const modal = modals[config.key] || {
    currentStep: 0,
    formData: config.initialData || {},
    errors: {},
    loading: { isSubmitting: false, isLoadingStep: false },
    isDirty: false,
  };

  // Initialize form with react-hook-form
  const form = useForm({
    defaultValues: modal.formData,
    resolver: zodResolver(createValidationSchema()),
    mode: "onSubmit",
  });

  // Register modal on mount only
  useEffect(() => {
    registerModal(config.key, config);
    const subscription = form.watch((data) => {
      if (!isUpdatingRef.current) {
        isUpdatingRef.current = true;
        const hasChanges = hasNonEmptyValues(data, config.initialData || {});
        setFormData(config.key, data);
        setDirty(config.key, hasChanges);
        isUpdatingRef.current = false;
      }
    });
    return () => {
      subscription.unsubscribe();
      unregisterModal(config.key);
    };
  }, [config]);

  /**
   * Opens the modal and triggers onOpen event
   */
  const openModal = useCallback(() => {
    events?.onOpen?.();
  }, [events]);

  /**
   * Closes the modal and triggers onClose event
   */
  const closeModal = useCallback(() => {
    isUpdatingRef.current = true;
    // Reset form to initial state when closing
    form.reset(config.initialData || {});
    form.clearErrors();
    setFormData(config.key, config.initialData || {});
    setDirty(config.key, false);
    events?.onClose?.();
    isUpdatingRef.current = false;
  }, [config.key, config.initialData, form, setFormData, setDirty, events]);

  /**
   * Advances to the next step, running step completion handler if defined
   */
  const nextStep = useCallback(async () => {
    const currentStep = modal.currentStep;
    const currentStepConfig = config.steps[currentStep];

    if (currentStepConfig.onStepComplete) {
      await currentStepConfig.onStepComplete(form.getValues());
    }

    nextStepStore(config.key);
    events?.onStepChange?.(currentStep, currentStep + 1);
  }, [config.key, config.steps, modal.currentStep, nextStepStore, events, form]);

  /**
   * Returns to the previous step
   */
  const previousStep = useCallback(() => {
    const currentStep = modal.currentStep;
    previousStepStore(config.key);
    events?.onStepChange?.(currentStep, currentStep - 1);
  }, [config.key, modal.currentStep, previousStepStore, events]);

  /**
   * Resets the form to its initial state
   */
  const resetForm = useCallback(() => {
    isUpdatingRef.current = true;
    // Reset form to initial configuration state
    form.reset(config.initialData || {});
    form.clearErrors();
    setFormData(config.key, config.initialData || {});
    setDirty(config.key, false);
    events?.onReset?.();
    isUpdatingRef.current = false;
  }, [config.initialData, form, config.key, setFormData, setDirty, events]);

  return {
    form,
    currentStep: modal.currentStep,
    totalSteps: config.steps.length,
    loading: modal.loading,
    errors: modal.errors,
    isDirty: modal.isDirty,
    openModal,
    closeModal,
    nextStep,
    previousStep,
    resetForm,
    setLoading: (loading: any) => setLoading(config.key, loading),
  };
};
