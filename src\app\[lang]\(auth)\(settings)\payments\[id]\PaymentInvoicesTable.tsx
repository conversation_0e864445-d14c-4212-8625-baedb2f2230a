import { Combobox } from "@/components/base/combobox";
import { DataTable } from "@/components/table";
import { Badge } from "@/components/ui/badge";
import { Download } from "lucide-react";
import { useLocale } from "next-intl";
import { formatCurrency } from "@/lib/utils";
import type { BadgeProps } from "@/components/ui/badge";

interface PaymentInvoicesTableProps {
  invoices: any[];
  invoiceYears: string[];
  selectedYears: string[];
  setSelectedYears: (years: string[]) => void;
  isLoading: boolean;
  t: any;
  getStatusBadgeVariant: (status: string) => BadgeProps["variant"];
}

export default function PaymentInvoicesTable({
  invoices,
  invoiceYears,
  selectedYears,
  setSelectedYears,
  isLoading,
  t,
  getStatusBadgeVariant,
}: PaymentInvoicesTableProps) {
  const locale = useLocale();
  const columns = [
    {
      id: "invoiceId",
      header: t("invoices.table.number"),
      accessorKey: "id",
      cell: ({ row }: any) => <span>{row.original.id}</span>,
      enableSorting: true,
    },
    {
      id: "status",
      header: t("invoices.table.status"),
      accessorKey: "status",
      cell: ({ row }: any) => (
        <Badge
          variant={getStatusBadgeVariant(row.original.status)}
          className="rounded-md font-light uppercase"
        >
          {t(`invoices.status.${row.original.status}`) || row.original.status}
        </Badge>
      ),
      enableSorting: true,
    },
    {
      id: "amount",
      header: t("invoices.table.amount"),
      accessorKey: "amountPaid",
      cell: ({ row }: any) => formatCurrency(row.original.amountPaid),
      enableSorting: true,
    },
    {
      id: "total",
      header: t("invoices.table.total"),
      accessorKey: "total",
      cell: ({ row }: any) => formatCurrency(row.original.total),
      enableSorting: true,
    },
    {
      id: "date",
      header: t("invoices.table.date"),
      accessorKey: "date",
      cell: ({ row }: any) =>
        new Date(row.original.date).toLocaleDateString(locale, {
          year: "numeric",
          month: "long",
          day: "numeric",
        }),
      enableSorting: true,
    },
    {
      id: "download",
      header: t("invoices.table.download"),
      accessorKey: "downloadURL",
      cell: ({ row }: any) => (
        <a href={row.original.downloadLink} target="_blank" rel="noopener noreferrer">
          <Download className="h-5 w-5 cursor-pointer" />
        </a>
      ),
      enableSorting: false,
    },
  ];

  return (
    <div>
      <div className="mb-2 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="text-xl font-semibold">
          {t("invoices.title", { count: invoices.length })}
        </div>
        <div className="flex items-center gap-2">
          <Combobox
            multiple
            value={
              selectedYears && selectedYears.length > 0
                ? selectedYears
                : t("invoices.filters.all-years")
            }
            onChange={(val) => setSelectedYears(Array.isArray(val) ? val : [val])}
            options={invoiceYears.map((year) => ({ value: year, label: year }))}
            label={t("invoices.filters.year")}
            placeholder={t("invoices.filters.year-placeholder")}
            triggerClassName="w-40"
            contentClassName="max-h-60 overflow-auto"
          />
        </div>
      </div>
      <DataTable
        columns={columns}
        data={invoices}
        isLoading={isLoading}
        enablePagination={true}
        enableSorting={true}
        pageSize={20}
        totalRows={invoices.length}
        getRowId={(row: any) => row.id}
        onStateChange={() => {}}
        noResults={t("errors.no-results")}
      />
    </div>
  );
}
