"use client";

import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";
import { IMediaItem } from "@px-shared-account/hermes";
import { useMediaGallery, useDeleteMedia } from "@/services/whats-new/client";
import { <PERSON>, <PERSON><PERSON>, Trash, CheckCircle, Loader2 } from "lucide-react";
import { Button } from "@/components/base/button";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import Image from "next/image";

interface MediaGalleryProps {
  onSelect?: (media: IMediaItem) => void;
  onClose?: () => void;
  isSelectable?: boolean;
  isModal?: boolean;
  className?: string;
}

export function MediaGallery({
  onSelect,
  onClose,
  isSelectable = false,
  isModal = false,
  className,
}: MediaGalleryProps) {
  const t = useTranslations("whats-new.admin");
  const { toast } = useToast();

  // State for tracking copied items
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);
  const [deletedItem, setDeletedItem] = useState<string | null>(null);

  // Fetch media items
  const { data, error, isLoading, mutate } = useMediaGallery({
    revalidateOnFocus: true,
  });

  // Delete media functionality
  const { deleteMedia, isLoading: isDeleting } = useDeleteMedia();

  // Handle media deletion
  const handleDelete = async (media: IMediaItem) => {
    if (window.confirm(t("confirm-media-delete"))) {
      setDeletedItem(media.pathname);
      try {
        await deleteMedia(media.pathname);
        // Refresh the media list after successful deletion
        mutate();
      } catch (error) {
        console.error("Failed to delete media:", error);
      } finally {
        setDeletedItem(null);
      }
    }
  };

  // Copy URL to clipboard
  const copyToClipboard = useCallback(
    (url: string) => {
      navigator.clipboard.writeText(url).then(
        () => {
          setCopiedUrl(url);
          toast({
            title: t("url-copied"),
            description: t("url-copied-description"),
            variant: "default",
          });

          // Reset the "copied" state after 2 seconds
          setTimeout(() => {
            setCopiedUrl(null);
          }, 2000);
        },
        (err) => {
          console.error("Could not copy text: ", err);
          toast({
            title: t("error.copy-failed"),
            description: t("error.copy-failed-description"),
            variant: "destructive",
          });
        },
      );
    },
    [toast, t],
  );

  // Handle media selection
  const handleSelect = useCallback(
    (media: IMediaItem) => {
      if (isSelectable && onSelect) {
        onSelect(media);
      }
    },
    [isSelectable, onSelect],
  );

  return (
    <div className={cn("space-y-4", className)}>
      {isModal && (
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">{t("media-gallery")}</h2>
          <Button type="button" variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      {error && (
        <div className="rounded-lg border border-red-500 bg-red-900/20 p-3 text-sm text-red-500">
          {error.message || t("error.loading-media")}
        </div>
      )}

      {isLoading ? (
        <div className="flex min-h-[200px] items-center justify-center">
          <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
        </div>
      ) : data?.media && data.media.length > 0 ? (
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
          {data.media.map((item) => (
            <div
              key={item.pathname}
              className={cn(
                "group relative overflow-hidden rounded-lg border border-white/10 bg-black/20",
                isSelectable && "hover:border-primary/70 cursor-pointer",
              )}
            >
              <Image
                src={item.url}
                alt={item.pathname.split("/").pop() || "Media item"}
                width={100}
                height={100}
                className="aspect-video h-full w-full object-cover"
                onClick={() => isSelectable && handleSelect(item)}
              />

              {item.pathname === deletedItem ? (
                <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                  <Loader2 className="h-5 w-5 animate-spin text-white" />
                </div>
              ) : (
                <div className="absolute right-0 bottom-0 left-0 flex justify-between bg-black/60 p-2 opacity-0 transition-opacity group-hover:opacity-100">
                  <button
                    type="button"
                    onClick={() => copyToClipboard(item.url)}
                    className="rounded p-1 text-white hover:bg-white/10"
                    title={t("copy-url")}
                  >
                    {copiedUrl === item.url ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </button>

                  <button
                    type="button"
                    onClick={() => handleDelete(item)}
                    className="rounded p-1 text-white hover:bg-white/10"
                    title={t("delete-media")}
                    disabled={deletedItem === item.pathname}
                  >
                    {deletedItem === item.pathname ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash className="h-4 w-4 text-red-500" />
                    )}
                  </button>
                </div>
              )}

              {isSelectable && (
                <div
                  className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 transition-opacity group-hover:opacity-100"
                  onClick={() => handleSelect(item)}
                >
                  <span className="bg-primary rounded-full px-3 py-1 text-sm font-medium text-white">
                    {t("select")}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="flex min-h-[200px] items-center justify-center">
          <p className="text-muted-foreground text-center">{t("no-media")}</p>
        </div>
      )}
    </div>
  );
}
