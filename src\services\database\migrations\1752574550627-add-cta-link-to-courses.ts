import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCtaLinkToCourses1752574550627 implements MigrationInterface {
    name = 'AddCtaLinkToCourses1752574550627'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "courses" ADD "ctaLink" character varying(500) NOT NULL DEFAULT 'https://paradox.io'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "ctaLink"`);
    }

}
