import { MigrationInterface, QueryRunner } from 'typeorm';

export class CronsSlackNotification1742981955476 implements MigrationInterface {
  name = 'CronsSlackNotification1742981955476';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ADD "slackNotified" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "cronLogs" DROP COLUMN "slackNotified"`,
    );
  }
}
