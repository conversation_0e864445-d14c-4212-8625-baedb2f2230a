import { MigrationInterface, QueryRunner } from 'typeorm';

export class CourseCatalogue1750929296379 implements MigrationInterface {
  name = 'CourseCatalogue1750929296379';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "courses" ADD "lmsId" character varying(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "courses" ADD "productFamilyId" integer`,
    );
    await queryRunner.query(
      `ALTER TABLE "courses" ADD CONSTRAINT "FK_course_productFamily" FOREIGN KEY ("productFamilyId") REFERENCES "productFamilies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "courses" DROP CONSTRAINT "FK_course_productFamily"`,
    );
    await queryRunner.query(
      `ALTER TABLE "courses" DROP COLUMN "productFamilyId"`,
    );
    await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "lmsId"`);
  }
}
