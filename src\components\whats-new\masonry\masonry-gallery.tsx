"use client";

import { useState } from "react";
import Image from "next/image";
import "./masonry.css";
import React from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { MasonryContext, useMasonry } from "./use-masonry";

interface MasonryGalleryProps {
  children: React.ReactNode;
  className?: string;
}

export function MasonryGallery({ children, className = "" }: MasonryGalleryProps) {
  const masonryRef = useMasonry();
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxImage, setLightboxImage] = useState<{
    src: string;
    alt: string;
  } | null>(null);

  // Function to open lightbox that can be passed down to MasonryItems
  const openLightbox = (src: string, alt: string) => {
    setLightboxImage({ src, alt });
    setLightboxOpen(true);
  };

  // Provide the context value
  const contextValue = {
    openLightbox,
  };

  return (
    <MasonryContext.Provider value={contextValue}>
      <div ref={masonryRef} className={`masonry-gallery ${className}`}>
        {children}
      </div>

      <Dialog open={lightboxOpen} onOpenChange={setLightboxOpen}>
        <DialogContent className="max-h-[90vh] w-auto max-w-[90vw] border-0 bg-transparent p-0">
          {lightboxImage && (
            <div className="relative flex items-center justify-center">
              <Image
                src={lightboxImage.src}
                alt={lightboxImage.alt}
                className="max-h-[85vh] max-w-full rounded-md object-contain"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </MasonryContext.Provider>
  );
}
