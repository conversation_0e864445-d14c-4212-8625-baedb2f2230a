import {
  Column,
  CreateDate<PERSON>olumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SessionType } from '@px-shared-account/hermes';
import { TriadEntity } from '@entities';
import { UserTable } from './user.table';
import { TriadParticipantTable } from './triad-participant.table';

@Entity('triads')
export class TriadTable implements TriadEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar' })
  title: string;

  @ManyToOne(() => UserTable, (user) => user.triadsManaged, {
    onDelete: 'SET NULL',
  })
  organizer: UserTable;

  @OneToMany(() => TriadParticipantTable, (participant) => participant.triad)
  participants: TriadParticipantTable[];

  @Column({ type: 'timestamptz' })
  sessionTime: Date;

  @Column({ type: 'double precision' })
  duration: number;

  @Column({ type: 'varchar' })
  meetingLink: string;

  @Column({ type: 'int' })
  maxParticipants: number;

  @Column({ type: 'text', array: true, default: [] })
  sessions: string[];

  @Column({
    type: 'enum',
    enum: SessionType,
    default: SessionType.PXS,
  })
  sessionType: SessionType;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt: Date;
}
