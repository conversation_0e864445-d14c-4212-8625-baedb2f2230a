import { ProductOfferAccess } from '@entities';
import { CommunityInfo, CourseInfo } from '@types';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity({ name: 'productOfferAccesses' })
export class ProductOfferAccessTable implements ProductOfferAccess {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'uuid' })
  offerId: string;

  @Column({ type: 'jsonb' })
  lmsIds: CourseInfo[];

  @Column({ type: 'jsonb' })
  communityIds: CommunityInfo[];

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp', nullable: true })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;
}
