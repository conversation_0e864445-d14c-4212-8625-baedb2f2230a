"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { useTranslations } from "next-intl";
import { useTableActions } from "./actions";
import { StatusBadge } from "@/components/base/courses/status-badge";
import { IBaseCourse } from "@px-shared-account/hermes";

export const useColumns = () => {
  const t = useTranslations();
  const { handleDelete, handlePublishStatusChange } = useTableActions();

  const columns: ColumnDef<IBaseCourse>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={t("courses.table.select_all")}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={t("courses.table.select_row", { id: row.original.id })}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    { accessorKey: "name", header: t("courses.table.name") },
    {
      accessorKey: "status",
      header: t("courses.table.status"),
      cell: ({ row }) => (
        <StatusBadge
          status={t(`courses.status.${row.original.status?.toLowerCase() || "draft"}`)}
        />
      ),
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const course = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">
                  {t("courses.table.open_menu", { name: course.name })}
                </span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>{t("courses.table.actions.edit")}</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handlePublishStatusChange(course)}>
                {course.status === "PUBLISHED"
                  ? t("courses.table.actions.unpublish")
                  : t("courses.table.actions.publish")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDelete(course)} className="text-destructive">
                {t("courses.table.actions.delete")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
};
