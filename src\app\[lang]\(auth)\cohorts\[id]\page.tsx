import { Suspense } from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { getTranslations } from "next-intl/server";
import { getCohortDetails } from "@/services/cohort/server";
import { ICohortWithStudents } from "@/types/cohort";

import { CohortInformation } from "./components/details/information";
import { CohortStudents } from "./components/details/students";
import { CohortOffers } from "./components/details/offers";
import { CohortTabs } from "./components/details/CohortTabs";
import { CohortHeader } from "./components/details/CohortHeader";

type CohortDetailsProps = {
  params: Promise<{
    id: number;
  }>;
  searchParams: Promise<{
    tab?: string;
  }>;
};

export default async function CohortDetailsPage({ params, searchParams }: CohortDetailsProps) {
  const { id } = await params;
  const { tab = "information" } = await searchParams;
  const cohortData = await getCohortDetails(id);

  // Cast the response to include students
  const cohort = cohortData as unknown as ICohortWithStudents;

  // Get available students (all students for now)
  const availableStudents = cohort?.students || [];

  let TabContent;
  switch (tab) {
    case "information":
      TabContent = (
        <Suspense fallback={<Skeleton className="h-[200px] w-full" />}>
          <CohortInformation id={id} />
        </Suspense>
      );
      break;
    case "students":
      TabContent = (
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <CohortStudents id={id} />
        </Suspense>
      );
      break;
    case "offers":
      TabContent = (
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <CohortOffers id={id} />
        </Suspense>
      );
      break;
    default:
      TabContent = null;
  }

  if (!cohort) {
    return (
      <div className="flex h-[200px] items-center justify-center">
        <p className="text-muted-foreground">Cohort not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <CohortHeader cohort={cohort} />
      <div className="space-y-8">
        <CohortTabs activeTab={tab} />
        {TabContent}
      </div>
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "cohorts" });

  return {
    title: t("details.title"),
  };
}
