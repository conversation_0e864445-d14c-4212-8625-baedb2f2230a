import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  UpdateProductPlanPriceDTO,
  CreatePlanWithPriceDTO,
  UpdatePlanWithPriceDTO,
} from '@dtos';
import {
  ProductPlanPriceUseCases,
  ProductPlanUseCases,
  ProductUseCases,
} from '@useCases';
import { HubspotService } from '@services/crm';
import { JwtPermissionsGuard } from '@auth';

@ApiTags('product-plans')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('product-plans')
export class ProductPlansController {
  constructor(
    private readonly productUseCases: ProductUseCases,
    private readonly productPlanUseCases: ProductPlanUseCases,
    private readonly productPlanPriceUseCases: ProductPlanPriceUseCases,
    private readonly hubspotService: HubspotService,
  ) {}

  @ApiOperation({ summary: 'Create a product plan and attached price' })
  @Post('/')
  async createProductPlanAndPrice(
    @Body() planAndPriceInfo: CreatePlanWithPriceDTO,
  ) {
    const product = await this.productUseCases.getOneBy({
      id: planAndPriceInfo.planInfo.productId,
    });

    try {
      const [createdPlan, createdPrice] =
        await this.productPlanUseCases.createWithPrice(
          planAndPriceInfo,
          product,
        );
      return {
        plan: createdPlan,
        price: createdPrice,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create plan.',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @ApiOperation({ summary: 'Update a product plan and attached price' })
  @Patch('/')
  async updateProductPlan(@Body() updates: UpdatePlanWithPriceDTO) {
    const {
      planInfo: { id: planId, ...planUpdates },
      priceInfo: { id: priceId, ...priceUpdates },
    } = updates;

    const result = {};
    if (planId) {
      result['planUpdateResult'] = await this.productPlanUseCases.updateById(
        planId,
        planUpdates,
      );
    }
    if (priceId) {
      result['priceUpdateResult'] =
        await this.productPlanPriceUseCases.updateById(priceId, priceUpdates);
    }
    const product = await this.productUseCases.getOneBy({
      id: result['planUpdateResult']?.updatedItem?.productId,
    });
    if (
      result['planUpdateResult']?.updatedItem &&
      result['planUpdateResult']?.updatedItem.crmId
    ) {
      await this.hubspotService.updateProduct(
        result['planUpdateResult']?.updatedItem,
        result['priceUpdateResult']?.updatedItem,
        product,
      );
    }
    return result;
  }

  @ApiOperation({ summary: 'Get all product plans' })
  @Get('/product/:productId')
  async getProductPlans(
    @Param('productId') productId: string,
    @Query('limit') limit = 500,
    @Query('page') page = 1,
    @Query('order') orderBy?: 'DESC' | 'ASC',
  ) {
    return this.productPlanUseCases.getAll(
      Number(productId),
      limit,
      page,
      orderBy,
    );
  }

  @ApiOperation({ summary: 'Lists all product plan prices' })
  @Get('/prices')
  async listProductPlanPrices(
    @Query('limit') limit = 500,
    @Query('page') page = 1,
    @Query('order') orderBy?: 'DESC' | 'ASC',
  ) {
    return this.productPlanPriceUseCases.getAll(limit, page, orderBy);
  }

  @ApiOperation({ summary: 'Get unique billing cycles' })
  @Get('/prices/uniqueBillingCycles')
  async getUniqueBillingCycles() {
    return this.productPlanPriceUseCases.getUniqueBillingCycles();
  }

  @ApiOperation({ summary: 'Get product plan price for the specified `id`' })
  @Get('/prices/:id')
  async getProductPlanPrice(@Param('id') id: string) {
    return this.productPlanPriceUseCases.getOne(Number(id));
  }

  @ApiOperation({
    summary: 'Update a product plan price for the specified `id`',
  })
  @Patch('/prices/:id')
  async updateProductPlanPrice(
    @Param('id') id: string,
    @Body() updates: UpdateProductPlanPriceDTO,
  ) {
    return this.productPlanPriceUseCases.updateById(Number(id), updates);
  }

  @ApiOperation({ summary: 'Get product plan for the specified `id`' })
  @Get('/:id')
  async getProductPlan(@Param('id') id: string) {
    return this.productPlanUseCases.getOne(Number(id));
  }
}
