"use client";

import { IRoleBase, IUserBase } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";
import { useCallback } from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Dialog } from "@/components/ui/dialog";
import { useConfirmationModal } from "@/hooks/store/confirmationModal";
import { Spinner } from "@/components/ui/spinner";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useUpdateUser } from "@/services/user";

type SelectRoleProps = {
  isLoading: boolean;
  forceUpdate?: boolean;
  roles: IRoleBase[];
  showLabel?: boolean;
  userData?: IUserBase;
  onChange?: (value: string) => void;
};

export default function SelectRole({
  isLoading,
  forceUpdate = true,
  roles,
  showLabel = true,
  userData,
  onChange,
}: SelectRoleProps) {
  const t = useTranslations("gestion.users");
  const confirmModal = useConfirmationModal();
  const { toast } = useToast();
  const { update, isLoading: isUpdating } = useUpdateUser();

  const handleRoleChange = useCallback(
    async (value: string) => {
      if (!userData) return;
      try {
        await update(userData.id, { role: value });
        toast({
          title: t("updated"),
          description: t("updated-description"),
        });
      } catch (error) {
        toast({
          title: t("error"),
          description: t("error-description"),
          variant: "destructive",
        });
      }
    },
    [userData, update, toast, t],
  );

  const handleValueChange = useCallback(
    (value: string) => {
      if (forceUpdate) {
        confirmModal.openModal(
          {
            title: t("confirmation-title"),
            description: t("confirmation-description"),
            type: "warning",
            confirmText: t("update"),
            cancelText: t("cancel"),
          },
          () => handleRoleChange(value),
        );
      } else {
        onChange?.(value);
      }
    },
    [confirmModal, t, handleRoleChange, forceUpdate],
  );

  return (
    <Dialog>
      {isLoading || isUpdating ? (
        <Spinner />
      ) : (
        <div className="relative w-full">
          <Select defaultValue={userData?.role?.name ?? ""} onValueChange={handleValueChange}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder={t("role")} />
            </SelectTrigger>
            <SelectContent>
              {roles?.map((role) => (
                <SelectItem key={role.id} value={role.name}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {showLabel && (
            <Label className="absolute -top-3 left-2 bg-[#0a0a0a] px-1 text-xs font-normal text-[#8c8c8c] transition-all peer-placeholder-shown:left-3 peer-placeholder-shown:top-2 peer-placeholder-shown:text-base peer-focus:-top-3 peer-focus:left-2 peer-focus:text-xs">
              {t("role")}
            </Label>
          )}
        </div>
      )}
    </Dialog>
  );
}
