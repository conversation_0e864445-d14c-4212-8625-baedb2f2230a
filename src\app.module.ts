import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { ConfigModule } from '@config';
import { LoggerModule } from 'nestjs-pino';
import { DataServicesModule } from '@services/database';
import { ControllersModule } from '@controllers';
import { CronsModule } from '@services/crons';
import { AuthModule } from '@auth';
import { ClerkModule } from '@services/sso';
import { json, urlencoded } from 'express';
import { APP_INTERCEPTOR } from '@nestjs/core';
import {
  DatabaseExceptionInterceptor,
  SentryModule,
  ZodValidationInterceptor,
} from '@services/monitoring';
import { QueueModule } from '@services/queue';

@Module({
  imports: [
    ConfigModule.forRootAsync(),
    LoggerModule.forRoot({
      pinoHttp: {
        level: process.env.NODE_ENV !== 'production' ? 'debug' : 'info',
        transport:
          process.env.NODE_ENV !== 'production'
            ? {
                target: 'pino-pretty',
                options: {
                  singleLine: true,
                },
              }
            : undefined,
      },
      exclude: [{ method: RequestMethod.ALL, path: 'check' }],
    }),
    ControllersModule,
    AuthModule,
    DataServicesModule,
    ScheduleModule.forRoot(),
    CronsModule,
    ClerkModule,
    SentryModule,
    QueueModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodValidationInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: DatabaseExceptionInterceptor,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(
        json({ limit: '150mb' }),
        urlencoded({ extended: true, limit: '150mb' }),
      )
      .exclude({ path: 'webhooks/clerk', method: RequestMethod.POST })
      .forRoutes('*');
  }
}
