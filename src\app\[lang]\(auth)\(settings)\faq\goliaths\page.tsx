import FAQSelector from "@/components/(settings)/faq/FAQSelector";
import IframePlayer from "@/components/(settings)/faq/IframePlayer";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

export default function Page() {
  return (
    <>
      <div className="px-4">
        <BreadcrumbSettings active="/faq/goliaths" />
      </div>
      <div className="px-4">
        <FAQSelector isActive="goliaths" />
        <div className="h-12" />
        <IframePlayer
          src="https://paradoxlearning.frontkb.com/en/categories/929730-goliaths"
          title="Goliaths FAQ"
        />
      </div>
    </>
  );
}

export async function generateMetadata() {
  return {
    title: "Goliaths FAQ",
  };
}
