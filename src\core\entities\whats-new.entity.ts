import { BaseEntity } from './base.entity';
import { IThumbnailImage } from '@px-shared-account/hermes';

export class WhatsNewVersionEntity extends BaseEntity {
    versionCode: string;
    date: Date;
    titleEn: string;
    titleFr: string;
    descriptionEn: string;
    descriptionFr: string;
    addedEn: string[];
    addedFr: string[];
    changedEn: string[];
    changedFr: string[];
    fixedEn: string[];
    fixedFr: string[];
    removedEn: string[];
    removedFr: string[];
    thumbnails: IThumbnailImage[];
}

export class WhatsNewReactionEntity extends BaseEntity {
    emoji: string;
    userId: string;
    versionId: number;
} 