"use client";

import { useTranslations } from "next-intl";
import { useEffect, useMemo, useState } from "react";
import { z } from "zod";

import { useToast } from "@/hooks/use-toast";
import { ModalConfig, MultiStepFormModal } from "../base/multi-step-form-modal";
import { getCommunityInfo } from "@/app/actions/circle-so-actions";
import { TreeViewItem } from "../tree-view";
import { useCreateCohort } from "@/services/cohort";

const TreeSelectCommunityInfoSchema = z.object({
  PXS: z.array(
    z.object({
      id: z.number(),
      slug: z.string(),
      name: z.string(),
    }),
  ),
  PXL: z.array(
    z.object({
      id: z.number(),
      slug: z.string(),
      name: z.string(),
    }),
  ),
});

// Get community IDs from environment variables
const PXS_COMMUNITY_ID = Number(process.env.NEXT_PUBLIC_PXS_COMMUNITY_ID);
const PXL_COMMUNITY_ID = Number(process.env.NEXT_PUBLIC_PXL_COMMUNITY_ID);

type CreateCohortModalProps = {
  courseId: number;
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  onComplete: () => void;
};

export default function CreateCohortModal({
  isModalOpen,
  setIsModalOpen,
  onComplete,
  courseId,
}: CreateCohortModalProps) {
  const t = useTranslations("cohorts");
  const [communities, setCommunities] = useState<TreeViewItem[]>([]);
  const { toast } = useToast();
  const { create: createCohort, isLoading } = useCreateCohort();

  const config: ModalConfig = useMemo(
    () => ({
      key: "create-cohort",
      title: t("create"),
      confirmClose: true,
      confirmReset: true,
      initialData: {
        name: "Cohort Name",
        description: "Description",
        maxParticipants: 0,
        startDate: new Date(),
        endDate: new Date(),
        courseId: "",
        status: "active",
        communityInfo: {
          PXS: [],
          PXL: [],
        },
      },
      steps: [
        {
          id: "name",
          title: t("name"),
          fields: [
            {
              id: "name",
              type: "text",
              label: t("name"),
              placeholder: t("name_description"),
              required: true,
              validation: z.string().min(3, { message: t("name_validation") }),
            },
            {
              id: "description",
              type: "text",
              label: t("description"),
              required: true,
              placeholder: t("description_description"),
              validation: z.string().min(3, { message: t("description_validation") }),
            },
            {
              id: "maxParticipants",
              type: "number",
              label: t("maxParticipants"),
              placeholder: t("maxParticipants_description"),
              required: true,
              validation: z.number().min(1, { message: t("maxParticipants_validation") }),
            },
            {
              id: "startDate",
              type: "date",
              label: t("startDate"),
              required: true,
              placeholder: t("startDate_description"),
            },
            {
              id: "endDate",
              type: "date",
              label: t("endDate"),
              required: true,
              placeholder: t("endDate_description"),
            },
            {
              id: "communityInfo",
              type: "tree-select",
              label: t("communityInfo"),
              description: t("communityInfo_description"),
              data: communities.map((item) => ({
                ...item,
                checked: false, // Initialize all items unchecked
              })),
              required: true,
              validation: TreeSelectCommunityInfoSchema,
              onChange: ({
                item,
                checked,
                value,
              }: {
                item: any;
                checked: boolean;
                value?: any;
              }) => {
                // Use passed form value if available, otherwise use default empty arrays
                const currentValue = value || { PXS: [], PXL: [] };

                // Only process file items
                if (item.type === "file") {
                  // Create space info object
                  const spaceInfo = {
                    id: Number(item.id),
                    name: item.name,
                    slug: item.name.toLowerCase().replace(/\s+/g, "-"),
                  };

                  if (checked) {
                    // Add to the appropriate array
                    if (item.communityId === PXS_COMMUNITY_ID) {
                      // PXS
                      return {
                        ...currentValue,
                        PXS: [
                          ...currentValue.PXS.filter((s: any) => s.id !== spaceInfo.id),
                          spaceInfo,
                        ],
                      };
                    } else if (item.communityId === PXL_COMMUNITY_ID) {
                      // PXL
                      return {
                        ...currentValue,
                        PXL: [
                          ...currentValue.PXL.filter((s: any) => s.id !== spaceInfo.id),
                          spaceInfo,
                        ],
                      };
                    }
                  } else {
                    // Remove from both arrays
                    return {
                      ...currentValue,
                      PXS: currentValue.PXS.filter((space: any) => space.id !== Number(item.id)),
                      PXL: currentValue.PXL.filter((space: any) => space.id !== Number(item.id)),
                    };
                  }
                } else if (item.type === "folder" && checked) {
                  // If a folder is checked, recursively process all its children
                  const newValue = { ...currentValue };

                  const processChildren = (children: any[]) => {
                    children.forEach((child) => {
                      if (child.type === "file") {
                        const spaceInfo = {
                          id: Number(child.id),
                          name: child.name,
                          slug: child.name.toLowerCase().replace(/\s+/g, "-"),
                        };

                        if (child.communityId === PXS_COMMUNITY_ID) {
                          // PXS
                          newValue.PXS = [
                            ...newValue.PXS.filter((s: any) => s.id !== spaceInfo.id),
                            spaceInfo,
                          ];
                        } else if (child.communityId === PXL_COMMUNITY_ID) {
                          // PXL
                          newValue.PXL = [
                            ...newValue.PXL.filter((s: any) => s.id !== spaceInfo.id),
                            spaceInfo,
                          ];
                        }
                      }
                      // Always process children recursively, regardless of type
                      if (child.children) {
                        processChildren(child.children);
                      }
                    });
                  };

                  if (item.children) {
                    processChildren(item.children);
                  }

                  return newValue;
                } else if (item.type === "folder" && !checked) {
                  // If a folder is unchecked, remove all its children
                  const newValue = { ...currentValue };

                  const processChildren = (children: any[]) => {
                    children.forEach((child) => {
                      if (child.type === "file") {
                        newValue.PXS = newValue.PXS.filter(
                          (space: any) => space.id !== Number(child.id),
                        );
                        newValue.PXL = newValue.PXL.filter(
                          (space: any) => space.id !== Number(child.id),
                        );
                      }
                      // Always process children recursively, regardless of type
                      if (child.children) {
                        processChildren(child.children);
                      }
                    });
                  };

                  if (item.children) {
                    processChildren(item.children);
                  }

                  return newValue;
                }

                return currentValue;
              },
            },
          ],
        },
      ],
    }),
    [t, communities],
  );

  useEffect(() => {
    (async () => {
      const comms = await getCommunityInfo();
      // TODO https://github.com/neigebaie/shadcn-ui-tree-view
      const spaces = comms.spaces.map((space: any) => ({
        id: space.id,
        name: space.name,
        type: "file",
        spaceGroupId: space.space_group_id,
        communityId: space.community_id,
      }));
      const spacesGroups = comms.spaceGroups.map((spaceGroup: any) => ({
        id: spaceGroup.id,
        name: spaceGroup.name,
        children: spaces.filter((space) => space.spaceGroupId === spaceGroup.id),
        type: "folder",
        communityId: spaceGroup.community_id,
      }));
      const treeViewItems = comms.communities.map((comm: any) => ({
        id: comm.id,
        name: comm.name,
        children: spacesGroups.filter((spaceGroup) => spaceGroup.communityId === comm.id),
        type: "folder",
        checked: false,
      }));
      setCommunities(treeViewItems);
    })();
  }, []);

  return (
    <MultiStepFormModal
      config={config}
      isOpen={isModalOpen}
      onOpenChange={setIsModalOpen}
      events={{
        onComplete: async (formData: any) => {
          try {
            const response = await createCohort({
              name: formData.name,
              description: formData.description,
              maxParticipants: formData.maxParticipants,
              startDate: formData.startDate,
              endDate: formData.endDate,
              courseId: courseId,
              communityInfo: formData.communityInfo,
            });

            // Success is handled by the hook automatically via toast
            onComplete();
            setIsModalOpen(false);
          } catch (error) {
            // Error is handled by the hook automatically via toast
            console.error("Failed to create cohort:", error);
          }
        },
      }}
    />
  );
}
