import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  Res,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ExportSubscriptionsDTO,
  GetSubscriptionsStatusDTO,
  ListSubscriptionsDTO,
  UpdateSubscriptionDTO,
} from '@dtos';
import { SubscriptionDownsellParams } from '@types';
import { SubscriptionUseCases } from '@useCases';
import { HubspotService } from '@services/crm';
import { JwtPermissionsGuard } from '@auth';
import { CurrentUser } from 'src/auth/decorators';
import { UserEntity } from '@entities';
import { CurrentUserInterceptor } from 'src/auth/interceptors';

@ApiTags('subscriptions')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('subscriptions')
export class SubscriptionsController {
  constructor(
    private readonly hubspotService: HubspotService,
    private readonly subscriptionUseCases: SubscriptionUseCases,
  ) {}

  @ApiOperation({
    summary: 'Retrieve subscription details by Chargebee ID',
  })
  @Get('/order-details/:chargebeeId')
  async getDetails(@Param('chargebeeId') chargebeeId: string) {
    return this.subscriptionUseCases.getOrderDetailsByChargebeeId(chargebeeId);
  }

  @ApiOperation({
    summary: 'Retrieve all subscriptions for a user',
  })
  @UseInterceptors(CurrentUserInterceptor)
  @Get('/user')
  async getAllForUser(@CurrentUser() user: UserEntity) {
    return this.subscriptionUseCases.getAllForUser(user.email);
  }

  @ApiOperation({
    summary: 'Retrieve a subscription with invoices',
  })
  @UseInterceptors(CurrentUserInterceptor)
  @Get('/:id/with-invoices')
  async getAllForUserWithInvoices(
    @Param('id') id: string,
    @CurrentUser() user: UserEntity,
  ) {
    return this.subscriptionUseCases.getDetailsWithInvoices(id, user.email);
  }

  @ApiOperation({
    summary: 'Retrieve subscriptions details (orderStatus)',
  })
  @Post('/orders-details')
  async getListSubscriptionsDetails(@Body() body: GetSubscriptionsStatusDTO) {
    return this.subscriptionUseCases.getOrdersDetails(body);
  }

  @ApiOperation({
    summary: 'Export the list of subscriptions',
  })
  @Post('/export')
  async export(
    @Res() response: Response,
    @Body() range: ExportSubscriptionsDTO,
  ) {
    return this.subscriptionUseCases.export(response, range);
  }

  @ApiOperation({
    summary: 'Import subscriptions and related data',
  })
  @Post('/import-json')
  async import(@Body() body: { jsonData: string }) {
    return this.subscriptionUseCases.importSubscriptionData(body);
  }

  @ApiOperation({
    summary: 'List subscriptions',
  })
  @Get('/')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  )
  async list(@Query() query: ListSubscriptionsDTO) {
    return this.subscriptionUseCases.searchAll(
      query.query,
      query.product,
      query.offer,
      query.orderStatus,
      query.isForever,
      query.limit,
      query.page,
      query.orderBy,
    );
  }

  @ApiOperation({
    summary: 'Get a subscription',
  })
  @Get('/:chargebeeId')
  async getOne(@Param('chargebeeId') chargebeeId: string) {
    return this.subscriptionUseCases.getOne(chargebeeId);
  }

  @ApiOperation({
    summary: 'Get customer details from deal',
  })
  @Get('/dealCustomer/:dealId')
  async getDealCustomer(@Param('dealId') dealId: string) {
    const foundContact = await this.hubspotService.getContactFromDeal(dealId);
    if (!foundContact) {
      throw new NotFoundException(
        `No contact was found on the deal: ${dealId}`,
      );
    }
    return foundContact;
  }

  @ApiOperation({
    summary: 'Mark a subscription as refunded and cancel in Chargebee',
  })
  @Patch('/:chargebeeId')
  async markRefundedAndCancel(@Param('chargebeeId') id: string) {
    return this.subscriptionUseCases.markRefundedAndCancel(id);
  }

  @ApiOperation({
    summary: 'Resyncs subscription data with Chargebee',
  })
  @Post('/resync/:chargebeeId')
  async resync(@Param('chargebeeId') chargebeeId: string) {
    return this.subscriptionUseCases.resyncWithChargebee(chargebeeId);
  }

  @ApiOperation({
    summary: 'Update billing cycle and amount info for subscription',
  })
  @Post('/:chargebeeId')
  async updateBillingCycles(
    @Param('chargebeeId') id: string,
    @Body() updates: UpdateSubscriptionDTO,
  ) {
    return this.subscriptionUseCases.updateBillingCyclesAndAmount(id, updates);
  }

  @ApiOperation({
    summary: 'Get the product list of a subscription',
  })
  @Get('/getAttachedProducts/:chargebeeId')
  async getAttachedProducts(@Param('chargebeeId') id: string) {
    return this.subscriptionUseCases.getAttachedProducts(id);
  }

  @ApiOperation({
    summary: 'Get the offer related information for a subscription',
  })
  @Get('/getOfferDetails/:chargebeeId')
  async getOfferDetails(@Param('chargebeeId') id: string) {
    return this.subscriptionUseCases.getOfferDetails(id);
  }

  @ApiOperation({
    summary: 'Downsell a subscription',
  })
  @Post('/downsell/:chargebeeId')
  async downsell(
    @Param('chargebeeId') id: string,
    @Body() updates: SubscriptionDownsellParams,
  ) {
    return await this.subscriptionUseCases.downsell(id, updates);
  }

  @ApiOperation({
    summary: 'Retrieve subscription accounting data by Chargebee ID',
  })
  @Get('/historical-data/:chargebeeId')
  async getHistoricalData(@Param('chargebeeId') chargebeeId: string) {
    return this.subscriptionUseCases.getHistoricalData(chargebeeId);
  }

  @ApiOperation({
    summary: 'Retrieve subscription line items by Chargebee ID',
  })
  @Get('/line-items/:chargebeeId')
  async getLineItems(@Param('chargebeeId') chargebeeId: string) {
    return this.subscriptionUseCases.getSubscriptionLineItems(chargebeeId);
  }

  @ApiOperation({
    summary: 'Get webhook events by chargebeeId',
  })
  @Get('/events/:chargebeeId')
  async getWebhookEvents(@Param('chargebeeId') chargebeeId: string) {
    return this.subscriptionUseCases.getWebhookEvents(chargebeeId);
  }
}
