"use client";

import { useUser } from "@clerk/nextjs";
import { useImpersonationStore } from "./store/impersonation";

/**
 * Hook to get the effective email based on impersonation status
 *
 * @returns The email to use for API calls - either the impersonated email if active,
 * or the user's actual email otherwise
 */
export function useImpersonatedEmail(): string {
  const { user } = useUser();
  const { isImpersonating, impersonatedEmail } = useImpersonationStore();

  // Get the actual user email
  const actualEmail = user?.primaryEmailAddress?.emailAddress || "";

  // If impersonating and we have an impersonated email, use that
  if (isImpersonating && impersonatedEmail) {
    return impersonatedEmail;
  }

  // Otherwise, return the actual user's email
  return actualEmail;
}
