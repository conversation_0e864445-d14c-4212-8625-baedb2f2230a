"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslations } from "next-intl";
import { motion } from "framer-motion";
import { Smile } from "lucide-react";
import { useUser } from "@clerk/nextjs";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { StarRating } from "@/components/ui/star-rating";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";

const MAX_CHARS = 3000;

export function TestimonialForm({ afterSubmit }: { afterSubmit?: () => void }) {
  const t = useTranslations("triad");
  const { user } = useUser();
  const [rating, setRating] = useState(1);
  const [content, setContent] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showFab, setShowFab] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Handle scroll behavior
  const controlFab = useCallback(() => {
    if (typeof window !== "undefined") {
      const currentScroll = window.scrollY;
      if (currentScroll > lastScrollY) {
        setShowFab(false);
      } else {
        setShowFab(true);
      }
      setLastScrollY(currentScroll);
    }
  }, [lastScrollY]);

  // Add scroll listener
  useEffect(() => {
    if (typeof window !== "undefined") {
      setLastScrollY(window.scrollY);
      window.addEventListener("scroll", controlFab);
      return () => window.removeEventListener("scroll", controlFab);
    }
  }, [controlFab]);

  const handleSubmit = async () => {
    if (!user?.emailAddresses?.[0]?.emailAddress) return;

    try {
      setIsSubmitting(true);
      const response = await fetch("/api/testimonials", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: user.id,
          email: user.emailAddresses[0].emailAddress,
          tool: "TRIADS",
          rating,
          content: content.trim() || null,
        }),
      });

      if (!response.ok) throw new Error("Failed to submit testimonial");

      toast.success(t("testimonial.success"));
      setIsOpen(false);
      if (afterSubmit) afterSubmit();
    } catch (error) {
      toast.error(t("testimonial.error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{
          opacity: showFab ? 1 : 0,
          y: showFab ? 0 : 20,
        }}
        transition={{ duration: 0.2 }}
        className="fixed right-6 bottom-12 z-50"
      >
        <Button
          size="icon"
          className="bg-primary hover:bg-primary/90 h-16 w-16 rounded-full"
          onClick={() => setIsOpen(true)}
        >
          <Smile className="h-6 w-6" />
        </Button>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: "100%" }}
        animate={{
          opacity: isOpen ? 1 : 0,
          y: isOpen ? 0 : "100%",
        }}
        transition={{ duration: 0.3 }}
        className={cn(
          "bg-background fixed right-0 bottom-0 z-50 w-full max-w-md rounded-t-2xl p-6 shadow-xl",
          !isOpen && "pointer-events-none",
        )}
      >
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white">{t("testimonial.title")}</h3>

          <div className="space-y-2">
            <label className="text-sm text-gray-400">{t("testimonial.rating")}</label>
            <StarRating value={rating} onChange={setRating} disabled={isSubmitting} />
          </div>

          <div className="space-y-2">
            <label className="text-sm text-gray-400">{t("testimonial.content")}</label>
            <Textarea
              value={content}
              onChange={(e) => {
                if (e.target.value.length <= MAX_CHARS) {
                  setContent(e.target.value);
                }
              }}
              disabled={isSubmitting}
              placeholder={t("testimonial.content_placeholder")}
              className="h-32 resize-none border-none bg-[#333333] text-white"
            />
            <div className="text-right text-xs text-gray-400">
              {content.length}/{MAX_CHARS}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="ghost"
              onClick={() => setIsOpen(false)}
              disabled={isSubmitting}
              className="text-white hover:text-gray-300"
            >
              {t("testimonial.cancel")}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-primary hover:bg-primary/90 text-white"
            >
              {isSubmitting ? t("testimonial.submitting") : t("testimonial.submit")}
            </Button>
          </div>
        </div>
      </motion.div>
    </>
  );
}
