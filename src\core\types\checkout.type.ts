import { ProductOfferStatus } from '@px-shared-account/hermes';

interface Warranty {
  icon: string;
  message: string;
}

interface Template {
  id: string;
  bannerImg?: string;
  primaryColor: string;
  warranty: Warranty[];
  testimonial?: Testimonial;
}

export interface CheckoutProduct {
  id: string;
  img: string;
  name: string;
  allowQuantity: boolean;
  isEvent: boolean;
  eventCountry: string;
}

export interface ForeverCheckoutProduct {
  id: string;
  img: string;
  name: string;
  periodicity: 'monthly' | 'yearly';
  trialDuration: number;
}

export interface CheckoutItems {
  products: CheckoutProduct[];
  recommended?: CheckoutProduct[] | null;
}

/**
 * Represents a product map in the Checkout config where keys are number of `billingCycle`s
 * and value is `CheckoutItems` containing `products` and `recommended` products chosen for the
 * specified `billingCycle`
 * @type {Record<string, CycleWithPlan[]>}
 */
export type ItemsWithBillingCycle = Record<string, CheckoutItems>;

export interface CheckoutConfig {
  name: string;
  template: Template;
  currency: string;
  usp?: string;
  cancellationPolicy?: string;
  gateway_account_id: string;
  planId: string;
  items?: ItemsWithBillingCycle;
  selectedBillingCycle?: string;
  display: 'products' | 'offer';
  returnUrl: string;
  redirectIfDisabled?: string;
  status: ProductOfferStatus;
  offerImg: string;
  subscriptionPlan?: string;
  isForever?: boolean;
  foreverItems?: ForeverCheckoutProduct[];
}

export type Testimonial = {
  name: string;
  title: string;
  comment: string;
  url: string;
  rating: number;
};
