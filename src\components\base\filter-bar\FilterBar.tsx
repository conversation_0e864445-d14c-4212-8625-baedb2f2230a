/**
 * A responsive filter bar component that adapts between desktop and mobile views.
 *
 * @component
 * @example
 * ```tsx
 * <FilterBar
 *   steps={[
 *     { type: 'filter', id: 'program', label: 'Program', options: [] },
 *     { type: 'separator' },
 *     { type: 'filter', id: 'course', label: 'Course', options: [] }
 *   ]}
 *   onChange={(values) => console.log(values)}
 * />
 * ```
 *
 * @remarks
 * The component automatically switches between:
 * - A horizontal bar layout for desktop views
 * - A modal interface for mobile views
 *
 * Features include:
 * - Grouped filters with scroll indicators
 * - Dependent filters with automatic option fetching
 * - Error and loading state handling
 * - Responsive design with breakpoint detection
 */
"use client";

import * as React from "react";
import { useBreakpoint } from "@/lib/hooks/tailwind-breakpoints";
import { FilterBarModal } from "./FilterBarModal";
import { cn } from "@/lib/utils";
import { FilterBarProps } from "./types";
import { useFilterBar } from "./useFilterBar";
import { GroupCarousel } from "./GroupCarousel";
import { Combobox } from "@/components/base/combobox";

export const FilterBar = React.forwardRef<HTMLDivElement, FilterBarProps>(
  (
    {
      className,
      steps,
      value,
      onChange,
      onOptionsNeeded,
      forceModal,
      forceBar,
      triggerText,
      ...props
    },
    ref,
  ) => {
    /** Loading state for the modal view's apply action */
    const [loading, setLoading] = React.useState(false);

    /** Determines if the current viewport is desktop size */
    const isDesktop = useBreakpoint("md");

    /** Hook providing filter bar state and logic */
    const {
      values,
      stepsState,
      groups,
      openStates,
      isStepEnabled,
      handleChange,
      setOpen,
      groupRefs,
    } = useFilterBar({
      steps,
      value,
      onChange,
      onOptionsNeeded,
    });

    /** Whether to show the modal view based on viewport and props */
    const showModal = forceModal || (!forceBar && !isDesktop);

    if (showModal) {
      return (
        <FilterBarModal
          steps={steps}
          value={value}
          onChange={onChange}
          onOptionsNeeded={onOptionsNeeded}
          loading={loading}
          onApply={async () => {
            setLoading(true);
            await new Promise((resolve) => setTimeout(resolve, 1000));
            setLoading(false);
          }}
          triggerText={triggerText}
          {...props}
        />
      );
    }

    return (
      <div
        ref={ref}
        className={cn(
          "divide-muted-foreground/20 flex w-full flex-wrap gap-4 divide-x-2",
          className,
        )}
        {...props}
      >
        {groups.map((group, groupIndex) => (
          <React.Fragment key={groupIndex}>
            <div
              className={cn(
                "min-w-[228px]",
                group.items.length > 1 ? "flex-[2_2_40%]" : "flex-[1_1_20%]",
              )}
            >
              <GroupCarousel
                group={group}
                groupRef={(el) => (groupRefs.current[groupIndex] = el)}
                wrapped={group.state.wrapped}
              >
                {group.items.map((step) => {
                  const stepState = stepsState[step.id];
                  const isEnabled = isStepEnabled(step.id);

                  return (
                    <Combobox
                      key={step.id}
                      id={step.id}
                      value={values[step.id]}
                      onChange={(value) => handleChange(step.id, value as string)}
                      options={stepState.options}
                      label={step.label}
                      placeholder={step.placeholder}
                      disabled={!isEnabled}
                      error={stepState.error}
                      loading={stepState.isLoading}
                      triggerClassName="min-w-40"
                      open={openStates[step.id]}
                      onOpenChange={(isOpen) => {
                        if (!isEnabled) return;
                        setOpen(step.id, isOpen);
                      }}
                    />
                  );
                })}
              </GroupCarousel>
            </div>
          </React.Fragment>
        ))}
      </div>
    );
  },
);

FilterBar.displayName = "FilterBar";
