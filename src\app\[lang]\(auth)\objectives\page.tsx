import { useTranslations } from "next-intl";
import { getTranslations } from "next-intl/server";

export default function Page() {
  const t = useTranslations("my-objectives");

  return (
    <div>
      <h1 className="text-2xl font-bold">{t("title")}</h1>
      <p className="mt-4 text-muted-foreground">{t("sub-title")}</p>
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "my-objectives" });
  return {
    title: t("title"),
    description: t("sub-title"),
  };
}
