import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { ConfigService } from '@config';
import { RoleUseCases } from '@useCases';
import { Reflector } from '@nestjs/core';

@Injectable()
export class JwtPermissionsGuard implements CanActivate {
  private readonly logger = new Logger(JwtPermissionsGuard.name);
  private readonly pxAppPK: string;
  private readonly pxOsPK: string;

  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly rolesService: RoleUseCases,
  ) {
    this.pxAppPK = configService.authSecrets.CLERK_PEM_PUBLIC_KEY;
    this.pxOsPK = configService.authSecrets.CLERK_PEM_PUBLIC_KEY_OS;
  }

  /**
   * Determines if the current request is authorized to proceed.
   * @param context - The execution context of the request.
   * @returns A boolean indicating if the request is authorized.
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(req);

    if (!token) {
      throw new UnauthorizedException(
        'You are not authorized to access this resource',
      );
    }

    try {
      const clientName = this.getClientName(req);
      if (!clientName) {
        this.logger.warn('No client name found in request');
      }
      const publicKey = this.getConcernedPK(clientName);
      const payload = await this.verifyTokenAndGetPayload(token, publicKey);
      if (this.isPxOS(clientName)) {
        req['user'] = payload;
      } else {
        const requiredPermissions = this.reflector.get<string[]>(
          'permissions',
          context.getHandler(),
        );
        if (!requiredPermissions) {
          req['user'] = payload;
          return true;
        }
        const permissions = await this.getPermissionsForRole(payload);
        this.checkPermissions(requiredPermissions, permissions);
        req['user'] = { ...payload, permissions };
      }
    } catch (error) {
      this.logger.error('Error verifying token', error);
      this.handleVerificationError(error);
    }

    return true;
  }

  /**
   * Retrieves the appropriate public key based on the client name.
   * @param clientName - The name of the client.
   * @returns The public key for the client.
   */
  getConcernedPK(clientName: string): string {
    if (this.isPxOS(clientName)) {
      return this.pxOsPK;
    }
    return this.pxAppPK;
  }

  /**
   * Extracts the JWT token from the request headers.
   * @param request - The incoming request.
   * @returns The extracted token or undefined if not found.
   */
  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  /**
   * Verifies the JWT token and returns the payload.
   * @param token - The JWT token to verify.
   * @returns The decoded token payload.
   */
  private async verifyTokenAndGetPayload(
    token: string,
    publicKey: string,
  ): Promise<any> {
    return this.jwtService.verifyAsync(token, {
      publicKey,
    });
  }

  /**
   * Retrieves the permissions for the given role.
   * @param payload - The decoded token payload.
   * @returns A promise that resolves to the permissions for the role.
   * @throws UnauthorizedException if the role is not found or permissions are not found.
   */
  private async getPermissionsForRole(payload: any): Promise<string[]> {
    const userRole = payload?.metaData?.role;
    if (!userRole) {
      throw new UnauthorizedException('User role not found in token');
    }

    const permissions = await this.rolesService.getPermissionsByRoleName(
      userRole,
    );
    if (!permissions) {
      throw new UnauthorizedException(
        `Permissions not found for role: ${userRole}`,
      );
    }

    return permissions;
  }

  /**
   * Checks if the user has the required permissions.
   * @param requiredPermissions - The permissions required to access the resource.
   * @param userPermissions - The permissions the user has.
   * @throws ForbiddenException if the user does not have the required permissions.
   */
  private checkPermissions(
    requiredPermissions: string[],
    userPermissions: string[],
  ): void {
    const hasPermission = requiredPermissions.every((permission) =>
      userPermissions.includes(permission),
    );
    if (!hasPermission) {
      throw new ForbiddenException(
        `You do not have the required permissions: ${requiredPermissions.join(
          ', ',
        )}`,
      );
    }
  }

  /**
   * Handles errors that occur during token verification.
   * @param error - The error that occurred.
   * @throws UnauthorizedException with the error as the cause.
   */
  private handleVerificationError(error: any): never {
    throw new UnauthorizedException(
      'You are not authorized to access this resource',
      { cause: error },
    );
  }

  /**
   * Extracts the client name from the request headers.
   * @param request - The incoming request.
   * @returns The extracted client name or undefined if not found.
   */
  private getClientName(request: Request): string | undefined {
    return request?.headers?.['client-name'] as string | undefined;
  }

  /**
   * Checks if the client is PxOS.
   * @param clientName - The name of the client.
   * @returns A boolean indicating if the client is PxOS.
   */
  private isPxOS(clientName: string): boolean {
    return clientName !== 'customer-portal' && clientName !== 'px-app';
  }
}
