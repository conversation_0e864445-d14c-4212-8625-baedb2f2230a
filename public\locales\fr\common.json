{"core": {"cancel": "Annuler", "confirm": "Confirmer", "edit": "Modifier"}, "subscription": {"details": {"title": "Détails du paiement", "started": "D<PERSON>but", "last-payment": "Dernier paiement", "next-payment": "Prochain paiement", "expires": "Expire le", "amount": "<PERSON><PERSON>", "change-payment-method": "Modifier le moyen de paiement", "cancel": "Annuler le paiement"}, "status": {"active": "Actif", "in trial": "<PERSON><PERSON><PERSON>", "non_renewing": "Non-renouvelable", "cancelled": "<PERSON><PERSON><PERSON>", "past_due": "En retard", "unpaid": "Impayé", "pending": "En attente", "default": "Inconnu"}, "invoices": {"title": "Factures", "no-invoices": "Aucune facture trouvée", "select-year": "Sélectionner l'année", "all-years": "Toutes les années", "invoice-number": "Numéro de facture", "download": "Télécharger", "status": {"paid": "Payée", "pending": "En attente", "failed": "É<PERSON><PERSON>e", "refunded": "Remboursée"}}, "payment-method": {"title": "Moyen de paiement", "no-payment-method": "Aucun moyen de paiement trouvé", "credit-card": "Carte de <PERSON>", "expires": "Expire", "unknown": "Inconnu", "expired": "Expiré"}, "cancel": {"title": "Annuler le paiement", "description": "Es-tu sûr de vouloir annuler ton paiement? Cette action est irréversible.", "warning": "Ton paiement sera annulé immédiatement. Tu perdras l'accès à tous les avantages de l'abonnement à la fin de ta période de facturation actuelle.", "error": "Une erreur s'est produite lors de l'annulation de ton paiement. Veuillez réessayer plus tard.", "confirm-button": "<PERSON><PERSON>, annuler le paiement", "cancel-button": "Non, conserver le paiement", "cancelling": "Annulation en cours..."}}, "impersonation": {"errors": {"empty_email": "<PERSON><PERSON><PERSON>z saisir une adresse email", "invalid_email": "Veuillez saisir une adresse email valide", "cannot_impersonate_admin": "Impossible d'imiter un administrateur Paradox"}, "started": "Tu es maintenant connecté en tant que {email}", "stopped": "Imitation arrêtée", "status": {"title": "Statut d'accès utilisateur", "email": "Email", "pxs_access": "Accès PXS", "pxl_access": "Accès PXL", "pom_access": "Accès POM", "is_paradox": "<PERSON><PERSON>", "pxs_session": "Session PXS", "loading": "Chargement...", "yes": "O<PERSON>", "no": "Non"}, "indicator": {"active": "Imitation active", "message": "<PERSON> ({originalEmail}) visualises actuellement en tant que {impersonatedEmail}"}, "currently_impersonating": "Actuellement connecté en tant que", "your_email": "Ton email", "stop": "Arrêter l'imitation", "user_email_label": "Email de l'utilisateur", "user_email_placeholder": "Saisissez l'email de l'utilisateur à imiter", "start": "Démarrer l'imitation", "close": "<PERSON><PERSON><PERSON>"}, "components": {"data-table": {"search": "<PERSON><PERSON><PERSON>", "selected": "sélectionnés", "clear": "<PERSON><PERSON><PERSON>"}, "tree-view": {"search_placeholder": "Rechercher...", "selection_text": "sélectionnés", "expand_all": "<PERSON><PERSON> d<PERSON>", "collapse_all": "Tout replier", "checkbox_labels": {"check": "<PERSON><PERSON>", "uncheck": "<PERSON><PERSON><PERSON><PERSON>"}}, "badge": {"new": "Nouveau", "locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "toolCard": {"accessibleWithCourse": "Outil accessible avec le cours"}, "multi-step-modal": {"required_fields_indicator": "Cette section contient des champs obligatoires", "section_error_count": "({count} erreur{s})", "next": "Suivant", "previous": "Précédent", "complete": "<PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "defaults": {"title": "Veuillez remplir les champs obligatoires", "description": "Certains champs sont obligatoires et doivent être remplis pour continuer", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "Annuler"}, "image-upload": {"drag_or_click": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z une image ici ou cliquez pour en sélectionner une", "drop_here": "Déposez l'image ici", "uploading": "Téléchargement en cours...", "error": "Erreur lors du téléchargement", "success": "Image téléchargée avec succès", "remove": "Supprimer l'image", "requirements": "Format accepté : JPG, PNG. Taille max : 5MB", "aspect_ratio": "L'image doit avoir un ratio {ratio}"}}}, "modal": {"confirm": {"close": {"title": "Modifications non enregistrées", "description": "Tu as des modifications non enregistrées. Es-tu sûr de vouloir fermer cette fenêtre ? Toutes les modifications seront perdues.", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "Continuer l'édition"}, "reset": {"title": "Réinitialiser le formulaire", "description": "Es-tu sûr de vouloir réinitialiser le formulaire ? Toutes les modifications seront perdues.", "confirm": "Réinitialiser", "cancel": "Annuler"}}}, "validation": {"required": "Ce champ est obligatoire", "min": "Doit contenir au moins {min} caractères", "max": "Doit contenir au plus {max} caractères", "email": "Doit être une adresse email valide", "url": "Doit être une URL valide", "number": "Doit être un nombre valide", "integer": "Doit être un nombre entier valide", "positive": "Doit être un nombre positif", "negative": "Doit être un nombre négatif", "date": "Doit être une date valide", "min_date": "<PERSON>it être après le {min}", "max_date": "Doit être avant le {max}", "min_number": "Doit être supérieur à {min}", "max_number": "Doit être inférieur à {max}", "matches": "Doit correspondre à {field}", "not_matches": "Ne doit pas correspondre à {field}", "different": "Les champs ne correspondent pas", "same": "Les champs doivent correspondre", "errors_found": "<PERSON><PERSON><PERSON><PERSON> corriger les erreurs ci-dessous pour continuer", "errors_in_steps": "Erreurs supplémentaires dans les étapes : {steps}", "name": {"min": "Le nom doit contenir au moins 3 caractères", "max": "Le nom doit contenir moins de 100 caractères"}, "description": {"min": "La description doit contenir au moins 10 caractères", "max": "La description doit contenir moins de 1000 caractères"}, "theme": {"min": "Sélectionnez au moins un thème"}, "image": {"size": "L'image doit faire moins de {size}MB"}, "common": {"no_results": "Aucun résultat trouvé"}}, "navigation": {"sidebar": {"my-profile": "Mon profil"}, "communities": {"title": "Communauté", "title_plural": "Communautés", "pxl": "PXL", "pxs": "PXS"}, "cohorts": {"title": "Cohortes"}, "practice": {"title": "Pratique", "stats": {"title": "Statistiques des pratiques"}}, "goliaths": "Goliaths", "faq": "FAQ", "sign-in": "Connexion", "sign-up": "Inscription", "logout": "Déconnexion", "dashboard": "Accueil", "calendars": "Calendriers", "courses": "Produits", "objectives": "Mes objectifs", "messages": "Messagerie", "users": "Gestion des utilisateurs", "products": {"programs": "Programmes", "courses": "Cours", "events": "Évènements", "cohorts": "Cohortes"}, "user-management": {"users": "Utilisateurs", "roles": "<PERSON><PERSON><PERSON>"}}, "locale-switcher": {"locale": "{locale, select, fr {Français} en {English} other {Unknown}}"}, "gestion": {"users": {"filter-by-role": "Filtrer par rôle", "filters": "Filtres", "selected": "{count} sélectionnés", "errors": {"no-access": "Tu n'as pas accès à cette page", "no-results": "Aucun utilisateur ne fait partie de cette liste", "loading-failed": "Une erreur est survenue lors du chargement des données"}, "bulk-update-role": "Modifier le rôle", "bulk-update-role-error": "Veuillez sélectionner un rôle", "bulk-update-role-error-description": "Le rôle est obligatoire", "bulk-update-role-success": "<PERSON><PERSON>le mis à jour", "bulk-update-role-success-description": "Le rôle a été mis à jour avec succès", "created-at": "Date de création", "updated-at": "Dernière édition", "score": "Score", "title": "Utilisateurs", "confirmation-title": "Mettre à jour le rôle", "confirmation-description": "Es-tu sûr de vouloir mettre à jour le rôle de l'utilisateur ?", "update": "Mettre à jour", "cancel": "Annuler", "profile": "Profil", "authorizations": "Autorisations", "status": "Statut", "actions-header": "Actions", "name": "Nom", "members": "Me<PERSON><PERSON>", "permissions": "Permissions", "role": "R<PERSON><PERSON>", "edit": "É<PERSON>er", "delete": "<PERSON><PERSON><PERSON><PERSON>", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "updated": "Mis à jour", "updated-description": "L'utilisateur a été mis à jour avec succès", "information": {"title": "Informations", "role": "R<PERSON><PERSON>", "profile": "Profil", "job": "Poste", "description": "Description", "date-creation": "Date de création"}, "activities": {"title": "Activités", "score-nps": "Score NPS", "cohorts-management": "Gestion des cohortes", "last-connection": "Dernière connexion", "last-courses": "Derniers cours"}, "authorization": {"title": "Autorisations", "courses": "Gestion des cours", "students": "Gestion des étudiants", "contents": "Gestion des contenus"}, "profiles": {"create": "<PERSON><PERSON>er un nouveau profil"}, "user": {"modal-title": "Modifier l'utilisateur", "role": "R<PERSON><PERSON>", "firstName": "Prénom", "lastName": "Nom", "email": "Email", "save": "Enregistrer", "saving": "Enregistrement...", "error": "<PERSON><PERSON><PERSON>", "error-description": "Une erreur est survenue lors de la mise à jour de l'utilisateur", "updated": "Mis à jour", "updated-description": "L'utilisateur a été mis à jour avec succès"}, "roles": {"created": "<PERSON><PERSON><PERSON>", "created-description": "Le rôle a été créé avec succès", "group": "Groupe", "group_description": "Sélectionnez le groupe du rôle", "group_validation": "Le groupe est obligatoire", "name": "Nom", "name_description": "Saisissez le nom du rôle", "name_validation": "Le nom est obligatoire", "members": "Me<PERSON><PERSON>", "permissions": "Permissions", "roles": "<PERSON><PERSON><PERSON>", "create": "Créer un nouveau rôle", "update": "Mettre à jour", "cancel": "Annuler", "edit": "É<PERSON>er", "add_permissions": "Ajouter des permissions", "error": "<PERSON><PERSON><PERSON>", "error-delete-description": "Une erreur est survenue lors de la suppression du rôle", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete-title": "Supprimer un rôle", "delete-description": "Es-tu sûr de vouloir supprimer ce rôle ?", "deleted": "Rôle supprimé", "deleted-description": "Le rôle a été supprimé avec succès", "edit_permissions": "É<PERSON>er les permissions", "duplicate_role": "Dup<PERSON>r un rôle", "updated": "<PERSON><PERSON>le mis à jour", "updated-description": "Le rôle a été mis à jour avec succès", "no-results": "<PERSON><PERSON><PERSON> rôle trouvé", "filter-by-group": "Filtrer par groupe", "filter-by-role": "Filtrer par rôle", "filters": "Filtres", "all-groups": "Tous les groupes", "all-roles": "To<PERSON> les rôles"}, "actions": {"create": "Ajouter une équipe"}, "filter": {"roles": {"placeholder": "<PERSON><PERSON><PERSON>", "all": "To<PERSON> les rôles"}, "team": {"placeholder": "Équipe", "all": "Toutes les équipes"}}}, "permissions": {"users": "Gestion des utilisateurs", "user:create": "C<PERSON>er un utilisateur", "user:read": "Lister les utilisateurs", "user:update": "Modifier un utilisateur", "user:delete": "Supprimer un utilisateur", "roles": "Gestion des rôles", "role:create": "<PERSON><PERSON>er un rôle", "role:read": "<PERSON><PERSON>ô<PERSON>", "role:update": "Modifier un rôle", "role:delete": "Supprimer un rôle", "courses": "Gestion des cours", "course:create": "<PERSON><PERSON><PERSON> un cours", "course:read": "Lister les cours", "course:update": "Modifier un cours", "course:archive": "Archiver un cours", "cohorts": "Gestion des cohortes", "cohort:create": "<PERSON><PERSON><PERSON> une cohorte", "cohort:read": "Lister les cohortes", "cohort:update": "Modifier une cohorte", "cohort:delete": "Supprimer une cohorte", "whats-new": "Gestion des \"Quoi de neuf ?\"", "whats-new:create": "<PERSON><PERSON><PERSON> un \"Quoi de neuf ?\"", "whats-new:read": "Lister les \"Quoi de neuf ?\"", "whats-new:update": "Modifier un \"Quoi de neuf ?\"", "whats-new:delete": "Supprimer un \"Quoi de neuf ?\"", "whats-new:upload": "Télécharger une image", "whats-new:react": "<PERSON><PERSON><PERSON><PERSON> à un \"Quoi de neuf ?\""}}, "cohorts": {"details": {"title": "Dé<PERSON> de la cohorte"}, "tabs": {"information": "Informations", "students": "Étudiants", "offers": "Offres"}, "information": {"duration_days": "{count} jours", "start_date": "Date de début", "end_date": "Date de fin", "duration": "<PERSON><PERSON><PERSON>", "status": "Statut", "description": "Description", "details": "Détails", "course": "Cours", "course_name": "Nom du cours", "participants": "Participants", "participants_count": "{current} / {max} participants", "not_available": "Non disponible"}, "details-page": {"title": "Cohorte", "plural-title": "Cohortes", "tabs": {"informations": "Informations", "students": "Étudiants", "offers": "Offres"}, "info": {"details-title": "Détails", "content-title": "Contenu", "start": "D<PERSON>but", "end": "Fin", "duration": "<PERSON><PERSON><PERSON>", "duration_days": "{count} jours", "status": "Statut", "seats": "Places", "score": "Score", "description": "Description", "course": "Cours", "course_name": "Nom du cours", "participants": "Participants", "participants_count": "{current} / {max} participants"}}, "status": {"upcoming": "À venir", "running": "En cours", "passed": "Terminée"}, "actions": {"see_calendar": "Voir le calendrier", "edit": "Modifier", "add_student": "Ajouter un étudiant", "view_course": "Voir le cours"}, "students": {"title": "Étudiants", "name": "Nom", "email": "Email", "status": "Statut", "joined_at": "Date d'inscription", "actions": "Actions", "view_details": "Voir les détails de {name}", "open_menu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu pour {name}", "no_results": "Aucun étudiant trouvé", "add_title": "Ajouter des étudiants à la cohorte", "add_submit": "Ajouter les étudiants", "select_label": "Sélectionner des étudiants", "select_placeholder": "Rechercher et sélectionner des étudiants...", "select_description": "Sélectionnez un ou plusieurs étudiants à ajouter à cette cohorte", "select_description_with_limit": "Sélectionnez un ou plusieurs étudiants à ajouter à cette cohorte. Actuellement {current}/{max} étudiants ({remaining} places restantes)", "max_participants_exceeded": "Impossible d'ajouter plus d'étudiants. La cohorte a atteint sa capacité maximale de {max} participants.", "error_title": "<PERSON><PERSON>ur lors de l'ajout d'étudiants", "error_description": "Une erreur est survenue lors de l'ajout d'étudiants à la cohorte", "add_success_title": "Étudiants ajoutés", "add_success_description": "Les étudiants ont été ajoutés à la cohorte avec succès", "remove_success_title": "Étudiants supprimés", "remove_success_description": "Les étudiants ont été supprimés de la cohorte avec succès", "remove_error_title": "<PERSON><PERSON><PERSON> lors de la suppression d'étudiants", "remove_error_description": "Une erreur est survenue lors de la suppression d'étudiants de la cohorte", "remove": "Supprimer l'étudiant", "remove_title": "Supprimer des étudiants", "remove_description_single": "Es-tu sûr de vouloir supprimer {name} de cette cohorte ?", "remove_description_multiple": "Es-tu sûr de vouloir supprimer {count} étudiants de cette cohorte ?", "remove_confirm": "<PERSON><PERSON><PERSON><PERSON>", "remove_cancel": "Annuler", "removing": "Suppression...", "selected": "étudiants sélectionnés", "all": "tous", "select_all": "<PERSON><PERSON>", "clear_selection": "Effacer la sélection", "move_to_cohort": "<PERSON><PERSON><PERSON><PERSON> vers une cohorte", "remove_from_cohort": "<PERSON><PERSON><PERSON><PERSON>", "move_title": "Déplacer les étudiants", "move_description": "<PERSON><PERSON><PERSON>r {count} étudiants vers une autre cohorte", "move_description_single": "<PERSON><PERSON><PERSON><PERSON> {name} vers une autre cohorte", "target_cohort": "Cohorte cible", "select_cohort_placeholder": "Sélectionner une cohorte", "select_target_cohort": "Veuillez sélectionner une cohorte cible", "move_confirm": "<PERSON><PERSON><PERSON><PERSON>", "moving": "Déplacement...", "moved_title": "Étudiants déplacés", "moved_description": "Les étudiants ont été déplacés avec succès", "move_error": "Une erreur est survenue lors du déplacement des étudiants", "cancel": "Annuler", "current_capacity": "Capacité actuelle : {current}/{max} étudiants ({remaining} places restantes)"}, "offers": {"title": "Offres", "name": "Nom", "id": "ID", "status": "Statut", "courses": "Cours", "created_at": "Date de création", "no_results": "<PERSON><PERSON>ne offre trouvée", "status_active": "Active", "status_inactive": "Inactive", "status_draft": "Brouillon", "status_archived": "Archivée"}, "created": "Co<PERSON><PERSON>", "created-description": "La cohorte a été créée avec succès", "updated": "Cohorte mise à jour", "updated-description": "La cohorte a été mise à jour avec succès", "edit": "Modifier la cohorte", "edit-submit": "Enregistrer les modifications", "error": "<PERSON><PERSON><PERSON>", "error-description": "Une erreur est survenue lors de la création de la cohorte", "title": "Cohortes", "description": "Description", "description_description": "Saisissez la description de la cohorte", "description_validation": "La description est obligatoire", "maxParticipants": "Places", "maxParticipants_description": "Saisissez le nombre de participants maximum", "maxParticipants_validation": "Le nombre de participants est obligatoire", "startDate": "Date de début", "startDate_description": "Saisissez la date de début de la cohorte", "endDate": "Date de fin", "endDate_description": "Saisissez la date de fin de la cohorte", "communityInfo": "Communautés", "communityInfo_description": "Sélectionnez au moins un espace communautaire de PXS ou PXL", "name": "Nom", "name_description": "Saisissez le nom de la cohorte", "name_validation": "Le nom est obligatoire", "create": "<PERSON><PERSON><PERSON> une cohorte", "errors": {"no-access": "Tu n'as pas accès à cette page", "no-results": "<PERSON><PERSON>ne cohorte trouvée", "loading-failed": "Une erreur est survenue lors du chargement des données"}, "table": {"title": "Cohortes", "name": "Nom", "start_date": "Date de début", "end_date": "Date de fin", "max_participants": "Places", "current_participants": "Étudiants", "status": {"self": "Statut", "active": "Active", "draft": "Brouillon", "completed": "Terminée", "cancelled": "<PERSON><PERSON><PERSON>"}, "no_results": "<PERSON><PERSON>ne cohorte trouvée", "actions": {"header": "Actions", "view_details": "Voir détails pour {name}"}}, "filters": {"status": {"label": "Statut", "options": {"active": "Actif", "past": "Passé", "future": "À venir"}}}}, "table": {"loading": "Chargement...", "no-data": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "no-results": "Aucun résultat trouvé", "of": "sur", "rows-selected": "lignes sélectionnées", "select-all": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "deselect-all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tout", "page": "Page", "goto-first": "Aller à la première page", "goto-last": "Aller à la dernière page", "goto-prev": "Aller à la page précédente", "goto-next": "Aller à la page suivante", "search": "<PERSON><PERSON><PERSON>", "view_details": "Voir les détails de {name}"}, "calendar": {"title": "Calendriers", "sub-title": "<PERSON><PERSON><PERSON> vos emplois du temps et plannings", "today": "<PERSON><PERSON><PERSON>'hui", "day": "Jour", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "suggest-a-slot": "Proposer un créneau", "new-slot": "Nouveau créneau", "what-is-this-event": "Quel est cet événement ?", "type-of-event": "Type d'événement", "event-types": {"mentoring": "Mentoring", "masterclass": "Masterclass", "supervision": "Supervision", "guided-triad": "Triade guidée", "customized-slot": "<PERSON><PERSON><PERSON><PERSON>"}, "date-and-time": "Date et horaire", "time-zone": "<PERSON><PERSON> ho<PERSON>", "duration": "<PERSON><PERSON><PERSON>", "recurrence": "<PERSON><PERSON><PERSON><PERSON>", "replay": "Replay", "who-are-the-speakers": "Qui sont les intervenants ?", "main-speaker": "Intervenant principal", "other-speakers": "Autres intervenants", "technical-support": "Support technique", "what-kind-of-audience": "Quel public est concerné ?", "cohort": "Cohorte", "participants": "Nombre de participants", "program": "Programme", "course": "Cours", "module": "<PERSON><PERSON><PERSON>", "any-comment": "Un commentaire ?", "comments": "Commentaires", "add-slot": "Ajouter un créneau", "edit-slot": "Modifier ce créneau", "all-events": "Tous les évenements", "upcoming-events": "A venir", "past-events": "Passé(s)"}, "dashboard": {"title": "Accueil", "sub-title": "Tableau de bord de votre espace personnel", "purchases": {"unknown": "Cours non disponible", "card": {"action": {"purchase": "Découvrir le cours", "consume": "Lancer le cours"}}, "status": {"active": "Actif", "paid": "<PERSON><PERSON>", "pending": "En attente", "voided": "<PERSON><PERSON><PERSON>", "not_paid": "Non payé", "stopped": "<PERSON><PERSON><PERSON><PERSON>", "overdue": "En retard", "in_trial": "En essai", "granted": "Actif", "revoked": "<PERSON><PERSON><PERSON><PERSON>", "suspended": "Suspendu", "error": "En erreur"}, "title": "Mes formations achetées", "empty-state": "Vous n'avez pas encore de cours.", "inactive": "Paiements inactifs", "sectionSubtitle": "Visualise et gère tes programmes et formations achetés. <em>*ce qui t'a été offert n’apparaîtra pas ici.</em>", "sectionActionLabel": "Voir tous mes paiements", "see-more": "Voir plus"}, "courses": {"title": "Tous les cours", "sectionSubtitle": "Découvre tous les cours de Paradox.", "empty-state": "Aucun cours trouvé"}, "eventsSection": {"title": "Évènements à venir", "subtitle": "Ne manque pas nos derniers évènements.", "sampleCard": {"description": "Rejoins-nous pour un événement exclusif afin de renforcer tes compétences et de réseauter avec des professionnels.", "details": {"improveConfidence": "Améliore ta confiance", "noFearOfJudgment": "Aucune crainte du jugement", "focusOnStrengths": "Concentre-toi sur tes points forts", "organizeBetter": "Organise-toi mieux", "manageTimeEffectively": "Gère ton temps efficacement", "unleashPotential": "Libère ton potentiel"}, "actionButtonLabel": "En savoir plus"}}, "heroCard": {"title": "Bienvenue {firstname} sur Paradox", "subtitle": "L'endroit où tu bâtis la personne que tu rêves de devenir.", "actionButtonLabel": "Découvrir nos formations"}, "wallOfDreams": {"title": "PARTAGE TON RÊVE", "subtitle": "Partage ton rêve en enregistrant ton témoignage vidéo pour figurer sur le mur des 100 000 rêves.", "buttonWriteStory": "É<PERSON>ris ta propre histoire", "buttonDiscoverStories": "Découvre leur histoire"}}, "my-courses": {"title": "Produits", "sub-title": "Accédez à vos produits et ressources pédagogiques"}, "my-objectives": {"title": "Mes objectifs", "sub-title": "<PERSON><PERSON>z vos objectifs et votre progression"}, "messages": {"title": "Messagerie", "new-message": "Nouveau message", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "type-message": "Écrivez un message..."}, "courses": {"title": "Cours", "search": {"placeholder": "Rechercher un cours..."}, "filters": {"button": "<PERSON><PERSON><PERSON>", "status": {"label": "Statut", "placeholder": "Sélectionner un statut"}, "theme": {"label": "Thème", "placeholder": "Sé<PERSON><PERSON>ner un thème"}}, "status": {"published": "<PERSON><PERSON><PERSON>", "draft": "Brouillon", "archived": "Archivé"}, "offer_status": {"active": "Active", "disabled": "Désactivée"}, "themes": {"MATHEMATICS": "Mathématiques", "PHYSICS": "Physique", "CHEMISTRY": "<PERSON><PERSON>", "BIOLOGY": "Biologie", "COMPUTER_SCIENCE": "Informatique", "LITERATURE": "Littérature", "HISTORY": "Histoire", "GEOGRAPHY": "Géographie", "ECONOMICS": "Économie", "PHILOSOPHY": "Philosophie", "ART": "Art", "MUSIC": "Musique", "SPORTS": "Sports", "LANGUAGES": "<PERSON><PERSON>", "programming": "Programmation", "design": "Design", "business": "Business", "marketing": "Marketing", "management": "Management", "leadership": "Leadership", "communication": "Communication", "productivity": "Productivité"}, "create": {"button": "<PERSON><PERSON><PERSON> un cours", "title": "<PERSON><PERSON>er un nouveau cours", "submit": {"publish": "Publier", "create": "<PERSON><PERSON><PERSON>", "draft": "Enregistrer en tant que brouillon"}, "success": {"title": "Cours créé", "description": "Le cours a été créé avec succès"}, "error": {"title": "<PERSON><PERSON><PERSON> lors de la création", "description": "Une erreur est survenue lors de la création du cours"}, "image": {"drag_or_click": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z une image ici ou cliquez pour en sélectionner une", "drop_here": "Déposez l'image ici", "uploading": "Téléchargement en cours...", "error": "Erreur lors du téléchargement", "success": "Image téléchargée avec succès", "remove": "Supprimer l'image", "requirements": "Format accepté : JPG, PNG. Taille max : 5MB"}}, "fields": {"name": "Nom", "description": "Description", "theme": "Thème", "theme_placeholder": "Sé<PERSON><PERSON>ner un thème", "card_image": "Card", "cover_image": "Cover", "thumbnail_image": "Miniature", "offers": "Offres", "offers_placeholder": "Sélectionner des offres", "offers_description": "Sélectionnez les offres qui incluront ce cours"}, "validation": {"name": {"min": "Le nom doit contenir au moins 3 caractères", "max": "Le nom doit contenir moins de 100 caractères"}, "description": {"min": "La description doit contenir au moins 10 caractères", "max": "La description doit contenir moins de 1000 caractères"}, "theme": {"min": "Sélectionnez au moins un thème"}, "image": {"required": "L'image est obligatoire", "size": "L'image doit faire moins de {size}MB", "url": "L'URL de l'image n'est pas valide", "type": "L'image doit être de l'un de ces types : {types}", "aspect_ratio": "L'image doit avoir un ratio {ratio}"}, "url": "L'URL n'est pas valide"}, "table": {"select_all": "<PERSON><PERSON><PERSON><PERSON><PERSON> tous les cours", "select_row": "Sé<PERSON><PERSON>ner le cours {id}", "name": "Nom", "description": "Description", "no_description": "Aucune description disponible", "status": "Statut", "theme": "Thème", "created_at": "Date de création", "no_results": "Aucun cours trouvé", "rows_per_page": "Lignes par page", "page_x_of_y": "Page {current} sur {total}", "first_page": "Aller à la première page", "previous_page": "Aller à la page précédente", "next_page": "Aller à la page suivante", "last_page": "Aller à la dernière page", "open_menu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu pour {name}", "view_details": "Voir les détails de {name}", "actions": {"header": "Actions", "edit": "Modifier", "publish": "Publier", "unpublish": "Dépublier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "actions": {"delete": {"confirm": {"title": "Supp<PERSON>er le cours", "description": "Es-tu sûr de vouloir supprimer {name} ? Cette action ne peut pas être annulée.", "action": "<PERSON><PERSON><PERSON><PERSON>"}, "success": "Cours supprimé avec succès", "error": "Échec de la suppression du cours"}, "publish": {"label": "Publier", "success": "Cours publié avec succès", "error": "Échec de la publication du cours"}, "unpublish": {"success": "Cours dépublié avec succès", "error": "Échec de la dépublication du cours"}, "archive": "Archiver", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier le cours"}, "delete": {"title": "Supp<PERSON>er le cours", "description": "Es-tu sûr de vouloir supprimer {name} ? Cette action ne peut pas être annulée.", "cancel": "Annuler", "confirm": "<PERSON><PERSON><PERSON><PERSON>"}, "details": {"title": "<PERSON><PERSON><PERSON> du cours", "not_found": "Cours introuvable", "general_info": "Informations", "visuals": "Visuels", "name": "Nom", "description": "Description", "banner": "Image de bannière", "thumbnail": "Miniature", "card": "Image de carte", "no_banner": "Pas d'image de bannière", "no_thumbnail": "Pas de miniature", "no_card": "Pas d'image de carte", "students": {"title": "Étudiants", "no_results": "Aucun étudiant trouvé", "name": "Nom", "joined_at": "Date d'inscription", "cohort": "Cohorte", "actions": "Actions", "view_details": "Voir les détails de {name}", "view_cohort": "Voir la cohorte {name}"}, "offers": {"title": "Offres", "no_results": "<PERSON><PERSON>ne offre trouvée", "name": "Nom", "status": "Statut", "image": "Image", "created_at": "Date de création"}, "cohorts": {"title": "Cohortes", "create": "<PERSON><PERSON><PERSON> une cohorte", "name": "Nom", "start_date": "Date de début", "end_date": "Date de fin", "status": {"self": "Status", "active": "Active", "inactive": "Inactive", "completed": "Terminée"}, "no_results": "<PERSON><PERSON>ne cohorte trouvée", "search": "Rechercher des cohortes..."}, "tabs": {"information": "Informations", "students": "Étudiants", "offers": "Offres", "cohorts": "Cohortes"}}, "edit": {"title": "Modifier le cours", "submit": {"save": "Enregistrer les modifications"}, "success": {"title": "Cours mis à jour", "description": "Le cours a été mis à jour avec succès"}, "error": {"title": "<PERSON><PERSON>ur lors de la mise à jour", "description": "Une erreur est survenue lors de la mise à jour du cours"}}}, "products": {"programs": {"title": "Programmes", "filter": {"programs": {"placeholder": "Filtrer par programme", "all": "Tous les programmes"}}, "actions": {"create": "Créer un programme", "edit": "Modifier", "add_course": "Ajouter un cours", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit_program": "Modifier le programme", "add_courses": "Ajouter des cours"}, "tabs": {"active": "Actifs", "inactive": "Inactifs"}, "status": {"active": "Actif", "inactive": "Inactif"}, "modal": {"create": {"title": "Créer un nouveau programme", "steps": {"basic": {"title": "Informations de base", "description": "Saisissez les informations de base de votre programme", "fields": {"name": {"label": "Nom du programme", "description": "Le nom qui sera affiché aux utilisateurs", "placeholder": "Entrez le nom du programme"}, "description": {"label": "Description", "description": "Une description détaillée du programme", "placeholder": "Entrez la description du programme"}, "isActive": {"label": "Statut actif", "description": "Activez cette option pour rendre le programme visible aux utilisateurs"}}}, "details": {"title": "Détails du programme", "description": "Configurez les détails spécifiques du programme", "fields": {"duration": {"label": "Du<PERSON>e (en semaines)", "description": "La durée totale du programme en semaines", "placeholder": "Entrez la durée du programme"}, "type": {"label": "Type de programme", "description": "Sélectionnez le type de programme", "options": {"certification": "Certification", "course": "Cours", "workshop": "Atelier"}}}}}}}, "detail": {"tabs": {"courses": "Cours", "students": "Étudiants", "community": "Communauté", "offers": "Offres", "information": "Informations"}, "courses": {"table": {"name": "Nom du cours", "score": "Score", "program": "Programme", "description": "Description", "associatedCourse": "Cours associé"}}, "students": {"table": {"name": "Étudiant", "score": "Score", "cohort": "Cohorte", "lastConnection": "Dernière connexion", "creationDate": "Date de création", "lastCourses": "Derniers cours", "student": "étudiant", "students": "étudiants"}}, "offers": {"table": {"name": "Nom de l'offre", "id": "ID", "status": "Statut", "programs": "Programmes", "courses": "Cours", "created_at": "Date de création", "offer": "offre", "offers": "offres", "active": "Actif", "inactive": "Inactif"}}}}}, "profile": {"title": "Profil", "sub-title": "Gérer vos informations de profil", "upload": "Changer l'avatar", "settings": {"payments": {"title": "Paiements", "sub-title": "Gérer vos paiements"}, "payments-details": {"title": "Détails", "sub-title": "G<PERSON>rer vos détails de paiement"}, "account": {"title": "<PERSON><PERSON><PERSON>", "sub-title": "Gérer vos informations de compte", "form": {"modify": "Modifier", "save": "Enregistrer", "cancel": "Annuler", "no-phone": "Aucun numéro de téléphone", "no-email": "Aucun email", "success": "Informations de compte mises à jour avec succès", "firstName": {"label": "Prénom", "placeholder": "Entrez votre prénom"}, "lastName": {"label": "Nom", "placeholder": "Entrez votre nom"}, "email": {"label": "Email", "placeholder": "Entrez votre adresse email"}, "phone": {"cancel": "Annuler", "label": "Téléphone", "placeholder": "Entrez votre numéro de téléphone", "submit": "Changer le numéro de téléphone", "unlink": "Supp<PERSON>er le numéro de téléphone", "unlink-success": "Numéro de téléphone supprimé avec succès", "unlink-error": "Impossible de supprimer le numéro de téléphone", "updated": "Numéro de téléphone mis à jour avec succès", "save": "Enregistrer le numéro de téléphone", "error": {"cannot-update": "Impossible de mettre à jour le numéro de téléphone"}}, "password": {"label": "Mot de passe", "placeholder": "Entrez votre mot de passe", "current-password": "Mot de passe actuel", "new-password": "Nouveau mot de passe", "confirm-password": "Confirmer le nouveau mot de passe", "submit": "Changer le mot de passe", "updated": "Mot de passe mis à jour avec succès", "cancel": "Annuler", "error": {"passwords-not-match": "Les mots de passe ne correspondent pas", "form_password_validation_failed": "Le mot de passe actuel est incorrect", "form_password_length_too_short": "Le mot de passe doit contenir au moins 8 caractères", "form_password_size_in_bytes_exceeded": "Le mot de passe est trop long (maximum 72 caractères)", "form_password_pwned": "Le nouveau mot de passe est trop commun", "cannot-update": "Impossible de mettre à jour le mot de passe"}}, "billingAddress": {"label": "Adresse de facturation", "placeholder": "Entrez votre adresse de facturation"}}, "info": {"one": {"title": "Pourquoi mes informations ne sont pas affichées ici ?", "description": "Nous masquons certains détails de votre compte afin de protéger votre identité."}, "two": {"title": "Quelles informations peuvent être modifiées ?", "description": "Chez Paradox, la possibilité de modifier tes informations personnelles est essentielle pour garantir une expérience utilisateur optimale. La gestion de ton profil est mise à jour afin de répondre à tes besoins et garantir que toutes les données inscrites sont à jour et correctes. Nous te offrons la flexibilité de modifier tes informations pour que tu puisses adapter ton profil à tes nouvelles exigences sans avoir à contacter le support."}}}, "whats-new": {"title": "Quoi de neuf ?", "sub-title": "Dernières mises à jour"}, "goliaths": {"title": "Goliaths"}, "faq": {"title": "FAQ", "sub-title": "Questions fréquentes", "pxs": "PXS", "pxl": "PXL", "goliaths": "Goliaths"}, "courses": {"title": "Cours", "titleId": "Cours {id}", "sub-title": "Gérer vos cours"}, "cohorts": {"title": "Cohortes", "titleId": "Cohorte {id}", "sub-title": "Gérer vos cohortes", "no-cohorts": "<PERSON><PERSON>ne cohorte trouvée", "create-cohort": "<PERSON><PERSON><PERSON> une cohorte"}}}, "common": {"cancel": "Annuler", "search": "<PERSON><PERSON><PERSON>", "no_results": "Aucun résultat", "filters": "Filtres", "back": "Retour"}, "services": {"user": {"create": {"success": {"title": "Utilisa<PERSON>ur c<PERSON>", "description": "L'utilisateur a été créé avec succès"}, "error": {"title": "<PERSON><PERSON><PERSON> lors de la création", "description": "Une erreur est survenue lors de la création de l'utilisateur"}}, "update": {"success": {"title": "Utilisateur mis à jour", "description": "L'utilisateur a été mis à jour avec succès"}, "error": {"title": "<PERSON><PERSON>ur lors de la mise à jour", "description": "Une erreur est survenue lors de la mise à jour de l'utilisateur"}}, "bulk_update": {"success": {"title": "Utilisateurs mis à jour", "description": "Les utilisateurs ont été mis à jour avec succès"}, "error": {"title": "<PERSON><PERSON>ur lors de la mise à jour", "description": "Une erreur est survenue lors de la mise à jour des utilisateurs"}}, "create_phone_number": {"success": {"title": "Numéro de téléphone créé", "description": "Le numéro de téléphone a été créé avec succès"}, "error": {"title": "<PERSON><PERSON><PERSON> lors de la création", "description": "Une erreur est survenue lors de la création du numéro de téléphone"}}, "unlink_phone_number": {"success": {"title": "Numéro de téléphone supprimé", "description": "Le numéro de téléphone a été supprimé avec succès"}, "error": {"title": "<PERSON><PERSON><PERSON> lors de <PERSON>", "description": "Une erreur est survenue lors de la suppression du numéro de téléphone"}}}}, "toolsSection": {"title": "<PERSON><PERSON>", "subtitle": "Découvre des outils pour t'aider dans ton parcours.", "nav": {"goliaths": "Goliaths", "goliathsDescription": "Trade avec David sur Goliaths", "chatAi": "Chat IA", "chatAiDescription": "Ton coach virtuel pour apprendre et exceller dans le coaching certifié Paradox.", "testDisc": "Test DISC", "testDiscDescription": "Découvrez qui tu es à travers ton profil comportemental, ton rapport à l'argent et bien plus.", "cic": "Calculateur d'intérêts composés", "cicDescription": "Simule la croissance de ton épargne avec les intérêts composés.", "ff": "Cal<PERSON><PERSON> <PERSON> Libert<PERSON>", "ffDescription": "Planifie ton chemin vers l'indépendance financière avec des calculs personnalisés.", "practice": "Pratique", "practiceDescription": "Entraîne-toi à des scénarios de coaching en groupe."}, "requiredCourse": {"psychologyOfMoney": "Psychologie de l'argent", "pxs": "Paradox School"}}, "product": {"modal": {"title": "Détails du produit", "details-title": "Détails", "payments-button": "Mes paiements", "community-button": "Communauté", "courses-title": "Cours", "course-item": "Cours {id}", "access-course": "Accéder au cours", "access-all-courses": "Accéder à tous les cours", "default-description": "Paradox est un projet fondé par <PERSON>, entrepreneur et conférencier international, destiné à aider chacun à transformer ses rêves en objectifs concrets. Paradox offre des formations, des outils et des expériences immersives pour renforcer la confiance en soi et atteindre des objectifs ambitieux. <PERSON><PERSON><PERSON><PERSON> à son approche dynamique, <PERSON> a déjà impacté des milliers de vies à travers le monde, incitant chacun à oser et à avancer vers une vie en accord avec ses aspirations profondes. Tu as choisi cette formation : prépare-toi, elle changera ta vie."}}, "goliaths": {"radial-chart": {"base-protective": "BASE PROTECTRICE", "controlled-growth": "CROISSANCE CONTRÔLÉE", "ambitious-returns": "RENDEMENTS AMBITIEUX"}, "followers": "Abonnés", "following": "Abonnements", "trades": "Trades", "giftTitle": "un cadeau pour toi", "giftGreeting": "Hello {name},", "giftLine1": "En tant que membre, on a un <strong>cadeau</strong> pour toi…", "giftLine2": "Pour marquer le lancement de notre nouveau programme", "giftProgramName": "Psychologie de l’Argent", "giftLine2Suffix": ", Paradox s’allie à Goliaths afin de t’offrir :", "giftList1": "L'accès à l'espace Paradox <strong>dans l’application Goliaths</strong>, pour investir aux côtés de <PERSON> et de la communauté <strong>à partir de 2€</strong>.", "giftList2": "{discount}% de réduction sur ton abonnement Goliaths grâce à ton code unique", "giftExpiration": "Ce coupon est valable pour une durée limitée.", "giftCTA": "<strong>Clique sur le bouton</strong> ci-dessous et suis les instructions pour créer ton compte Goliaths et réaliser <strong>tes premiers investissements</strong> !", "invest-with-david": "É<PERSON>ris ta propre histoire", "showCode": "Afficher le code", "hideCode": "Cacher le code", "products": "Les produits Paradox", "products-description": "Ces produits ont été réalisés par des experts de la finance de marchés après de nombreuses analyses réalisées.", "strategy": "La stratégie Paradox", "strategy-description": "La stratégie Barbell permet de conserver un niveau de risque maîtriser sans se fermer aux opportunités.", "giftCodeCopied": "Code de réduction copié !", "giftCodeCopiedDescription": "Tu peux le coller lors de ton inscription.", "modal": {"errors": {"already_registered": "Tu as sûrement déjà un compte Goliaths. Rendez-vous sur l'application pour trader ave<PERSON> <PERSON>.", "phone_number_already_exists": "Ce numéro de téléphone est déjà utilisé par un autre utilisateur, veuillez en utiliser un autre.", "email_already_exists": "Cette adresse email est déjà utilisée par un autre utilisateur, veuillez en utiliser une autre.", "too_many_requests": "Nous recevons beaucoup de demandes, veuillez réessayer dans quelques instants !", "error": "Erreur lors de l'envoi du formulaire, veuillez réessayer dans quelques instants."}, "success": "Ren<PERSON>-vous sur l'application pour continuer l'inscription", "title": "Rejoin<PERSON> <PERSON> Goliaths", "create-account": "C<PERSON>ez votre compte en quelques secondes pour commencer à investir aux côté de David 🚀", "email": "Email", "phone": "Téléphone", "submit": "<PERSON><PERSON><PERSON> mon compte", "risk": "Investir comporte des risques", "is-pom": "En tant qu'étudiant du Programme Psychologie de l'argent, tu bénéficies d'un accès spécial à tarif réduit à l'espace Membre Paradox dans Goliaths"}}, "whats-new": {"page-title": "Paradox App - <PERSON><PERSON><PERSON> de <PERSON> ?", "title": "Quoi de neuf ?", "no-entries": "Aucune mise à jour disponible pour le moment.", "reactions": {"sign-in-required": "Veuillez vous connecter pour réagir à cette mise à jour", "be-first": "Sois le premier à réagir avec {emoji}", "count": "{count} {count, plural, one {personne a} other {personnes ont}} réagi avec {emoji}", "add-reaction": "Ajouter une réaction"}, "admin": {"add-version": "Ajouter une version", "edit-version": "Modifier la version", "create-version": "<PERSON><PERSON><PERSON> une version", "update-version": "Mettre à jour la version", "confirm-delete": "Êtes-vous sûr de vouloir supprimer cette version ?", "creating": "Création en cours...", "updating": "Mise à jour en cours...", "cancel": "Annuler", "next": "Suivant", "previous": "Précédent", "required-fields": "Tous les champs marqués d'un * sont obligatoires.", "export-image": "Exporter en image", "media-management": "Gestion des images", "upload-new-media": "Télécharger une nouvelle image", "browse-media": "Parcourir les images", "media-upload-note": "Téléchargez des images pour la galerie d'images à utiliser dans la section \"Quoi de neuf ?\".", "media-gallery": "Galerie d'images", "no-media": "Aucune image trouvée", "copy-url": "Copier l'URL", "delete-media": "Supprimer l'image", "confirm-media-delete": "Êtes-vous sûr de vouloir supprimer cette image ?", "url-copied": "URL copiée dans le presse-papiers", "url-copied-description": "L'URL a été copiée dans le presse-papiers", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "steps": {"version-info": "Infos de version", "english-content": "<PERSON><PERSON><PERSON> anglais", "french-content": "Contenu français", "preview": "<PERSON><PERSON><PERSON><PERSON>"}, "date": "Date de sortie", "version-code": "Code de version", "fields": {"version-code": "Code de version", "version-code-placeholder": "ex: 1.0.0", "release-date": "Date de sortie", "title-en": "Titre (Anglais)", "title-en-placeholder": "Entrez le titre en anglais", "description-en": "Description (Anglais)", "description-en-placeholder": "Entrez la description en anglais", "title-fr": "<PERSON><PERSON><PERSON> (Français)", "description-fr": "Description (Français)", "thumbnails": "Images miniatures"}, "features": {"added": "Fonctionnalités ajoutées (Anglais)", "changed": "Fonctionnalités modifiées (Anglais)", "fixed": "Problèmes corrigés (Anglais)", "removed": "Fonctionnalités supprimées (Anglais)", "placeholder": "Tapez une fonctionnalité et appuyez sur Entrée ou cliquez sur +", "thumbnails": "Images miniatures", "added-fr": "Fonctionnalités ajoutées (Français)", "changed-fr": "Fonctionnalités modifiées (Français)", "fixed-fr": "Problèmes corrigés (Français)", "removed-fr": "Fonctionnalités supprimées (Français)"}, "drag-images": "Glissez-déposez des images ici", "click-to-upload": "ou cliquez pour télécharger", "uploading": "Téléchargement des images...", "paste-image": "ou collez depuis le presse-papiers", "success": {"created": "Version créée avec succès", "created-description": "La version a été créée avec succès", "updated": "Version mise à jour avec succès", "updated-description": "La version a été mise à jour avec succès", "deleted": "Version supprimée avec succès", "deleted-description": "La version a été supprimée avec succès", "uploaded": "Images téléchargées avec succès", "uploaded-description": "Les images ont été téléchargées avec succès", "media-deleted": "Image supprimée avec succès", "media-deleted-description": "L'image a été supprimée avec succès"}, "error": {"create": "Échec de la création de la version", "create-description": "Une erreur est survenue lors de la création de la version", "update": "Échec de la mise à jour de la version", "update-description": "Une erreur est survenue lors de la mise à jour de la version", "delete": "Échec de la suppression de la version", "delete-description": "Une erreur est survenue lors de la suppression de la version", "upload": "Échec du téléchargement des images", "upload-description": "Une erreur est survenue lors du téléchargement des images", "media-delete": "Échec de la suppression de l'image"}}, "changelog": {"added": "<PERSON><PERSON><PERSON>", "changed": "<PERSON><PERSON><PERSON><PERSON>", "fixed": "Corrigé", "removed": "Supprimé"}}, "payments": {"title": "Mes paiements", "sub-title": "Gérer vos paiements", "loading": "Chargement...", "table": {"title": "Paiements ({count})", "name": "Nom", "status": {"title": "Statut", "options": {"paid": "<PERSON><PERSON>", "active": "Actif", "stopped": "<PERSON><PERSON><PERSON><PERSON>", "paused": "En pause", "refunded": "Re<PERSON><PERSON><PERSON>", "overdue": "En retard"}}, "started-at": "Date de début", "actions": {"header": "Voir les détails", "view-details": "Voir le détails"}}, "list": {"title": "Paiements ({count})", "loading": "Chargement...", "status": {"title": "Statut", "options": {"paid": "<PERSON><PERSON>", "active": "Actif", "stopped": "<PERSON><PERSON><PERSON><PERSON>", "paused": "En pause", "refunded": "Re<PERSON><PERSON><PERSON>", "overdue": "En retard"}}, "started-at": "Date de début", "no-more-results": "Plus de résultats"}, "details": {"title": "Paiement {id}", "plural-title": "Paiements", "started-at": "Date de début", "next-payment": "Prochain paiement", "no-next-payment": "Aucun paiement restant", "paid-amount": "<PERSON><PERSON> payé", "total-amount": "Montant total", "payment-method": "Méthode de paiement", "remaining-billing-cycles": "Cycles de facturation restants", "download-all": "Télécharger tous", "information": "Information", "invoices": {"title": "Factures ({count})", "table": {"number": "ID", "status": "Statut", "amount": "<PERSON><PERSON>", "total": "Total", "date": "Date", "download": "Télécharger"}, "list": {"title": "Factures ({count})", "title-number": "Facture# {id}", "loading": "Chargement...", "no-more-results": "Plus de résultats", "amount": "<PERSON><PERSON>", "total": "Total", "status": "Statut", "date": "Date"}, "filters": {"year": "<PERSON><PERSON>", "year-placeholder": "Sélectionner une année", "all-years": "Tous"}, "status": {"paid": "<PERSON><PERSON>", "active": "Actif", "stopped": "<PERSON><PERSON><PERSON><PERSON>", "paused": "En pause", "refunded": "Re<PERSON><PERSON><PERSON>", "overdue": "En retard"}}, "errors": {"no-results": "Aucune facture trouvée"}}, "errors": {"no-results": "Aucun paiement trouvé"}}, "triad": {"group": "Groupe {letter}", "list": {"at": "à", "title": "Titre:", "duration": "Durée:", "duration_value": "{hours}h", "participants": "Participants:", "no_participants": "-", "access": "Accès:", "meeting_link": "Lien de la réunion", "delete": "<PERSON><PERSON><PERSON><PERSON>", "leave": "<PERSON><PERSON><PERSON>", "full": "Complet", "join": "Rejoindre", "loading": "Chargement..."}, "table": {"title": "Titre", "availability": "Disponibilité", "session_time": "<PERSON><PERSON> de la session", "duration": "<PERSON><PERSON><PERSON>", "duration_value": "{hours}h", "session_type": "Type", "sessions": "Sessions", "no_session_restriction": "-", "participant2": "Participant 2", "participant3": "Participant 3", "access": "Accès", "meeting_link": "Lien de la réunion", "no_triads": "Aucun groupe pour le moment.", "delete": "<PERSON><PERSON><PERSON><PERSON>", "leave": "<PERSON><PERSON><PERSON>", "full": "Complet", "join": "Rejoindre", "loading": "Chargement...", "success": {"deleted": "Groupe supprimé avec succès", "left": "<PERSON> as quitté le groupe", "joined": "Tu as rejoint le groupe"}, "errors": {"failed_to_delete": "Échec de la suppression du groupe", "failed_to_join": "Échec de la jointure du groupe", "failed_to_leave": "Échec de la quitture du groupe", "unauthorized": "Tu dois être connecté pour effectuer cette action", "forbidden": "Tu n'as pas les permissions pour effectuer cette action", "not_found": "Le groupe demandé n'a pas été trouvé", "cannot_join_own_triad": "Tu ne peux pas rejoindre ton propre groupe", "already_joined": "<PERSON> as d<PERSON><PERSON><PERSON> rejoint ce groupe", "triad_full": "Ce groupe est déjà complet"}, "participants": "Participants", "no_participants": "Aucun participant"}, "view": {"loading": "Chargement des groupes...", "count": "GROUPES", "description": "<PERSON><PERSON><PERSON> ou rejoins un groupe pour pratiquer ensemble", "create": "Créer un nouveau groupe", "search": "<PERSON><PERSON><PERSON>", "filters": {"period": "Période", "status": "Places disponibles", "status_placeholder": "Rechercher...", "registered": "Statut de réservation", "registered_placeholder": "Rechercher...", "all": "Tous", "status_options": {"8slots": "8 places", "7slots": "7 places", "6slots": "6 places", "5slots": "5 places", "4slots": "4 places", "3slots": "3 places", "2slotsandmore": "2 places et plus", "2slots": "2 places", "1slot": "1 place", "full": "Complet"}, "registered_options": {"registered": "Mes places", "not_registered": "Places disponibles"}, "time_window": "Période", "time_window_placeholder": "Tranche horaire", "time_window_start": "<PERSON><PERSON> d<PERSON>", "time_window_end": "Heure de fin", "time_window_error": "L'heure de fin doit être après l'heure de début"}}, "errors": {"unauthorized": "Tu dois être connecté pour effectuer cette action", "forbidden": "Tu n'as pas les permissions pour effectuer cette action", "not_found": "Le groupe demandé n'a pas été trouvé", "fetch": "Échec de la récupération des groupes. Veuillez réessayer plus tard.", "already_joined": "<PERSON> as d<PERSON><PERSON><PERSON> rejoint ce groupe", "not_participant": "Tu n'es pas un participant dans ce groupe", "group_full": "Ce groupe est déjà complet", "invalid_meeting_link": "Format de lien de réunion invalide. Doit être un lien valide Google Meet ou Zoom", "missing_fields": "Veuillez fournir tous les champs requis", "internal_error": "Une erreur inattendue est survenue. Veuillez réessayer plus tard", "failed_to_delete": "Échec de la suppression du groupe", "failed_to_join": "Échec de la jointure du groupe", "failed_to_leave": "Échec de la quitture du groupe", "cannot_join_own_group": "Tu ne peux pas rejoindre ton propre groupe", "date_in_past": "Ne peut pas créer un groupe dans le passé", "failed_to_create": "Échec de la création du groupe", "too_many_open_triads": "Tu as atteint le nombre maximum de groupes que tu peux créer pour le moment. Veuillez réessayer plus tard.", "session_too_far_in_future": "Ne peut pas planifier des sessions plus de {days} jours à l'avance", "too_many_open_triads_with_limit": "Tu ne peux avoir que {max} groupe(s) ouvert(s) dans les {days} prochains jours, sauf pour les groupes de 2 personnes (actuellement {current}). Rejoins d'autres groupes pour en créer plus", "too_many_open_triads_weekly": "Tu as atteint la limite hebdomadaire de groupes que tu peux organiser pour cette semaine. Rejoins d'autres groupes pour en créer plus.", "no_access": "Tu n'as pas accès à ce type de session", "no_access_short": "<PERSON><PERSON> d'a<PERSON>", "unknown": "Une erreur inattendue est survenue", "no_triads": "Aucun groupe pour le moment."}, "filters": {"period": "Période", "status": {"label": "Places disponibles", "placeholder": "Rechercher...", "options": {"2_slots_and_more": "2 places et plus", "1_slot": "1 place", "full": "Complet"}}, "session_time": {"label": "Période", "placeholder": "Sélectionner la période"}, "registered": "Statut de réservation", "registered_placeholder": "Rechercher...", "all": "Tous", "registered_options": {"registered": "Mes places", "not_registered": "Places disponibles"}}, "success": {"deleted": "Groupe supprimé avec succès", "joined": "Tu as rejoint le groupe", "left": "<PERSON> as quitté le groupe", "created": "Groupe créé avec succès"}, "session": "Session", "sessions": "Sessions", "restricted_to": "Restreint à", "no_session_restriction": "-", "session_badge": "{session}", "confirm_join": {"title": "Confirmation d'inscription au groupe", "description": "Tu as déjà une session à venir le {date}. Es-tu sûr de vouloir rejoindre un autre groupe ?", "confirm": "Rejoindre quand même", "cancel": "Annuler"}, "confirm_leave": {"title": "Confirmation de désinscription du groupe", "description": "Es-tu sûr de vouloir quitter ce groupe ?", "note": "Veuillez noter que ta place sera disponible pour d'autres participants une fois que tu auras quitté le groupe.", "confirm": "<PERSON><PERSON><PERSON> le groupe", "cancel": "Annuler"}, "confirm_delete": {"title": "Confirmation de suppression du groupe", "description": "Ce groupe a {count} participant(s) inscrit(s). Es-tu sûr de vouloir le supprimer ?", "note": "Cette action ne peut pas être annulée. Veuillez noter que les autres participants ne seront pas notifiés de cette suppression.", "confirm": "Supprimer le groupe", "cancel": "Annuler"}, "stats": {"title": "Statistiques des Trinômes", "description": "Statistiques détaillées des groupes et des sessions", "export": "Exporter", "tabs": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "engagement": "Engagement", "growth": "Croissance", "organizers": "Organisateurs"}, "time_range": {"label": "Période", "placeholder": "Sélectionner la période", "pick_date": "Sélectionner une date", "options": {"day": "Journalier", "week": "Hebdomadaire", "month": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>"}}, "overview": {"loading": "Chargement des statistiques...", "error": "Erreur lors du chargement des statistiques", "metrics": {"total_triads": "Total de Trinômes", "total_participants": "Total de participants", "avg_participants": "Moyenne de participants/Trinôme", "completion_rate": "<PERSON><PERSON> de comp<PERSON>"}, "charts": {"growth_trends": "Tendances de croissance", "participant_growth": "Croissance des participants", "triads_created": "<PERSON><PERSON><PERSON><PERSON>", "participants_joined": "Participants rejoints", "time_period": "<PERSON><PERSON><PERSON> {period}"}}, "engagement": {"loading": "Chargement des statistiques...", "error": "Erreur lors du chargement des statistiques", "recurring_participants": {"title": "Participants récurrents", "description": "Participants qui ont rejoint plusieurs sessions de trinômes"}, "session_duration": {"distribution": {"title": "Distribution de la durée des sessions", "by_hours": "Par heures", "sessions": "Nombre de sessions"}, "breakdown": {"title": "Répartition de la durée des sessions", "distribution": "Répartition", "hours": "{hours}h", "sessions": "Nombre de sessions", "description": "{count} sessions avec une durée de {hours}"}}}, "growth": {"loading": "Chargement des statistiques...", "error": "Erreur lors du chargement des statistiques", "charts": {"cumulative_growth": {"title": "Croissance cumulée", "total_triads": "Total de Trinômes", "time_period": "<PERSON><PERSON><PERSON> {period}"}, "monthly_growth": {"title": "Taux de croissance mensuel", "subtitle": "<PERSON><PERSON> sur mois", "new_triads": "Nouveaux Trinômes", "growth_rate": "<PERSON>x de croissance (%)"}, "comparison": {"title": "Trinômes vs Participants", "subtitle": "Comparaison", "new_triads": "Nouveaux Trinômes", "new_participants": "Nouveaux Participants"}}}, "time_periods": {"day": "Jour", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON> {period}"}, "organizers": {"loading": "Chargement des statistiques...", "error": "Erreur lors du chargement des statistiques", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "top_organizers": {"title": "Top Organisateurs", "triads_organized": "Trinômes organisés"}, "leaderboard": {"title": "Classement des organisateurs", "columns": {"rank": "<PERSON>ng", "organizer": "Organisateur", "triads": "Trinômes organisés", "contribution": "Contribution %"}}}}, "filter": {"description": "Filtrer les trinômes"}, "create": {"title": "NOUVEAU GROUPE", "select_type": "SÉLECTIONNER LE TYPE DE GROUPE", "date": "Date:", "time": "Heure:", "duration": "Du<PERSON>e (en heures):", "access": "Accès:", "sessions": "Sessions", "title_field": "Titre:", "max_participants": "Nombre maximum de participants:", "placeholder": {"link": "Google Meet, Zoom, etc.", "sessions": "Rechercher les sessions...", "title": "Entrer un titre pour votre groupe", "max_participants": "Sélectionner le nombre de participants (max 8)"}, "cancel": "Annuler", "back": "Retour", "creating": "Création...", "create": "<PERSON><PERSON><PERSON>", "celebration": {"title": "Groupe créé avec succès!", "description": "Votre groupe a été créé et est maintenant prêt pour les participants à rejoindre!", "loading": "Nous cherchons à quel point tu es bon pour organiser des groupes..."}, "stats": {"total_triads": "Groupe créés", "total_participants": "Total de participants"}}, "leave": {"celebration": {"title": "Groupe quitté avec succès!", "description": "<PERSON> as quitté le groupe et n'es plus un participant."}}, "join": {"celebration": {"title": "Groupe rejoint avec succès!", "description": "Tu as rejoint le groupe et es maintenant un participant.", "loading": "Nous cherchons à quel point tu es bon pour rejoindre des groupes..."}}, "delete": {"celebration": {"title": "Groupe supprimé avec succès!", "description": "Votre groupe a été supprimé et n'est plus disponible."}}, "date_range_picker": {"select_date": "Sélectionner la période", "apply": "Appliquer", "clear": "<PERSON><PERSON><PERSON><PERSON>"}, "time_picker": {"select_time": "Sélectionner l'heure", "hours": "<PERSON><PERSON>", "minutes": "Minutes", "local_time": "Heure locale"}, "time_range_picker": {"start_time": "<PERSON><PERSON> d<PERSON>", "end_time": "Heure de fin", "select_start": "Sélectionner l'heure de début", "select_end": "Sélectionner l'heure de fin", "apply": "Appliquer", "clear": "<PERSON><PERSON><PERSON><PERSON>"}, "testimonial": {"title": "Partagez votre avis", "rating": "Comment aimerais-tu évaluer ton expérience?", "content": "Veux-tu partager plus? (optionnel)", "content_placeholder": "Dis-nous ce que tu en penses...", "submit": "Soumettre ton avis", "submitting": "Soumission...", "cancel": "Annuler", "success": "Merci pour ton avis!", "error": "Échec de la soumission de l'avis. Veuillez réessayer."}, "filter-bar": {"title": "FILTRES", "button": "Filtres", "reset": "Réinitialiser", "apply": "Appliquer"}, "availability-badge": {"full": "Complet", "one-spot": "1 place", "spots": "{count} places"}}, "date_range_picker": {"select_date": "Sélectionner la période", "apply": "Appliquer", "clear": "<PERSON><PERSON><PERSON><PERSON>"}, "time_picker": {"select_time": "Sélectionner l'heure", "hours": "<PERSON><PERSON>", "minutes": "Minutes", "local_time": "Heure locale"}, "time_range_picker": {"start_time": "<PERSON><PERSON> d<PERSON>", "end_time": "Heure de fin", "select_start": "Sélectionner l'heure de début", "select_end": "Sélectionner l'heure de fin", "apply": "Appliquer", "clear": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"close": "<PERSON><PERSON><PERSON>"}}