"use client";

import { Avatar as ShadcnAvatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import type { AvatarProps as ShadcnAvatarProps } from "@radix-ui/react-avatar";

export interface AvatarProps extends ShadcnAvatarProps {
  src?: string;
  alt?: string;
  fallback?: string;
}

export const Avatar = ({ src, alt, fallback, ...props }: AvatarProps) => {
  return (
    <ShadcnAvatar {...props}>
      <AvatarImage src={src} alt={alt || "Avatar utilisateur"} />
      <AvatarFallback>{fallback}</AvatarFallback>
    </ShadcnAvatar>
  );
};

Avatar.displayName = "Avatar";
