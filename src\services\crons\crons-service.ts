import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { writeFileSync } from 'fs';
import path from 'path';
import {
  CronLogRefPrefix,
  CronLogStatus,
  CronLogType,
  SubscriptionOrderStatus,
} from '@enums';
import { ChargebeeBillingService } from '@services/billing';
import {
  CronLogUseCases,
  CustomerAccessUseCases,
  UserUseCases,
  SubscriptionUseCases,
  WebhookUseCases,
} from '@useCases';
import { ConfigService } from '@config';
import { SlackService } from '@services/notification';
import { Subscription } from '@entities';
import { CronLogResult, ExhaustedCronJob } from '@types';
import { CustomerAccessStatus } from '@px-shared-account/hermes';

const inLocal = !['staging', 'production'].includes(process.env.NODE_ENV);

@Injectable()
export class CronsService {
  private readonly PRODUCT_DELIVERY_ENABLED: boolean;
  private readonly TEMP_DISABLE_ACCESS_SUSPENSION: boolean;
  private readonly SLACK_ERRORS_CHANNEL: string;
  private readonly MAX_RETRIES = 2;

  /**
   * Constructor for CronsService
   * @param cronLogUseCases Use cases for cron logs
   * @param chargebeeService Service for Chargebee billing
   * @param subscriptionService Service for subscriptions
   * @param customerAccessUseCases Use cases for customer access
   * @param customerUseCases Use cases for customers
   * @param webhookUseCases Use cases for webhooks
   * @param slackService Service for Slack notifications
   */
  constructor(
    private readonly configService: ConfigService,
    private readonly cronLogUseCases: CronLogUseCases,
    private readonly chargebeeService: ChargebeeBillingService,
    private readonly subscriptionService: SubscriptionUseCases,
    private readonly customerAccessUseCases: CustomerAccessUseCases,
    private readonly userUseCases: UserUseCases,
    private readonly webhookUseCases: WebhookUseCases,
    private readonly slackService: SlackService,
  ) {
    this.PRODUCT_DELIVERY_ENABLED =
      configService.appSecrets.PRODUCT_DELIVERY_ENABLED;
    this.SLACK_ERRORS_CHANNEL =
      configService.notificationSecrets.SLACK_ERRORS_CHANNEL;
    this.TEMP_DISABLE_ACCESS_SUSPENSION =
      configService.appSecrets.TEMP_DISABLE_ACCESS_SUSPENSION;
  }

  /**
   * Cron job to check for overdue invoices
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
    disabled: inLocal,
  })
  async overdueInvoicesCron() {
    const overdueInvoices = await this.chargebeeService.getOverdueInvoices();
    const cronLogType = CronLogType.OVERDUE_INVOICE_CHECK;

    for (const { invoice } of overdueInvoices) {
      const logId = `${CronLogRefPrefix.OVERDUE_INVOICE}${invoice?.id}`;
      await this.processCronJob(cronLogType, invoice, logId);
    }
  }

  /**
   * Cron job to check for access suspensions
   */
  @Cron(CronExpression.EVERY_DAY_AT_10AM, {
    disabled: inLocal,
  })
  async accessSuspensionCron() {
    if (!this.PRODUCT_DELIVERY_ENABLED) return;
    const cronLogType = CronLogType.ACCESS_SUSPENSION_CHECK;
    const queryResult = await this.subscriptionService.searchAll(
      null,
      null,
      null,
      SubscriptionOrderStatus.OVERDUE,
    );
    const overdueSubscriptions = queryResult?.items;
    if (!overdueSubscriptions) return;

    if (this.TEMP_DISABLE_ACCESS_SUSPENSION) {
      await this.notifySlackAboutAccessSuspension(overdueSubscriptions);
      return;
    }

    const exhaustedJobs: ExhaustedCronJob[] = [];
    for (const subscription of overdueSubscriptions) {
      const { id: subscriptionId, chargebeeId } = subscription;
      const logId = `${CronLogRefPrefix.ACCESS_SUSPENSION_CHECK}-${chargebeeId}`;
      const canBeSuspended = await this.subscriptionService.canBeSuspended(
        subscription,
      );
      if (!canBeSuspended) {
        continue;
      }

      const { result, isExhausted, slackNotified } = await this.processCronJob(
        cronLogType,
        subscriptionId,
        logId,
      );
      if (isExhausted && !slackNotified) {
        exhaustedJobs.push({
          type: cronLogType,
          logId,
          result,
        });
      }
    }
    if (exhaustedJobs.length > 0) {
      await this.notifySlackAboutExhaustedCrons(cronLogType, exhaustedJobs);
      await Promise.all(
        exhaustedJobs?.map(({ logId }) => {
          return this.cronLogUseCases.update(logId, {
            slackNotified: true,
          });
        }),
      );
    }
  }

  /**
   * Cron job to reprocess failed customer accesses
   */
  @Cron(CronExpression.EVERY_10_MINUTES, {
    disabled: inLocal,
  })
  async reProcessFailedCustomerAccesses() {
    if (!this.PRODUCT_DELIVERY_ENABLED) return;
    const { items } = await this.customerAccessUseCases.filterByStatus(
      CustomerAccessStatus.ERROR,
    );
    const cronLogType = CronLogType.ERRORED_CUSTOMER_ACCESS_CHECK;
    const exhaustedJobs: ExhaustedCronJob[] = [];
    for (const access of items) {
      const { id: customerAccessId } = access;
      const logId = `${CronLogRefPrefix.ERRORED_CUSTOMER_ACCESS}-${customerAccessId}`;
      const linkedCustomer = await this.userUseCases.getById(access.customerId);
      const { result, isExhausted, slackNotified } = await this.processCronJob(
        cronLogType,
        {
          customerAccess: access,
          customerEmail: linkedCustomer?.email,
          customerName: `${linkedCustomer?.firstName} ${linkedCustomer?.lastName}`,
        },
        logId,
      );
      if (isExhausted && !slackNotified) {
        exhaustedJobs.push({
          type: cronLogType,
          logId,
          result,
        });
      }
    }
    if (exhaustedJobs.length > 0) {
      await this.notifySlackAboutExhaustedCrons(cronLogType, exhaustedJobs);
      await Promise.all(
        exhaustedJobs?.map(({ logId }) => {
          return this.cronLogUseCases.update(logId, {
            slackNotified: true,
          });
        }),
      );
    }
  }

  /**
   * Cron job to reprocess failed HubSpot syncs
   */
  @Cron(CronExpression.EVERY_10_MINUTES, {
    disabled: inLocal,
  })
  async reProcessFailedHubspotSyncs() {
    const failedHSLogs = await this.cronLogUseCases.getAllFailedHubspotSyncs();
    const cronLogType = CronLogType.FAILED_HUBSPOT_SYNC;
    const exhaustedJobs: ExhaustedCronJob[] = [];
    for (const log of failedHSLogs) {
      const input = log?.result?.input;
      const { subscription, customer } = input;
      const logId = `${CronLogRefPrefix.HS_SYNC}${subscription?.id}`;
      const { result, isExhausted, slackNotified } = await this.processCronJob(
        cronLogType,
        {
          subscription,
          customer,
        },
        logId,
      );
      if (isExhausted && !slackNotified) {
        exhaustedJobs.push({
          type: cronLogType,
          logId,
          result,
        });
      }
      if (exhaustedJobs.length > 0) {
        await this.notifySlackAboutExhaustedCrons(cronLogType, exhaustedJobs);
        await Promise.all(
          exhaustedJobs?.map(({ logId }) => {
            return this.cronLogUseCases.update(logId, {
              slackNotified: true,
            });
          }),
        );
      }
    }
  }

  /**
   * Cron job to fetch exchange rates
   */
  @Cron(CronExpression.EVERY_DAY_AT_1AM, {
    disabled: inLocal,
  })
  async exchangeRateCron() {
    const cronLogType = CronLogType.EXCHANGE_RATE_CRAWLER;
    const logId = `${
    CronLogRefPrefix.EXCHANGE_RATE
      }${new Date().toISOString()}`;
    await this.processCronJob(cronLogType, null, logId);
  }

  /**
   * Cron job to process failed webhooks every 2 minutes
   */
  @Cron('0 */2 * * * *', {
    disabled: inLocal,
  })
  async failedWebhooksCron() {
    const cronLogType = CronLogType.FAILED_WEBHOOKS;
    const { items, total } = await this.webhookUseCases.getFailedWebhooks();
    if (total === 0) {
      return;
    }
    const exhaustedJobs: ExhaustedCronJob[] = [];
    for (const webhook of items) {
      const logId = `${CronLogRefPrefix.FAILED_WEBHOOKS}${webhook.id}`;
      const sender = webhook.sender;
      if (sender !== 'chargebee') {
        continue;
      }
      const webhookId = webhook.senderWebhookId;
      const webhookData = await this.chargebeeService.getWebhookPayload(
        webhookId,
      );
      const jobInput = { sender, data: webhookData, id: webhookId };
      const { result, isExhausted, slackNotified } = await this.processCronJob(
        cronLogType,
        jobInput,
        logId,
      );
      if (isExhausted && !slackNotified) {
        exhaustedJobs.push({
          type: cronLogType,
          logId,
          result,
        });
      }
    }
    if (exhaustedJobs.length > 0) {
      await this.notifySlackAboutExhaustedCrons(cronLogType, exhaustedJobs);
      await Promise.all(
        exhaustedJobs?.map(({ logId }) => {
          return this.cronLogUseCases.update(logId, {
            slackNotified: true,
          });
        }),
      );
    }
  }

  /**
   * Notifies Slack about access suspensions
   * @param overdueSubscriptions Subscriptions whose accesses need to be revoked
   */
  async notifySlackAboutAccessSuspension(overdueSubscriptions: Subscription[]) {
    const subscriptionLinksWithDueAmount = overdueSubscriptions
      .map((subscription) => {
        return `https://os.paradoxgroup.com/dashboard/subscriptions/${subscription.chargebeeId}, ${subscription.totalDues}`;
      })
      .join('\n');
    const message = `Overdue Subscriptions whose accesses need to be revoked`;
    const headers = 'link,total dues\n';
    const csvContent = headers + subscriptionLinksWithDueAmount;
    const fileName = `overdue_subscriptions_${new Date().toISOString()}.csv`;
    const filePath = path.join(process.env.HOME || '/root', fileName);
    writeFileSync(filePath, csvContent, 'utf8');
    await this.slackService.sendMessageWithAttachment(
      this.SLACK_ERRORS_CHANNEL,
      message,
      filePath,
      fileName,
      'csv',
    );
  }

  /**
   * Processes a cron job
   * @param cronLogType Type of cron log
   * @param jobInput Input for the cron job
   * @param logId ID of the cron log
   */
  async processCronJob(
    cronLogType: CronLogType,
    jobInput: any,
    logId: string,
  ): Promise<{
    result: CronLogResult;
    isExhausted: boolean;
    slackNotified: boolean;
  }> {
    const existingLog = await this.cronLogUseCases.getOne(logId);
    const retryCount = existingLog?.retryCount || 0;
    if (this.maxRetriesExceeded(retryCount)) {
      return {
        result: existingLog?.result,
        isExhausted: true,
        slackNotified: existingLog?.slackNotified || false,
      };
    }

    const input = jobInput || existingLog?.result?.input;
    let result: CronLogResult;
    if (existingLog) {
      if (existingLog?.status === CronLogStatus.SUCCESS) return;
      result = await this.cronLogUseCases.delegateToProcessor(
        cronLogType,
        input,
        existingLog.id,
        retryCount,
      );
      return { result, isExhausted: false, slackNotified: false };
    }
    result = await this.cronLogUseCases.delegateToProcessor(
      cronLogType,
      jobInput,
      logId,
      retryCount,
    );
    return { result, isExhausted: false, slackNotified: false };
  }

  /**
   * Checks if the retry count has exceeded the maximum allowed retries
   * @param retryCount Retry count for the cron job
   * @returns True if the retry count has exceeded the maximum allowed retries, false otherwise
   */
  maxRetriesExceeded(retryCount: number): boolean {
    return retryCount >= this.MAX_RETRIES;
  }

  /**
   * Notifies Slack about exhausted cron jobs
   * @param type Type of cron log
   * @param exhaustedJobs List of exhausted cron jobs
   */
  async notifySlackAboutExhaustedCrons(
    type: CronLogType,
    exhaustedJobs: ExhaustedCronJob[] = [],
  ): Promise<void> {
    const snippetContent = JSON.stringify(exhaustedJobs, null, 2);
    const message = `List of exhausted cron jobs for \`${type}\``;
    const fileName = `exhausted_cron_jobs_${new Date().toISOString()}.json`;
    const filePath = path.join(process.env.HOME || '/root', fileName);
    writeFileSync(filePath, snippetContent, 'utf8');
    await this.slackService.sendMessageWithAttachment(
      this.SLACK_ERRORS_CHANNEL,
      message,
      filePath,
      fileName,
      'text',
    );
  }
}
