import { CalendarEvent } from "@/hooks/store/calendar";
import createEvent from "@/lib/calendar/createEvent";

/**
 * Mock data for testing filter dependencies and error cases
 * @internal
 */
const mockData = {
  courses: {
    bachelor: [
      { value: "math101", label: "Mathématiques 101" },
      { value: "phys101", label: "Physique 101" },
      { value: "cs101", label: "Informatique 101" },
      { value: "error_course", label: "Cours Erreur" }, // Triggers server error
    ],
    master: [
      { value: "ai501", label: "Intelligence Artificielle" },
      { value: "ml502", label: "Machine Learning" },
    ],
    mba: [
      { value: "fin401", label: "Finance" },
      { value: "mkt402", label: "Marketing" },
    ],
  },
  modules: {
    math101: [
      { value: "algebra", label: "Algèbre" },
      { value: "calculus", label: "Calcul" },
      { value: "error_module", label: "Module Erreur" },
    ],
    ai501: [
      { value: "neural", label: "Réseaux de Neurones" },
      { value: "deep", label: "Deep Learning" },
    ],
  },
  events: {
    algebra: [
      { value: "exam1", label: "Examen Mid-term" },
      { value: "exam2", label: "Examen Final" },
    ],
    neural: [
      { value: "tp1", label: "TP: Classification" },
      { value: "project", label: "Projet Final" },
    ],
  },
};

export const defaultEvents: CalendarEvent[] = [
  createEvent({
    // Part 1 - Event Details
    id: 1,
    type: "mentoring",
    title: "Test Event",
    replay: false,
    recurrence: "one-time",
    description: "This is a test event",
    begin: new Date(),
    end: new Date(new Date().getTime() + 60 * 60 * 1000),
    status: "confirmed",
    // Part 2 - Speakers
    mainSpeaker: "John Doe",
    otherSpeakers: ["Jane Doe", "Alice Doe"],
    technicalSupport: "John Doe",
    // Part 3 - Audience
    cohort: "Bachelor",
    participantMin: 10,
    participantMax: 20,
    program: "Bachelor",
    course: "Mathématiques 101",
    module: "Algèbre",
    // Part 4 - Comments
    comments: "This is a comment",
  }),
  createEvent({
    // Part 1 - Event Details
    id: 2,
    type: "mentoring",
    title: "Tomorrow's Event",
    replay: false,
    recurrence: "one-time",
    description: "This is an event for tomorrow",
    begin: new Date(new Date().setDate(new Date().getDate() + 1)),
    end: new Date(
      new Date(new Date().setDate(new Date().getDate() + 1)).getTime() + 3 * 60 * 60 * 1000,
    ),
    status: "pending",
    // Part 2 - Speakers
    mainSpeaker: "John Doe",
    otherSpeakers: ["Jane Doe", "Alice Doe"],
    technicalSupport: "John Doe",
    // Part 3 - Audience
    cohort: "Bachelor",
    participantMin: 10,
    participantMax: 20,
    program: "Bachelor",
    course: "Mathématiques 101",
    module: "Algèbre",
    // Part 4 - Comments
    comments: "This is a comment",
  }),
  createEvent({
    // Part 1 - Event Details
    id: 3,
    type: "mentoring",
    title: "Previous Event",
    replay: false,
    recurrence: "one-time",
    description: "This is a test event",
    begin: new Date(new Date().setDate(new Date().getDate() - 1)),
    end: new Date(
      new Date(new Date().setDate(new Date().getDate() - 1)).getTime() + 3 * 60 * 60 * 1000,
    ),
    status: "confirmed",
    // Part 2 - Speakers
    mainSpeaker: "John Doe",
    otherSpeakers: ["Jane Doe", "Alice Doe"],
    technicalSupport: "John Doe",
    // Part 3 - Audience
    cohort: "Bachelor",
    participantMin: 10,
    participantMax: 20,
    program: "Bachelor",
    course: "Mathématiques 101",
    module: "Algèbre",
    // Part 4 - Comments
    comments: "This is a comment",
  }),
  createEvent({
    // Part 1 - Event Details
    id: 4,
    type: "mentoring",
    title: "2nd Previous Event",
    replay: false,
    recurrence: "one-time",
    description: "This is a test event",
    begin: new Date(new Date().setDate(new Date().getDate() - 2)),
    end: new Date(
      new Date(new Date().setDate(new Date().getDate() - 2)).getTime() + 3 * 60 * 60 * 1000,
    ),
    status: "confirmed",
    // Part 2 - Speakers
    mainSpeaker: "John Doe",
    otherSpeakers: ["Jane Doe", "Alice Doe"],
    technicalSupport: "John Doe",
    // Part 3 - Audience
    cohort: "Bachelor",
    participantMin: 10,
    participantMax: 20,
    program: "Bachelor",
    course: "Mathématiques 101",
    module: "Algèbre",
    // Part 4 - Comments
    comments: "This is a comment",
  }),
  createEvent({
    // Part 1 - Event Details
    id: 5,
    type: "mentoring",
    title: "3rd Previous Event",
    replay: false,
    recurrence: "one-time",
    description: "This is a test event",
    begin: new Date(new Date().setDate(new Date().getDate() - 3)),
    end: new Date(
      new Date(new Date().setDate(new Date().getDate() - 3)).getTime() + 3 * 60 * 60 * 1000,
    ),
    status: "confirmed",
    // Part 2 - Speakers
    mainSpeaker: "John Doe",
    otherSpeakers: ["Jane Doe", "Alice Doe"],
    technicalSupport: "John Doe",
    // Part 3 - Audience
    cohort: "Bachelor",
    participantMin: 10,
    participantMax: 20,
    program: "Bachelor",
    course: "Mathématiques 101",
    module: "Algèbre",
    // Part 4 - Comments
    comments: "This is a comment",
  }),
];

export default mockData;
