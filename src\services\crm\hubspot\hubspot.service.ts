import { SlackService } from '@services/notification';
import { Client as HubspotClient } from '@hubspot/api-client';
import { AssociationSpecAssociationCategoryEnum } from '@hubspot/api-client/lib/codegen/crm/deals';
import { Customer as ChargeBeeCustomer } from 'chargebee-typescript/lib/resources';
import { SubscriptionItem } from 'chargebee-typescript/lib/resources/subscription';
import NodeCache from 'node-cache';
import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  ChargebeeCustomer,
  RequestMethods,
  ExchangeTokenProof,
  HubspotInvoiceAmountFields,
  OAuthSuccessResponse as OAuthResponse,
  PXActionResult,
} from '@types';
import {
  Product,
  ProductPlan,
  ProductPlanPrice,
  Invoice,
  Subscription,
} from '@entities';
import { HubspotFactoryService } from './hubspot.factory.service';
import { ChargebeeBillingService } from '@services/billing';
import { ChargeBeeDurationPeriodUnit } from '@enums';
import { UpdateSubscriptionDTO } from '@dtos';
import { ConfigService, HubspotSecrets, NotificationSecrets } from '@config';
import { GlobalHelpers } from '@helpers';
import {
  BatchResponsePropertyStatusEnum,
  BatchResponsePropertyWithErrorsStatusEnum,
  PropertyCreate,
  StandardError,
} from '@hubspot/api-client/lib/codegen/crm/properties';

@Injectable()
export class HubspotService {
  private refreshToken: string;
  private readonly oAuthUrl: string;
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly oAuthRedirectUri: string;
  private accessTokenCache: NodeCache;
  private accessTokenCacheKey: string;
  private readonly clientProperties: string[];
  private readonly HUBSPOT_SYNC_ERRORS_CHANNEL: string;
  private readonly secrets: HubspotSecrets;

  constructor(
    private readonly chargebeeService: ChargebeeBillingService,
    private readonly hubspotFactory: HubspotFactoryService,
    private readonly slackService: SlackService,
    private readonly configService: ConfigService,
  ) {
    this.accessTokenCache = new NodeCache({ deleteOnExpire: true });
    this.secrets = this.configService.hubspotSecrets;
    this.refreshToken = this.secrets.HUBSPOT_REFRESH_TOKEN;
    this.oAuthUrl = this.secrets.HUBSPOT_OAUTH_URL;
    this.clientId = this.secrets.HUBSPOT_CLIENT_ID;
    this.clientSecret = this.secrets.HUBSPOT_CLIENT_SECRET;
    this.oAuthRedirectUri = this.secrets.HUBSPOT_REDIRECT_URI;
    this.accessTokenCacheKey = this.secrets.HUBSPOT_ACCESS_TOKEN_CACHE_KEY;
    this.HUBSPOT_SYNC_ERRORS_CHANNEL =
      this.configService.notificationSecrets.HUBSPOT_SYNC_ERRORS_CHANNEL;
    this.clientProperties = [
      'firstname',
      'lastname',
      'email',
      'phone',
      'country',
      'city',
      'address',
      'zip',
      'hs_additional_emails',
      'session_edec',
    ];
  }

  /**
   * Gets the access token from cache if exists, otherwise generates a new one
   * @returns Hubspot access token
   */
  async getAccessToken(): Promise<string> {
    if (!this.accessTokenCache.get(this.accessTokenCacheKey)) {
      await this.refreshAccessToken();
    }
    return this.accessTokenCache.get(this.accessTokenCacheKey);
  }

  /**
   * Gets `access_token` using the `refresh_token` and also caches the new `access_token`
   * @param exchangeProof Object containing necessary data for generating `access_token`
   * @returns Newly generated `access_token`
   */
  async exchangeForTokens(exchangeProof: ExchangeTokenProof): Promise<string> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    const params = new URLSearchParams(exchangeProof);
    const tokens: OAuthResponse = await GlobalHelpers.makeAxiosRequest(
      this.oAuthUrl,
      RequestMethods.POST,
      null,
      headers,
      params,
    );

    this.refreshToken = tokens.refresh_token;
    this.accessTokenCache.set(
      this.accessTokenCacheKey,
      tokens.access_token,
      Math.round(tokens.expires_in * 0.75),
    );

    return tokens.access_token;
  }

  /**
   * Refreshes the `access_token` if it's expired
   * @returns Newly generated `access_token`
   */
  async refreshAccessToken(): Promise<string> {
    const refreshTokenProof: ExchangeTokenProof = {
      grant_type: 'refresh_token',
      client_id: this.clientId,
      client_secret: this.clientSecret,
      redirect_uri: this.oAuthRedirectUri,
      refresh_token: this.refreshToken,
    };
    return await this.exchangeForTokens(refreshTokenProof);
  }

  /**
   * Search contact in Hubspot using a deal ID
   * @param dealId Deal ID to get customer info from
   */
  async getContactFromDeal(
    dealId: string,
  ): Promise<{ total: number; customers: ChargebeeCustomer[] }> {
    const accessToken = await this.getAccessToken();
    const hubspotDealsAPI = new HubspotClient({
      accessToken,
    }).crm.deals;

    try {
      const foundDeal = await hubspotDealsAPI.basicApi.getById(
        dealId,
        [],
        [],
        ['contacts'],
      );
      if (!foundDeal) {
        console.log('Deal was not found in Hubspot');
        throw new NotFoundException('Deal not found');
      }
      const contacts = foundDeal?.associations?.['contacts'];
      if (!contacts) {
        console.log('No contacts associated with the deal');
        return null;
      }
      const contactId = contacts?.results[0]?.id;
      const contactInfo = await this.getHubspotContact(contactId, false);
      const contactFromChargebee =
        await this.chargebeeService.getCustomersByEmail(contactInfo.email);

      if (!contactFromChargebee || contactFromChargebee.length === 0) {
        console.log('Customer not found in Chargebee');
        return { total: 1, customers: [contactInfo] };
      }

      return {
        total: contactFromChargebee.length,
        customers: contactFromChargebee,
      };
    } catch (err) {
      console.log('error: ', err);
      if (err.code === 404) throw new NotFoundException('Deal not found');
    }
  }

  /**
   * Retrieves a Hubspot contact by its ID.
   * @param contactId - The ID of the contact to retrieve.
   * @param idIsEmail - Defines if the `contactId` passed is an email address
   * @returns A Promise that resolves to the retrieved contact.
   */
  async getHubspotContact(
    contactId: string,
    idIsEmail: boolean,
  ): Promise<ChargebeeCustomer> {
    console.log('getting contact for : ', contactId);
    const accessToken = await this.getAccessToken();
    const hubspotContactsAPI = new HubspotClient({
      accessToken,
    }).crm.contacts.basicApi;

    let idProperty = undefined;
    if (idIsEmail) {
      idProperty = 'email';
    }

    try {
      const contact = await hubspotContactsAPI.getById(
        contactId,
        this.clientProperties,
        undefined,
        undefined,
        false,
        idProperty,
      );
      return {
        id: contact.properties.hs_object_id,
        firstName: contact.properties.firstname,
        lastName: contact.properties.lastname,
        email: contact.properties.email,
        phone: contact.properties.phone,
        country: contact.properties.country,
        city: contact.properties.city,
        address: contact.properties.address,
        zip: contact.properties.zip,
        session_edec: contact.properties.session_edec || null,
        wasFoundInChargeBee: false,
      };
    } catch (err) {
      if (err.code === 404) {
        return null;
      }

      throw new Error(`Error getting Hubspot Contact. Code: ${err.code}`);
    }
  }

  /**
   * Creates missing properties from Hubspot for incoming metadata.
   * This is crucial as Hubspot doesn't allow automatic properties creation when a contact is created.
   * If the properties are not created, the contact creation call will fail.
   * @param metadata - The metadata object to create properties for.
   * @returns A promise that resolves to metadatas to be used in Hubspot. Will be empty if properties creation fails to prevent errors from Hubspot.
   */
  async createMissingMetadataProperties(
    metadata?: UserUnsafeMetadata,
  ): Promise<{
    metadata?: UserUnsafeMetadata;
    responseStatus?:
      | BatchResponsePropertyStatusEnum
      | BatchResponsePropertyWithErrorsStatusEnum;
    errorBody?: StandardError;
  }> {
    if (!metadata) {
      return { metadata: {} };
    }

    let missingProperties: string[] = [];
    let existingProperties: UserUnsafeMetadata = {};

    try {
      const accessToken = await this.getAccessToken();
      const hubspotAPI = new HubspotClient({
        accessToken,
      }).crm.properties;

      const allPropertiesFromHubspot = await hubspotAPI.coreApi.getAll(
        'contacts',
      );
      missingProperties = Object.keys(metadata).filter(
        (property) =>
          !allPropertiesFromHubspot.results.find(
            (result) => result.name === property,
          ),
      );
      existingProperties = Object.keys(metadata).reduce((acc, property) => {
        if (!missingProperties.includes(property)) {
          acc[property] = metadata[property];
        }
        return acc;
      }, {});

      const properties = missingProperties.map(
        (property) =>
          ({
            name: property,
            label: property,
            groupName: 'analyticsinformation',
            type: 'string',
            fieldType: 'text',
          } as PropertyCreate),
      );

      const response = await hubspotAPI.batchApi.create('contacts', {
        inputs: properties,
      });
      return { metadata, responseStatus: response.status };
    } catch (err) {
      console.error(
        'Error creating metadata properties in Hubspot, returning only existing properties as a fallback.\nError from Hubspot: ',
        { responseStatus: err.body },
      );
      return {
        metadata: existingProperties,
        errorBody: err.body as StandardError,
      };
    }
  }

  /**
   * Tries to find a customer in Hubspot, creates one if nothing is found for provided email
   * @param email Customer email to search in Hubspot
   * @param customerToCreate Info of the customer to create in case the customer isn't found
   * @returns A promise that resolves to `Customer` object
   */
  async findByEmailOrCreateContact(
    email: string,
    customerToCreate?: Partial<ChargeBeeCustomer>,
    metadata?: UserUnsafeMetadata,
  ): Promise<ChargebeeCustomer> {
    const contactFound = await this.getHubspotContact(email, true);

    if (!contactFound) {
      const accessToken = await this.getAccessToken();
      const hubspotContactsAPI = new HubspotClient({
        accessToken,
      }).crm.contacts.basicApi;
      // responseStatus and errorBody are not used yet, we'll add them later to unit tests
      const { metadata: metadataToUse /*, responseStatus, errorBody*/ } =
        await this.createMissingMetadataProperties(metadata);
      const newContact = await hubspotContactsAPI.create({
        associations: [],
        properties: {
          email: customerToCreate.email,
          firstname: customerToCreate.first_name,
          lastname: customerToCreate.last_name,
          phone: customerToCreate.phone,
          hs_lead_status: 'NEW',
          // unpack metadata object into properties
          ...metadataToUse,
        },
      });

      return {
        id: newContact?.id,
        email: newContact?.properties?.email,
        firstName: newContact?.properties?.firstname,
        lastName: newContact?.properties?.lastname,
        phone: newContact?.properties?.phone,
        wasFoundInChargeBee: true,
      };
    }
    return contactFound;
  }

  /**
   * Creates a new deal in HubSpot or updates an existing deals properties
   * If the deal already exists, almost all the fields as the newly created one
   * are set/updated, except `dealname`, `pipeline` and `hubspot_owner_id`
   *
   * @param {Subscription} subscription - The subscription object containing the deal information.
   * @param {ChargebeeCustomer} linkedCustomer - The Chargebee event content.
   * @returns {Promise<void>} - A promise that resolves when the deal is created successfully.
   */
  async createDealOrUpdateExisting(
    subscription: Subscription,
    linkedCustomer: ChargeBeeCustomer,
    isExistingDeal: boolean,
  ): Promise<any> {
    const accessToken = await this.getAccessToken();
    const hubspotDealsAPI = new HubspotClient({
      accessToken,
    }).crm.deals.basicApi;

    const dealProperties = this.hubspotFactory.generateDealFromSubscription(
      subscription,
      `${linkedCustomer.first_name} ${linkedCustomer.last_name}`,
      isExistingDeal,
    );
    const linkedContactEmail = subscription.customerEmail;
    let hubspotContact = await this.getHubspotContact(linkedContactEmail, true);

    if (!hubspotContact) {
      hubspotContact = await this.findByEmailOrCreateContact(
        linkedContactEmail,
        linkedCustomer,
      );
    }

    if (!isExistingDeal) {
      return hubspotDealsAPI.create({
        associations: [
          {
            to: { id: hubspotContact.id },
            types: [
              {
                associationCategory:
                  AssociationSpecAssociationCategoryEnum.HubspotDefined,
                associationTypeId: 3,
              },
            ],
          },
        ],
        properties: dealProperties,
      });
    } else {
      return hubspotDealsAPI.update(subscription.crmId, {
        properties: dealProperties,
      });
    }
  }

  /**
   * Updates an existing deal's properties in HubSpot.
   *
   * @param {string} dealId - The Hubspot id of the deal to be updated.
   * @param {Partial<Subscription>} updates - The updates for the deal.
   * @returns {Promise<void>} - A promise that resolves when the deal is updated successfully.
   */
  async updateDealProperties(
    dealId: string,
    updates: Partial<Subscription>,
    updateIsFromInvoice?: boolean,
    updatesFromInvoice?: HubspotInvoiceAmountFields,
  ): Promise<PXActionResult> {
    if (!dealId || dealId === '') {
      return {
        success: false,
        message: `No deal id provided for deal update`,
      };
    }
    const accessToken = await this.getAccessToken();
    const hubspotDealsAPI = new HubspotClient({
      accessToken,
    }).crm.deals.basicApi;

    let dealProperties = {};
    if (!updateIsFromInvoice) {
      dealProperties =
        this.hubspotFactory.generateDealUpdatesFromSubscription(updates);
    } else {
      dealProperties =
        this.hubspotFactory.generateDealUpdatesFromInvoice(updatesFromInvoice);
    }

    if (Object.keys(dealProperties).length === 0) {
      console.log(
        'No deal properties need to be updated. Available updates: ',
        { ...updates },
      );
      return;
    }
    try {
      const existingDeal = await hubspotDealsAPI.getById(dealId);
      if (existingDeal.properties['dealstage'] === '623623141') {
        return {
          success: true,
          message: `Deal: ${dealId} is already lost, no need to update`,
        };
      }
      const result = await hubspotDealsAPI.update(dealId, {
        properties: dealProperties,
      });
      return {
        success: true,
        message: `Deal: ${dealId} updated successfully`,
        data: { updateResult: result },
      };
    } catch (err) {
      return {
        success: false,
        message: `Error updating deal: ${dealId}. Error: ${err}`,
      };
    }
  }

  /**
   * Creates a new `Invoice_px` custom object in HubSpot.
   *
   * @param invoice - The invoice object containing the invoice information.
   * @param linkedDeal - The Hubspot `id` of the deal this invoice is linked to
   * @returns {Promise<void>} - A promise that resolves when the `Invoice_px` is created successfully.
   */
  async createInvoice_px(invoice: Invoice, linkedDeal: string): Promise<any> {
    if (!linkedDeal) {
      throw new Error(
        `Invalid deal ID (${linkedDeal}) provided for Chargebee invoice: ${invoice.chargebeeId}`,
      );
    }
    const accessToken = await this.getAccessToken();
    const hubspotObjectsAPI = new HubspotClient({
      accessToken,
    }).crm.objects.basicApi;

    const invoice_pxProperties = this.hubspotFactory.generateInvoice(invoice);
    return hubspotObjectsAPI.create('2-127733292', {
      associations: [
        {
          to: { id: linkedDeal },
          types: [
            {
              associationCategory:
                AssociationSpecAssociationCategoryEnum.UserDefined,
              associationTypeId: 195,
            },
          ],
        },
      ],
      properties: invoice_pxProperties,
    });
  }

  /**
   * Updates an existing `Invoice_px` custom object in HubSpot.
   *
   * @param invoicePXId - `id` of the Hubspot invoice
   * @param updates - Updates for the Invoice_px object in Hubspot
   * @returns {Promise<void>} - A promise that resolves when the `Invoice_px` is created successfully.
   */
  async updateInvoice_px(
    invoicePXId: string,
    updates: Partial<Invoice>,
  ): Promise<{
    updatedInvoice: any;
    updatedHubspotProps: HubspotInvoiceAmountFields;
  }> {
    if (!invoicePXId) {
      return { updatedHubspotProps: null, updatedInvoice: null };
    }
    const accessToken = await this.getAccessToken();
    const hubspotObjectsAPI = new HubspotClient({
      accessToken,
    }).crm.objects.basicApi;

    const invoice_pxProperties =
      this.hubspotFactory.generateInvoice_pxUpdates(updates);
    if (Object.keys(invoice_pxProperties).length === 0) {
      return {
        updatedHubspotProps: null,
        updatedInvoice: null,
      };
    }
    const updatedInvoice_px = await hubspotObjectsAPI.update(
      '2-127733292',
      invoicePXId,
      {
        properties: invoice_pxProperties,
      },
    );
    return {
      updatedInvoice: updatedInvoice_px,
      updatedHubspotProps: invoice_pxProperties,
    };
  }

  /**
   * Creates a new product in HubSpot based on a product plan.
   *
   * @param {productPlan} - The product plan to create a product for.
   * @returns {Promise<string>} - A promise that resolves to the id of the newly created
   * product in hubspot.
   */
  async createProduct(
    productPlan: ProductPlan,
    planPrice: ProductPlanPrice,
    product: Product,
    productLine: string,
    productFamily: string,
  ): Promise<string> {
    const accessToken = await this.getAccessToken();
    const hubspotDealsAPI = new HubspotClient({
      accessToken,
    }).crm.products.basicApi;

    try {
      const productProperties = this.hubspotFactory.generateProductFromPlan(
        productPlan,
        planPrice,
        product,
        productLine,
        productFamily,
      );
      const productCreated = await hubspotDealsAPI.create({
        properties: productProperties,
      });
      if (productCreated?.id) {
        return productCreated?.id;
      }
      return null;
    } catch (error) {
      throw new HttpException(
        'Failed to sync product in Hubspot',
        HttpStatus.PRECONDITION_FAILED,
      );
    }
  }

  /**
   * Updates an existing product in HubSpot based on the updated product plan.
   *
   * @param {productPlan} - The updated product plan.
   * @returns {Promise<void>} - A promise that resolves when the product is updated successfully.
   */
  async updateProduct(
    productPlan: ProductPlan,
    planPrice: ProductPlanPrice,
    product: Product,
  ): Promise<void> {
    const accessToken = await this.getAccessToken();
    const hubspotDealsAPI = new HubspotClient({
      accessToken,
    }).crm.products.basicApi;

    const productProperties = this.hubspotFactory.generateProductFromPlan(
      productPlan,
      planPrice,
      product,
    );
    await hubspotDealsAPI.update(productPlan.crmId, {
      properties: productProperties,
    });
  }

  /**
   * Adds a new product line to the existing values of the custom field in hubspot.
   *
   * @param {ProductLine} - The name of the new product line.
   * @param {description} - The description of the new product line.
   * @returns {Promise<boolean>} - A promise that resolves when the values are updated successfully.
   */
  async createProductLine(
    productLine: string,
    description: string,
  ): Promise<boolean> {
    const accessToken = await this.getAccessToken();
    const hubspotAPI = new HubspotClient({
      accessToken,
    }).crm.properties.coreApi;

    const existingProductLines = await hubspotAPI.getByName(
      'product',
      'product_line',
    );
    const productLineOptions = existingProductLines.options;
    if (!productLineOptions.find((x) => x.value === productLine)) {
      productLineOptions.push({
        label: productLine,
        value: productLine,
        description: description,
        hidden: false,
      });
      const result = await hubspotAPI.update('product', 'product_line', {
        options: productLineOptions,
      });

      if (result.options) {
        return true;
      }
      return false;
    }
    return true;
  }

  /**
   * Adds a new product family to the existing values of the custom field in hubspot.
   *
   * @param {ProductFamily} - The name of the new product family.
   * @param {description} - The description of the new product family.
   * @returns {Promise<boolean>} - A promise that resolves when the values are updated successfully.
   */
  async createProductFamily(
    productFamily: string,
    description: string,
  ): Promise<boolean> {
    const accessToken = await this.getAccessToken();
    const hubspotAPI = new HubspotClient({
      accessToken,
    }).crm.properties.coreApi;

    const existingProductFamilies = await hubspotAPI.getByName(
      'product',
      'product_family',
    );
    const productFamilyOptions = existingProductFamilies.options;
    if (!productFamilyOptions.find((x) => x.value === productFamily)) {
      productFamilyOptions.push({
        label: productFamily,
        value: productFamily,
        description: description,
        hidden: false,
      });
      const result = await hubspotAPI.update('product', 'product_family', {
        options: productFamilyOptions,
      });

      if (result.options) {
        return true;
      }
      return false;
    }
    return true;
  }

  /**
   * Adds a new product code to the existing values of the custom fields in hubspot.
   * Custom fields in Hubspot to be updated are product_code_px_os or product_code_list
   *
   * @param {productCode} - The product code to add to Hubspot properties.
   * @returns {Promise<boolean>} - A promise that resolves when the values are updated successfully.
   */
  async updateProductCodeProperty(
    productCode: string,
    propertyName: string,
    entityName: string,
  ): Promise<boolean> {
    const accessToken = await this.getAccessToken();
    const hubspotAPI = new HubspotClient({
      accessToken,
    }).crm.properties.coreApi;

    const existingProductCodes = await hubspotAPI.getByName(
      entityName,
      propertyName,
    );
    const productCodeOptions = existingProductCodes.options;
    if (!productCodeOptions.find((x) => x.value === productCode)) {
      productCodeOptions.push({
        label: productCode,
        value: productCode,
        hidden: false,
      });
      const result = await hubspotAPI.update(entityName, propertyName, {
        options: productCodeOptions,
      });

      if (result.options) {
        return true;
      }
      return false;
    }
    return true;
  }

  /**
   * Updates the product code list of a deal in Hubspot.
   *
   * @param {string} dealId - The deal on which to perform the update.
   * @param {number[]} productCodes - List of product codes to add to the deal.
   * @returns {Promise<void>} - A promise that resolves when the deal is created successfully.
   */
  async updateDealProductCodeList(
    dealId: string,
    productCodes: string[],
  ): Promise<any> {
    const accessToken = await this.getAccessToken();
    const hubspotDealsAPI = new HubspotClient({
      accessToken,
    }).crm.deals.basicApi;
    return hubspotDealsAPI.update(dealId, {
      properties: {
        product_code_list: `${productCodes.join(';')}`,
      },
    });
  }

  /**
   * Create line items for a deal in Hubspot.
   *
   * @param {ProductPlanPrice[]} productPrices - The product plan prices that will be converted to line items.
   * @param {string} dealId - The deal on which to perform the update.
   * @param {SubscriptionItem[]} subscriptionItems - Line items of the subscription.
   * @param {boolean} isForever - Whether the subscription is forever.
   * @param {ChargeBeeDurationPeriodUnit} billingPeriodUnit - Chargebee billing period unit.
   * @returns {Promise<void>} - A promise that resolves when the deal is created successfully.
   */
  async createLineItems(
    productPrices: ProductPlanPrice[],
    dealId: string,
    subscriptionItems: SubscriptionItem[],
    isForever: boolean,
    billingPeriodUnit: ChargeBeeDurationPeriodUnit,
  ): Promise<void> {
    const accessToken = await this.getAccessToken();
    const hubspotClient = new HubspotClient({ accessToken });

    const {
      deals: { basicApi: dealsApi },
      associations: {
        v4: { basicApi: associationsApi },
      },
      lineItems: { basicApi: lineItemsApi },
    } = hubspotClient.crm;

    const deal = await dealsApi.getById(dealId, [], [], ['line_items']);
    if (
      deal?.associations &&
      deal?.associations['line items'] &&
      deal?.associations['line items']?.results
    ) {
      for (const items of deal?.associations['line items']?.results) {
        await associationsApi.archive('deal', dealId, 'line_item', items.id);
      }
    }
    const newLineItems = [];
    for (const price of productPrices) {
      const lineItem = this.hubspotFactory.generateLineItem(
        price.productPlan,
        price,
        subscriptionItems.find((x) => x.item_price_id === price.chargebeeId),
        isForever,
        billingPeriodUnit,
      );
      newLineItems.push(lineItem);
    }
    for (const item of newLineItems) {
      await lineItemsApi.create({
        properties: item,
        associations: [
          {
            to: { id: dealId },
            types: [
              {
                associationCategory:
                  AssociationSpecAssociationCategoryEnum.HubspotDefined,
                associationTypeId: 20,
              },
            ],
          },
        ],
      });
    }
  }

  /**
   * Updates line items for a deal in Hubspot in case of a downsell.
   * @param {Subscription} subscription - The subscription to downsell.
   * @param {UpdateSubscriptionDTO} updates - The updates for the subscription.
   * @param {ProductPlanPrice[]} planPrices - The product plan prices that will be converted to line items.
   * @returns {Promise<void>} - A promise that resolves when the deal is updated successfully.
   */
  async updateLineItemsForDownsell(
    subscription: Subscription,
    updates: UpdateSubscriptionDTO,
    planPrices: ProductPlanPrice[],
  ): Promise<any> {
    const accessToken = await this.getAccessToken();
    const hubspotClient = new HubspotClient({ accessToken });
    const {
      deals: { basicApi: dealsApi },
      lineItems: { basicApi: lineItemsApi, batchApi: batchLineItemsApi },
    } = hubspotClient.crm;

    const deal = await dealsApi.getById(
      subscription.crmId,
      [],
      [],
      ['line_items'],
    );
    const existingLineItemUpdates = {
      inputs: [],
    };

    if (
      deal?.associations &&
      deal?.associations['line items'] &&
      deal?.associations['line items']?.results
    ) {
      for (const items of deal?.associations['line items']?.results) {
        existingLineItemUpdates.inputs.push({
          id: items.id,
          properties: {
            quantity: '0',
          },
        });
      }
    }
    await batchLineItemsApi.update(existingLineItemUpdates);

    const newLineItems = [];
    for (const update of updates.updatedItems) {
      const price = planPrices.find((x) => x.chargebeeId === update.id);
      const lineItem = this.hubspotFactory.generateLineItemUpdateForDownsell(
        price.productPlan,
        price,
        update,
      );
      newLineItems.push(lineItem);
    }

    for (const item of newLineItems) {
      await lineItemsApi.create({
        properties: item,
        associations: [
          {
            to: { id: subscription.crmId },
            types: [
              {
                associationCategory:
                  AssociationSpecAssociationCategoryEnum.HubspotDefined,
                associationTypeId: 20,
              },
            ],
          },
        ],
      });
    }

    const dealProperties = {};

    const newAmountToPay = updates.updatedItems.reduce(
      (acc, item) =>
        acc + (item.updatedAmount / 100) * item.updatedBillingCycles,
      0,
    );

    dealProperties['amount'] = subscription.amountPaid / 100 + newAmountToPay;
    await dealsApi.update(subscription.crmId, {
      properties: dealProperties,
    });
  }
}
