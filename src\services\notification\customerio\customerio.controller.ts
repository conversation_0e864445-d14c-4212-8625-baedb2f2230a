import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { CustomerioService } from './customerio.service';

@Controller('customerio')
export class CustomerioController {
  constructor(private readonly customerioService: CustomerioService) {}

  @Post('send-discount')
  @HttpCode(HttpStatus.NO_CONTENT)
  async sendDiscount(
    @Body()
    body: {
      email: string;
      code: string;
      discount: number;
      expirationDate: Date;
    },
  ) {
    // For the moment, we disable the email sending
    return null;
    await this.customerioService.sendDiscountEmail(
      body.email,
      body.code,
      body.discount,
      body.expirationDate,
    );
  }
}
