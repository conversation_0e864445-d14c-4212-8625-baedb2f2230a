import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  JoinTable,
  ManyToMany,
} from 'typeorm';
import {
  DiscountStatus,
  DiscountType,
  DiscountDurationType,
  DiscountDurationPeriodUnit,
  DiscountApplication,
} from '@enums';
import { Discount } from '@entities';
import { DiscountConstraint } from '@types';
import { ProductPlanPriceTable } from './product-plan-price.table';

@Entity({
  name: 'discounts',
})
export class DiscountTable implements Discount {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'varchar', length: 50, unique: true })
  code: string;

  @Column({
    type: 'enum',
    enum: DiscountStatus,
    default: DiscountStatus.Active,
  })
  status: DiscountStatus;

  @Column({ type: 'varchar', name: 'invoiceName', length: 100, nullable: true })
  invoiceName?: string;

  @Column({ type: 'enum', enum: DiscountType })
  type: DiscountType;

  @Column({
    type: 'varchar',
    length: 5,
    default: 'EUR',
    nullable: true,
    comment: 'Only if type === fixed',
  })
  currency?: string;

  @Column({ type: 'numeric' })
  amount: number;

  @Column({ type: 'enum', enum: DiscountApplication })
  applyOn: DiscountApplication;

  @Column({ type: 'enum', enum: DiscountDurationType })
  durationType: DiscountDurationType;

  @Column({ type: 'enum', enum: DiscountDurationPeriodUnit, nullable: true })
  durationPeriodUnit?: DiscountDurationPeriodUnit;

  @Column({ type: 'int', nullable: true })
  durationPeriodAmount?: number;

  @Column({ type: 'timestamptz', nullable: true })
  validUntil?: Date;

  @Column({ type: 'integer', nullable: true })
  maxRedemptions?: number;

  @Column({ type: 'jsonb', nullable: true })
  attachedTo: DiscountConstraint[];

  @ManyToMany(() => ProductPlanPriceTable, { nullable: true })
  @JoinTable({ name: 'Discount_ProductPlanPrice' })
  pricesAttachedTo?: ProductPlanPriceTable[];

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
