import {
  CanActivate,
  ExecutionContext,
  Injectable,
  RawBodyRequest,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { ConfigService } from '@config';
import { Webhook, WebhookRequiredHeaders } from 'svix';

@Injectable()
export class ClerkWebhooksGuard implements CanActivate {
  private readonly webhookSecret: string;

  constructor(configService: ConfigService) {
    this.webhookSecret = configService.authSecrets.CLERK_WEBHOOKS_SECRET;
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: RawBodyRequest<Request> = context
      .switchToHttp()
      .getRequest();
    const payload = request.rawBody.toString('utf8');
    const webhook = new Webhook(this.webhookSecret);

    try {
      const svixHeaders: WebhookRequiredHeaders = {
        'svix-id': request.headers['svix-id'] as string,
        'svix-timestamp': request.headers['svix-timestamp'] as string,
        'svix-signature': request.headers['svix-signature'] as string,
      };

      webhook.verify(payload, svixHeaders);
      return true;
    } catch (err) {
      console.log(err);
      throw new UnauthorizedException('Invalid webhook signature');
    }
  }
}
