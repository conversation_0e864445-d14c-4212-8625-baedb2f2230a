"use client";

import { useTranslations } from "next-intl";
import { Suspense, useState } from "react";
import FloatingActionButton from "@/components/base/floating-action-button";
import { CreateCourseModal } from "./components/modals/create/CreateCourseModal";
import { useRouter } from "next/navigation";
import { CoursesTable } from "./components/table";
import { useToast } from "@/hooks/use-toast";
import { BreadcrumbSettings } from "@/components/(settings)/BreadcrumbSettings";

export const dynamic = "force-dynamic";

export default function CoursesPage() {
  const t = useTranslations();
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { toast } = useToast();

  const handleComplete = async (data: any) => {
    try {
      router.refresh();
      setIsModalOpen(false);
    } catch (error) {
      toast({
        title: t("courses.create.error"),
        description: t("courses.create.error-description"),
        variant: "destructive",
      });
    }
  };

  const modalProps = {
    isOpen: isModalOpen,
    onOpenChange: setIsModalOpen,
    onComplete: handleComplete,
  };

  return (
    <div className="space-y-4">
      <BreadcrumbSettings active="/courses" />
      <div className="py-4">
        <div className="space-y-4 border-b pb-4">
          <div className="flex flex-wrap items-center justify-between"></div>
        </div>

        <CoursesTable modalProps={modalProps} />
        <div className="md:hidden">
          <FloatingActionButton
            onClick={() => setIsModalOpen(true)}
            label={t("courses.create.button")}
          />
        </div>
        <CreateCourseModal {...modalProps} />
      </div>
    </div>
  );
}
