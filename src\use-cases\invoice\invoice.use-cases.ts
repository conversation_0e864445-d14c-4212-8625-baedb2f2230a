import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { Brackets, UpdateResult } from 'typeorm';
import { Injectable } from '@nestjs/common';
import {
  FiscalEntity,
  InvoiceStatus,
  SubscriptionOrderStatus,
  SubscriptionStatus,
} from '@enums';
import { ProductDeliveryService } from '../product-delivery';
import { ChargebeeBillingService } from '@services/billing';
import { InvoiceFactoryService } from '.';
import { ChargeBeeWebhookPayload, PXActionResult } from '@types';
import { HubspotService } from '@services/crm';
import { IDataServices } from '@abstracts';
import { Invoice } from '@entities';

@Injectable()
export class InvoiceUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly invoiceFactory: InvoiceFactoryService,
    private readonly chargebeeService: ChargebeeBillingService,
    private readonly hubspotService: HubspotService,
    private readonly productDeliveryService: ProductDeliveryService,
  ) {}

  /**
   * Finds a invoice or creates a new one if it doesn't exist
   * @param invoice Invoice entity
   * @returns Invoice item record from database
   */
  async findOrCreate(invoice: Invoice): Promise<Invoice> {
    const existingInvoice = await this.dataServices.invoice.getOneBy({
      chargebeeId: invoice.chargebeeId,
    });

    if (existingInvoice) {
      return existingInvoice;
    }

    return this.dataServices.invoice.create(invoice);
  }

  /**
   * Creates a `Invoice` from a Chargebee event.
   *
   * @param chargebeeEventContent - The content of the Chargebee webhook payload.
   * @returns A Promise that resolves to the created Transaction.
   */
  async createFromChargebeeEvent(
    chargebeeEventContent: ChargeBeeWebhookPayload['content'],
  ): Promise<PXActionResult> {
    try {
      const chargebeeInvoice = chargebeeEventContent.invoice;
      const invoice =
        this.invoiceFactory.generateFromChargebeeEvent(chargebeeInvoice);

      const { first_name, last_name, email } =
        await this.chargebeeService.getCustomerInfo(invoice.customerId);
      invoice.customerEmail = email?.toLowerCase()?.trim();
      invoice.customerName = `${first_name} ${last_name}`;

      await this.invoiceFactory.addImageAndDescriptionToLineItems(
        this.dataServices,
        invoice.lineItems,
      );
      const createdInvoice = await this.findOrCreate(invoice);
      const linkedSubscription = await this.dataServices.subscription.getOneBy({
        chargebeeId: createdInvoice?.subscriptionId,
      });

      if (linkedSubscription?.crmId) {
        await this.updateDealForNewInvoice(
          linkedSubscription?.chargebeeId,
          linkedSubscription?.crmId,
          createdInvoice?.total,
          createdInvoice?.status,
          chargebeeInvoice?.first_invoice || false,
        );
      }
      return {
        data: { id: createdInvoice?.chargebeeId },
        success: !!createdInvoice?.id,
        message: !!createdInvoice?.id
          ? 'Invoice created successfully'
          : 'No Invoice created',
      };
    } catch (err) {
      return {
        data: { id: chargebeeEventContent?.invoice?.id },
        success: false,
        message: `Error occurred while creating invoice: ${err}`,
      };
    }
  }

  /**
   * Updates an `Invoice` from a Chargebee event.
   *
   * @param chargebeeEventContent - The content of the Chargebee webhook payload.
   * @returns A Promise that resolves when the invoice is updated
   */
  async updateFromChargebeeEvent(
    chargebeeEventContent: ChargeBeeWebhookPayload['content'],
  ): Promise<PXActionResult> {
    try {
      const { invoice: chargebeeInvoice } = chargebeeEventContent;
      const invoiceFromDB = await this.dataServices.invoice.getOneBy({
        chargebeeId: chargebeeInvoice.id,
      });

      if (!invoiceFromDB) {
        return {
          success: false,
          message: `Invoice to be updated not found. Chargebee ID: ${chargebeeInvoice.id}`,
        };
      }

      const updatedInvoice = this.invoiceFactory.getUpdatedFields(
        chargebeeInvoice,
        invoiceFromDB,
      );

      const updateResult = await this.update(
        invoiceFromDB.chargebeeId,
        updatedInvoice,
      );
      const updatedStatus =
        InvoiceStatus[updatedInvoice?.status?.toUpperCase()];

      await this.updateAccessesAndSubscriptionStatus(
        invoiceFromDB.subscriptionId,
        invoiceFromDB.status,
        updatedStatus,
      );

      return {
        success: !!updateResult?.affected,
        message: updateResult?.affected
          ? 'Invoice updated successfully'
          : 'No Invoice updated',
      };
    } catch (err) {
      return {
        success: false,
        message: `Error occurred while updating invoice: ${err}`,
      };
    }
  }

  /**
   * Updates the subscription status based on invoice status transitions.
   * Also restores the accesses if the subscription is not overdue anymore.
   * If the invoice status transitions from `PAYMENT_DUE` to `PAID` and the subscription
   * has no remaining amount due, the subscription status is updated to `ACTIVE`.
   *
   * @param {string} subscriptionChargebeeId - The ID of the subscription to update.
   * @param {InvoiceStatus} previousInvoiceStatus - The previous status of the invoice.
   * @param {InvoiceStatus} currentInvoiceStatus - The current status of the invoice.
   * @returns {Promise<void>} A promise that resolves when the subscription status has been updated.
   */
  async updateAccessesAndSubscriptionStatus(
    subscriptionChargebeeId: string,
    previousInvoiceStatus: InvoiceStatus,
    currentInvoiceStatus: InvoiceStatus,
  ): Promise<void> {
    const invoiceMovedToPaid =
      (previousInvoiceStatus === InvoiceStatus.PAYMENT_DUE ||
        previousInvoiceStatus === InvoiceStatus.NOT_PAID) &&
      currentInvoiceStatus === InvoiceStatus.PAID;

    if (invoiceMovedToPaid) {
      const { amountDue } =
        await this.chargebeeService.getSubscriptionAmountInfo(
          subscriptionChargebeeId,
        );

      if (amountDue === 0) {
        const subscriptionRepo = this.dataServices.subscription;
        const linkedSubscription = await subscriptionRepo.getOneBy({
          chargebeeId: subscriptionChargebeeId,
        });
        let subscriptionStatus = SubscriptionStatus.ACTIVE;
        let subscriptionOrderStatus = SubscriptionOrderStatus.ACTIVE;
        if (linkedSubscription?.remainingBillingCycles === 0) {
          subscriptionStatus = SubscriptionStatus.CANCELLED;
          subscriptionOrderStatus = SubscriptionOrderStatus.PAID;
        }
        const updatedSubscription = await subscriptionRepo.updateAndReturnItem(
          'chargebeeId',
          subscriptionChargebeeId,
          {
            status: subscriptionStatus,
            orderStatus: subscriptionOrderStatus,
          },
        );
        const subscriptionId = updatedSubscription?.updatedItem?.id;
        await this.productDeliveryService.unSuspendAccessForSubscription(
          subscriptionId,
        );
      }
    }
  }

  /**
   * Updates the deal's amount fields after an invoice is updated
   * @param subscriptionId Chargebee id of the subscription that needs to be updated
   * @param invoiceTotal `total` amount of the Chargebee invoice
   * @param invoiceStatus `status` of the Chargebee invoice
   * @param isFirstInvoice Whether this is the first invoice for the subscription
   */
  async updateDealForUpdatedInvoice(
    subscriptionId: string,
    invoiceTotal: number,
    invoiceStatus: string,
    isFirstInvoice: boolean,
  ): Promise<any> {
    const subscription = await this.dataServices.subscription.getOneBy({
      chargebeeId: subscriptionId,
    });
    const { amountDue, amountPaid } =
      await this.chargebeeService.getSubscriptionAmountInfo(subscriptionId);
    await this.hubspotService.updateDealProperties(
      subscription?.crmId,
      null,
      true,
      {
        invoice_amount: (invoiceTotal / 100)?.toString(),
        invoice_status: invoiceStatus,
        amount_due: (amountDue / 100)?.toString(),
        amount_paid: (amountPaid / 100)?.toString(),
        isFirstInvoice: isFirstInvoice,
      },
    );
  }

  /**
   * Updates the deal's amount fields after a subscription gets a new invoice
   * @param subscriptionId Chargebee id of the subscription that needs to be updated
   * @param subscriptionHubspotId Hubspot id of the subscription
   * @param invoiceTotal `total` amount of the Chargebee invoice
   * @param invoiceStatus `status` of the Chargebee invoice
   * @param isFirstInvoice Whether this is the first invoice for the subscription
   */
  async updateDealForNewInvoice(
    subscriptionChargebeeId: string,
    subscriptionHubspotId: string,
    invoiceTotal: number,
    invoiceStatus: string,
    isFirstInvoice: boolean,
  ): Promise<any> {
    const { amountDue, amountPaid } =
      await this.chargebeeService.getSubscriptionAmountInfo(
        subscriptionChargebeeId,
      );
    await this.hubspotService.updateDealProperties(
      subscriptionHubspotId,
      null,
      true,
      {
        invoice_amount: (invoiceTotal / 100)?.toString(),
        invoice_status: invoiceStatus,
        amount_due: (amountDue / 100)?.toString(),
        amount_paid: (amountPaid / 100)?.toString(),
        isFirstInvoice: isFirstInvoice,
      },
    );
  }

  /**
   * Updates an `Invoice` with the specified `chargebeeId`.
   *
   * @param chargebeeId - The `chargebeeId` of the `Invoice` to update.
   * @param updates - The updates to apply to the `Invoice`.
   * @returns A promise that resolves to the result of the update operation.
   */
  async update(
    chargebeeId: string,
    updates: QueryDeepPartialEntity<Invoice>,
  ): Promise<UpdateResult> {
    return this.dataServices.invoice.update(
      {
        chargebeeId,
      },
      updates,
    );
  }

  /**
   * Retrieves all `Invoice` records matching provided filters
   * @param limit Number of `Invoice`s to fetch
   * @param page Page number
   */
  async list(
    limit?: number,
    page?: number,
  ): Promise<{ items: Invoice[]; total: number }> {
    return this.dataServices.invoice.getAll(limit, page, {
      createdAt: 'DESC',
    });
  }

  /**
   * Searches and returns a `Invoice` record from database
   * @param id Chargebee `id` of the `Invoice` to search for
   * @returns `Invoice` record from database OR null
   */
  async getOne(id: string): Promise<Invoice> {
    return this.dataServices.invoice.getOneBy({ chargebeeId: id });
  }

  /**
   * Searches for invoices based on the provided search criteria.
   *
   * @param fiscalEntity - The fiscal entity associated with the invoices.
   * @param customer - The customer ID to filter invoices.
   * @param subscription - The subscription ID to filter invoices.
   * @param status - The status of the invoices to filter.
   * @param limit - The maximum number of invoices to retrieve.
   * @param page - The page number of the results.
   * @param orderBy - The order in which the results should be sorted. Defaults to 'DESC'.
   * @returns A promise that resolves to an object containing the retrieved invoices and the total count.
   */
  async searchAll(
    searchQuery?: string,
    fiscalEntity?: FiscalEntity,
    customer?: string,
    subscription?: string,
    status?: InvoiceStatus,
    paidAtStart?: number,
    paidAtEnd?: number,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: Invoice[]; total: number }> {
    const queryBuilder = this.dataServices.invoice
      .getRepository()
      .createQueryBuilder('invoice');

    if (status) {
      queryBuilder.andWhere('invoice.status = :status', { status });
    }

    if (customer) {
      queryBuilder.andWhere('invoice.customerId = :customer', { customer });
    }

    if (subscription) {
      queryBuilder.andWhere('invoice.subscriptionId = :subscription', {
        subscription,
      });
    }

    if (fiscalEntity) {
      queryBuilder.andWhere('invoice.businessEntityId = :fiscalEntity', {
        fiscalEntity,
      });
    }

    if (paidAtStart && paidAtEnd) {
      queryBuilder.andWhere(
        'invoice.paidAt BETWEEN :paidAtStart AND :paidAtEnd',
        {
          paidAtStart,
          paidAtEnd,
        },
      );
    }

    if (searchQuery) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('invoice.customerEmail ILIKE :searchQuery', {
            searchQuery: `%${searchQuery}%`,
          }).orWhere('invoice.customerName ILIKE :searchQuery', {
            searchQuery: `%${searchQuery}%`,
          });
          if (Number.isFinite(Number(searchQuery))) {
            qb.orWhere('invoice.total/100 = :searchQuery', {
              searchQuery,
            });
          }
        }),
      );
    }

    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.orderBy('invoice.createdAt', orderBy || 'DESC');

    const [items, total] = await queryBuilder.getManyAndCount();
    return {
      items,
      total,
    };
  }

  /**
   * Retrieves invoices that match the given search query.
   * @param searchQuery - The search query to match against invoice properties.
   * @returns A promise that resolves to an array of matching invoices.
   */
  async getInvoicesForMatchingBankTransfers(
    searchQuery: string,
  ): Promise<Invoice[]> {
    const queryBuilder = this.dataServices.invoice
      .getRepository()
      .createQueryBuilder('invoice');
    queryBuilder
      .orWhere('invoice.customerEmail ILIKE :searchQuery', {
        searchQuery,
      })
      .orWhere('invoice.customerName ILIKE :searchQuery', {
        searchQuery: `%${searchQuery}%`,
      });

    const searchQueryNumber = Number(searchQuery);

    if (!isNaN(searchQueryNumber)) {
      queryBuilder.orWhere('invoice.amountDue = :searchQueryNumber', {
        searchQueryNumber,
      });
    }
    return queryBuilder.getMany();
  }

  /**
   * Sends a newly created invoice to Hubspot
   * @param invoiceId Chargebee `id` of the invoice to create in Hubspot
   * @param dealToLinkTo Hubspot `id` of the deal to link this invoice to
   */
  async createInHubspot(
    invoiceId: string,
    dealToLinkTo: string,
  ): Promise<void> {
    if (!dealToLinkTo) {
      throw new Error(
        `Deal ID (${dealToLinkTo}) is invalid for linking invoice (${invoiceId}) in Hubspot.`,
      );
    }
    const existingInvoice = await this.getOne(invoiceId);
    if (existingInvoice && existingInvoice?.crmId) return;

    let invoiceFromDB = existingInvoice;
    if (!existingInvoice) {
      const chargebeeInvoice = await this.chargebeeService.getInvoice(
        invoiceId,
      );
      const newInvoice =
        this.invoiceFactory.generateFromChargebeeEvent(chargebeeInvoice);
      invoiceFromDB = await this.findOrCreate(newInvoice);
    }
    const hubspotResult = await this.hubspotService.createInvoice_px(
      invoiceFromDB,
      dealToLinkTo,
    );
    const invoiceHubspotId = hubspotResult?.id;
    if (invoiceHubspotId) {
      await this.update(invoiceFromDB?.chargebeeId, {
        crmId: invoiceHubspotId,
      });
    }
  }

  /**
   * Checks if an invoice exists in our database & synced with Hubspot
   * @param chargebeeId Chargebee id of the invoice to look for
   * @returns A promise that resolves to `true` if invoice exists, `false` otherwise
   */
  async invoiceSynced(chargebeeId: string): Promise<boolean> {
    const invoice = await this.dataServices.invoice.getOneBy({
      chargebeeId,
    });

    return !!invoice && !!invoice?.crmId;
  }
}
