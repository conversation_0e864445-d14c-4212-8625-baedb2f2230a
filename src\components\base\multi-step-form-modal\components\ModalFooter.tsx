import { But<PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Loader2, <PERSON>ota<PERSON><PERSON><PERSON><PERSON>, ArrowLeft, ArrowRight, Check } from "lucide-react";
import { LoadingState, SubmitButtonConfig } from "../types";
import { cn } from "@/lib/utils";

interface ModalFooterProps {
  currentStep: number;
  totalSteps: number;
  onPrevious: () => void;
  onNext: (e: React.FormEvent, buttonData?: Record<string, any>) => void;
  onReset?: () => void;
  loading: LoadingState;
  isDirty?: boolean;
  submitButtons?: SubmitButtonConfig[];
}

export const ModalFooter = ({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onReset,
  loading,
  isDirty = false,
  submitButtons,
}: ModalFooterProps) => {
  const t = useTranslations();
  const isLastStep = currentStep === totalSteps - 1;
  const isLoading = loading.isSubmitting || loading.isLoadingStep;

  const renderSubmitButtons = () => {
    if (!isLastStep) {
      return (
        <Button
          type="submit"
          onClick={(e) => onNext(e)}
          disabled={isLoading}
          size="sm"
          className="flex items-center gap-2 rounded-full"
        >
          {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
          <span>{t("components.multi-step-modal.next")}</span>
          <ArrowRight className="h-4 w-4" />
        </Button>
      );
    }

    if (!submitButtons?.length) {
      return (
        <Button
          type="submit"
          onClick={(e) => onNext(e)}
          disabled={isLoading}
          size="sm"
          className="flex items-center gap-2 rounded-full"
        >
          {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
          <span>{t("components.multi-step-modal.complete")}</span>
          <Check className="h-4 w-4" />
        </Button>
      );
    }

    return submitButtons.map((button, index) => (
      <Button
        key={index}
        type="submit"
        onClick={(e) => onNext(e, button.data)}
        disabled={isLoading}
        size="sm"
        variant={button.variant}
        className={cn("flex items-center gap-2 rounded-full", button.className)}
      >
        {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
        <span>{button.label}</span>
        {button.isPrimary && <Check className="h-4 w-4" />}
      </Button>
    ));
  };

  return (
    <div className="flex items-center justify-between border-t pt-4">
      <div className="flex items-center space-x-2">
        {onReset && isDirty && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onReset}
            disabled={isLoading}
            className="flex items-center gap-2 rounded-full"
          >
            <RotateCcw className="h-4 w-4" />
            {t("components.multi-step-modal.reset")}
          </Button>
        )}
      </div>
      <div className="flex items-center space-x-2">
        {currentStep > 0 && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onPrevious}
            disabled={isLoading}
            className="flex items-center gap-2 rounded-full"
          >
            <ArrowLeft className="h-4 w-4" />
            {t("components.multi-step-modal.previous")}
          </Button>
        )}
        {renderSubmitButtons()}
      </div>
    </div>
  );
};
