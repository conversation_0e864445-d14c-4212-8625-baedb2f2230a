"use client";

import { useUser } from "@clerk/nextjs";
import { Menu } from "lucide-react";
import Image from "next/image";
import { usePathname } from "next/navigation";

import {
  Sidebar as ShadcnSidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarGroup,
  SidebarGroupContent,
  useSidebar,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import ParadoxLogo, { ParadoxSmallLogo } from "../logos/Paradox";
import { Link } from "@/i18n/routing";
import { isActiveLink } from "@/lib/router";
import { SidebarItem } from "./SidebarItem";
import { NavigationItem } from "@/types";
import { useTranslations } from "next-intl";
import LocaleSwitcher from "../LocaleSwitcher";

export interface SidebarProps {
  className?: string;
  items: NavigationItem[];
  hideFooterOnMobile?: boolean;
}

const Trigger = ({ className }: { className: string }) => {
  const { toggleSidebar } = useSidebar();
  return (
    <button onClick={toggleSidebar} className={className}>
      <Menu />
    </button>
  );
};

export const Sidebar = ({ className, items, hideFooterOnMobile }: SidebarProps) => {
  const t = useTranslations("navigation.sidebar");
  const pathname = usePathname();
  const { user } = useUser();
  const label = user?.firstName || t("my-profile");

  return (
    <>
      {/* Mobile Trigger */}
      <div className="fixed top-0 z-50 flex w-full flex-row justify-between bg-[#222222] px-6 py-4 md:hidden">
        <Link href="/" className="flex items-center justify-center">
          <ParadoxLogo className="h-auto" />
        </Link>
        <div className="flex flex-row gap-2">
          <LocaleSwitcher className="cursor-pointer md:hidden" />
          <Trigger className="cursor-pointer md:hidden" />
        </div>
      </div>

      {/* Sidebar */}
      <ShadcnSidebar className={cn("border-border border-r bg-white", className)}>
        {/* Header */}
        <SidebarHeader>
          <Link href="/" className="flex items-center justify-center pt-4 pb-16">
            <ParadoxSmallLogo className="h-auto w-[20%] md:hidden" />
            <ParadoxLogo className="hidden h-auto w-[80%] md:block" />
          </Link>
        </SidebarHeader>

        {/* Content */}
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                {items.map((item) => (
                  <SidebarItem
                    key={item.id}
                    item={item}
                    isActive={() => isActiveLink(pathname, item.href!)}
                  />
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter className={cn("px-4 py-6")}>
          <Link href="/profile" className="flex h-auto w-full flex-col items-center rounded-2xl">
            <div className="border-accent flex h-10 flex-row items-center rounded-full border-2">
              <Image
                src={user?.imageUrl || ""}
                alt="avatar"
                className="size-8 rounded-full object-cover"
                loading="lazy"
                width={28}
                height={28}
              />
              <span className="ml-3 px-4">{label}</span>
            </div>
          </Link>
        </SidebarFooter>
      </ShadcnSidebar>
    </>
  );
};

Sidebar.displayName = "Sidebar";
