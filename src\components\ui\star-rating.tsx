"use client";

import { Star } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface StarRatingProps {
  value: number;
  onChange: (value: number) => void;
  disabled?: boolean;
}

export function StarRating({ value, onChange, disabled = false }: StarRatingProps) {
  const [hoverValue, setHoverValue] = useState<number | null>(null);
  const stars = Array.from({ length: 5 }, (_, i) => i + 1);

  return (
    <div className="flex gap-1">
      {stars.map((star) => (
        <button
          key={star}
          type="button"
          disabled={disabled}
          className={cn("transition-all duration-200", disabled && "cursor-not-allowed opacity-50")}
          onMouseEnter={() => setHoverValue(star)}
          onMouseLeave={() => setHoverValue(null)}
          onClick={() => onChange(star)}
        >
          <Star
            className={cn(
              "h-8 w-8 transition-all duration-200",
              (hoverValue !== null ? star <= hoverValue : star <= value)
                ? "fill-primary stroke-primary"
                : "stroke-gray-400",
            )}
          />
        </button>
      ))}
    </div>
  );
}
