{"models": {"main": {"provider": "openrouter", "modelId": "google/gemini-2.5-flash-preview", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "google/gemini-2.5-flash-preview", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "google/gemini-2.5-flash-preview", "maxTokens": 120000, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/"}}