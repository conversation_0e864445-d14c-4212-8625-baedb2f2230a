import { SubscriptionFactoryService } from './subscription.factory';
import { SubscriptionOrderStatus, SubscriptionStatus } from '@enums';

describe('SubscriptionFactoryService', () => {
  let service: SubscriptionFactoryService;

  beforeEach(() => {
    service = new SubscriptionFactoryService(null, null);
  });

  describe('getOrderStatus', () => {
    it('should return IN_TRIAL status when subscription is in trial', () => {
      const result = service.getOrderStatus({
        incomingStatus: SubscriptionStatus.IN_TRIAL,
        remainingBillingCycles: 1,
      });
      expect(result).toBe(SubscriptionOrderStatus.IN_TRIAL);
    });

    it('should return OVERDUE status when there are dues and not stopped', () => {
      const result = service.getOrderStatus({
        incomingStatus: SubscriptionStatus.ACTIVE,
        totalDues: 100,
        remainingBillingCycles: 1,
      });
      expect(result).toBe(SubscriptionOrderStatus.OVERDUE);
    });

    describe('when subscription is cancelled', () => {
      it('should return STOPPED when cancelled with stop reason and no refund', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.CANCELLED,
          cancellationReasonCode: 'SOME_REASON',
          amountPaid: 100,
          amountRefunded: 0,
        });
        expect(result).toBe(SubscriptionOrderStatus.STOPPED);
      });

      it('should return PAID when cancelled with reason code `Paid in full via Bank Transfer`', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.CANCELLED,
          cancellationReasonCode: 'Paid in full via Bank Transfer',
          amountPaid: 100,
          amountRefunded: 0,
        });
        expect(result).toBe(SubscriptionOrderStatus.PAID);
      });

      it('should NOT return STOPPED when cancelled with reason code `No Card`', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.CANCELLED,
          cancellationReasonCode: 'No Card',
          amountPaid: 100,
          amountRefunded: 0,
        });
        expect(result).not.toBe(SubscriptionOrderStatus.STOPPED);
      });

      it('should return REFUNDED when cancelled with stop reason and refunded', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.CANCELLED,
          cancellationReasonCode: 'SOME_REASON',
          amountPaid: 100,
          amountRefunded: 100,
        });
        expect(result).toBe(SubscriptionOrderStatus.REFUNDED);
      });

      it('should return PAID when cancelled and fully paid', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.CANCELLED,
          remainingBillingCycles: 0,
          totalDues: 0,
          amountPaid: 100,
        });
        expect(result).toBe(SubscriptionOrderStatus.PAID);
      });
    });

    describe('paid status scenarios', () => {
      it('should return PAID when subscription is fully paid', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.ACTIVE,
          remainingBillingCycles: 0,
          totalDues: 0,
          amountPaid: 100,
        });
        expect(result).toBe(SubscriptionOrderStatus.PAID);
      });

      it('should return PAID when non-renewing and no amount remaining', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.NON_RENEWING,
          amountRemaining: 0,
          amountPaid: 100,
        });
        expect(result).toBe(SubscriptionOrderStatus.PAID);
      });
    });

    describe('active status scenarios', () => {
      it('should return ACTIVE when subscription is active with remaining cycles', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.ACTIVE,
          remainingBillingCycles: 5,
        });
        expect(result).toBe(SubscriptionOrderStatus.ACTIVE);
      });

      it('should return ACTIVE when subscription is forever', () => {
        const result = service.getOrderStatus({
          incomingStatus: SubscriptionStatus.ACTIVE,
          isForever: true,
        });
        expect(result).toBe(SubscriptionOrderStatus.ACTIVE);
      });
    });

    it('should return current status when no conditions are met', () => {
      const result = service.getOrderStatus({
        currentOrderStatus: SubscriptionOrderStatus.ACTIVE,
        incomingStatus: SubscriptionStatus.FUTURE,
      });
      expect(result).toBe(SubscriptionOrderStatus.ACTIVE);
    });
  });
});
