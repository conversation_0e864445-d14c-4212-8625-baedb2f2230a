import { Injectable } from '@nestjs/common';
import { AxiosError } from 'axios';
import { ConfigService, CircleSecrets } from '@config';
import { GlobalHelpers } from '@helpers';
import { PXActionResult, RequestMethods } from '@types';
import { CommunityType } from '@enums';
import { IDataServices } from '@abstracts';

@Injectable()
export class CircleService {
  private readonly PXS_API_KEY: string;
  private readonly PXL_API_KEY: string;
  private readonly PXS_API_V2_KEY: string;
  private readonly PXL_API_V2_KEY: string;
  private readonly v1ApiUrl: string;
  private readonly v2ApiUrl: string;
  private PXL_COMMUNITY_ID: string;
  private PXS_COMMUNITY_ID: string;
  private readonly USER_NOT_FOUND_ERROR = 'User record not found';
  private readonly secrets: CircleSecrets;
  constructor(
    private readonly dataServices: IDataServices,
    private readonly configService: ConfigService,
  ) {
    this.secrets = this.configService.circleSecrets;
    this.PXS_API_KEY = this.secrets.CIRCLE_PXS_API_KEY;
    this.PXL_API_KEY = this.secrets.CIRCLE_PXL_API_KEY;
    this.PXS_API_V2_KEY = this.secrets.CIRCLE_PXS_API_V2_KEY;
    this.PXL_API_V2_KEY = this.secrets.CIRCLE_PXL_API_V2_KEY;
    this.PXL_COMMUNITY_ID = this.secrets.CIRCLE_PXL_COMMUNITY_ID;
    this.PXS_COMMUNITY_ID = this.secrets.CIRCLE_PXS_COMMUNITY_ID;
    this.v1ApiUrl = `https://app.circle.so/api/v1`;
    this.v2ApiUrl = `https://app.circle.so/api/admin/v2`;
  }

  /**
   * Adds a new member to a community, doesn't check if the member already exists
   * @param email Email address of the member to be added
   * @param name Full name of the member to be added
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   * @param spaceIds Spaces to which the customer needs to be added
   * @param spaceGroups Space groups to which the customer needs to be added
   * @param memberTagIds Member tags that need to be added to the customer
   */
  async addCommunityMember(
    email: string,
    name: string,
    domain: CommunityType,
    spaces?: number[],
    spaceGroups?: number[],
    memberTagIds?: number[],
  ): Promise<any> {
    const apiToken =
      domain === CommunityType.PXL ? this.PXL_API_KEY : this.PXS_API_KEY;
    const endpoint = `${this.v2ApiUrl}/community_members`;
    const headers = {
      Authorization: `Token ${apiToken}`,
      'Content-Type': 'application/json',
    };
    const requestBody = {
      email,
      name,
      space_ids: spaces,
      space_group_ids: spaceGroups,
      member_tag_ids: memberTagIds,
    };
    try {
      const result = await GlobalHelpers.makeAxiosRequest(
        endpoint,
        RequestMethods.POST,
        null,
        headers,
        requestBody,
      );
      return result;
    } catch (error) {
      console.error(`Error while adding ${domain} member: ${error}`);
    }
  }

  /**
   * Adds a new member to a community using API `v1`, doesn't check if the member already exists
   * @param email Email address of the member to be added
   * @param name Full name of the member to be added
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   * @param spaceIds Spaces to which the customer needs to be added
   * @param spaceGroups Space groups to which the customer needs to be added
   * @param memberTagIds Member tags that need to be added to the customer
   */
  async addCommunityMemberV1(
    email: string,
    name: string,
    domain: CommunityType,
  ): Promise<{
    success: boolean;
    accessId?: number;
    error: boolean;
    message?: string;
  }> {
    const { apiToken, communityId } = this.getApiTokenAndCommunityID(domain);
    const endpoint = `${this.v1ApiUrl}/community_members`;
    const headers = {
      Authorization: `Token ${apiToken}`,
      'Content-Type': 'application/json',
    };
    const requestBody = {
      email,
      name,
      community_id: communityId,
      skip_invitation: true,
    };
    try {
      const result = await GlobalHelpers.makeAxiosRequest(
        endpoint,
        RequestMethods.POST,
        null,
        headers,
        requestBody,
      );
      const isAdded = result?.success;
      await this.dataServices.user.update(
        { email },
        {
          communityId: result?.community_member?.id,
        },
      );
      return {
        success: isAdded,
        accessId: result?.community_member?.id,
        error: !isAdded,
      };
    } catch (error) {
      return { success: false, error: true, message: error?.message };
    }
  }

  /**
   * Removes a member from a community
   * @param memberId Circle `member_id` of the member to be deleted
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   */
  async removeCommunityMember(
    memberId: number,
    domain: CommunityType,
  ): Promise<any> {
    const { apiToken } = this.getApiTokenAndCommunityID(domain);
    const endpoint = `${this.v2ApiUrl}/community_members/${memberId}`;
    const headers = {
      Authorization: `Token ${apiToken}`,
      'Content-Type': 'application/json',
    };

    try {
      const result = await GlobalHelpers.makeAxiosRequest(
        endpoint,
        RequestMethods.DELETE,
        null,
        headers,
      );
      return result;
    } catch (error) {
      console.error(
        `Error while deleting ${domain} member: ${memberId} ${error}`,
      );
    }
  }

  /**
   * Removes a member from a community using `v1` of the Circle API
   * @param memberId Circle `member_id` of the member to be deleted
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   */
  async removeCommunityMemberV1(
    email: string,
    domain: CommunityType,
  ): Promise<PXActionResult> {
    const { apiToken, communityId } = this.getApiTokenAndCommunityID(domain);
    const endpoint = `${this.v1ApiUrl}/community_members?email=${email}&community_id=${communityId}`;
    const headers = {
      Authorization: `Token ${apiToken}`,
      'Content-Type': 'application/json',
    };

    try {
      const result = await GlobalHelpers.makeAxiosRequest(
        endpoint,
        RequestMethods.DELETE,
        null,
        headers,
        null,
      );
      const isRemoved = result?.success;
      return {
        success: isRemoved,
        message: result?.message,
      };
    } catch (error) {
      return {
        success: false,
        message: error?.message,
      };
    }
  }

  /**
   * Adds a new member to a space, doesn't check if the member already exists
   * @param email Email address of the member to be added
   * @param spaceId Id of the Circle space to which the member needs to be added
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   */
  async addSpaceMember(
    email: string,
    spaceId: number,
    domain: CommunityType,
  ): Promise<any> {
    const { apiToken } = this.getApiTokenAndCommunityID(domain);
    const endpoint = `${this.v1ApiUrl}/space_members`;
    const headers = {
      Authorization: `Token ${apiToken}`,
      'Content-Type': 'application/json',
    };
    const requestBody = {
      email,
      space_id: spaceId,
    };
    try {
      const result = await GlobalHelpers.makeAxiosRequest(
        endpoint,
        RequestMethods.POST,
        null,
        headers,
        requestBody,
      );
      return result;
    } catch (error) {
      console.error(
        `Error while adding ${domain} member to space ${spaceId}: ${error}`,
      );
    }
  }

  /**
   * Adds a new member to a space using API `v1`, doesn't check if the member already exists
   * @param email Email address of the member to be added
   * @param spaceId Id of the Circle space to which the member needs to be added
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   */
  async addSpaceMemberV1(
    email: string,
    spaceId: number,
    domain: CommunityType,
  ): Promise<PXActionResult> {
    const { apiToken, communityId } = this.getApiTokenAndCommunityID(domain);
    const endpoint = `${this.v1ApiUrl}/space_members`;
    const headers = {
      Authorization: `Token ${apiToken}`,
      'Content-Type': 'application/json',
    };
    const requestBody = {
      email,
      community_id: communityId,
      space_id: spaceId,
    };
    try {
      const result = await GlobalHelpers.makeAxiosRequest(
        endpoint,
        RequestMethods.POST,
        null,
        headers,
        requestBody,
      );

      if (result?.success) {
        return { success: true, message: result?.message };
      }

      if (this.isUserNotInCommunity(result?.message)) {
        return {
          success: false,
          message:
            'User not found in community, please try adding to community first',
        };
      }

      const accessId = await this.getSpaceMemberIdV1(spaceId, email, domain);
      const message = !!accessId
        ? 'User already added to space'
        : result?.message;
      return { success: !!accessId, message };
    } catch (error) {
      return { success: false, message: error?.message };
    }
  }

  /**
   * Checks if the user is not found in the community
   * @param messageFromCircle Message received from Circle API
   */
  private isUserNotInCommunity(messageFromCircle: string): boolean {
    return messageFromCircle.includes(this.USER_NOT_FOUND_ERROR);
  }

  /**
   * Removes a member from a space
   * @param email Email address of the member to be removed
   * @param spaceId Id of the Circle space from which the member needs to be removed
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   */
  async removeSpaceMember(
    email: string,
    spaceId: number,
    domain: CommunityType,
  ): Promise<any> {
    const { apiToken } = this.getApiTokenAndCommunityID(domain);
    const endpoint = `${this.v2ApiUrl}/space_members?email=${email}&space_id=${spaceId}`;
    const headers = {
      Authorization: `Token ${apiToken}`,
      'Content-Type': 'application/json',
    };
    try {
      const result = await GlobalHelpers.makeAxiosRequest(
        endpoint,
        RequestMethods.POST,
        null,
        headers,
      );
      return result;
    } catch (error) {
      console.error(
        `Error while adding ${domain} member to space ${spaceId}: ${error}`,
      );
    }
  }

  /**
   * Removes a member from a space using `v1` of the Circle API
   * @param email Email address of the member to be removed
   * @param spaceId Id of the Circle space from which the member needs to be removed
   * @param domain Circle community to which the customer needs to be added (PXL | PXS)
   */
  async removeSpaceMemberV1(
    email: string,
    spaceId: number,
    domain: CommunityType,
  ): Promise<PXActionResult> {
    const { apiToken, communityId } = this.getApiTokenAndCommunityID(domain);
    const endpoint = `${this.v1ApiUrl}/space_members?email=${email}&space_id=${spaceId}&community_id=${communityId}`;
    const headers = {
      Authorization: `Token ${apiToken}`,
      'Content-Type': 'application/json',
    };
    try {
      const result = await GlobalHelpers.makeAxiosRequest(
        endpoint,
        RequestMethods.DELETE,
        null,
        headers,
      );
      const isRemoved = result?.success;
      return {
        success: isRemoved,
        message: result?.message,
      };
    } catch (error) {
      return {
        success: false,
        message: error?.message,
      };
    }
  }

  /**
   * Generates a link to a space based on the provided domain and space slug
   * @param domain Domain of the space (PXL | PXS)
   * @param spaceSlug Slug of the space
   * @returns A string representing the link to the space
   */
  generateSpaceLink(domain: CommunityType, spaceSlug: string): string {
    if (domain === CommunityType.PXL) {
      return `https://paradox-learning.circle.so/c/${spaceSlug}`;
    } else if (domain === CommunityType.PXS) {
      return `https://paradox-school.circle.so/c/${spaceSlug}`;
    }
    return 'https://portal.paradox.io';
  }

  /**
   * Generates a link to a community based on the provided domain
   * @param domain Domain of the community (PXL | PXS)
   * @returns A string representing the link to the community
   */
  generateCommunityLink(domain: CommunityType): string {
    if (domain === CommunityType.PXL) {
      return `https://paradox-learning.circle.so/home`;
    } else if (domain === CommunityType.PXS) {
      return `https://paradox-school.circle.so/home`;
    }
    return 'https://portal.paradox.io';
  }

  /**
   * Returns the API token and community ID for the given Circle domain.
   * @param domain The domain of the community.
   */
  getApiTokenAndCommunityID(domain: CommunityType): {
    apiToken: string;
    communityId: string;
  } {
    let apiToken: string;
    let communityId: string;

    switch (domain) {
      case CommunityType.PXL:
        apiToken = this.PXL_API_KEY;
        communityId = this.PXL_COMMUNITY_ID;
        break;
      case CommunityType.PXS:
        apiToken = this.PXS_API_KEY;
        communityId = this.PXS_COMMUNITY_ID;
        break;
      default:
        throw new Error(`Invalid domain for Circle Access: ${domain}`);
    }

    return { apiToken, communityId };
  }

  /**
   * Gets a space member's ID using the `space_id` and `email` of the space member
   * @param spaceId ID of the space to get the member from
   * @param email Email of the space member
   * @param community Type of the community (PXL or PXS)
   */
  async getSpaceMemberIdV1(
    spaceId: number,
    email: string,
    community: CommunityType,
  ): Promise<number> {
    try {
      const url = `${this.v2ApiUrl}/space_member?space_id=${spaceId}&email=${email}`;
      const token =
        community === CommunityType.PXL
          ? this.PXL_API_V2_KEY
          : this.PXS_API_V2_KEY;
      const headers = {
        'Content-Type': 'application/json',
        Authorization: `Token ${token}`,
      };
      const memberInfo = await GlobalHelpers.makeAxiosRequest(
        url,
        RequestMethods.GET,
        null,
        headers,
      );
      return memberInfo?.community_member_id;
    } catch (err) {
      if (err instanceof AxiosError) {
        if (err.response?.status === 404) {
          return null;
        }
      }
      throw err;
    }
  }
}
