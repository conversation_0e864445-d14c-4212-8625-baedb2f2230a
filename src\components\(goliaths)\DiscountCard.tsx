"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "../ui/card";
import { buttonVariants, cardVariants, itemVariants } from "@/lib/goliaths";
import Button from "../base/button";
import { useTranslations } from "next-intl";
import { useUser } from "@clerk/nextjs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

type DiscountCardProps = {
  discountCode?: { code: string; discount: number };
  onOpenModal: () => void;
};

export default function DiscountCard({ discountCode, onOpenModal }: DiscountCardProps) {
  const { user } = useUser();
  const t = useTranslations("goliaths");
  const { toast } = useToast();

  return (
    <motion.div className="my-4 w-10/12" variants={cardVariants} initial="hidden" animate="visible">
      <Card className="bg-[#171717] p-4 text-white">
        <CardHeader>
          <CardTitle>
            <div className="font-anton text-2xl font-medium tracking-wide uppercase">
              {t("giftTitle")}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <motion.div variants={itemVariants} className="space-y-4 text-base leading-relaxed">
            <p>{t("giftGreeting", { name: user?.firstName ?? "" })}</p>
            <p>
              {t.rich("giftLine1", {
                strong: (chunks: React.ReactNode) => (
                  <span className="font-semibold">{chunks}</span>
                ),
              })}
            </p>
            <div>
              {t("giftLine2")}{" "}
              <a
                href="https://www.paradox.io/fr/pxl/psychologie-de-largent?referrerPath=%2Ffr"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:underline"
              >
                {t("giftProgramName")}
              </a>
              {t("giftLine2Suffix")}
            </div>
            <ul className="list-inside list-disc space-y-2 pl-4">
              <li>
                {t.rich("giftList1", {
                  strong: (chunks: React.ReactNode) => (
                    <span className="font-semibold">{chunks}</span>
                  ),
                })}
              </li>
              {discountCode && discountCode?.discount > 0 && (
                <li className="font-semibold">
                  {t("giftList2", { discount: discountCode.discount, code: discountCode.code })} :
                  <span className="ml-2 space-x-1">
                    <Badge
                      className="cursor-pointer bg-white text-black hover:text-white"
                      onClick={() => {
                        navigator.clipboard.writeText(discountCode.code);
                        toast({
                          title: t("giftCodeCopied"),
                          description: t("giftCodeCopiedDescription"),
                          variant: "default",
                          duration: 5000,
                        });
                      }}
                    >
                      {discountCode.code}
                    </Badge>
                  </span>
                </li>
              )}
            </ul>
            {discountCode && discountCode?.discount > 0 && (
              <p className="text-xs text-yellow-400">{t("giftExpiration")}</p>
            )}
            <p>
              {t.rich("giftCTA", {
                strong: (chunks: React.ReactNode) => (
                  <span className="font-semibold">{chunks}</span>
                ),
              })}
            </p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
              className="mt-4 inline-block"
            >
              <motion.div variants={buttonVariants} whileHover="hover">
                <Button onClick={onOpenModal}>{t("invest-with-david")}</Button>
              </motion.div>
            </motion.div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
