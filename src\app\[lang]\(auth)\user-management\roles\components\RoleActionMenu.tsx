"use client";

import { IListRole, IRoleBase } from "@px-shared-account/hermes";
import { Co<PERSON>, Edit, Ellipsis, Trash } from "lucide-react";
import { useTranslations } from "next-intl";
import { memo } from "react";

import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const RoleActionMenu = memo(
  ({
    role,
    onEdit,
    onDuplicate,
    onDelete,
  }: {
    role: IListRole;
    onEdit: (role: IRoleBase) => void;
    onDuplicate: (role: IRoleBase) => void;
    onDelete: (role: IListRole) => void;
  }) => {
    const t = useTranslations("gestion.users.roles");

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Ellipsis className="cursor-pointer" />
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem
            className="flex cursor-pointer items-center gap-2"
            onClick={() => onEdit(role)}
          >
            <Edit className="mr-2 h-4 w-4" />
            {t("edit")}
          </DropdownMenuItem>
          <DropdownMenuItem
            className="flex cursor-pointer items-center gap-2"
            onClick={() => onDuplicate(role)}
          >
            <Copy className="mr-2 h-4 w-4" />
            {t("duplicate")}
          </DropdownMenuItem>
          <DropdownMenuItem
            disabled={role.members > 0}
            className="flex cursor-pointer items-center gap-2 text-destructive"
            onClick={() => onDelete(role)}
          >
            <Trash className="mr-2 h-4 w-4" />
            {t("delete")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  },
);

RoleActionMenu.displayName = "RoleActionMenu";

export default RoleActionMenu;
