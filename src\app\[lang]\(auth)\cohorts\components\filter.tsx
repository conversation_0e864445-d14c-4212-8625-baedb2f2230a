import { useTranslations } from "next-intl";
import { FilterBar } from "@/components/base/filter-bar";
import { FilterStep } from "@/components/base/filter-bar/types";
import { CohortStatus } from "@px-shared-account/hermes";

interface FilterProps {
  onCreateClick?: () => void;
  onChange?: (values: Record<string, string>) => void;
  value?: Record<string, string>;
}

export function Filter({ onCreateClick, onChange, value }: FilterProps) {
  const t = useTranslations();

  const filterConfig: { groups: FilterStep[] } = {
    groups: [
      {
        id: "status",
        label: t("cohorts.filters.status.label"),
        type: "filter",
        options: Object.values(CohortStatus).map((status) => ({
          label: t(`cohorts.filters.status.options.${status.toLowerCase()}`),
          value: status,
        })),
      },
    ],
  };

  return (
    <div className="flex items-center gap-2">
      <FilterBar
        steps={filterConfig.groups}
        forceModal={true}
        triggerText={t("common.filters")}
        onChange={onChange}
        value={value}
      />
    </div>
  );
}
