"use client";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

interface FetcherConfig {
  method?: string;
  body?: any;
}

/**
 * Creates a fetch wrapper function that automatically includes authentication
 * @param tokenFn - Function that returns a Promise resolving to an authentication token or null
 * @returns An async function that takes a URL and makes an authenticated API request
 * @throws Will throw an error if the API request fails
 * @example
 * const apiFetch = fetcher(getAuthToken);
 * const data = await apiFetch('/api/users');
 */
const fetcher =
  (tokenFn: () => Promise<string | null>) =>
  async (url: string, config: FetcherConfig = {}) => {
    try {
      const token = await tokenFn();
      if (!token) {
        return { data: null, isLoading: false, error: null };
      }

      // Detect if body is FormData to set appropriate headers
      const isFormData = config.body instanceof FormData;

      const headers: Record<string, string> = {
        Authorization: `Bearer ${token}`,
        "client-name": "px-app",
      };

      // Only set Content-Type for JSON, let browser set it for FormData
      if (!isFormData) {
        headers["Content-Type"] = "application/json";
      }

      const response = await fetch(`${API_URL}${url}`, {
        method: config.method || "GET",
        headers,
        ...(config.body && {
          body: isFormData ? config.body : JSON.stringify(config.body),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || errorData?.code || `HTTP error! status: ${response.status}`,
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`#(fetcher): ${url}`, { error });
      throw error;
    }
  };

export default fetcher;
