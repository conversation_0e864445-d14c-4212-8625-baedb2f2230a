import { Injectable } from '@nestjs/common';
import { ChargeBee } from 'chargebee-typescript';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import {
  Customer as ChargeBeeCustomer,
  Invoice as ChargebeeInvoice,
  Estimate,
  Coupon,
  InvoiceIssuedCreditNote,
  Card,
} from 'chargebee-typescript/lib/resources';
import {
  Discount,
  ProductOffer,
  ProductPlan,
  ProductPlanPrice,
  Subscription,
} from '@entities';
import {
  AttachmentMap,
  CouponApplicabilityInfo,
  ChargebeeCustomer,
  RequestMethods,
  SubscriptionDownsellParams,
  ChargeBeeWebhookPayload,
} from '@types';
import {
  DiscountApplication,
  DiscountDurationType,
  DiscountStatus,
  DiscountType,
  FiscalEntity,
} from '@enums';
import {
  ChargeBeeConstraintItem,
  ChargeBeeCouponConstraint,
  DiscountConstraint,
} from '@types';
import { UpdateSubscriptionDTO } from '@dtos';
import { ConfigService, ChargebeeSecrets } from '@config';
import { GlobalHelpers } from '@helpers';

@Injectable()
export class ChargebeeBillingService {
  private chargebeeApiUrl: string;
  private chargebeeApiKey: string;
  private chargebeeSite: string;
  private chargebeeClient: ChargeBee;
  private readonly secrets: ChargebeeSecrets;

  constructor(private readonly configService: ConfigService) {
    this.secrets = this.configService.chargebeeSecrets;
    this.chargebeeApiKey = this.secrets.CHARGEBEE_API_KEY;
    this.chargebeeApiUrl = this.secrets.CHARGEBEE_API_URL;
    this.chargebeeSite = this.secrets.CHARGEBEE_SITE;
    this.chargebeeClient = new ChargeBee();
    this.chargebeeClient.configure({
      api_key: this.chargebeeApiKey,
      site: this.chargebeeSite,
    });
  }

  /**
   * Creates an Add-on in ChargeBee which maps to a plan in Paradox OS
   * @param planDetails Details of the Add-on to create in ChargeBee
   * @param productFamily Value for the `cf_product_family` custom field for the Add-on
   * @param productLine Value for the `cf_product_line` custom field for the Add-on
   */
  async createAddon(
    planDetails: Partial<ProductPlan>,
    productFamily: string,
    productLine: string,
  ): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const addOnData = {
      id: planDetails.chargebeeId,
      name: planDetails.internalName,
      external_name: planDetails.externalName,
      description: planDetails.description,
      cf_product_family: productFamily,
      cf_product_line: productLine,
      type: 'ADDON',
      item_family_id: 'WEB',
      cf_sku: planDetails.crmSku,
    };

    return GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + '/items',
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      addOnData,
    );
  }

  /**
   * Updates an Add-on in ChargeBee
   * @param planId Chargebee ID of the plan to update
   * @param planUpdates Updates for the plan
   */
  async updateAddon(
    planId: string,
    planUpdates: QueryDeepPartialEntity<ProductPlan>,
  ): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const addOnData = {
      name: planUpdates.internalName ?? undefined,
      external_name: planUpdates.externalName ?? undefined,
      description: planUpdates.description ?? undefined,
    };

    return GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/items/${planId}`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      addOnData,
    );
  }

  /**
   * Creates a plan in ChargeBee which maps to an offer in Paradox OS
   * @param offerDetails Details of the Plan to create in ChargeBee
   */
  async createPlan(offerDetails: Partial<ProductOffer>): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const planData = {
      id: offerDetails.chargebeeId,
      name: offerDetails.name,
      external_name: offerDetails.externalName,
      item_applicability: 'restricted',
      type: 'PLAN',
      item_family_id: 'WEB',
    };

    const createdPlan = await GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + '/items',
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      planData,
    );

    if (offerDetails.isForever) {
      await this.createItemPriceForForeverPlan(
        planData.id,
        planData.name,
        offerDetails.trialDaysMonthly,
        'month',
      );
      await this.createItemPriceForForeverPlan(
        planData.id,
        planData.name,
        offerDetails.trialDaysYearly,
        'year',
      );
    } else {
      await this.createItemPriceForPlan(planData.id, planData.name);
    }
    return createdPlan;
  }

  /**
   * Updates a plan in ChargeBee
   * @param offerId Chargebee ID of the offer to update
   * @param offerUpdates Updates for the offer
   */
  async updatePlan(
    offerId: string,
    offerUpdates: QueryDeepPartialEntity<ProductOffer>,
    fiscalEntity: FiscalEntity,
  ): Promise<any> {
    const chargebeeBusinessEntity =
      this.secrets[`CHARGEBEE_BUSINESS_ID_${fiscalEntity}`];
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'chargebee-business-entity-id': chargebeeBusinessEntity,
    };

    const addOnData = {
      name: offerUpdates.name ?? undefined,
      external_name: offerUpdates.name ?? undefined,
    };

    if (offerUpdates?.name) {
      const planPrices = await GlobalHelpers.makeAxiosRequest(
        this.chargebeeApiUrl + `/item_prices?item_id[is]=${offerId}`,
        RequestMethods.GET,
        {
          username: this.chargebeeApiKey,
          password: '',
        },
        headers,
      );
      for (const price of planPrices.list) {
        const res = await this.updateItemPriceNameForPlan(
          price.item_price.id,
          offerUpdates.name.toString() + '-' + price.item_price.currency_code,
          offerUpdates.name.toString(),
        );
      }
    }

    return GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/items/${offerId}`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      addOnData,
    );
  }

  /**
   *
   * @param itemPriceInfo Details of the Add-on price to create in ChargeBee
   * @param planChargebeeId ChargeBee ID of the Add-on to which this price will attach
   * @param taxProfile Tax profile of this item price
   */
  async createItemPrice(
    itemPriceInfo: Partial<ProductPlanPrice>,
    planChargebeeId: string,
    taxProfile: string,
  ): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    const isForeverMonthly =
      itemPriceInfo?.periodUnit?.includes('monthly-forever');
    const isForeverYearly =
      itemPriceInfo?.periodUnit?.includes('yearly-forever');

    const itemPriceData = {
      id: itemPriceInfo.chargebeeId,
      name: itemPriceInfo.internalName,
      external_name: itemPriceInfo.externalName,
      description: itemPriceInfo.description,
      pricing_model: 'per_unit',
      period: 1,
      period_unit: isForeverYearly ? 'year' : 'month',
      currency_code: 'EUR',
      price: Math.round(itemPriceInfo.amountPerBillingCycle * 100),
      item_id: planChargebeeId,
      item_type: 'ADDON',
      cf_billing_cycles:
        isForeverMonthly || isForeverYearly
          ? undefined
          : itemPriceInfo.totalBillingCycles,
      item_family_id: 'WEB',
      tax_detail: { tax_profile_id: taxProfile },
    };

    return GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + '/item_prices',
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      itemPriceData,
    );
  }

  /**
   *
   * @param planId ChargeBee ID of the Add-on to which this price will attach
   */
  async createItemPriceForPlan(planId: string, planName: string): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const itemPriceData = {
      id: null,
      name: null,
      external_name: planName,
      description: `Price with 0 amount for plan: ${planName}`,
      currency_code: null,
      price: 0,
      period: 1,
      period_unit: 'month',
      item_id: planId,
      item_type: 'PLAN',
      item_family_id: 'WEB',
    };

    for (const currency of ['AED', 'EUR']) {
      itemPriceData.name = `${planName}-${currency}`;
      itemPriceData.id = crypto.randomUUID();
      itemPriceData.currency_code = currency;
      await GlobalHelpers.makeAxiosRequest(
        this.chargebeeApiUrl + '/item_prices',
        RequestMethods.POST,
        {
          username: this.chargebeeApiKey,
          password: '',
        },
        headers,
        itemPriceData,
      );
    }
  }

  /**
   * Creates an item price in Chargebee for a Forever billing cycles plan
   * @param planId ChargeBee ID of the Plan to which this price will attach
   */
  async createItemPriceForForeverPlan(
    planId: string,
    planName: string,
    trialPeriod: number,
    recurrence: 'month' | 'year',
  ): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const itemPriceData = {
      id: null,
      name: null,
      external_name: planName,
      description: `Per ${recurrence} item price for : ${planName}`,
      currency_code: null,
      price: 0,
      period: 1,
      period_unit: recurrence,
      item_id: planId,
      item_type: 'PLAN',
      item_family_id: 'WEB',
      trial_period: trialPeriod,
      trial_period_unit: 'day',
    };

    for (const currency of ['AED', 'EUR']) {
      itemPriceData.name = `${planName}- ${recurrence}ly - ${currency}`;
      itemPriceData.id = crypto.randomUUID();
      itemPriceData.currency_code = currency;
      await GlobalHelpers.makeAxiosRequest(
        this.chargebeeApiUrl + '/item_prices',
        RequestMethods.POST,
        {
          username: this.chargebeeApiKey,
          password: '',
        },
        headers,
        itemPriceData,
      );
    }
  }

  /**
   * updates the name of the plan price in chargebee when the plan
   * name is changed (used when duplicating an offer).
   * @param planPriceId id of the plan price to update
   * @param priceName new name for the plan price
   * @param externalName new external name for the plan price
   */
  async updateItemPriceNameForPlan(
    planPriceId: string,
    priceName: string,
    externalName: string | undefined,
  ): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const itemPriceData = {
      name: priceName,
      externalName: externalName ? externalName : priceName,
    };

    const res = await GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/item_prices/${planPriceId}`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      itemPriceData,
    );
    return res;
  }

  /**
   * Updates an Add-on item price in ChargeBee
   * @param itemPriceId ChargeBee ID of the item price to update
   * @param itemPriceUpdates  Updates for the Add-on price
   * @param taxProfile Tax profile of this item price
   */
  async updateItemPrice(
    itemPriceId: string,
    itemPriceUpdates: QueryDeepPartialEntity<ProductPlanPrice>,
    taxProfile?: string,
  ): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    let itemPriceData = {};
    if (itemPriceUpdates !== null) {
      itemPriceData = {
        name: itemPriceUpdates.internalName ?? undefined,
        external_name: itemPriceUpdates.externalName ?? undefined,
        description: itemPriceUpdates.description ?? undefined,
        price:
          Math.round(
            Number(itemPriceUpdates.amountPerBillingCycle.toString()) * 100,
          ) ?? undefined,
        cf_billing_cycles: itemPriceUpdates.totalBillingCycles ?? undefined,
      };
    }

    if (taxProfile) {
      itemPriceData['tax_detail'] = { tax_profile_id: taxProfile };
    }

    return GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/item_prices/${itemPriceId}`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      itemPriceData,
    );
  }

  /**
   * Deletes a plan(add-on) in ChargeBee
   * @param id Chargebee `id` of the plan (add-on) to delete
   */
  async deletePlan(id: string): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const deletedPlan = await GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/items/${id}/delete`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
    );
    return deletedPlan;
  }

  /**
   * Deletes a item price in ChargeBee
   * @param id Chargebee `id` of the item-price to delete
   */
  async deletePrice(id: string): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const deletedPrice = await GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/item_prices/${id}/delete`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
    );
    return deletedPrice;
  }

  /**
   * Creates a coupon in ChargeBee which maps to a `Discount` in Paradox OS
   * @param couponInfo Details of the coupon to create in ChargeBee
   */
  async createCoupon(couponInfo: Partial<Discount>): Promise<any> {
    const couponConstraints = this.generateCouponConstraints(
      couponInfo.applyOn,
      couponInfo?.code,
    );

    const couponData = {
      id: encodeURIComponent(couponInfo.code),
      name: encodeURIComponent(couponInfo.name),
      status:
        couponInfo.status !== DiscountStatus.Active
          ? 'archived'
          : DiscountStatus.Active,
      invoice_name: couponInfo.invoiceName,
      discount_type: couponInfo.type,
      discount_percentage:
        couponInfo.type === DiscountType.PERCENTAGE
          ? couponInfo.amount
          : undefined,
      discount_amount:
        couponInfo.type === DiscountType.FIXED
          ? Math.round(couponInfo.amount * 100)
          : undefined,
      currency_code: couponInfo.type === DiscountType.FIXED ? 'EUR' : undefined,
      apply_on: couponInfo.applyOn,
      duration_type: couponInfo.durationType,
      period: undefined,
      period_unit: undefined,
      valid_till: undefined,
      max_redemptions: couponInfo.maxRedemptions,
      item_constraints: couponConstraints,
    };

    if (couponInfo.durationType === DiscountDurationType.LIMITED_PERIOD) {
      couponData.period = couponInfo.durationPeriodAmount;
      couponData.period_unit = couponInfo.durationPeriodUnit;
    }

    if (couponInfo.validUntil) {
      couponData.valid_till = Math.floor(
        new Date(couponInfo.validUntil).getTime() / 1000,
      );
    }

    await this.chargebeeClient.coupon.create_for_items(couponData).request();
  }

  /**
   * Updates a coupon in ChargeBee
   * @param id Chargebee `id` of the coupon to update
   * @param couponUpdates Updates for the coupon
   */
  async updateCoupon(
    id: string,
    couponUpdates: Partial<Discount>,
  ): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    const couponData = {
      id,
      name: couponUpdates.name ?? undefined,
      status:
        couponUpdates.status !== DiscountStatus.Active
          ? 'archived'
          : DiscountStatus.Active,
      invoice_name: couponUpdates.invoiceName ?? undefined,
      discount_type: couponUpdates.type ?? undefined,
      discount_percentage:
        couponUpdates.type === DiscountType.PERCENTAGE
          ? couponUpdates.amount
          : undefined,
      discount_amount:
        couponUpdates.type === DiscountType.FIXED
          ? couponUpdates.amount
          : undefined,
      currency_code:
        couponUpdates.type === DiscountType.FIXED ? 'EUR' : undefined,
      apply_on: couponUpdates.applyOn ?? undefined,
      duration_type: couponUpdates.durationType ?? undefined,
      period: undefined,
      period_unit: undefined,
      valid_till: undefined,
      max_redemptions: couponUpdates.maxRedemptions ?? undefined,
    };

    if (couponData.duration_type === DiscountDurationType.LIMITED_PERIOD) {
      couponData.period = couponUpdates.durationPeriodAmount ?? undefined;
      couponData.period_unit = couponUpdates.durationPeriodUnit ?? undefined;
    }

    if (couponUpdates.validUntil) {
      couponData.valid_till = Math.floor(
        new Date(couponUpdates.validUntil).getTime() / 1000,
      );
    }

    return GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/coupons/${id}/update_for_items`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      couponData,
    );
  }

  /** Deletes a coupon in ChargeBee if it's not applied to any subscription
   * otherwise archives it. Archived coupons can't be used for new subscriptions
   * @param id Chargebee `id` of the coupon to delete
   */
  async deleteCoupon(id: string): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    return GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/coupons/${id}/delete`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
    );
  }

  /**
   * Generates `item_constraints` for ChargeBee coupon
   * @param couponApplication Specifies whether coupon should be applied to whole invoice
   * @param couponCode Coupon code, used to determine coupon constraints for quick discounts
   * or individual items
   */
  generateCouponConstraints(
    couponApplication: DiscountApplication,
    couponCode?: string,
  ): ChargeBeeCouponConstraint[] | undefined {
    if (couponCode?.startsWith('PX24HEROES')) {
      return [
        { constraint: 'all', item_type: 'plan' },
        { constraint: 'all', item_type: 'addon' },
      ];
    }
    switch (couponApplication) {
      case DiscountApplication.INVOICE_AMOUNT:
        return [
          { constraint: 'all', item_type: 'plan' },
          { constraint: 'all', item_type: 'addon' },
          { constraint: 'all', item_type: 'charge' },
        ];
      case DiscountApplication.EACH_SPECIFIED_ITEM:
        return [
          { constraint: 'none', item_type: 'plan' },
          { constraint: 'none', item_type: 'addon' },
          { constraint: 'none', item_type: 'charge' },
        ];
      default:
        return undefined;
    }
  }

  /**
   * Updates `item_constraints` for ChargeBee coupon
   * @param id Chargebee `id` of the coupon to update
   * @param attachedTo Specifies items to which this discount is applied
   */
  async updateCouponConstraints(
    id: string,
    attachedTo: DiscountConstraint[] = [],
  ): Promise<any> {
    const constraintsForChargeBee: ChargeBeeCouponConstraint[] = [];
    const itemTypeMapping: Record<string, ChargeBeeConstraintItem> = {
      offer: 'plan',
      plan: 'addon',
    };

    attachedTo.forEach(({ itemType, itemIds }) => {
      if (itemIds[0] === 'all') {
        constraintsForChargeBee.push({
          item_type: itemTypeMapping[itemType],
          constraint: 'all',
        });
      }

      if (itemIds[0] === 'none') {
        constraintsForChargeBee.push({
          item_type: itemTypeMapping[itemType],
          constraint: 'none',
        });
      }

      if (itemIds[0] !== 'all' && itemIds[0] !== 'none') {
        constraintsForChargeBee.push({
          item_type: itemTypeMapping[itemType],
          constraint: 'specific',
          item_price_ids: `[${itemIds.toString()}]`,
        });
      }
    });

    const chargebee = new ChargeBee();
    chargebee.configure({
      site: this.chargebeeSite,
      api_key: this.chargebeeApiKey,
    });

    return new Promise((resolve, reject) => {
      chargebee.coupon
        .update_for_items(id, {
          item_constraints: this.generateUpdateConstraints(attachedTo),
        })
        .request(function (error: any, result: any) {
          if (error) {
            console.log(error);
            reject(error);
          } else {
            const coupon: typeof chargebee.coupon = result.coupon;
            resolve(coupon);
          }
        });
    });
  }

  /**
   * Generates Chargebee's version of constraints for attaching `addon` or `plan` to a discount
   * @param attachedTo Discount constraints to generate Chargebee Constraints from
   * @returns An array of `ChargeBeeCouponConstraint`
   */
  generateUpdateConstraints(
    attachedTo: DiscountConstraint[] = [],
  ): ChargeBeeCouponConstraint[] {
    const constraintsForChargeBee: ChargeBeeCouponConstraint[] = [];
    const itemTypeMapping: Record<string, ChargeBeeConstraintItem> = {
      offer: 'plan',
      plan: 'addon',
    };

    for (const { itemIds, itemType } of attachedTo) {
      if (itemIds[0] === 'all') {
        constraintsForChargeBee.push({
          item_type: itemTypeMapping[itemType],
          constraint: 'all',
        });
        return constraintsForChargeBee;
      }
      if (itemIds[0] === 'none') {
        constraintsForChargeBee.push({
          item_type: itemTypeMapping[itemType],
          constraint: 'none',
        });
        return constraintsForChargeBee;
      }
      if (itemIds.length >= 1 && itemIds[0] !== 'all') {
        constraintsForChargeBee.push({
          item_type: itemTypeMapping[itemType],
          constraint: 'specific',
          item_price_ids: `[${itemIds.toString()}]`,
        });
      }
    }

    return constraintsForChargeBee;
  }

  /**
   * Attaches `add-ons` to `plan` in ChargeBee
   * @param planId `chargebeeId` of the `ProductOffer` to attach `ProductPlan`s to
   * @param addons Array of `chargebeeId`s of `ProductPlan`s to attach
   * @param fiscalEntity Business entity in Chargebee this offer is created in
   */
  async attachAddonsToPlan(
    planId: string,
    addons: string[],
    fiscalEntity: FiscalEntity,
  ): Promise<AttachmentMap> {
    const chargebeeBusinessEntity =
      this.secrets[`CHARGEBEE_BUSINESS_ID_${fiscalEntity}`];
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'chargebee-business-entity-id': chargebeeBusinessEntity,
    };
    const chargebeeAttachmentMap = {};
    for (const addon of addons) {
      const attachmentInfo = {
        item_id: addon,
        type: 'optional',
      };
      const chargeBeeResponse = await GlobalHelpers.makeAxiosRequest(
        this.chargebeeApiUrl + `/items/${planId}/attached_items`,
        RequestMethods.POST,
        {
          username: this.chargebeeApiKey,
          password: '',
        },
        headers,
        attachmentInfo,
      );
      const attachmentId = chargeBeeResponse?.attached_item?.id;
      chargebeeAttachmentMap[addon] = attachmentId;
    }

    return chargebeeAttachmentMap;
  }

  /**
   * Removes `add-ons` from `plan` in ChargeBee
   * @param planId `chargebeeId` of the `ProductOffer` to remove `ProductPlan`s from
   * @param attachmentIds Array of `chargebeeId`s `attachment_id`s of `ProductPlan`s to remove
   * @param fiscalEntity Business entity in Chargebee this offer is created in
   */
  async removeAddonsFromPlan(
    planId: string,
    attachmentIds: string[],
    fiscalEntity: FiscalEntity,
  ): Promise<any> {
    const chargebeeBusinessEntity =
      this.secrets[`CHARGEBEE_BUSINESS_ID_${fiscalEntity}`];
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'chargebee-business-entity-id': chargebeeBusinessEntity,
    };
    const detachedAddonIds: string[] = [];
    for (const attachment of attachmentIds) {
      const attachmentInfo = {
        parent_item_id: planId,
      };
      const chargeBeeResponse = await GlobalHelpers.makeAxiosRequest(
        this.chargebeeApiUrl + `/attached_items/${attachment}/delete`,
        RequestMethods.POST,
        {
          username: this.chargebeeApiKey,
          password: '',
        },
        headers,
        attachmentInfo,
      );
      const {
        attached_item: { item_id: addonId, status: attachmentStatus },
      } = chargeBeeResponse;
      if (attachmentStatus === 'deleted') {
        detachedAddonIds.push(addonId);
      }
    }
    return detachedAddonIds;
  }

  /**
   * Retrieves the name and email of a customer from Chargebee.
   * @param customerId - The ID of the customer.
   * @returns A promise that resolves to an object containing the customer's name and email.
   */
  async getCustomerInfo(customerId: string): Promise<ChargeBeeCustomer> {
    const chargebee = new ChargeBee();
    chargebee.configure({
      site: this.chargebeeSite,
      api_key: this.chargebeeApiKey,
    });

    return new Promise((resolve, reject) => {
      chargebee.customer
        .retrieve(customerId)
        .request(function (error: any, result: any) {
          if (error) {
            console.log(error);
            reject(error);
          } else {
            const customer: ChargeBeeCustomer = result.customer;
            resolve(customer);
          }
        });
    });
  }

  /**
   * Retrieves a customer by their email address.
   * @param email - The email address of the customer.
   * @returns A Promise that resolves to a list of customer objects.
   */
  async getCustomersByEmail(email: string): Promise<ChargebeeCustomer[]> {
    const chargebee = new ChargeBee();
    chargebee.configure({
      site: this.chargebeeSite,
      api_key: this.chargebeeApiKey,
    });

    const customers: ChargebeeCustomer[] = [];
    return new Promise((resolve, reject) => {
      chargebee.customer
        .list({
          email: {
            is: email,
          },
        })
        .request(function (error: any, result: any) {
          if (error) {
            console.log(error);
            reject(error);
          } else {
            for (const list of result.list) {
              const customer: ChargeBeeCustomer = list?.customer;
              customers.push({
                id: customer?.id,
                firstName: customer?.first_name,
                lastName: customer?.last_name,
                email: customer?.email,
                phone: customer?.phone,
                country: customer?.billing_address?.country || 'N/A',
                city: customer?.billing_address?.city || 'N/A',
                address: customer?.billing_address?.line1 || 'N/A',
                zip: customer?.billing_address?.zip || 'N/A',
                wasFoundInChargeBee: true,
                businessEntityId: customer?.business_entity_id,
              });
            }
            resolve(customers);
          }
        });
    });
  }

  /**
   * Fetches a subscription's amount paid and amount remaining info
   * @param id ID of the subscription to get amount info for
   * @returns A promise that resolves to an object containing info about a subscription's amount
   */
  async getSubscriptionAmountInfo(id: string): Promise<{
    amountPaid: number;
    amountDue: number;
    amountRefunded: number;
  }> {
    const response = await this.chargebeeClient.invoice
      .list({
        subscription_id: { is: id },
      })
      .request();
    const invoicesList: ChargebeeInvoice[] = [];
    for (let i = 0; i < response?.list.length; i++) {
      const invoice: ChargebeeInvoice = response.list[i]?.invoice;
      invoicesList.push(invoice);
    }
    const invoiceCreditNotes: InvoiceIssuedCreditNote[] = [];
    for (const invoice of invoicesList) {
      if (invoice.issued_credit_notes?.length > 0) {
        invoiceCreditNotes.push(...invoice.issued_credit_notes);
      }
    }

    let amountRefunded = 0;
    for (const creditNote of invoiceCreditNotes) {
      if (creditNote?.cn_status !== 'refunded') continue;
      amountRefunded += creditNote?.cn_total;
    }

    const paid = invoicesList.reduce((acc: number, item: any) => {
      acc += item?.amount_paid;
      return acc;
    }, 0);
    const due = invoicesList.reduce((acc: number, item: any) => {
      acc += item?.amount_due;
      return acc;
    }, 0);

    return {
      amountPaid: paid - amountRefunded,
      amountDue: due,
      amountRefunded,
    };
  }

  /**
   * Gets coupon applicability and amount info
   * @param couponCode Chargebee's coupon code
   * @returns A promise that resolves to `CouponApplicabilityInfo`
   */
  async getCouponInfo(couponCode: string): Promise<CouponApplicabilityInfo> {
    if (!couponCode || couponCode === '') {
      return {
        couponApplicable: false,
      };
    }
    const response = await this.chargebeeClient.coupon
      .retrieve(couponCode)
      .request();
    console.log('cb response: ', response);
    const coupon: Coupon = response?.coupon;
    const couponTypeRaw = coupon?.discount_type?.replace('_', '');
    const couponType: DiscountType = DiscountType[couponTypeRaw?.toUpperCase()];
    const couponAmount = coupon?.discount_amount;
    const couponPercentageAmount = coupon?.discount_percentage;
    const couponDurationType =
      DiscountDurationType[coupon?.duration_type?.toUpperCase()];
    if (couponDurationType !== DiscountDurationType.FOREVER) {
      return {
        couponApplicable: false,
      };
    }
    return {
      couponApplicable: true,
      couponType,
      couponAmount,
      couponPercentageAmount,
    };
  }

  /**
   * Cancels a subscription in Chargebee
   * @param id Chargebee id of the subscription that needs to be cancelled
   * @returns A promise that resolves once the subscription is cancelled
   */
  async cancelSubscription(id: string): Promise<void> {
    return this.chargebeeClient.subscription.cancel(id).request();
  }

  /**
   *
   * @param id Chargebee id of the subscription that needs to be cancelled
   * @param cancelReasonCode Reason code for cancelling the subscription
   * @returns A promise that resolves once the subscription is cancelled
   */
  async cancelSubscriptionForItems(
    id: string,
    cancelReasonCode: string,
  ): Promise<void> {
    return this.chargebeeClient.subscription
      .cancel_for_items(id, { cancel_reason_code: cancelReasonCode })
      .request();
  }

  /**
   * Updates a Subscription in Chargebee - for now, only updating `crmId` is allowed
   * @param id Chargebee `id` of the subscription to update
   * @param updates Updates for the subscription, only `crmId` is allowed to be updated
   */
  async updateSubscription(
    id: string,
    updates: Pick<Subscription, 'crmId'>,
  ): Promise<void> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    await GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/subscriptions/${id}/update_for_items`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      {
        cf_deal_id: updates.crmId,
      },
    );
  }

  /**
   * Returns overdue invoices
   * @returns A promise that resolves to a list of overdue invoices for all the subscriptions
   */
  async getOverdueInvoices(): Promise<any> {
    const overdueInvoices = [];
    let hasMore = false;
    let offset: string;

    do {
      const result = await this.chargebeeClient.invoice
        .list({
          limit: 100,
          status: { in: ['payment_due'] },
          'sort_by[desc]': 'date',
          offset,
        })
        .request();
      offset = result?.next_offset;
      hasMore = offset ? true : false;
      overdueInvoices.push(...result?.list);
    } while (hasMore);
    return overdueInvoices;
  }

  /**
   * Retrieves an invoice from Chargebee
   * @param invoiceId Chargebee `id` of the invoice to retrieve
   * @returns A promise that resolves to the invoice object from Chargebee
   */
  async getInvoice(invoiceId: string): Promise<ChargebeeInvoice> {
    return this.chargebeeClient.invoice.retrieve(invoiceId).request();
  }

  /**
   * Retrieves the PDF link for an invoice from Chargebee
   * @param invoiceId Chargebee `id` of the invoice to retrieve the PDF link for
   * @returns A promise that resolves to the PDF link for the invoice
   */
  async getInvoicePDFLink(invoiceId: string): Promise<string> {
    const invoice = await this.chargebeeClient.invoice.pdf(invoiceId).request();
    return invoice?.download?.download_url;
  }

  /**
   * Retrieves the payment source for a subscription
   * @param subscriptionId Chargebee `id` of the subscription to retrieve the payment source for
   * @returns A promise that resolves to the payment source for the subscription
   */
  async getPaymentSourceForSubscription(subscriptionId: string): Promise<Card | 'bank_transfer'> {
      const subscription = await this.chargebeeClient.subscription.retrieve(subscriptionId).request();
    if (subscription?.card) {
      return subscription?.card as Card;
    }
    const paidInvoices = await this.chargebeeClient.invoice.list({
      subscription_id: { is: subscriptionId },
      status: { in: ['paid'] },
    }).request();
    if (paidInvoices?.list?.length > 0) {
      return 'bank_transfer';
    }
    return null;
  }

  /**
   * Returns overdue invoices for a specific subscription
   * @returns A promise that resolves to a list of overdue invoices for the specified subscription
   */
  async getOverdueInvoicesForSubscription(
    subscriptionId: string,
  ): Promise<any> {
    const overdueInvoices = [];
    let hasMore = false;
    let offset: string;

    do {
      const result = await this.chargebeeClient.invoice
        .list({
          limit: 100,
          subscription_id: { is: subscriptionId },
          status: { in: ['payment_due', 'not_paid'] },
          'sort_by[desc]': 'date',
          offset,
        })
        .request();
      offset = result?.next_offset;
      hasMore = offset ? true : false;
      overdueInvoices.push(...result?.list);
    } while (hasMore);
    return overdueInvoices;
  }

  /**
   *
   * @param invoiceId Chargebee `id` of the invoice to record offline payment for
   * @param amount Amount (not in cents) to record for the invoice
   * @param paymentRef Reference number of the payment
   * @param paymentMethodId Payment method ID, optional but when sent must be one of (`AirWallex`, `IbanFirst` or `Revolut`)
   * @param date Time at which the payment occurred, in seconds
   * @returns A promise which resolves to the API response from Chargebee (including info about `invoice` & newly added `transaction`)
   */
  async recordOfflinePaymentForInvoice(
    invoiceId: string,
    amount: number,
    paymentRef?: string,
    paymentMethodId?: string,
    date?: number,
  ): Promise<any> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    return GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/invoices/${invoiceId}/record_payment`,
      RequestMethods.POST,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
      headers,
      {
        comment: 'Payment auto-matched by Paradox OS',
        transaction: {
          amount: Math.round(amount * 100),
          payment_method: 'custom',
          reference_number: paymentRef,
          custom_payment_method_id: paymentMethodId,
          date,
        },
      },
    );
  }

  /**
   * Updates the line items' amount and billing cycles for a subscription. Also updates the
   * billing cycle for the whole subscription.
   * @param id Chargebee `id` of the subscription to update items for
   * @param updates Updates for the subscription, including line items & updated billing cycles
   * @returns A promise that resolves with
   */
  async updateSubscriptionLineItems(
    id: string,
    updates: UpdateSubscriptionDTO,
  ): Promise<any> {
    const updatedItems = [];
    for (const item of updates?.updatedItems) {
      updatedItems.push({
        item_price_id: item.id,
        item_type: 'addon',
        quantity: item.updatedQuantity,
        unit_price: item.updatedAmount,
        billing_cycles: item.updatedBillingCycles,
      });
    }

    const result = await this.chargebeeClient.subscription
      .update_for_items(id, {
        subscription_items: updatedItems,
        billing_cycles: updates?.updatedBillingCycles,
        meta_data: {
          billingCyclesChanged: true,
        },
      })
      .request();

    return result;
  }

  /**
   * Retrieves plan price from Chargebee based on the Id
   * @param id plan_price_id of the plan_price to retrieve
   * @returns A promise that resolves to the plan_price object from Chargebee
   */
  async getPlanPrice(id: string): Promise<any> {
    return await this.chargebeeClient.item_price.retrieve(id).request();
  }

  /**
   * Creates a new subscription in Chargebee
   * @param customer Chargebee ID of the customer to create the subscription for
   * @param subscriptionDetails object containing the new subscription details
   * @returns A promise that resolves to a new subscription object
   */
  async createSubscription(
    customerId: string,
    subscriptionDetails: SubscriptionDownsellParams,
  ): Promise<any> {
    return this.chargebeeClient.subscription
      .create_with_items(customerId, subscriptionDetails)
      .request();
  }

  /**
   * Updates the metadata of a subscription in Chargebee
   * @param subscriptionId Chargebee ID of the subscription to update
   * @param metadata New metadata for the subscription
   * @returns A promise that resolves to the updated subscription object
   */
  async updateSubscriptionMetadata(subscriptionId: string, metadata: any) {
    return this.chargebeeClient.subscription
      .update_for_items(subscriptionId, {
        meta_data: metadata,
      })
      .request();
  }

  /**
   * Returns the future invoice estimate for a subscription
   * @param subscriptionId Chargebee ID of the subscription to get the future invoice
   * @returns A promise that resolves to the future invoice estimate
   */
  async getFutureInvoiceEstimate(subscriptionId: string): Promise<Estimate> {
    const estimate = await this.chargebeeClient.estimate
      .renewal_estimate(subscriptionId)
      .request();
    return estimate.response.estimate;
  }

  /**
   * Returns the subscription details for a subscription
   * @param subscriptionId Chargebee ID of the subscription to get the subscription
   * @returns A promise that resolves to the subscription object from Chargebee
   */
  async getSubscription(subscriptionId: string): Promise<any> {
    const chargebeeResponse = await this.chargebeeClient.subscription
      .retrieve(subscriptionId)
      .request();
    return chargebeeResponse?.subscription;
  }

  /**
   * Returns the webhook payload for a given event id
   * @param eventId Chargebee ID of the event to get the webhook payload for
   * @returns A promise that resolves to the webhook payload from Chargebee
   */
  async getWebhookPayload(eventId: string): Promise<ChargeBeeWebhookPayload> {
    const result = await GlobalHelpers.makeAxiosRequest(
      this.chargebeeApiUrl + `/events/${eventId}`,
      RequestMethods.GET,
      {
        username: this.chargebeeApiKey,
        password: '',
      },
    );
    return result?.event;
  }

  /**
   * Creates a new portal session in Chargebee
   * @param customerId Chargebee ID of the customer to create the portal session for
   * @returns A promise that resolves to the portal session object from Chargebee
   */
  async createPortalSession(customerId: string) {
    const result = await this.chargebeeClient.portal_session
      .create({
        customer: {
          id: customerId,
        },
      })
      .request();
    return result?.portal_session;
  }
}
