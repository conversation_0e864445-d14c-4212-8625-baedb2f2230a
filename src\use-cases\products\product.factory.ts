import { Injectable } from '@nestjs/common';
import { ProductCatalogEntityStatus } from '@enums';
import { Product, ProductFamily } from '@entities';
import { CreateProductDTO } from '@dtos';

@Injectable()
export class ProductFactory {
  /**
   * Generates a product entity for storing in database
   * @param productInfo Product info coming from frontend
   * @returns Product entity
   */
  generateProduct(productInfo: CreateProductDTO): Product {
    const product = new Product();
    product.internalName = productInfo.internalName;
    product.externalName = productInfo.externalName;
    product.code = productInfo.code;
    product.description = productInfo.description;
    const productFamily = new ProductFamily();
    productFamily.id = productInfo.productFamilyId;
    product.productFamily = productFamily;
    product.status = ProductCatalogEntityStatus.active;
    product.image = productInfo.image ?? '';
    product.taxProfile = productInfo.taxProfile;
    product.isEvent = productInfo.isEvent;
    product.isForever = productInfo.isForever;
    product.eventYear = productInfo?.eventYear;
    product.eventLocation = productInfo?.eventLocation;
    product.lmsIds = productInfo?.lmsIds;
    product.communityIds = productInfo?.communityIds;
    return product;
  }
}
