"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { AlertTriangle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useCancelSubscription } from "@/services/customers";

interface CancelSubscriptionProps {
  id: string;
  onClose: () => void;
}

export function CancelSubscription({ id, onClose }: CancelSubscriptionProps) {
  const t = useTranslations("subscription.cancel");
  const [isConfirming, setIsConfirming] = useState(false);
  const router = useRouter();

  const { cancelSubscription, isLoading, error } = useCancelSubscription(id);

  const handleCancel = async () => {
    setIsConfirming(true);

    try {
      if (cancelSubscription) {
        await cancelSubscription();

        // Refresh the page after cancellation
        router.refresh();

        // Close the dialog
        onClose();
      } else {
        console.error("Cancel subscription function is not available");
      }
    } catch (error) {
      console.error("Error cancelling subscription:", error);
    } finally {
      setIsConfirming(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>

        <div className="flex items-start gap-3 rounded-lg border border-amber-200 bg-amber-50 p-4">
          <AlertTriangle className="mt-0.5 h-5 w-5 flex-shrink-0 text-amber-600" />
          <div className="text-sm text-amber-800">{t("warning")}</div>
        </div>

        {error && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800">
            {t("error")}
          </div>
        )}

        <DialogFooter className="sm:justify-between">
          <DialogClose asChild>
            <Button variant="ghost">{t("cancel-button")}</Button>
          </DialogClose>
          <Button variant="destructive" onClick={handleCancel} disabled={isLoading || isConfirming}>
            {isLoading || isConfirming ? t("cancelling") : t("confirm-button")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
