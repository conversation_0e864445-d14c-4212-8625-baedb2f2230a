"use client";

import { useState, useCallback, useMemo } from "react";
import { SelectedRows, TableState } from "@/types/table";

interface UseTableProps {
  pageSize?: number;
  onStateChange: (state: TableState) => void;
  onRowSelectionChange?: (selectedRows: SelectedRows) => void;
  initialState?: Partial<TableState>;
}

export function useTable({
  pageSize = 10,
  onStateChange,
  onRowSelectionChange,
  initialState = {},
}: UseTableProps) {
  // Memoize initial state to prevent unnecessary re-renders
  const defaultState = useMemo(
    () => ({
      pageIndex: 0,
      pageSize,
      searchQuery: "",
      ...initialState,
    }),
    [pageSize, initialState],
  );

  const [tableState, setTableState] = useState<TableState>(defaultState);
  const [selectedRows, setSelectedRows] = useState<SelectedRows>([]);

  // Memoize state update function to ensure consistent behavior
  const updateState = useCallback(
    (newState: Partial<TableState> | ((prev: TableState) => TableState)) => {
      setTableState((prev) => {
        const updatedState =
          typeof newState === "function" ? newState(prev) : { ...prev, ...newState };

        // Only trigger onStateChange if there are actual changes
        if (JSON.stringify(prev) !== JSON.stringify(updatedState)) {
          onStateChange(updatedState);
        }
        return updatedState;
      });
    },
    [onStateChange],
  );

  const handlePageChange = useCallback(
    (newPage: number) => {
      updateState({ pageIndex: newPage });
    },
    [updateState],
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      updateState({ searchQuery: e.target.value, pageIndex: 0 });
    },
    [updateState],
  );

  const handleSortChange = useCallback(
    (columnId: string) => {
      updateState((prev: TableState) => ({
        ...prev,
        sortBy: {
          id: columnId,
          desc: prev.sortBy?.id === columnId && !prev.sortBy.desc,
        },
        pageIndex: 0,
      }));
    },
    [updateState],
  );

  const handleRowSelection = useCallback(
    (rowIds: string[] | "all") => {
      setSelectedRows(rowIds);
      onRowSelectionChange?.(rowIds);
    },
    [onRowSelectionChange],
  );

  const handleSelectRange = useCallback(
    (checked: boolean, allRowIds: string[]) => {
      const newSelection = checked ? allRowIds : [];
      setSelectedRows(newSelection);
      onRowSelectionChange?.(newSelection);
    },
    [onRowSelectionChange],
  );

  const resetSelection = useCallback(() => {
    setSelectedRows([]);
    onRowSelectionChange?.([]);
  }, [onRowSelectionChange]);

  // Memoize all handlers to prevent unnecessary re-renders
  const handlers = useMemo(
    () => ({
      handlePageChange,
      handleSearchChange,
      handleSortChange,
      handleRowSelection,
      handleSelectRange,
      resetSelection,
    }),
    [
      handlePageChange,
      handleSearchChange,
      handleSortChange,
      handleRowSelection,
      handleSelectRange,
      resetSelection,
    ],
  );

  return {
    tableState,
    selectedRows,
    ...handlers,
  };
}
