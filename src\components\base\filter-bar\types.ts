/**
 * Core type definitions for the Filter Bar system
 */

/**
 * Represents a single option in a filter dropdown
 */
export interface FilterOption {
  value: string;
  label: string;
}

/**
 * Defines the dependency relationship between filters
 */
export interface StepDependency {
  /** Whether this dependency must have a value for the dependent filter to be enabled */
  required: boolean;
}

/**
 * Base interface for all step types in the filter system
 */
export interface BaseStep {
  /** Discriminator for step types */
  type: "filter" | "separator";
}

/**
 * Configuration for a filter step
 * Represents a single filter dropdown in the system
 */
export interface FilterStep extends BaseStep {
  type: "filter";
  /** Unique identifier for the filter */
  id: string;
  /** Display label for the filter */
  label: string;
  /** Placeholder text shown when no value is selected */
  placeholder?: string;
  /** Initial set of options for the filter */
  options: FilterOption[];
  /** Initial value for the filter */
  defaultValue?: string;
  /** Dependencies on other filters */
  dependsOn?: Partial<Record<string, StepDependency>>;
}

/**
 * Represents a visual separator between groups of filters
 */
export interface SeparatorStep extends BaseStep {
  type: "separator";
}

/** Union type of all possible step types */
export type Step = FilterStep | SeparatorStep;

/**
 * State management for filter groups
 */
export interface GroupState {
  /** Whether the group content exceeds its container */
  scrollable: boolean;
  /** Controls the display of scroll indicators */
  gradientState: GradientState;
  /** Whether the group content is wrapped to multiple lines */
  wrapped: boolean;
}

/**
 * Controls the visibility of scroll indicators
 */
export interface GradientState {
  /** Show left scroll indicator */
  left: boolean;
  /** Show right scroll indicator */
  right: boolean;
}

/**
 * Represents a group of related filters
 */
export interface ProcessedGroup {
  /** Filters in this group */
  items: FilterStep[];
  /** Visual state of the group */
  state: GroupState;
}

/**
 * Runtime state for a single filter
 */
export interface StepState {
  /** Whether the filter is currently fetching options */
  isLoading: boolean;
  /** Current error message, if any */
  error: string | null;
  /** Number of failed fetch attempts */
  retryCount: number;
  /** Current available options */
  options: FilterOption[];
}

/**
 * Complete state of the filter bar system
 */
export interface FilterBarState {
  /** Current values of all filters */
  values: Record<string, string>;
  /** State of each filter */
  stepsState: Record<string, StepState>;
  /** Processed groups of filters */
  groups: ProcessedGroup[];
}

/**
 * Props for the FilterBar component
 */
export interface FilterBarProps {
  /** Configuration of all filters and separators */
  steps: Step[];
  /** Optional class name for styling */
  className?: string;
  /** Current filter values */
  value?: Record<string, string>;
  /** Callback for value changes */
  onChange?: (value: Record<string, string>) => void;
  /** Callback to fetch options for dependent filters */
  onOptionsNeeded?: (
    stepId: string,
    dependencies: Record<string, string>,
  ) => Promise<FilterOption[]>;
  /** Force modal view regardless of screen size */
  forceModal?: boolean;
  /** Force bar view regardless of screen size */
  forceBar?: boolean;
  /** Text to display on the trigger button */
  triggerText?: string;
}

/**
 * Props for the useFilterBar hook
 */
export interface UseFilterBarProps {
  steps: Step[];
  value?: Record<string, string>;
  onChange?: (value: Record<string, string>) => void;
  onOptionsNeeded?: FilterBarProps["onOptionsNeeded"];
}
