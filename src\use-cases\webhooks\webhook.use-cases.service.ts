import { UserWebhookEvent } from '@clerk/backend';
import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, UpdateResult } from 'typeorm';
import { CreateBankTransferDTO } from '@dtos';
import { IDataServices } from '@abstracts';
import { WebhookStatus } from '@enums';
import { Webhook } from '@entities';
import {
  ChargeBeeEventType,
  ChargeBeeWebhookPayload,
  CronLogResult,
  PXActionResult,
  WebhookPayload,
  WebhookSender,
} from '@types';
import { ChargeBeeWebhooksHandler } from './chargebee-webhooks.service';
import { ClerkWebhooksHandler } from './clerk-webhooks.service';
import { BankTransferUseCases } from '../bank-transfer';

@Injectable()
export class WebhookUseCases {
  private webhookEventHandlers: Record<
    string,
    (
      payload:
        | ChargeBeeWebhookPayload
        | UserWebhookEvent
        | CreateBankTransferDTO,
      isCron?: boolean,
    ) => Promise<PXActionResult>
  >;

  private chargebeeAffectedEntities: Record<ChargeBeeEventType, string>;

  constructor(
    private readonly dataServices: IDataServices,
    private readonly chargebeeWebhooksHandler: ChargeBeeWebhooksHandler,
    private readonly clerkWebhooksHandler: ClerkWebhooksHandler,
    private readonly bankTransferUseCases: BankTransferUseCases,
  ) {
    this.webhookEventHandlers = {
      chargebee: (payload: ChargeBeeWebhookPayload, isCron?: boolean) =>
        this.chargebeeWebhooksHandler.handle(payload, isCron),
      clerk: (payload: UserWebhookEvent) =>
        this.clerkWebhooksHandler.handle(payload),
      bankTransfer: (payload: CreateBankTransferDTO) =>
        this.bankTransferUseCases.handle(payload),
    };

    this.chargebeeAffectedEntities = {
      subscription_created: 'subscription',
      subscription_changed: 'subscription',
      subscription_cancelled: 'subscription',
      subscription_renewed: 'subscription',
      invoice_generated: 'invoice',
      invoice_updated: 'invoice',
      payment_succeeded: 'transaction',
      payment_failed: 'transaction',
      payment_refunded: 'transaction',
      transaction_created: 'transaction',
      transaction_updated: 'transaction',
      credit_note_created: 'credit_note',
      credit_note_updated: 'credit_note',
      coupon_updated: 'discount',
    };
  }

  /**
   * Creates a new webhook in database
   * @param webhook Webhook entity
   * @returns Webhook record from database
   */
  async create(webhook: Webhook): Promise<Webhook> {
    return this.dataServices.webhook.create(webhook);
  }

  /**
   * Gets the status of a webhook based on the result of the event
   * @param result Result of the event
   * @returns Webhook status
   */
  getWebhookStatus(result: PXActionResult) {
    return result.success ? WebhookStatus.PROCESSED : WebhookStatus.FAILED;
  }

  /**
   * Gets the affected entity for a webhook event
   * @param event Webhook event
   * @returns Name of the affected entity
   */
  getAffectedEntity(event: WebhookPayload): string {
    if (event.clerk) return 'user';
    if (event.bankTransfer) return 'bankTransfer';
    return this.chargebeeAffectedEntities[event.chargebee.event_type];
  }

  /**
   * Updates a webhook in database
   * @param senderWebhookId Webhook Id from webhook sender
   * @param updates Updates for the webhook
   * @returns Updated webhook record from database
   */
  async update(
    senderWebhookId: string,
    updates: Partial<Webhook>,
  ): Promise<UpdateResult> {
    return this.dataServices.webhook.update({ senderWebhookId }, updates);
  }

  /**
   * Gets all failed webhooks
   * @returns A promise that resolves to all failed webhooks
   */
  async getFailedWebhooks() {
    const webhooks = await this.dataServices.webhook.getAllBy(
      { status: WebhookStatus.FAILED },
      500,
      1,
    );
    return webhooks;
  }

  /**
   * Delegates a webhook to the appropriate handler
   * @param webhookSender Webhook sender
   * @param event Webhook event
   * @returns A promise that resolves to a `PXActionResult` or `CronLogResult`
   */
  async delegateToHandler(
    webhookSender: WebhookSender,
    event: WebhookPayload,
    isCron?: boolean,
  ): Promise<PXActionResult> {
    if ((await this.alreadyReceived(event.id)) && !isCron) {
      return {
        success: true,
        message: `Webhook already received`,
        data: null,
      };
    }
    const receivedAt = Date.now();
    const handler = this.webhookEventHandlers[webhookSender];
    const payload = this.extractPayload(event, webhookSender);
    const result = handler
      ? await handler(payload, isCron)
      : {
          success: true,
          message: `No handler for webhook sender ${webhookSender}`,
          error: null,
          data: null,
        };
    if (isCron) {
      return {
        success: result?.success,
        message: result?.message,
        data: result?.data,
      };
    }
    if (result?.success || webhookSender === 'chargebee') {
      await this.logWebhookEvent(event, result, receivedAt);
    }
    return result;
  }

  /**
   * Checks if a webhook has already been received
   * @param id Webhook ID
   * @returns A promise that resolves to a boolean indicating if the webhook has already been received
   */
  async alreadyReceived(id: string): Promise<boolean> {
    const webhook = await this.dataServices.webhook.getOneBy({
      senderWebhookId: id,
    });
    return !!webhook;
  }

  /**
   * Logs a webhook event in the database
   * @param event Webhook event
   * @param result Result of the event
   * @param receivedAt Time at which the event was received
   * @returns A promise that resolves when the webhook is logged
   */
  async logWebhookEvent(
    event: WebhookPayload,
    result: PXActionResult,
    receivedAt: number,
  ): Promise<any> {
    const webhook = new Webhook();
    webhook.sender = this.getSender(event);
    webhook.senderWebhookId = event.id;
    webhook.outcome = {
      message: result.message,
      affectedEntity: this.getAffectedEntity(event),
      affectedEntityId: result?.data?.id,
    };
    webhook.status = this.getWebhookStatus(result);
    webhook.occurredAt = receivedAt;
    return this.dataServices.webhook.create(webhook);
  }

  /**
   * Gets the sender of the webhook event
   * @param event Webhook event
   * @returns Webhook sender
   */
  getSender(event: WebhookPayload): WebhookSender {
    if (event.chargebee) return 'chargebee';
    if (event.clerk) return 'clerk';
    if (event.bankTransfer) return 'bankTransfer';
    throw new Error('Unsupported webhook sender');
  }

  /**
   * Reprocesses a failed webhook
   * @param webhookId Webhook ID
   * @param sender Webhook sender
   * @param data Webhook data
   * @returns A promise that resolves to a `CronLogResult`
   */
  async reprocessFailedChargebeeWebhook(
    webhookId: string,
    sender: WebhookSender,
    data: WebhookPayload,
  ): Promise<CronLogResult> {
    const payload: WebhookPayload = { id: webhookId, [sender]: data };
    const result = await this.delegateToHandler(sender, payload, true);
    if (result.success) {
      await this.update(webhookId, {
        status: WebhookStatus.PROCESSED,
        outcome: {
          message: result.message,
          affectedEntity: this.getAffectedEntity(payload),
          affectedEntityId: result.data?.id,
        },
      });
    }
    return {
      success: result.success,
      output: result,
      message: result.message,
      input: { id: webhookId, sender, data },
    };
  }

  /**
   * Extracts the payload from the webhook event based on the sender
   * @param event Webhook event
   * @param sender Webhook sender
   * @returns Extracted payload
   */
  extractPayload(
    event: WebhookPayload,
    sender: WebhookSender,
  ): ChargeBeeWebhookPayload | UserWebhookEvent | CreateBankTransferDTO {
    switch (sender) {
      case 'chargebee':
        return event.chargebee;
      case 'clerk':
        return event.clerk;
      case 'bankTransfer':
        return event.bankTransfer;
      default:
        throw new Error(`Unsupported webhook sender: ${sender}`);
    }
  }

  /**
   * Gets all webhooks based on the provided filters
   * @param filters Filters to be applied to the webhook search
   * @returns A promise that resolves to an object containing the retrieved webhooks & the total count.
   */
  async getAllBy(
    filters: FindOptionsWhere<Webhook>,
    limit?: number,
    page?: number,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: Webhook[]; total: number }> {
    return this.dataServices.webhook.getAllBy(filters, limit, page, {
      createdAt: orderBy || 'DESC',
    });
  }

  /**
   * Searches and returns a `Webhook` record from database
   * @param id `id` of the `Webhook` to search for
   * @returns A promise which resolves to a `Webhook` record from database OR null
   */
  async getOne(id: string): Promise<Webhook> {
    return this.dataServices.webhook.getOneBy({ id });
  }
}
