import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

const isVercel = process.env.VERCEL === "1";

const withTranspilePackages = {
  transpilePackages: ["@px-shared-account/hermes"],
};

const nextConfig: NextConfig = {
  ...(isVercel ? {} : withTranspilePackages),
  images: {
    remotePatterns: [
      {
        hostname: "images.unsplash.com",
      },
      {
        hostname: "fastly.picsum.photos",
      },
      {
        hostname: "px-app.s3.eu-west-3.amazonaws.com",
      },
      {
        hostname: "paradox-os.s3.eu-west-3.amazonaws.com",
      },
      {
        hostname: "google.com",
      },
      {
        hostname: "placehold.co",
      },
      {
        hostname: "img.clerk.com",
      },
      {
        hostname: "7s9lyh49djptgjnh.public.blob.vercel-storage.com",
      },
      {
        hostname: "lwfiles.mycourse.app",
      },
      {
        hostname: "api.us-e2.learnworlds.com"
      }
    ],
  },
};

export default withNextIntl(nextConfig);
