import { z } from "zod";
import { type ColumnDef } from "@/types/data-table";

/** Base field types for form inputs */
export type BaseFieldType =
  | "text"
  | "number"
  | "select"
  | "switch"
  | "date"
  | "combobox"
  | "long-text";

/** Complex field types that can contain nested fields or special layouts */
export type ComplexFieldType =
  | "accordion-select"
  | "data-table"
  | "layout"
  | "permissions"
  | "image-upload"
  | "tree-select";

/** Union of all possible field types */
export type FieldType = BaseFieldType | ComplexFieldType;

/** Layout types for arranging fields */
export type LayoutType = "grid" | "flex";

/** Configuration for layout fields */
export interface LayoutConfig {
  /** The type of layout to use */
  type: LayoutType;
  /** Number of columns for grid layout */
  columns?: number;
  /** Gap between elements in pixels */
  gap?: number;
  /** Additional CSS classes */
  className?: string;
  /** Fields to be arranged in this layout */
  fields: FieldConfig[];
}

/** Option configuration for select and accordion-select fields */
export interface FieldOption {
  /** Display label for the option */
  label: string;
  /** Value to be stored when selected */
  value: string;
  /** Nested fields that appear when this option is selected */
  fields?: FieldConfig[];
}

/** Configuration for field dependencies and conditional rendering */
export interface FieldDependency {
  /** ID of the field this dependency depends on */
  field: string;
  /** Function that determines if the condition is met */
  condition: (value: any) => boolean;
  /** Action to take when condition is met */
  action: "show" | "hide" | "enable" | "disable";
}

/** Base metadata for field styling and layout */
export interface BaseFieldMeta {
  /** Width of the field (CSS value) */
  width?: string;
  /** Text alignment within the field */
  align?: "left" | "center" | "right";
  /** Number of columns this field should span in a grid */
  columnSpan?: number;
  /** Additional CSS classes */
  className?: string;
}

/** Metadata specific to data table fields */
export interface DataTableMeta extends BaseFieldMeta {
  /** Column definitions for the table */
  columns: ColumnDef<any>[];
  /** Label for single data item */
  dataLabel?: string;
  /** Label for multiple data items */
  dataLabelPlural?: string;
  /** Whether multiple rows can be selected */
  multiSelect?: boolean;
}

/** Metadata specific to layout fields */
export interface LayoutFieldMeta extends BaseFieldMeta {
  /** Layout configuration for nested fields */
  layout: LayoutConfig;
  /** Additional CSS classes */
  className?: string;
}

/** Loading state for the modal and steps */
export interface LoadingState {
  /** Whether the form is being submitted */
  isSubmitting: boolean;
  /** Whether a step transition is in progress */
  isLoadingStep: boolean;
}

/** Base configuration for all field types */
interface BaseField {
  /** Unique identifier for the field */
  id: string;
  /** Display label for the field */
  label: string;
  /** Help text shown below the field */
  description?: string;
  /** Placeholder text for input fields */
  placeholder?: string;
  /** Whether the field is required */
  required?: boolean;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Default value for the field */
  defaultValue?: any;
  /** Dependencies that control field visibility/state */
  dependencies?: FieldDependency[];
  /** Zod validation schema for the field */
  validation?: z.ZodType<any>;
  /** Whether the field is in a loading state */
  isLoading?: boolean;
  /** Callback when field value changes */
  onChange?: (value: any) => void;
}

/** Configuration for text input fields */
export interface TextField extends BaseField {
  type: "text";
  meta?: BaseFieldMeta;
}

/** Configuration for date input fields */
export interface DateField extends BaseField {
  type: "date";
  meta?: BaseFieldMeta;
}

/** Configuration for number input fields */
export interface NumberField extends BaseField {
  type: "number";
  meta?: BaseFieldMeta;
}

/** Configuration for select fields */
export interface SelectField extends BaseField {
  type: "select";
  options: { label: string; value: string }[];
  placeholder?: string;
  isMulti?: boolean;
  validation?: z.ZodType<any>;
  defaultValue?: string | string[];
  meta?: BaseFieldMeta;
}

/** Configuration for switch/toggle fields */
export interface SwitchField extends BaseField {
  type: "switch";
  meta?: BaseFieldMeta;
}

/** Configuration for accordion select fields */
export interface AccordionSelectField extends BaseField {
  type: "accordion-select";
  /** Available options with nested fields */
  options: FieldOption[];
  meta?: BaseFieldMeta;
}

/** Configuration for data table fields */
export interface DataTableField extends BaseField {
  type: "data-table";
  /** Table-specific metadata */
  meta: DataTableMeta;
}

/** Configuration for layout container fields */
export interface LayoutField extends BaseField {
  type: "layout";
  /** Layout-specific metadata */
  meta: LayoutFieldMeta;
}

export interface PermissionsField extends BaseField {
  type: "permissions";
  meta?: BaseFieldMeta;
  onChange: (value: string[]) => void;
}

export interface ImageUploadFieldMeta extends BaseFieldMeta {
  /** Required aspect ratio in format "width/height" (e.g., "16/9", "1/1", "4/3") */
  aspectRatio: string;
  /** Maximum file size in MB */
  maxSize?: number;
  /** Accepted file types */
  acceptedTypes?: string[];
  /** Additional CSS classes */
  className?: string;
}

export interface ImageUploadField extends BaseField {
  type: "image-upload";
  meta: ImageUploadFieldMeta;
  validation?: z.ZodType<any>;
  defaultValue?: { url: string; file?: File };
}

export interface TreeSelectField extends BaseField {
  type: "tree-select";
  data: any[];
  meta?: BaseFieldMeta;
}

/** Configuration for combobox fields */
export interface ComboboxField extends BaseField {
  type: "combobox";
  options: {
    label: string;
    value: string;
    render?: () => React.ReactNode;
  }[];
  placeholder?: string;
  isMulti?: boolean;
  validation?: z.ZodType<any>;
  defaultValue?: string | string[];
  meta?: BaseFieldMeta;
}

/** Configuration for long text (textarea) fields */
export interface LongTextField extends BaseField {
  type: "long-text";
  meta?: BaseFieldMeta;
  rows?: number;
}

/** Union type of all possible field configurations */
export type FieldConfig =
  | TextField
  | NumberField
  | DateField
  | SelectField
  | SwitchField
  | AccordionSelectField
  | DataTableField
  | LayoutField
  | PermissionsField
  | ImageUploadField
  | TreeSelectField
  | ComboboxField
  | LongTextField;

/** Configuration for a single step in the multi-step modal */
export interface StepConfig {
  /** Unique identifier for the step */
  id: string;
  /** Title shown in the step header */
  title: string;
  /** Description shown below the title */
  description?: string;
  /** Whether this step can be skipped */
  isOptional?: boolean;
  /** Fields to be rendered in this step */
  fields: FieldConfig[];
  /** Zod validation schema for the entire step */
  validation?: z.ZodSchema;
  /** Callback when step is completed */
  onStepComplete?: (data: any) => Promise<void>;
}

/** Configuration for a submit button in the modal */
export interface SubmitButtonConfig {
  /** Label to display on the button */
  label: string;
  /** Variant of the button (matches shadcn/ui button variants) */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  /** Additional CSS classes for the button */
  className?: string;
  /** Custom data to be passed to onComplete callback */
  data?: Record<string, any>;
  /** Whether this is the primary submit button */
  isPrimary?: boolean;
}

/** Configuration for the entire multi-step modal */
export interface ModalConfig {
  /** Unique identifier for the modal */
  key: string;
  /** Title shown in the modal header */
  title: string;
  /** Description shown below the title */
  description?: string;
  /** Array of step configurations */
  steps: StepConfig[];
  /** Initial form data */
  initialData?: Record<string, any>;
  /** Whether users can navigate between steps freely */
  allowFreeNavigation?: boolean;
  /** Whether to show confirmation when closing */
  confirmClose?: boolean;
  /** Whether to show confirmation when resetting */
  confirmReset?: boolean;
  /** Custom submit buttons configuration */
  submitButtons?: SubmitButtonConfig[];
}

/** Event handlers for modal lifecycle events */
export interface ModalEvents {
  /** Called when modal is opened */
  onOpen?: () => void;
  /** Called when modal is closed */
  onClose?: () => void;
  /** Called when current step changes */
  onStepChange?: (from: number, to: number) => void;
  /** Called when any field value changes */
  onFieldChange?: (fieldId: string, value: any) => void;
  /** Called when validation fails */
  onValidationError?: (errors: any) => void;
  /** Called when form is submitted successfully */
  onComplete?: (data: any) => Promise<void>;
  /** Called when form is reset */
  onReset?: () => void;
}

/** Internal state for modal management */
export interface ModalState {
  /** Whether the modal is currently open */
  isOpen: boolean;
  /** Index of current step */
  currentStep: number;
  /** Current form data */
  formData: Record<string, any>;
  /** Validation errors by field ID */
  errors: Record<string, string>;
  /** Loading states */
  loading: LoadingState;
  /** Whether form has been modified */
  isDirty: boolean;
  /** Whether there are unsaved changes */
  hasUnsavedChanges: boolean;
}

/** Store interface for managing multiple modals */
export interface ModalStore {
  /** Map of modal states by key */
  modals: Record<string, ModalState>;
  /** Register a new modal configuration */
  registerModal: (key: string, config: ModalConfig) => void;
  /** Unregister a modal configuration */
  unregisterModal: (key: string) => void;
  /** Update modal state */
  setModalData: (key: string, data: Partial<ModalState>) => void;
  /** Open a modal */
  openModal: (key: string) => void;
  /** Close a modal */
  closeModal: (key: string) => void;
  /** Move to next step */
  nextStep: (key: string) => void;
  /** Move to previous step */
  previousStep: (key: string) => void;
  /** Set current step */
  setStep: (key: string, step: number) => void;
  /** Update form data */
  setFormData: (key: string, data: Record<string, any>) => void;
  /** Update validation errors */
  setErrors: (key: string, errors: Record<string, string>) => void;
  /** Update loading state */
  setLoading: (key: string, loading: Partial<LoadingState>) => void;
  /** Reset form to initial state */
  resetForm: (key: string, config: ModalConfig) => void;
  /** Update dirty state */
  setDirty: (key: string, isDirty: boolean) => void;
}
