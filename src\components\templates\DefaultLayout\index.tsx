"use client";

import { Permissions } from "@px-shared-account/hermes";
import { Home, Shield, MessageSquareDiff, Users, Wallet, HelpCircle } from "lucide-react";
import { useTranslations } from "next-intl";

import { useCanAccessAll } from "@/lib/permissions/client";
import { PageContainer } from "@/components/layout/PageContainer";
import { NavigationProvider } from "./NavigationProvider";
import { COMMUNITY_IMAGES } from "@/constants/images";
import { useCustomerAccessDetails } from "@/services/customers";
import { NavigationItem, Notification, BaseProps } from "@/types";

export interface DefaultLayoutProps extends BaseProps {
  title?: string;
  notifications?: Notification[];
  onSearch?: (value: string) => void;
  onSignOut?: () => void;
  onProfile?: () => void;
  onSettings?: () => void;
  onMarkNotificationAsRead?: (id: string) => void;
  onViewAllNotifications?: () => void;
  onNewSlot?: () => void;
}

export default function DefaultLayout({
  children,
  className,
  title,
  notifications = [],
  onSearch,
}: DefaultLayoutProps) {
  const { hasAccessRoleRead, hasAccessUserRead, hasAccessCourseRead, hasAccessCohortRead } =
    useCanAccessAll([
      Permissions.Role.READ,
      Permissions.User.READ,
      Permissions.Course.READ,
      Permissions.Cohort.READ,
    ]);
  const { data: currentUserAccessData } = useCustomerAccessDetails();
  const { isPXL, isPXS } = currentUserAccessData?.accessInfo || {};

  const t = useTranslations("navigation");

  let productNavigation: NavigationItem[] = [];
  if (isPXL || isPXS) {
    productNavigation = [
      {
        id: "communities",
        label: isPXL && isPXS ? t("communities.title_plural") : t("communities.title"),
        icon: Users,
        href: "#",
        children: [
          ...(isPXL
            ? [
                {
                  id: "communities/pxl",
                  icon: COMMUNITY_IMAGES.pxl,
                  label: t("communities.pxl"),
                  href: "https://paradox-learning.circle.so/home",
                  target: "_blank",
                  external: true,
                  hasCustomIcon: true,
                },
              ]
            : []),
          ...(isPXS
            ? [
                {
                  id: "communities/pxs",
                  icon: COMMUNITY_IMAGES.pxs,
                  label: t("communities.pxs"),
                  href: "https://paradox-school.circle.so/home",
                  target: "_blank",
                  external: true,
                  hasCustomIcon: true,
                },
              ]
            : []),
        ],
      },
    ];
  }

  // * Disabled for now, we will add it back when we have the access to the courses and cohorts (after the V1)
  // if (hasAccessCourseRead || hasAccessCohortRead) {
  //   productNavigation = [
  //     { id: "courses", label: t("products.courses"), icon: GraduationCap, href: "/courses" },
  //     { id: "cohorts", label: t("products.cohorts"), icon: Layers, href: "/cohorts" },
  //   ];
  // }

  let defaultNavigation: NavigationItem[] = [
    { id: "dashboard", label: t("dashboard"), icon: Home, href: "/dashboard" },
    ...(hasAccessCourseRead || hasAccessCohortRead ? productNavigation : []),
    { id: "practice", label: t("practice.title"), icon: MessageSquareDiff, href: "/practice" },
    { id: "goliaths", label: t("goliaths"), icon: Wallet, href: "/goliaths" },
    { id: "faq", label: t("faq"), icon: HelpCircle, href: "/faq" },
  ];

  let adminNavigation: NavigationItem[] = [];
  if (hasAccessRoleRead || hasAccessUserRead) {
    adminNavigation = [
      {
        id: "user-management",
        label: t("users"),
        icon: Shield,
        children: [
          ...(hasAccessUserRead
            ? [
                {
                  id: "user-management/users",
                  label: t("user-management.users"),
                  href: "/user-management/users",
                },
              ]
            : []),
          ...(hasAccessRoleRead
            ? [
                {
                  id: "user-management/roles",
                  label: t("user-management.roles"),
                  href: "/user-management/roles",
                },
              ]
            : []),
        ],
      },
    ];
  }

  defaultNavigation = [
    ...defaultNavigation,
    ...(hasAccessRoleRead || hasAccessUserRead ? adminNavigation : []),
  ];

  return (
    <NavigationProvider>
      <PageContainer
        className={className}
        title={title}
        navigation={defaultNavigation}
        notificationCount={notifications.filter((n) => !n.read).length}
      >
        {/* Page Content */}
        {children}
      </PageContainer>
    </NavigationProvider>
  );
}
