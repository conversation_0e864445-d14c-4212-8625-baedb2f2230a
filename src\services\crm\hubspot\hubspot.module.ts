import { Module } from '@nestjs/common';
import { ChargebeeBillingModule } from '@services/billing';
import { SlackModule } from '@services/notification';
import { HubspotFactoryService } from './hubspot.factory.service';
import { HubspotService } from './hubspot.service';

@Module({
  imports: [ChargebeeBillingModule, SlackModule],
  providers: [HubspotService, HubspotFactoryService],
  exports: [HubspotService, HubspotFactoryService],
})
export class HubspotModule {}
