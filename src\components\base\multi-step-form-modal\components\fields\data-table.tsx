"use client";

import { useFormContext } from "react-hook-form";
import { DataTable } from "@/components/composite/data-table";
import { DataTableField as DataTableFieldType } from "../../types";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface DataTableFieldProps {
  field: DataTableFieldType;
  className?: string;
}

export function DataTableField({ field, className }: DataTableFieldProps) {
  const {
    register,
    setValue,
    formState: { errors },
  } = useFormContext();

  const t = useTranslations("validation");
  const error = errors[field.id]?.message as string | undefined;

  // Register the field with react-hook-form
  register(field.id);

  const handleRowSelect = (selectedRows: any[]) => {
    // Store the selected row IDs in the form
    setValue(
      field.id,
      selectedRows.map((row) => row.id),
      {
        shouldValidate: true,
        shouldDirty: true,
      },
    );

    // Call the field's onChange handler if provided
    field.onChange?.(selectedRows);
  };

  const handleRowAction = (row: any) => {
    // If the field is not multi-select, store only the clicked row ID
    if (!field.meta.multiSelect) {
      setValue(field.id, [row.id], {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="overflow-auto">
        <DataTable
          data={field.defaultValue || []}
          columns={field.meta.columns}
          selectable={true}
          dataLabel={field.meta.dataLabel || "item"}
          dataLabelPlural={field.meta.dataLabelPlural || "items"}
          onRowSelect={handleRowSelect}
          onRowAction={handleRowAction}
        />
      </div>

      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
}
