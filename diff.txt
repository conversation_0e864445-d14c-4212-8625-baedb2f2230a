diff --git a/package.json b/package.json
index 9bf0152..005efee 100644
--- a/package.json
+++ b/package.json
@@ -11,12 +11,12 @@
     "docker:run": "docker-compose up --build",
     "docker:stop": "docker-compose down",
     "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
-    "start": "NODE_ENV=development nest start",
-    "start:dev": "NODE_ENV=development nest start --watch",
-    "start:debug": "NODE_ENV=development nest start --debug --watch",
+    "start": "nest start",
+    "start:dev": "nest start --watch",
+    "start:debug": "nest start --debug --watch",
     "start:prod": "node --max-old-space-size=4096 dist/main",
     "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
-    "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli",
+    "typeorm": "ts-node -r tsconfig-paths/register -P tsconfig.json ./node_modules/typeorm/cli",
     "migration:run": "infisical run --env=$NODE_ENV --path=/px_db -- pnpm run typeorm migration:run -d ./src/services/database/migration.service.ts",
     "migration:generate": "infisical run --env=$NODE_ENV --path=/px_db -- pnpm run typeorm -d ./src/services/database/migration.service.ts migration:generate ./src/services/database/migrations/$migration && eslint ./src/services/database/migrations/*.ts --fix",
     "migration:revert": "infisical run --env=$NODE_ENV --path=/px_db -- pnpm run typeorm -d ./src/services/database/migration.service.ts migration:revert",
diff --git a/src/controllers/course.controller.ts b/src/controllers/course.controller.ts
index 5594fe1..41d1a07 100644
--- a/src/controllers/course.controller.ts
+++ b/src/controllers/course.controller.ts
@@ -7,6 +7,7 @@ import {
   Post,
   Query,
   UseGuards,
+  UseInterceptors,
 } from '@nestjs/common';
 import { Permissions } from '@px-shared-account/hermes';
 import {
@@ -14,12 +15,15 @@ import {
   GetCourseByIdDto,
   GetCoursesByOfferIdDTO,
   GetStudentsForCourseDto,
+  ListCourseCatalogDto,
   ListCoursesDTO,
   UpdateCourseDto,
 } from '@dtos';
 import { CourseUseCases } from '@useCases';
-import { RequirePermissions } from 'src/auth/decorators';
+import { CurrentUser, RequirePermissions } from 'src/auth/decorators';
 import { JwtPermissionsGuard } from 'src/auth/guards';
+import { UserEntity } from '@entities';
+import { CurrentUserInterceptor } from 'src/auth/interceptors';
 
 @Controller('course')
 @UseGuards(JwtPermissionsGuard)
@@ -52,6 +56,16 @@ export class CourseController {
     );
   }
 
+  @Get('/catalog')
+  @RequirePermissions(Permissions.Course.READ)
+  @UseInterceptors(CurrentUserInterceptor)
+  async listCourseCatalog(
+    @Query() payload: ListCourseCatalogDto,
+    @CurrentUser() user: UserEntity,
+  ) {
+    return this.courseUseCases.listCatalog(payload, user);
+  }
+
   @Post('/:id/publish')
   @RequirePermissions(Permissions.Course.UPDATE)
   async publishCourse(@Param() params: GetCourseByIdDto) {
diff --git a/src/controllers/customer-accesses.controller.ts b/src/controllers/customer-accesses.controller.ts
index 10beb02..b84b6c7 100644
--- a/src/controllers/customer-accesses.controller.ts
+++ b/src/controllers/customer-accesses.controller.ts
@@ -13,11 +13,6 @@ export class CustomerAccessesController {
     private readonly productDeliveryService: ProductDeliveryService,
   ) {}
 
-  @Get('/courses/:email')
-  async getCourseAccesses(@Param('email') email: string) {
-    return this.customerAccesses.getCourseAccesses(email);
-  }
-
   @ApiOperation({ summary: 'Get by customer email' })
   @Get('/customer/email')
   async listByCustomerEmail(@Query('email') email: string) {
diff --git a/src/core/dtos/course.dto.ts b/src/core/dtos/course.dto.ts
index 55114b1..4726c49 100644
--- a/src/core/dtos/course.dto.ts
+++ b/src/core/dtos/course.dto.ts
@@ -9,6 +9,8 @@ import {
   GetCourseByOfferIdSchema,
   GetStudentsForCourseSchema,
   GetStudentsForCourseResponseSchema,
+  ListCourseCatalogParamsSchema,
+  ListCourseCatalogResponseSchema,
 } from '@px-shared-account/hermes';
 
 export class CreateCourseDto extends createZodDto(CreateCourseSchema) {}
@@ -29,3 +31,9 @@ export class GetStudentsForCourseDto extends createZodDto(
 export class GetStudentsForCourseResponseDto extends createZodDto(
   GetStudentsForCourseResponseSchema,
 ) {}
+export class ListCourseCatalogDto extends createZodDto(
+  ListCourseCatalogParamsSchema,
+) {}
+export class ListCourseCatalogResponseDto extends createZodDto(
+  ListCourseCatalogResponseSchema,
+) {}
diff --git a/src/core/entities/course.entity.ts b/src/core/entities/course.entity.ts
index 08ea0f1..6f76776 100644
--- a/src/core/entities/course.entity.ts
+++ b/src/core/entities/course.entity.ts
@@ -17,4 +17,5 @@ export class CourseEntity extends BaseEntity {
   offers: ProductOffer[];
   lmsId: string;
   productFamily: ProductFamily;
+  ctaLink: string;
 }
diff --git a/src/core/entities/customer-access.entity.ts b/src/core/entities/customer-access.entity.ts
index 13dfa27..b3cef34 100644
--- a/src/core/entities/customer-access.entity.ts
+++ b/src/core/entities/customer-access.entity.ts
@@ -1,4 +1,5 @@
-import { CustomerAccessStatus, CustomerAccessType } from '@enums';
+import { CustomerAccessType } from '@enums';
+import { CustomerAccessStatus } from '@px-shared-account/hermes';
 import { AccessDetails } from '@types';
 
 export class CustomerAccess {
diff --git a/src/core/enums/common.enum.ts b/src/core/enums/common.enum.ts
index c4289df..a666938 100644
--- a/src/core/enums/common.enum.ts
+++ b/src/core/enums/common.enum.ts
@@ -176,14 +176,6 @@ export enum AccessTarget {
   EXTERNAL_MEDIA = 'externalMedia',
 }
 
-export enum CustomerAccessStatus {
-  ERROR = 'error',
-  GRANTED = 'granted',
-  REVOKED = 'revoked',
-  SUSPENDED = 'suspended',
-  PENDING = 'pending',
-}
-
 export enum CustomerAccessType {
   LMS = 'lms',
   COMMUNITY = 'community',
diff --git a/src/core/types/common.type.ts b/src/core/types/common.type.ts
index 57e9e6e..a909bf8 100644
--- a/src/core/types/common.type.ts
+++ b/src/core/types/common.type.ts
@@ -3,11 +3,11 @@ import { CreateBankTransferDTO } from '@dtos';
 import {
   AccessTarget,
   CommunityType,
-  CustomerAccessStatus,
   CustomerAccessType,
   DiscountType,
 } from '@enums';
 import { ChargeBeeWebhookPayload } from './chargebee.type';
+import { CustomerAccessStatus } from '@px-shared-account/hermes';
 
 /**
  * HTTP request methods used by helper method `makeAxiosRequest`
@@ -352,16 +352,4 @@ export interface SubscriptionInvoiceDetails {
     date: Date;
     status: string;
   }[];
-}
-
-export type RawCourseAccessResult = {
-  id: number;
-  status: CustomerAccessStatus;
-  type: CustomerAccessType;
-  details: AccessDetails;
-  name: string;
-  slug: string;
-  bannerImage: string;
-  cardImage: string;
-  thumbnail: string;
-};
+}
\ No newline at end of file
diff --git a/src/services/crons/crons-service.ts b/src/services/crons/crons-service.ts
index 1c47207..c717449 100644
--- a/src/services/crons/crons-service.ts
+++ b/src/services/crons/crons-service.ts
@@ -6,7 +6,6 @@ import {
   CronLogRefPrefix,
   CronLogStatus,
   CronLogType,
-  CustomerAccessStatus,
   SubscriptionOrderStatus,
 } from '@enums';
 import { ChargebeeBillingService } from '@services/billing';
@@ -21,6 +20,7 @@ import { ConfigService } from '@config';
 import { SlackService } from '@services/notification';
 import { Subscription } from '@entities';
 import { CronLogResult, ExhaustedCronJob } from '@types';
+import { CustomerAccessStatus } from '@px-shared-account/hermes';
 
 const inLocal = !['staging', 'production'].includes(process.env.NODE_ENV);
 
@@ -231,8 +231,8 @@ export class CronsService {
   async exchangeRateCron() {
     const cronLogType = CronLogType.EXCHANGE_RATE_CRAWLER;
     const logId = `${
-      CronLogRefPrefix.EXCHANGE_RATE
-    }${new Date().toISOString()}`;
+    CronLogRefPrefix.EXCHANGE_RATE
+      }${new Date().toISOString()}`;
     await this.processCronJob(cronLogType, null, logId);
   }
 
diff --git a/src/services/database/migrations/*************-add-cta-link-to-courses.ts b/src/services/database/migrations/*************-add-cta-link-to-courses.ts
new file mode 100644
index 0000000..ba93316
--- /dev/null
+++ b/src/services/database/migrations/*************-add-cta-link-to-courses.ts
@@ -0,0 +1,14 @@
+import { MigrationInterface, QueryRunner } from "typeorm";
+
+export class AddCtaLinkToCourses************* implements MigrationInterface {
+    name = 'AddCtaLinkToCourses*************'
+
+    public async up(queryRunner: QueryRunner): Promise<void> {
+        await queryRunner.query(`ALTER TABLE "courses" ADD "ctaLink" character varying(500) NOT NULL DEFAULT 'https://paradox.io'`);
+    }
+
+    public async down(queryRunner: QueryRunner): Promise<void> {
+        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "ctaLink"`);
+    }
+
+}
diff --git a/src/services/database/postgres/tables/course.table.ts b/src/services/database/postgres/tables/course.table.ts
index ebe4e9b..bcdaa0a 100644
--- a/src/services/database/postgres/tables/course.table.ts
+++ b/src/services/database/postgres/tables/course.table.ts
@@ -84,6 +84,9 @@ export class CourseTable implements CourseEntity {
   @ManyToOne(() => ProductFamilyTable, (productFamily) => productFamily.courses)
   productFamily: ProductFamilyTable;
 
+  @Column({ type: 'varchar', length: 500, default: 'https://paradox.io' })
+  ctaLink: string;
+
   @CreateDateColumn({ default: () => 'CURRENT_TIMESTAMP' })
   createdAt: Date;
 
diff --git a/src/services/database/postgres/tables/customer-access.table.ts b/src/services/database/postgres/tables/customer-access.table.ts
index 655e5d7..91ae476 100644
--- a/src/services/database/postgres/tables/customer-access.table.ts
+++ b/src/services/database/postgres/tables/customer-access.table.ts
@@ -7,9 +7,10 @@ import {
   DeleteDateColumn,
   Index,
 } from 'typeorm';
-import { CustomerAccessStatus, CustomerAccessType } from '@enums';
+import { CustomerAccessType } from '@enums';
 import { AccessDetails } from '@types';
 import { CustomerAccess } from '@entities';
+import { CustomerAccessStatus } from '@px-shared-account/hermes';
 
 @Entity({ name: 'customerAccesses' })
 export class CustomerAccessTable implements CustomerAccess {
diff --git a/src/use-cases/course/course.factory.ts b/src/use-cases/course/course.factory.ts
index ed58320..fc2107c 100644
--- a/src/use-cases/course/course.factory.ts
+++ b/src/use-cases/course/course.factory.ts
@@ -36,8 +36,9 @@ export class CourseFactory {
       : [];
     course.lmsId = courseInfo.lmsId;
     const productFamily = new ProductFamily();
-    productFamily.id = courseInfo.familyId;
+    productFamily.id = courseInfo.productFamilyId;
     course.productFamily = productFamily;
+    course.ctaLink = courseInfo.ctaLink;
     return course;
   }
 
diff --git a/src/use-cases/course/course.use-cases.ts b/src/use-cases/course/course.use-cases.ts
index 3145e11..93be275 100644
--- a/src/use-cases/course/course.use-cases.ts
+++ b/src/use-cases/course/course.use-cases.ts
@@ -2,16 +2,22 @@ import { Injectable, NotFoundException } from '@nestjs/common';
 import { CourseFactory } from './course.factory';
 import { CourseTable } from '@tables';
 import { CreateCourseDto, UpdateCourseDto } from '@dtos';
-import { CourseEntity } from '@entities';
+import { CourseEntity, CustomerAccess } from '@entities';
 import {
   CourseStatus,
   IListCoursesResponse,
   PXActionResult,
   UpdateResultWithItemInfo,
   IGetStudentsForCourseResponse,
+  IListCourseCatalogResponse,
+  ICourseCatalogItem,
 } from '@px-shared-account/hermes';
 import { FindOptionsWhere } from 'typeorm';
 import { IDataServices } from '@abstracts';
+import { ListCourseCatalogDto } from '@dtos';
+import { UserEntity } from '@entities';
+import { AccessTarget, CustomerAccessType } from '@enums';
+import { CourseAccess } from '@types';
 
 @Injectable()
 export class CourseUseCases {
@@ -136,6 +142,96 @@ export class CourseUseCases {
     };
   }
 
+  async listCatalog(
+    params: ListCourseCatalogDto,
+    user: UserEntity,
+  ): Promise<IListCourseCatalogResponse> {
+    const { limit = 10, cursor = 0, search, status, owned } = params;
+    const courseTableName = this.databaseService.course.getTableName();
+    const customerAccessesTableName = this.databaseService.customerAccess.getTableName();
+    const queryBuilder = this.databaseService.course
+      .getRepository()
+      .createQueryBuilder(courseTableName)
+      .leftJoinAndSelect(`${courseTableName}.productFamily`, 'productFamily')
+      .leftJoinAndSelect('productFamily.productLine', 'productLine');
+
+    if (search) {
+      queryBuilder.andWhere(
+        `(${courseTableName}.name ILIKE :search OR ${courseTableName}.description ILIKE :search)`,
+        { search: `%${search}%` },
+      );
+    }
+
+    if (status) {
+      queryBuilder.andWhere(`${courseTableName}.status = :status`, { status });
+    }
+
+    const customerAccesses =
+      await this.databaseService.customerAccess.getAllMatchingWithRelationIds(
+        customerAccessesTableName,
+        'customerId',
+        user.id
+      );
+    const ownedLmsIds = new Map<string, string>();
+    for (const access of customerAccesses) {
+      if (access.type === CustomerAccessType.LMS) {
+        const courseAccess = access.details as CourseAccess;
+        ownedLmsIds.set(courseAccess.slug, courseAccess.link);
+      }
+    }
+
+    if (owned === true) {
+      if (ownedLmsIds.size === 0) {
+        return { data: [], nextCursor: null };
+      }
+      queryBuilder.andWhere(
+        `${courseTableName}.lmsId IN (:...ownedLmsIds)`,
+        {
+          ownedLmsIds: Array.from(ownedLmsIds.keys()),
+        },
+      );
+    } else if (owned === false) {
+      queryBuilder.andWhere(
+        `${courseTableName}.lmsId NOT IN (:...ownedLmsIds)`,
+        {
+          ownedLmsIds: Array.from(ownedLmsIds.keys()),
+        },
+      );
+    }
+
+    queryBuilder
+      .orderBy(`${courseTableName}.updatedAt`, 'DESC')
+      .skip(cursor)
+      .take(limit);
+
+    const courses = await queryBuilder.getMany();
+    const total = await queryBuilder.getCount();
+
+    const data = courses.map((course: CourseEntity) => {
+      const isOwned = ownedLmsIds.has(course.lmsId);
+      const access: CustomerAccess | undefined = isOwned
+        ? customerAccesses.find(
+          (access) => access.details.target === AccessTarget.LW_COURSE &&
+            (access.details as CourseAccess).slug === course.lmsId
+        )
+        : undefined;
+
+      return {
+        ...course,
+        owned: isOwned,
+        accessStatus: access ? access.status : undefined,
+        lmsUrl: isOwned ? ownedLmsIds.get(course.lmsId) : undefined,
+      } as ICourseCatalogItem;
+    });
+
+    const nextCursor = total > cursor + limit ? cursor + limit : null;
+
+    return {
+      data,
+      nextCursor,
+    };
+  }
+
   /**
    * Publishes a course
    * @param id `id` of the course to publish
diff --git a/src/use-cases/customer-access/customer-access.factory.ts b/src/use-cases/customer-access/customer-access.factory.ts
index 3434d66..558a312 100644
--- a/src/use-cases/customer-access/customer-access.factory.ts
+++ b/src/use-cases/customer-access/customer-access.factory.ts
@@ -1,7 +1,8 @@
 import { Injectable } from '@nestjs/common';
 import { CustomerAccess } from '@entities';
-import { CustomerAccessStatus, CustomerAccessType } from '@enums';
+import { CustomerAccessType } from '@enums';
 import { AccessDetails } from '@types';
+import { CustomerAccessStatus } from '@px-shared-account/hermes';
 
 @Injectable()
 export class CustomerAccessFactory {
diff --git a/src/use-cases/customer-access/customer-access.use-cases.ts b/src/use-cases/customer-access/customer-access.use-cases.ts
index 31c8a78..480669f 100644
--- a/src/use-cases/customer-access/customer-access.use-cases.ts
+++ b/src/use-cases/customer-access/customer-access.use-cases.ts
@@ -1,21 +1,21 @@
 import { IDataServices } from '@abstracts';
 import { CustomerAccess } from '@entities';
-import { AccessTarget, CustomerAccessStatus, CustomerAccessType } from '@enums';
-import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
+import { AccessTarget, CustomerAccessType } from '@enums';
+import { Injectable, Logger } from '@nestjs/common';
 import { CustomerAccessFactory } from './customer-access.factory';
 import {
   AccessDetails,
   AccessInfoWithCustomerInfo,
   CourseAccess,
   PXActionResult,
-  RawCourseAccessResult,
   SpaceAccess,
   SubscriptionAccesses,
 } from '@types';
-import { UserTable, SubscriptionTable, CourseTable } from '@tables';
+import { UserTable, SubscriptionTable } from '@tables';
 import { CircleService } from '@services/community';
 import { LearnworldsService } from '@services/lms';
 import { GoliathService } from '../goliaths';
+import { CustomerAccessStatus } from '@px-shared-account/hermes';
 
 @Injectable()
 export class CustomerAccessUseCases {
@@ -386,77 +386,4 @@ export class CustomerAccessUseCases {
       ])
       .getRawOne<AccessInfoWithCustomerInfo>();
   }
-
-  /**
-   * Retrieves course accesses for a given customer email
-   * @param email Customer's email to fetch course accesses for
-   * @returns A promise that resolves to an array of `CourseAccess` objects
-   */
-  async getCourseAccesses(email: string): Promise<CourseAccess[]> {
-    try {
-      const accesses = await this.fetchRawCourseAccesses(email);
-      return accesses.map(this.generateCourseAccessFromRaw);
-    } catch (err) {
-      this.logger.error(err);
-      throw new HttpException(err.message, HttpStatus.INTERNAL_SERVER_ERROR);
-    }
-  }
-
-  /**
-   * Builds and executes the query to fetch raw course access data for a customer email.
-   * @param email Customer's email
-   * @returns A promise that resolves to an array of `RawCourseAccessResult` objects
-   */
-  async fetchRawCourseAccesses(
-    email: string,
-  ): Promise<RawCourseAccessResult[]> {
-    return this.dataServices.customerAccess
-      .getRepository()
-      .createQueryBuilder('customer_access')
-      .innerJoin(UserTable, 'user', 'user.id = customer_access."customerId"')
-      .innerJoin(
-        CourseTable,
-        'course',
-        'course."lmsId" = customer_access.details->>\'slug\'',
-      )
-      .where('user.email = :email', { email })
-      .andWhere('customer_access.type = :type', {
-        type: CustomerAccessType.LMS,
-      })
-      .andWhere('customer_access.status = :status', {
-        status: CustomerAccessStatus.GRANTED,
-      })
-      .select([
-        'customer_access.id AS id',
-        'customer_access.status AS status',
-        'customer_access.type AS type',
-        'customer_access.details AS details',
-        'course.name AS name',
-        'course."lmsId" AS slug',
-        'course."bannerImage" AS "bannerImage"',
-        'course."cardImage" AS "cardImage"',
-        'course.thumbnail AS thumbnail',
-      ])
-      .getRawMany();
-  }
-
-  /**
-   * Maps a raw DB result to a CourseAccess object.
-   * @param raw Raw course access result
-   * @returns A `CourseAccess` object
-   */
-  generateCourseAccessFromRaw(raw: RawCourseAccessResult): CourseAccess {
-    const details = raw.details as Partial<CourseAccess>;
-    return {
-      id: raw.id,
-      name: raw.name,
-      slug: raw.slug,
-      target: AccessTarget.LW_COURSE,
-      link: details.link,
-      bannerImage: raw.bannerImage,
-      cardImage: raw.cardImage,
-      thumbnail: raw.thumbnail,
-      status: raw.status,
-    };
-  }
 }
diff --git a/src/use-cases/product-delivery/product-delivery.service.ts b/src/use-cases/product-delivery/product-delivery.service.ts
index 4c2dd09..657a3d4 100644
--- a/src/use-cases/product-delivery/product-delivery.service.ts
+++ b/src/use-cases/product-delivery/product-delivery.service.ts
@@ -3,7 +3,6 @@ import { UserUseCases } from '../user';
 import { LearnworldsService } from '@services/lms';
 import { CustomerAccessUseCases } from '../customer-access';
 import {
-  CustomerAccessStatus,
   CustomerAccessType,
   AccessTarget,
   CommunityType,
@@ -22,6 +21,7 @@ import {
 } from '@types';
 import { CircleService } from '@services/community';
 import { UserEntity, CustomerAccess } from '@entities';
+import { CustomerAccessStatus } from '@px-shared-account/hermes';
 
 @Injectable()
 export class ProductDeliveryService {
