import { HttpException, HttpStatus } from '@nestjs/common';
import { ZodError } from 'zod';

export class ValidationError extends HttpException {
  constructor(
    public readonly errors: { field: string; message: string }[],
    public readonly originalError?: Error,
  ) {
    super(
      {
        statusCode: HttpStatus.BAD_REQUEST,
        error: 'Validation Error',
        message: 'Validation failed',
        details: errors,
      },
      HttpStatus.BAD_REQUEST,
    );
    this.name = 'ValidationError';
  }

  /**
   * Gets the original error for logging purposes
   * @returns The original error or the current instance if no original error is available
   */
  getOriginalErrorForLogging(): Error {
    return this.originalError || this;
  }

  /**
   * Gets additional context for Sentry logging
   * @returns The context for Sentry logging
   */
  getContextForSentry(): Record<string, any> {
    return {
      errorType: this.name,
      statusCode: this.getStatus(),
      validationErrors: this.errors,
    };
  }

  /**
   * Creates a ValidationError from a ZodError
   * @param zodError - The ZodError to create the ValidationError from
   * @returns A new ValidationError instance
   */
  static fromZodError(zodError: ZodError): ValidationError {
    const formattedErrors = zodError.errors.map((err) => {
      const field = err.path.join('.');
      const message = err.message;
      return { field, message };
    });

    return new ValidationError(formattedErrors, zodError);
  }
}
