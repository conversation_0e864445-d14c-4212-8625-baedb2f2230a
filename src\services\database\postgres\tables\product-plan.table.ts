import {
  Entity,
  Column,
  ManyToOne,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  PrimaryGeneratedColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { ProductPlan } from '@entities';
import { FiscalEntity } from '@enums';
import { ProductTable } from './product.table';
import { ProductOfferTable } from './product-offers.table';
import { ProductPlanPriceTable } from './product-plan-price.table';

@Entity('productPlans')
export class ProductPlanTable implements ProductPlan {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 255, unique: true })
  internalName: string;

  @Column({ type: 'varchar', length: 255 })
  externalName: string;

  @Column({ type: 'varchar', length: 500 })
  description: string;

  @Column({ type: 'varchar', length: 8 })
  status: string;

  @Column({ type: 'enum', enum: FiscalEntity, nullable: true })
  fiscalEntity?: FiscalEntity;

  @ManyToOne(() => ProductTable, (product) => product.plans)
  product: ProductTable;

  @OneToMany(
    () => ProductPlanPriceTable,
    (planPrice) => planPrice.productPlan,
    {
      eager: true,
    },
  )
  prices: ProductPlanPriceTable[];

  @ManyToMany(() => ProductOfferTable)
  @JoinTable({
    name: 'ProductOffer_Plans',
    joinColumn: {
      name: 'planId',
      referencedColumnName: 'id',
      foreignKeyConstraintName: 'FK_ProductOffer_Plans_planId',
    },
    inverseJoinColumn: {
      name: 'offerId',
      referencedColumnName: 'id',
      foreignKeyConstraintName: 'FK_ProductOffer_Plans_offerId',
    },
  })
  offers?: ProductOfferTable[];

  @Column({ type: 'varchar', length: 100 })
  chargebeeId: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  crmId?: string;

  @Column({ type: 'varchar', length: 99, nullable: true })
  crmSku: string;

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
