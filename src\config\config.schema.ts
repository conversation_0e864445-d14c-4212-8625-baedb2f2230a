import { z } from 'zod';

export const AppSecretsSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'staging']),
  PORT: z
    .string()
    .nonempty('PORT is required')
    .transform((value) => {
      const parsed = parseInt(value, 10);
      if (isNaN(parsed)) {
        throw new Error('PORT must be a valid number');
      }
      return parsed;
    }),
  PX_API_URL: z.string().url().nonempty('PX_API_URL is required'),
  CHECKOUT_URL: z.string().url().nonempty('CHECKOUT_URL is required'),
  CUSTOMER_PORTAL_SIGNUP_LINK: z
    .string()
    .url()
    .nonempty('CUSTOMER_PORTAL_SIGNUP_LINK is required'),
  MAKE_SUBSCRIPTION_NOTIFICATION_URL: z
    .string()
    .url()
    .nonempty('MAKE_SUBSCRIPTION_NOTIFICATION_URL is required'),
  PRODUCT_DELIVERY_ENABLED: z.coerce.boolean().default(true),
  TEMP_DISABLE_ACCESS_SUSPENSION: z.coerce.boolean().default(true),
  PX_OS_URL: z.string().url().nonempty('PX_OS_URL is required'),
  SUBSCRIPTION_EXPORT_FIELDS: z
    .string()
    .nonempty('SUBSCRIPTION_EXPORT_FIELDS is required'),
  UAE_BANK_EXR_URL: z.string().url().nonempty('UAE_BANK_EXR_URL is required'),
  VERCEL_BLOB_READ_WRITE_TOKEN: z.string().nonempty('VERCEL_BLOB_READ_WRITE_TOKEN is required'),
});
export type AppSecrets = z.infer<typeof AppSecretsSchema>;

export const AuthSecretsSchema = z.object({
  CLERK_PEM_PUBLIC_KEY: z.string().nonempty('CLERK_PEM_PUBLIC_KEY is required'),
  CLERK_SECRET_KEY: z.string().nonempty('CLERK_SECRET_KET is required'),
  CLERK_WEBHOOKS_SECRET: z
    .string()
    .nonempty('CLERK_WEBHOOKS_SECRET is required'),
  CLERK_PEM_PUBLIC_KEY_OS: z
    .string()
    .nonempty('CLERK_PEM_PUBLIC_KEY_OS is required'),
});
export type AuthSecrets = z.infer<typeof AuthSecretsSchema>;

export const ChargebeeSecretsSchema = z.object({
  CHARGEBEE_API_KEY: z.string().nonempty('CHARGEBEE_API_KEY is required'),
  CHARGEBEE_API_URL: z.string().nonempty('CHARGEBEE_API_URL is required'),
  CHARGEBEE_BUSINESS_ID_PG: z
    .string()
    .nonempty('CHARGEBEE_BUSINESS_ID_PG is required'),
  CHARGEBEE_BUSINESS_ID_PI: z
    .string()
    .nonempty('CHARGEBEE_BUSINESS_ID_PI is required'),
  CHARGEBEE_BUSINESS_ID_PP: z
    .string()
    .nonempty('CHARGEBEE_BUSINESS_ID_PP is required'),
  CHARGEBEE_GATEWAY_ACCOUNT_ID_PG: z
    .string()
    .nonempty('CHARGEBEE_GATEWAY_ACCOUNT_ID_PG is required'),
  CHARGEBEE_GATEWAY_ACCOUNT_ID_PI: z
    .string()
    .nonempty('CHARGEBEE_GATEWAY_ACCOUNT_ID_PI is required'),
  CHARGEBEE_GATEWAY_ACCOUNT_ID_PT: z
    .string()
    .nonempty('CHARGEBEE_GATEWAY_ACCOUNT_ID_PT is required'),
  CHARGEBEE_SITE: z.string().nonempty('CHARGEBEE_SITE is required'),
});
export type ChargebeeSecrets = z.infer<typeof ChargebeeSecretsSchema>;

export const DBSecretsSchema = z.object({
  HOST: z.string().nonempty('HOST is required'),
  PORT: z.string().nonempty('PORT is required'),
  USERNAME: z.string().nonempty('USERNAME is required'),
  PASSWORD: z.string().nonempty('PASSWORD is required'),
  DATABASE: z.string().nonempty('DATABASE is required'),
});
export type DBSecrets = z.infer<typeof DBSecretsSchema>;

export const GeneralConfigSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'staging']),
});
export type GeneralConfig = z.infer<typeof GeneralConfigSchema>;

export const MonitoringSecretsSchema = z.object({
  SENTRY_DSN: z.string().nonempty('SENTRY_DSN is required'),
});
export type MonitoringSecrets = z.infer<typeof MonitoringSecretsSchema>;

export const CacheSecretsSchema = z.object({
  PX_API_REDIS_CONNECTION_STRING: z
    .string()
    .nonempty('PX_API_REDIS_CONNECTION_STRING is required'),
  VEGA_REDIS_CONNECTION_STRING: z
    .string()
    .nonempty('VEGA_REDIS_CONNECTION_STRING is required'),
});
export type CacheSecrets = z.infer<typeof CacheSecretsSchema>;

export const NotificationSecretsSchema = z.object({
  SLACK_TOKEN: z.string().nonempty('SLACK_TOKEN is required'),
  SLACK_ERRORS_CHANNEL: z.string().nonempty('SLACK_ERRORS_CHANNEL is required'),
  SLACK_OVERDUE_SUBSCRIPTIONS_NOTIFICATION_CHANNEL: z
    .string()
    .nonempty('SLACK_OVERDUE_SUBSCRIPTIONS_NOTIFICATION_CHANNEL is required'),
  HUBSPOT_SYNC_ERRORS_CHANNEL: z
    .string()
    .nonempty('HUBSPOT_SYNC_ERRORS_CHANNEL is required'),
});
export type NotificationSecrets = z.infer<typeof NotificationSecretsSchema>;

export const CircleSecretsSchema = z.object({
  CIRCLE_PXL_API_KEY: z.string().nonempty('CIRCLE_PXL_API_KEY is required'),
  CIRCLE_PXL_API_V2_KEY: z
    .string()
    .nonempty('CIRCLE_PXL_API_V2_KEY is required'),
  CIRCLE_PXL_COMMUNITY_ID: z
    .string()
    .nonempty('CIRCLE_PXL_COMMUNITY_ID is required'),
  CIRCLE_PXS_API_KEY: z.string().nonempty('CIRCLE_PXS_API_KEY is required'),
  CIRCLE_PXS_API_V2_KEY: z
    .string()
    .nonempty('CIRCLE_PXS_API_V2_KEY is required'),
  CIRCLE_PXS_COMMUNITY_ID: z
    .string()
    .nonempty('CIRCLE_PXS_COMMUNITY_ID is required'),
});
export type CircleSecrets = z.infer<typeof CircleSecretsSchema>;

export const HubspotSecretsSchema = z.object({
  HUBSPOT_ACCESS_TOKEN_CACHE_KEY: z
    .string()
    .nonempty('HUBSPOT_ACCESS_TOKEN_CACHE_KEY is required'),
  HUBSPOT_CLIENT_ID: z.string().nonempty('HUBSPOT_CLIENT_ID is required'),
  HUBSPOT_CLIENT_SECRET: z
    .string()
    .nonempty('HUBSPOT_CLIENT_SECRET is required'),
  HUBSPOT_DEAL_DEFAULT_OWNER: z
    .string()
    .nonempty('HUBSPOT_DEAL_DEFAULT_OWNER is required'),
  HUBSPOT_DEAL_LOST_STAGE: z
    .string()
    .nonempty('HUBSPOT_DEAL_LOST_STAGE is required'),
  HUBSPOT_DEAL_PIPELINE: z
    .string()
    .nonempty('HUBSPOT_DEAL_PIPELINE is required'),
  HUBSPOT_DEAL_WON_STAGE: z
    .string()
    .nonempty('HUBSPOT_DEAL_WON_STAGE is required'),
  HUBSPOT_OAUTH_URL: z.string().nonempty('HUBSPOT_OAUTH_URL is required'),
  HUBSPOT_REDIRECT_URI: z.string().nonempty('HUBSPOT_REDIRECT_URI is required'),
  HUBSPOT_REFRESH_TOKEN: z
    .string()
    .nonempty('HUBSPOT_REFRESH_TOKEN is required'),
});
export type HubspotSecrets = z.infer<typeof HubspotSecretsSchema>;

export const LearnworldsSecretsSchema = z.object({
  LEARNWORLDS_API_ENDPOINT: z
    .string()
    .nonempty('LEARNWORLDS_API_ENDPOINT is required'),
  LEARNWORLDS_CLIENT_ID: z
    .string()
    .nonempty('LEARNWORLDS_CLIENT_ID is required'),
  LEARNWORLDS_CLIENT_SECRET: z
    .string()
    .nonempty('LEARNWORLDS_CLIENT_SECRET is required'),
  LW_ACCESS_TOKEN_CACHE_KEY: z
    .string()
    .nonempty('LW_ACCESS_TOKEN_CACHE_KEY is required'),
});
export type LearnworldsSecrets = z.infer<typeof LearnworldsSecretsSchema>;

export const GoliathsSecretsSchema = z.object({
  GOLIATHS_X_API_KEY_DEV: z
    .string()
    .nonempty('GOLIATHS_X_API_KEY_DEV is required'),
  GOLIATHS_X_API_KEY_PROD: z
    .string()
    .nonempty('GOLIATHS_X_API_KEY_PROD is required'),
  GOLIATHS_ENDPOINT_DEV: z
    .string()
    .nonempty('GOLIATHS_ENDPOINT_DEV is required'),
  GOLIATHS_ENDPOINT_PROD: z
    .string()
    .nonempty('GOLIATHS_ENDPOINT_PROD is required'),
});
export type GoliathsSecrets = z.infer<typeof GoliathsSecretsSchema>;

export const CustomerioSecretsSchema = z.object({
  CUSTOMERIO_API_KEY: z.string().nonempty('CUSTOMERIO_API_KEY is required'),
});
export type CustomerioSecrets = z.infer<typeof CustomerioSecretsSchema>;
