import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { useAuth } from "@clerk/nextjs";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Smile } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useWhatsNewReactions, useToggleReaction } from "@/services/whats-new";
import { ALLOWED_EMOJIS } from "@px-shared-account/hermes";

interface Reaction {
  emoji: string;
  count: number;
  userReacted: boolean;
}

interface EmojiReactionsProps {
  versionId: number;
  className?: string;
}

export default function EmojiReactions({ versionId, className }: EmojiReactionsProps) {
  const t = useTranslations("whats-new.reactions");
  const { userId, isSignedIn } = useAuth();
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [pendingReactions, setPendingReactions] = useState<Set<string>>(new Set());
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Use the service layer hooks
  const { data: reactionsData, error, isLoading, mutate } = useWhatsNewReactions(versionId);
  const { toggleReaction } = useToggleReaction();

  // Transform API data to component format
  const reactions = useMemo(() => {
    if (!reactionsData?.reactions) return [];

    // Create a complete list of emojis with counts
    const reactionMap = new Map(reactionsData.reactions.map((r) => [r.emoji, r]));

    return ALLOWED_EMOJIS.map((emoji) => {
      const reaction = reactionMap.get(emoji);
      return reaction || { emoji, count: 0, userReacted: false };
    }) as Reaction[];
  }, [reactionsData]);

  // Clear any pending timeouts when unmounting
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleReaction = useCallback(
    async (emoji: string) => {
      if (!isSignedIn) {
        // Prompt user to sign in
        alert(t("sign-in-required"));
        return;
      }

      // Add to pending reactions to show loading state
      setPendingReactions((prev) => new Set(prev).add(emoji));

      try {
        // Find the current reaction state
        const currentReaction = reactions.find((r) => r.emoji === emoji);
        const isRemoving = currentReaction?.userReacted;

        // Optimistically update the UI
        const optimisticData = {
          ...reactionsData,
          reactions:
            reactionsData?.reactions?.map((r) => {
              if (r.emoji === emoji) {
                return {
                  ...r,
                  count: isRemoving ? Math.max(0, r.count - 1) : r.count + 1,
                  userReacted: !r.userReacted,
                };
              }
              return r;
            }) || [],
        };

        // If the emoji doesn't exist in reactions yet, add it
        if (!optimisticData.reactions.some((r) => r.emoji === emoji)) {
          optimisticData.reactions.push({
            emoji,
            count: 1,
            userReacted: true,
          });
        }

        // Update UI immediately with optimistic data
        mutate(optimisticData, false);

        // Debounce the actual API call to prevent rapid clicking
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(async () => {
          try {
            await toggleReaction({
              versionId,
              emoji,
            });

            // Refresh reactions data from server in the background
            mutate();
          } catch (error) {
            console.error("Error toggling reaction:", error);
            // Revert optimistic update on error by re-fetching
            mutate();
          } finally {
            // Remove from pending reactions
            setPendingReactions((prev) => {
              const newSet = new Set(prev);
              newSet.delete(emoji);
              return newSet;
            });
          }
        }, 300);
      } catch (error) {
        console.error("Error toggling reaction:", error);
        // Remove from pending reactions
        setPendingReactions((prev) => {
          const newSet = new Set(prev);
          newSet.delete(emoji);
          return newSet;
        });
      }
    },
    [isSignedIn, versionId, t, toggleReaction, mutate, reactions, reactionsData],
  );

  // Animation for reaction button when clicked
  const getReactionAnimation = useCallback(
    (emoji: string) => {
      const isPending = pendingReactions.has(emoji);
      const hasReacted = reactions.find((r) => r.emoji === emoji)?.userReacted;

      if (isPending) {
        return "scale-110 transition-transform";
      } else if (hasReacted) {
        return "animate-bounce-once";
      }
      return "";
    },
    [pendingReactions, reactions],
  );

  // Filter reactions to only show ones with counts or user reactions
  const activeReactions = useMemo(() => {
    return reactions.filter((r) => r.count > 0 || r.userReacted);
  }, [reactions]);

  return (
    <div className={cn("mt-4 flex flex-wrap items-center gap-2", className)}>
      {/* Only show reactions that have counts or user has reacted to */}
      {activeReactions.map((reaction) => (
        <button
          key={reaction.emoji}
          onClick={() => handleReaction(reaction.emoji)}
          className={cn(
            "flex items-center gap-1 rounded-full px-2 py-1 text-sm transition-all duration-200",
            reaction.count > 0 ? "bg-white/10" : "bg-transparent",
            reaction.userReacted
              ? "border border-white/30 bg-white/15"
              : "border border-transparent hover:border-white/10",
            "hover:bg-white/15",
            getReactionAnimation(reaction.emoji),
          )}
          disabled={isLoading}
        >
          <span>{reaction.emoji}</span>
          {reaction.count > 0 && <span className="text-xs text-gray-300">{reaction.count}</span>}
        </button>
      ))}

      {/* Add reaction button with emoji picker */}
      <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 rounded-full border-dashed border-white/20 bg-transparent px-2 text-white hover:bg-white/10 hover:text-white/80"
            disabled={!isSignedIn}
          >
            <Smile className="mr-1 h-4 w-4" />
            <span className="text-xs">{t("add-reaction")}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto border border-white/20 bg-black/90 p-2">
          <div className="grid grid-cols-4 gap-2">
            {ALLOWED_EMOJIS.map((emoji) => {
              const hasReacted = reactions.find((r) => r.emoji === emoji)?.userReacted;
              return (
                <button
                  key={emoji}
                  onClick={() => {
                    handleReaction(emoji);
                    setShowEmojiPicker(false);
                  }}
                  className={cn(
                    "rounded-md p-2 transition-colors hover:bg-white/10",
                    hasReacted && "bg-white/15",
                  )}
                >
                  <span className="text-xl">{emoji}</span>
                </button>
              );
            })}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
