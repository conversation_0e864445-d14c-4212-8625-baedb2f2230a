import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  ManyToMany,
} from 'typeorm';
import { ProductPlanPrice } from '@entities';
import { FiscalEntity } from '@enums';
import { DiscountTable } from './discount.table';
import { ProductPlanTable } from './product-plan.table';
import { SubscriptionTable } from './subscription.table';

@Entity('productPlanPrices')
export class ProductPlanPriceTable implements ProductPlanPrice {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 255, unique: true })
  internalName: string;

  @Column({ type: 'varchar', length: 255 })
  externalName: string;

  @Column({ type: 'varchar', length: 500 })
  description: string;

  @Column({ type: 'varchar', length: 8 })
  status: string;

  @Column({ type: 'varchar', length: 10, default: 'per_unit' })
  pricingModel: string;

  @Column({ type: 'decimal' })
  amountPerBillingCycle: number;

  @Column({ type: 'decimal' })
  amount: number;

  @Column({ type: 'int', nullable: true })
  period: number;

  @Column({ type: 'varchar', length: 15, default: 'month' })
  periodUnit: string;

  @Column({ type: 'varchar', length: 5, default: 'EUR' })
  currencyCode: string;

  @Column({ type: 'int', nullable: true })
  totalBillingCycles?: number;

  @ManyToOne(() => ProductPlanTable, (productPlan) => productPlan.prices)
  productPlan: ProductPlanTable;

  @Column({ type: 'varchar' })
  chargebeeId: string;

  @Column({ type: 'enum', enum: FiscalEntity, nullable: true })
  fiscalEntity?: FiscalEntity;

  @ManyToMany(() => DiscountTable, { nullable: true })
  discounts?: DiscountTable[];

  @ManyToMany(() => SubscriptionTable, { nullable: true })
  subscriptions?: SubscriptionTable[];

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
