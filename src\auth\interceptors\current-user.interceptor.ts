import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallH<PERSON>ler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { UserUseCases } from '@useCases';

@Injectable()
export class CurrentUserInterceptor implements NestInterceptor {
  constructor(private readonly userUseCases: UserUseCases) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const userJwt = request.user;

    if (userJwt && userJwt.sub) {
      try {
        const user = await this.userUseCases.getBySSOId(userJwt.sub);
        request.fullUser = user;
      } catch (error) {
        request.fullUser = null;
      }
    } else {
      request.fullUser = null;
    }

    return next.handle();
  }
}
