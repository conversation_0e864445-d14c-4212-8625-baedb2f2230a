"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import BadgeSubscriptionStatus, {
  SubscriptionStatus,
} from "@/components/purchases/badge/subscriptionStatus";
import { InvoiceList } from "./InvoiceList";
import { PaymentMethod } from "./PaymentMethod";
import { CancelSubscription } from "./CancelSubscription";
import { useOrderStatus } from "@/services/customers";
import { formatCurrency, formatDate } from "@/lib/utils";
import { PurchaseRecord, InvoiceRecord, UserAccessDetailsResponse } from "@/services/customers";

interface PurchaseDetailsProps {
  subscription: PurchaseRecord;
  invoices: InvoiceRecord[];
  customerAccessDetails?: UserAccessDetailsResponse;
  paymentMethod?: {
    brand?: string;
    last4?: string;
    expMonth?: number;
    expYear?: number;
  } | null;
}

export function PurchaseDetails({
  subscription,
  invoices,
  customerAccessDetails,
  paymentMethod = null,
}: PurchaseDetailsProps) {
  const t = useTranslations("subscription");
  const [canceling, setCanceling] = useState(false);
  const [startedDate, setStartedDate] = useState("");
  const [lastPaymentDate, setLastPaymentDate] = useState("");
  const [nextPaymentDate, setNextPaymentDate] = useState("");

  const { orderStatus } = useOrderStatus(subscription.subscription.id);
  const isForever = subscription.subscription.cfIsForever ?? false;
  const status = orderStatus || subscription.subscription.status;

  useEffect(() => {
    // Format dates
    if (subscription.subscription.startedAt) {
      setStartedDate(formatDate(subscription.subscription.startedAt));
    }

    // Format last payment date for cancelled subscriptions
    if (
      ["cancelled", "non_renewing"].includes(subscription.subscription.status.toLowerCase()) &&
      subscription.subscription.cancelledAt
    ) {
      setLastPaymentDate(formatDate(subscription.subscription.cancelledAt));
    } else if (subscription.subscription.lastPaymentDate) {
      setLastPaymentDate(formatDate(subscription.subscription.lastPaymentDate));
    }

    // Format next payment date
    if (subscription.subscription.currentTermEnd) {
      setNextPaymentDate(formatDate(subscription.subscription.currentTermEnd));
    }
  }, [subscription]);

  return (
    <>
      <div className="grid gap-6 sm:grid-cols-2">
        <section>
          <h1 className="my-6 text-2xl font-bold">{t("details.title")}</h1>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div>
                  <h2 className="my-0 text-xl font-semibold">
                    {subscription.plan.externalName || subscription.plan.name}
                  </h2>
                  <span className="text-sm text-gray-500">ID: {subscription.subscription.id}</span>
                </div>
                <BadgeSubscriptionStatus status={status as SubscriptionStatus} />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Subscription details rows */}
              {startedDate && (
                <div className="flex justify-between py-2">
                  <span className="text-gray-500">{t("details.started")}</span>
                  <span>{startedDate}</span>
                </div>
              )}

              {lastPaymentDate && (
                <div className="flex justify-between py-2">
                  <span className="text-gray-500">{t("details.last-payment")}</span>
                  <span>{lastPaymentDate}</span>
                </div>
              )}

              {nextPaymentDate && status.toLowerCase() !== "cancelled" && (
                <div className="flex justify-between py-2">
                  <span className="text-gray-500">
                    {status.toLowerCase() === "non_renewing"
                      ? t("details.expires")
                      : t("details.next-payment")}
                  </span>
                  <span>{nextPaymentDate}</span>
                </div>
              )}

              {subscription.subscription.mrr !== undefined && (
                <div className="flex justify-between py-2">
                  <span className="text-gray-500">{t("details.amount")}</span>
                  <span className="font-semibold">
                    {formatCurrency(subscription.subscription.mrr / 100)}
                  </span>
                </div>
              )}

              <div className="mt-6 flex flex-col gap-3 sm:flex-row">
                <Link href="/settings" className="w-full">
                  <Button
                    variant="outline"
                    className="w-full"
                    disabled={status.toLowerCase() === "cancelled"}
                  >
                    {t("details.change-payment-method")}
                  </Button>
                </Link>

                {isForever && status.toLowerCase() !== "cancelled" && (
                  <Button
                    variant="destructive"
                    className="w-full"
                    onClick={() => setCanceling(true)}
                  >
                    {t("details.cancel")}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {paymentMethod && (
            <div className="mb-6">
              <PaymentMethod
                paymentMethod={paymentMethod}
                cardholderName={customerAccessDetails?.email}
              />
            </div>
          )}
        </section>

        <section>
          <h2 className="my-6 mt-0 text-2xl font-bold sm:mt-6">{t("invoices.title")}</h2>
          <InvoiceList invoices={invoices} />
        </section>
      </div>

      {canceling && (
        <CancelSubscription id={subscription.subscription.id} onClose={() => setCanceling(false)} />
      )}
    </>
  );
}
