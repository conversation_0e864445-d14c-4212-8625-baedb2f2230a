import { Injectable } from '@nestjs/common';
import { Product, ProductPlan } from '@entities';
import { CreateProductPlanDto } from '@dtos';
import { IDataServices } from '@abstracts';

@Injectable()
export class ProductPlanFactory {
  constructor(private readonly dataServices: IDataServices) {}

  async generate(planInfo: CreateProductPlanDto): Promise<
    ProductPlan & {
      productFamily: string;
      productLine: string;
      taxProfile: string;
    }
  > {
    const { productId } = planInfo;
    const productPlan = new ProductPlan();
    productPlan.internalName = planInfo.internalName;
    productPlan.externalName = planInfo.externalName;
    productPlan.description = planInfo.description;
    productPlan.crmSku = planInfo.crmSku;
    productPlan.status = 'active';
    const product = new Product();
    product.id = productId;
    productPlan.product = product;
    productPlan.chargebeeId = crypto.randomUUID();
    const { productFamily, productLine, taxProfile } =
      await this.getProductInfo(productId);
    return {
      ...productPlan,
      productLine,
      productFamily,
      taxProfile,
    };
  }

  async getProductInfo(productId: number): Promise<any> {
    const productInfo = await this.dataServices.product.getOneBy({
      id: productId,
    });

    const familyInfo =
      await this.dataServices.productFamily.getOneWithRelations(
        'productFamilies',
        'id',
        productInfo.productFamily.id,
      );

    return {
      productFamily: productInfo.productFamily.chargebeeId,
      productLine: familyInfo.productLine.chargebeeId,
      taxProfile: productInfo.taxProfile,
    };
  }
}
