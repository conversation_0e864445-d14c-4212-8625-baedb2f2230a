import {
  Allocation,
  BillingAddress,
  ShippingAddress,
} from 'chargebee-typescript/lib/resources/credit_note';
import {
  CreditNoteLineItem,
  CreditNoteLinkedRefund,
} from 'chargebee-typescript/lib/resources';
import {
  CreditNoteStatus,
  Currencies,
  CreditNoteType,
  InvoicePriceType,
} from '@enums';

export class CreditNote {
  id: number;
  chargebeeId: string;
  status: CreditNoteStatus;
  date: number;
  generatedAt: number;
  refundedAt?: number;
  currencyCode: Currencies;
  total: number;
  exchangeRate: number;
  customerId: string;
  customerEmail: string;
  customerName: string;
  subscriptionId?: string;
  referenceInvoiceId?: string;
  type: CreditNoteType;
  businessEntityId: string;
  amountAvailable: number;
  amountAllocated: number;
  amountRefunded: number;
  createReason: string;
  channel: string;
  priceType: InvoicePriceType;
  allocations?: Allocation[];
  lineItems?: CreditNoteLineItem[];
  linkedRefunds?: CreditNoteLinkedRefund[];
  billingAddress?: BillingAddress;
  shippingAddress?: ShippingAddress;
  customerNotes?: string;
  createdAt?: Date;
}
