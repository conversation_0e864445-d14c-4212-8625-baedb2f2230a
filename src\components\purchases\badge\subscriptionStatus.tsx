"use client";

import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

export type SubscriptionStatus =
  | "active"
  | "paid"
  | "stopped"
  | "overdue"
  | "in trial"
  | ""
  | "null"
  | "undefined";

// Define colors directly or import from a central theme if available
const statusColors = {
  green: "bg-[#E5F7D1] text-[#296218]",
  red: "bg-[#FBE2D5] text-[#BE2E2D]",
  gray: "bg-[#BBBBBB] text-[#222222]",
  white: "bg-white text-[#222222]", // Assuming default/unknown status color
};

const computeStatusColorClasses = (status: SubscriptionStatus): string => {
  switch (status) {
    case "active":
    case "in trial":
    case "paid":
      return statusColors.green;
    case "overdue":
      return statusColors.red;
    case "stopped":
      return statusColors.gray;
    default:
      return statusColors.white; // Fallback color
  }
};

export default function BadgeSubscriptionStatus({ status }: { status: SubscriptionStatus }) {
  const t = useTranslations();
  const [localStatus, setStatus] = useState<SubscriptionStatus>(status);

  useEffect(() => {
    setStatus(status);
  }, [status]);

  const commonBadgeStyles =
    "z-20 h-6 rounded-md px-2.5 py-0.5 text-xs font-light uppercase inline-flex items-center justify-center";

  return (
    <div className={cn(commonBadgeStyles, computeStatusColorClasses(localStatus))}>
      {t(`dashboard.purchases.status.${localStatus || "default"}`)}
    </div>
  );
}
