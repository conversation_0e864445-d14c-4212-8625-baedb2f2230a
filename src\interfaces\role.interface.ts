import { z } from "zod";
import { RoleGroup, Roles } from "../enums";

export const RoleBaseSchema = z.object({
  id: z.number().positive(),
  group: z.nativeEnum(RoleGroup),
  name: z.string().nonempty(),
  permissions: z.array(z.string()),
});
export type IRoleBase = z.infer<typeof RoleBaseSchema>;

export const CreateRoleSchema = RoleBaseSchema.partial({
  id: true,
});
export type ICreateRole = z.infer<typeof CreateRoleSchema>;

export const UpdateRoleSchema = z.object({
  permissions: z.array(z.string()).optional(),
  name: z.string().optional(),
});
export type IUpdateRole = z.infer<typeof UpdateRoleSchema>;

export const GetRoleByIdSchema = z.object({
  id: z.number().positive(),
});
export type IGetRoleById = z.infer<typeof GetRoleByIdSchema>;

export const FilterRoleSchema = z.object({
  group: z.nativeEnum(RoleGroup).optional(),
  search: z.string().optional(),
});
export type IFilterRole = z.infer<typeof FilterRoleSchema>;

export const ListRoleSchema = RoleBaseSchema.extend({
  members: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
});
export type IListRole = z.infer<typeof ListRoleSchema>;

export const ListRolesResponseSchema = z.object({
  data: z.array(ListRoleSchema),
  page: z.number(),
  total: z.number(),
});
export type IListRolesResponse = z.infer<typeof ListRolesResponseSchema>;

export const BulkUpdateRoleSchema = z.object({
  targetRole: z.number().positive(),
  users: z.union([z.literal("all"), z.array(z.number().positive())]),
  filters: z
    .object({
      search: z.string().optional(),
      role: z.number().positive().optional(),
    })
    .optional(),
});
export type IBulkUpdateRole = z.infer<typeof BulkUpdateRoleSchema>;
