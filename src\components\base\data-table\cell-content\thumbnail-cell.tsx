"use client";

/**
 * A cell component for displaying a stack of thumbnails with overflow indicator
 * Used for showing multiple related images in a compact format
 * Displays a "+X" indicator when there are more images than the maxDisplay limit
 */

import { cn } from "@/lib/utils";
import { Avatar, AvatarImage } from "@/components/ui/avatar";

interface ThumbnailCellProps {
  /** Array of image URLs to display */
  images: string[];
  /** Maximum number of thumbnails to show before using "+X" overflow */
  maxDisplay?: number;
  /** Optional CSS class name for styling */
  className?: string;
}

export function ThumbnailCell({ images, maxDisplay = 4, className }: ThumbnailCellProps) {
  const displayImages = images.slice(0, maxDisplay);
  const remaining = Math.max(0, images.length - maxDisplay);

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <div className="flex -space-x-3">
        {displayImages.map((image, i) => (
          <Avatar key={i} className="h-8 w-8 rounded-md border-2 border-background">
            <AvatarImage src={image} alt={`Thumbnail ${i + 1}`} />
          </Avatar>
        ))}
      </div>
      {remaining > 0 && <span className="ml-2 text-sm text-muted-foreground">+{remaining}</span>}
    </div>
  );
}
