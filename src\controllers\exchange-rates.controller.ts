import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ExchangeRateService } from '@useCases';
import { ExchangeRate } from '@entities';
import { GetExchangeRateDTO, ListExchangeRateDTO } from '@dtos';

@ApiTags('exchange-rates')
@Controller('exchange-rates')
export class ExchangeRateController {
  constructor(private readonly exchangeRateServices: ExchangeRateService) {}

  @ApiOperation({
    summary: 'Get all exchange rates for the specified date',
  })
  @Get('/all')
  async getAllByDate(
    @Query() query: ListExchangeRateDTO,
  ): Promise<{ items: ExchangeRate[]; total: number }> {
    return this.exchangeRateServices.getAllByDate(query.date, query.limit);
  }

  @ApiOperation({
    summary: 'Get an exchange rate for the specified date',
  })
  @Get('/')
  async getOneByDate(
    @Query() query: GetExchangeRateDTO,
  ): Promise<ExchangeRate> {
    return this.exchangeRateServices.getOneByDate(query.from, query.date);
  }
}
