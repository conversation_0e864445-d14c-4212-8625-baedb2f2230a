{"branches": [{"name": "main", "channel": "latest"}, {"name": "staging", "channel": "beta", "prerelease": "beta"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/github", ["@semantic-release/npm", {"npmPublish": false}], {"path": "@semantic-release/git", "assets": ["package.json", "pnpm-lock.yaml", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]"}]}