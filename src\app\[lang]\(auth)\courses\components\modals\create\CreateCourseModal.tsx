"use client";

import { MultiStepFormModal } from "@/components/base/multi-step-form-modal";
import { ModalConfig } from "@/components/base/multi-step-form-modal/types";
import { useTranslations } from "next-intl";
import { useCreateCourse } from "@/services/course";
import { useListOffers } from "@/services/product-offers";
import { createCourseValidationSchema } from "./validation";
import { useToast } from "@/hooks/use-toast";
import { useMemo } from "react";
import { ICourseOffer } from "@px-shared-account/hermes";

/**
 * Uploads an image file and returns its URL
 * @param file The image file to upload
 * @returns The URL of the uploaded image
 */
async function getImageUrl(file: File): Promise<string> {
  // TODO: Implement actual image upload logic
  // For now, return a data URL for testing
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      resolve(reader.result as string);
    };
    reader.readAsDataURL(file);
  });
}

interface CreateCourseModalProps {
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onComplete?: (data: any) => Promise<void>;
}

export const CreateCourseModal = ({ isOpen, onOpenChange, onComplete }: CreateCourseModalProps) => {
  const tc = useTranslations("courses");
  const { toast } = useToast();
  const { create } = useCreateCourse();

  // Get all offers without backend sorting (we'll sort on client side)
  const { data: offersData, isLoading: isLoadingOffers } = useListOffers();

  const validationSchema = createCourseValidationSchema(tc);

  // Create options for the MultiSelect component from the offers data
  const offerOptions = useMemo(() => {
    if (!offersData?.items) return [];

    // Sort offers by createdAt in descending order (newest first)
    const sortedOffers = [...offersData.items].sort((a, b) => {
      const dateA = new Date(a.createdAt || 0).getTime();
      const dateB = new Date(b.createdAt || 0).getTime();
      return dateB - dateA; // Descending order (newest first)
    });

    // Map offers to combobox options
    return sortedOffers.map((offer: ICourseOffer) => ({
      value: String(offer.id),
      label: offer.name,
    }));
  }, [offersData]);

  const config = useMemo<ModalConfig>(
    () => ({
      key: "create-course",
      title: tc("create.title"),
      confirmClose: true,
      confirmReset: true,
      submitButtons: [
        {
          label: tc("create.submit.create"),
          variant: "default",
          className: "bg-burgundy hover:bg-burgundy/90",
          isPrimary: true,
        },
      ],
      steps: [
        {
          id: "course-details",
          title: tc("create.title"),
          fields: [
            {
              id: "images-layout",
              type: "layout",
              label: "",
              meta: {
                layout: {
                  type: "grid",
                  className: "flex flex-col md:flex-row",
                  fields: [
                    {
                      id: "cardImage",
                      type: "image-upload",
                      label: tc("fields.card_image"),
                      required: true,
                      meta: {
                        aspectRatio: "9/16",
                        maxSize: 5,
                        acceptedTypes: [".jpeg", ".jpg", ".png"],
                        className: "aspect-[9/16] overflow-hidden",
                      },
                      validation: validationSchema.shape.card,
                    },
                    {
                      id: "right-column-layout",
                      type: "layout",
                      label: "",
                      meta: {
                        layout: {
                          type: "grid",
                          className: "flex flex-col",
                          fields: [
                            {
                              id: "bannerImage",
                              type: "image-upload",
                              label: tc("fields.cover_image"),
                              required: true,
                              meta: {
                                aspectRatio: "21/9",
                                maxSize: 5,
                                acceptedTypes: [".jpeg", ".jpg", ".png"],
                                className: "aspect-[2/1] overflow-hidden",
                              },
                              validation: validationSchema.shape.cover,
                            },
                            {
                              id: "thumbnail",
                              type: "image-upload",
                              label: tc("fields.thumbnail_image"),
                              required: true,
                              meta: {
                                aspectRatio: "1/1",
                                maxSize: 5,
                                acceptedTypes: [".jpeg", ".jpg", ".png"],
                                className: "aspect-square md:max-w-64 overflow-hidden",
                              },
                              validation: validationSchema.shape.thumbnail,
                            },
                          ],
                        },
                      },
                    },
                  ],
                },
              },
            },
            {
              id: "basic-info-layout",
              type: "layout",
              label: "",
              meta: {
                layout: {
                  type: "grid",
                  className: "grid grid-cols-1",
                  fields: [
                    {
                      id: "name",
                      type: "text",
                      label: tc("fields.name"),
                      required: true,
                      validation: validationSchema.shape.name,
                    },
                    {
                      id: "description",
                      type: "long-text",
                      label: tc("fields.description"),
                      required: true,
                      validation: validationSchema.shape.description,
                    },
                    // Add the offers field
                    {
                      id: "offers",
                      type: "combobox",
                      label: tc("fields.offers"),
                      isMulti: true,
                      options: offerOptions,
                      placeholder: tc("fields.offers_placeholder"),
                      disabled: isLoadingOffers,
                      description: tc("fields.offers_description"),
                    },
                  ],
                },
              },
            },
          ],
        },
      ],
    }),
    [tc, validationSchema.shape, offerOptions, isLoadingOffers],
  );

  const handleComplete = async (data: any) => {
    console.log("[CreateCourseModal] Starting course creation with data:", data);
    try {
      // Get the full offer objects from the offersData based on selected IDs
      const selectedOfferObjects = data.offers
        ? data.offers
            .map((id: string) => {
              // Find the full offer object from offersData
              const offer = offersData?.items?.find((item: ICourseOffer) => item.id === Number(id));

              if (offer) {
                // Return the complete offer object from the API
                return {
                  id: offer.id,
                  name: offer.name,
                };
              } else {
                // If we can't find the offer, it's better to skip it than provide invalid data
                console.warn(`Offer with ID ${id} not found in available offers`);
                return null;
              }
            })
            .filter(Boolean) // Remove any null entries
        : [];

      // Format the course data according to the API spec
      const courseData = {
        name: data.name,
        description: data.description,
        bannerImage: data.bannerImage,
        cardImage: data.cardImage,
        thumbnail: data.thumbnail,
        managers: data.managers || [],
        theme: data.theme,
        offers: selectedOfferObjects,
        lmsId: data.lmsId,
        productFamilyId: data.productFamilyId,
      };

      console.log("[CreateCourseModal] Formatted course data:", courseData);

      // Create the course using the real API
      const course = await create(courseData);
      console.log("[CreateCourseModal] Course created successfully:", course);

      // If the button data indicates PUBLISHED status, publish the course
      if (data.status === "PUBLISHED" && course.id) {
        // TODO: Call the publish endpoint
        console.log("[CreateCourseModal] Publishing course...");
      }

      toast({
        title: tc("create.success.title"),
        description: tc("create.success.description"),
        variant: "default",
      });

      // Call the onComplete callback if provided
      if (onComplete) {
        console.log("[CreateCourseModal] Calling onComplete callback");
        await onComplete(course);
        console.log("[CreateCourseModal] onComplete callback finished");
      }
    } catch (error) {
      console.error("[CreateCourseModal] Error creating course:", error);
      toast({
        title: tc("create.error.title"),
        description: error instanceof Error ? error.message : tc("create.error.description"),
        variant: "destructive",
      });
      throw error;
    }
  };

  return (
    <MultiStepFormModal
      config={config}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      events={{
        onComplete: handleComplete,
      }}
    />
  );
};
