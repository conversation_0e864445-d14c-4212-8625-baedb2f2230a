"use client";

import { useTranslations } from "next-intl";
import { ColumnDef } from "@tanstack/react-table";
import Image from "next/image";
import { ProductOfferStatus, ICourseOffer } from "@px-shared-account/hermes";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/table";
import { useCohortDetails } from "@/services/cohort";
import { useGetCoursesByOffer } from "@/services/course/client";
import { useListOffers } from "@/services/product-offers";
import { format } from "date-fns";
import { useState, useCallback, useEffect, useMemo } from "react";

interface CohortOffersProps {
  id: number;
}

interface CourseThumbnail {
  id: number;
  name: string;
  thumbnail: string;
}

function CourseThumbnailList({ courses }: { courses: CourseThumbnail[] }) {
  const displayedCourses = courses.slice(0, 4);
  const remainingCount = Math.max(0, courses.length - 4);

  return (
    <div className="flex items-center">
      <div className="flex -space-x-2">
        {displayedCourses.map((course, index) => (
          <div
            key={course.id}
            className="relative h-8 w-8 rounded-full border-2 border-background"
            style={{ zIndex: displayedCourses.length - index }}
          >
            <Image
              src={course.thumbnail || "https://placehold.co/32x32"}
              alt={course.name}
              fill
              className="rounded-full object-cover"
            />
          </div>
        ))}
      </div>
      {remainingCount > 0 && (
        <span className="ml-2 text-sm text-muted-foreground">+{remainingCount}</span>
      )}
    </div>
  );
}

// Create a proper React component to use the hook
function CourseCell({ offerId }: { offerId: number }) {
  const { data: courses } = useGetCoursesByOffer(offerId);
  const courseThumbnails =
    courses?.map((course) => ({
      id: course.id,
      name: course.name,
      thumbnail: course.thumbnail || "https://placehold.co/32x32",
    })) || [];

  return <CourseThumbnailList courses={courseThumbnails} />;
}

export function CohortOffers({ id }: CohortOffersProps) {
  const t = useTranslations("cohorts.offers");
  const tOfferStatus = useTranslations("courses.offer_status");
  const { data: cohort } = useCohortDetails(id);
  const [sortState, setSortState] = useState<{ sortBy?: string; sortOrder?: "asc" | "desc" }>({
    sortBy: "createdAt",
    sortOrder: "desc",
  });
  const { data: offersData, isLoading } = useListOffers();

  // Filter and sort offers to only show the ones associated with the cohort's course
  const courseOffers = useMemo(() => {
    if (!offersData?.items || !cohort?.course?.offers) return [];

    // Filter offers to only show the ones associated with the cohort's course
    const filteredOffers = offersData.items.filter((offer: ICourseOffer) =>
      cohort.course.offers.some((courseOffer) => courseOffer.id === offer.id),
    );

    // Apply client-side sorting
    if (sortState.sortBy === "createdAt") {
      return [...filteredOffers].sort((a, b) => {
        const dateA = new Date(a.createdAt || 0).getTime();
        const dateB = new Date(b.createdAt || 0).getTime();
        return sortState.sortOrder === "asc" ? dateA - dateB : dateB - dateA;
      });
    }

    return filteredOffers;
  }, [offersData, cohort, sortState]);

  // Handle table state changes including sorting
  const handleTableStateChange = useCallback((state: any) => {
    if (state.sortBy) {
      setSortState({
        sortBy: state.sortBy.id,
        sortOrder: state.sortBy.desc ? "desc" : "asc",
      });
    }
  }, []);

  const columns: ColumnDef<ICourseOffer>[] = [
    {
      accessorKey: "name",
      header: t("name"),
      enableSorting: true,
    },
    {
      accessorKey: "id",
      header: t("id"),
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: t("status"),
      cell: ({ row }) => {
        const status = row.original.status.toLowerCase() as Lowercase<ProductOfferStatus>;
        return <Badge>{tOfferStatus(status)}</Badge>;
      },
      enableSorting: true,
    },
    {
      id: "createdAt",
      accessorKey: "createdAt",
      header: t("created_at"),
      cell: ({ row }) => (
        <span>
          {row.original.createdAt ? format(new Date(row.original.createdAt), "MMM dd, yyyy") : "-"}
        </span>
      ),
      enableSorting: true,
    },
    {
      id: "courses",
      header: t("courses"),
      cell: ({ row }) => {
        return <CourseCell offerId={row.original.id} />;
      },
    },
  ];

  return (
    <div className="space-y-4">
      <DataTable
        columns={columns}
        data={courseOffers}
        isLoading={isLoading}
        enableSearch
        enableSorting
        useClientPagination={true}
        noResults={t("no_results")}
        totalRows={courseOffers.length}
        getRowId={(row: ICourseOffer) => String(row.id)}
        onStateChange={handleTableStateChange}
        title={`${t("title")} (${courseOffers.length})`}
      />
    </div>
  );
}
