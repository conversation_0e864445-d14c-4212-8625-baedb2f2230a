"use client";

import { <PERSON>H<PERSON>zon<PERSON>, <PERSON>cilIcon, PlusIcon, CopyIcon, TrashIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { Link } from "@/i18n/routing";
import { useRef, useState } from "react";

export interface ProgramCardProps {
  id: string;
  name: string;
  description: string;
  rating: number;
  image?: string;
  className?: string;
  onEdit?: () => void;
  onDelete?: () => void;
  onDuplicate?: () => void;
}

export function ProgramCard({
  id,
  name,
  description,
  rating,
  image = "https://images.unsplash.com/photo-*************-66273c2fd55f?q=80&w=800&auto=format&fit=crop",
  className,
  onEdit,
  onDelete,
  onDuplicate,
}: ProgramCardProps) {
  const t = useTranslations("products.programs.actions");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  const longPressTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [isLongPress, setIsLongPress] = useState(false);

  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    setIsLongPress(false);
    longPressTimer.current = setTimeout(() => {
      setIsLongPress(true);
      setIsMenuOpen(true);
    }, 500);
  };

  const handleTouchEnd = (e: React.TouchEvent<HTMLDivElement>) => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  };

  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  };

  return (
    // @ts-expect-error - Link requires locale-aware href
    <Link href={{ pathname: "/products/programs/[id]", params: { id } }}>
      <motion.div
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        whileHover={{ y: -5 }}
        transition={{ duration: 0.2 }}
        className={cn(
          "group bg-muted hover:bg-muted/80 relative aspect-[9/16] max-h-[60svh] w-full cursor-pointer overflow-hidden rounded-3xl transition-colors",
          className,
        )}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onTouchMove={handleTouchMove}
      >
        {/* Background Image */}
        <Image
          src={image}
          alt={name}
          fill
          className="object-cover transition-transform duration-200 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-black/10" />

        {/* Rating Badge */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="absolute top-4 left-4 rounded-full bg-black/20 px-2 py-1 text-sm font-medium text-white backdrop-blur-sm lg:text-base"
        >
          {rating.toFixed(1)}
        </motion.div>

        {/* More Options Menu */}
        <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              ref={menuButtonRef}
              variant="ghost"
              size="icon"
              className="absolute top-3 right-3 text-white transition-opacity hover:bg-white/20 hover:text-white md:opacity-0 md:group-hover:opacity-100"
              onClick={(e) => e.preventDefault()}
            >
              <MoreHorizontal className="h-4 w-4 lg:h-5 lg:w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={(e) => {
                e.preventDefault();
                onEdit?.();
              }}
            >
              <span className="flex items-center">
                <PencilIcon className="mr-2 h-4 w-4" />
                {t("edit")}
              </span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => e.preventDefault()}>
              <span className="flex items-center">
                <PlusIcon className="mr-2 h-4 w-4" />
                {t("add_course")}
              </span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.preventDefault();
                onDuplicate?.();
              }}
            >
              <span className="flex items-center">
                <CopyIcon className="mr-2 h-4 w-4" />
                {t("duplicate")}
              </span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.preventDefault();
                onDelete?.();
              }}
              className="text-destructive"
            >
              <span className="flex items-center">
                <TrashIcon className="mr-2 h-4 w-4" />
                {t("delete")}
              </span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="absolute right-0 bottom-0 left-0 space-y-2 p-4 lg:space-y-3 lg:p-6"
        >
          <h3 className="text-base font-semibold text-white lg:text-xl">{name}</h3>
          <p className="line-clamp-2 text-sm text-white/80 lg:text-base">{description}</p>
        </motion.div>
      </motion.div>
    </Link>
  );
}
