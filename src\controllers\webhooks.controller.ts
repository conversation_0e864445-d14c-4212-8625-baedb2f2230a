import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { UserWebhookEvent } from '@clerk/backend';
import { WebhookUseCases } from '@useCases';
import { CreateBankTransferDTO, ListWebhooksDTO } from '@dtos';
import { ClerkWebhooksGuard } from '@auth';
import { ChargeBeeWebhookPayload } from '@types';

@ApiTags('webhooks')
@Controller('webhooks')
export class WebhooksController {
  constructor(private readonly webhookUseCases: WebhookUseCases) {}

  @ApiOperation({
    summary: 'Webhook receiver for updates to discount in ChargeBee',
  })
  @Post('/chargebee')
  async processChargeBeeEvent(@Body() payload: ChargeBeeWebhookPayload) {
    return this.webhookUseCases.delegateToHandler('chargebee', {
      id: `${payload.id}`,
      chargebee: payload,
    });
  }

  @ApiOperation({
    summary: 'Webhook for receiving bank transfer updates',
  })
  @Post('/bank-transfers')
  async processBankTransferEvent(@Body() payload: CreateBankTransferDTO) {
    return this.webhookUseCases.delegateToHandler('bankTransfer', {
      id: `BANK_TRANSFER-${Date.now()}`,
      bankTransfer: payload,
    });
  }

  @UseGuards(ClerkWebhooksGuard)
  @Post('/clerk')
  async processClerkWebhook(
    @Body()
    eventData: UserWebhookEvent,
  ) {
    return this.webhookUseCases.delegateToHandler('clerk', {
      id: `CLERK-${eventData.type}-${eventData?.data?.id}`,
      clerk: eventData,
    });
  }

  @ApiOperation({
    summary: 'List Webhooks',
  })
  @Get('/')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  )
  async list(@Query() query: ListWebhooksDTO) {
    return this.webhookUseCases.getAllBy(
      {
        sender: query?.sender,
        status: query?.status,
      },
      query?.limit,
      query?.page,
    );
  }

  @ApiOperation({
    summary: 'Get a webhook',
  })
  @Get('/:id')
  async getOne(@Param('id') id: string) {
    return this.webhookUseCases.getOne(id);
  }
}
