import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Coupon } from 'chargebee-typescript/lib/resources/coupon';
import {
  UpdateResultWithItemInfo,
  DiscountConstraint,
  PXActionResult,
} from '@types';
import { ChargebeeBillingService } from '@services/billing';
import { Discount, ProductPlanPrice } from '@entities';
import { FindOptionsWhere, Repository } from 'typeorm';
import { DiscountFactoryService } from '.';
import { IDataServices } from '@abstracts';
import { DiscountStatus } from '@enums';

@Injectable()
export class DiscountUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly discountFactory: DiscountFactoryService,
    private readonly chargebeeService: ChargebeeBillingService,
  ) {}

  /**
   * Creates a new discount in database
   * @param discount Discount entity
   * @returns Discount record from database
   */
  async create(discount: Discount): Promise<Discount> {
    const createdDiscount = await this.dataServices.discount.create(discount);
    await this.chargebeeService.createCoupon(createdDiscount);
    return createdDiscount;
  }

  /**
   * Retrieves a `Discount` matching provided `id``
   * @param id `id` of `Discount` to fetch
   */
  async getOne(id: number): Promise<Discount> {
    return this.dataServices.discount.getOneBy({
      id,
    });
  }

  /**
   * Returns all discounts
   * @returns Discount records from database
   */
  async getAll(
    status: DiscountStatus,
    limit: number,
    page: number,
  ): Promise<{ items: Discount[]; total: number }> {
    const filters: FindOptionsWhere<Discount> = {};

    if (status) {
      filters.status = status;
    }

    return this.dataServices.discount.getAllBy(filters, limit, page);
  }

  /**
   * Updates the discount specified by the `id`
   * @param id `id` of the `Discount` to update
   * @param updates Updates for the `Discount`
   */
  async update(
    id: number,
    updates: Partial<Discount>,
  ): Promise<UpdateResultWithItemInfo<Discount>> {
    const updatedResult = await this.dataServices.discount.updateAndReturnItem(
      'id',
      id.toString(),
      updates,
    );

    if (updatedResult?.updatedItem) {
      await this.chargebeeService.updateCoupon(
        updatedResult?.updatedItem.code,
        updates,
      );
    }

    return updatedResult;
  }

  /**
   * Attaches a discount to items
   * @param id `id` of the discount
   * @param attachedTo Items to which this discount is going to be attached
   */
  async attachToItems(
    id: number,
    attachedTo: DiscountConstraint[],
  ): Promise<any> {
    const discountsRepository = this.dataServices.discount.getRepository();
    const discount = await this.dataServices.discount.getOneWithRelations(
      'discounts',
      'id',
      id,
    );
    if (discount) {
      const pricesAttachedTo = attachedTo.find(
        (constraint) => constraint.itemType === 'plan',
      );
      if (pricesAttachedTo) {
        return this.attachToPrices(
          discount,
          discountsRepository,
          pricesAttachedTo,
        );
      }
    }
    throw new NotFoundException({
      error: `Discount not found`,
      discountId: id,
    });
  }

  /**
   * Attaches the discount to all items where items can be `ProductPlanPrices` or `Offers`
   * @param discount Discount to which items need to be attached
   * @param repository Discount repository
   * @param attachedTo `DiscountConstraints` containing items which are to be attached to the Discount
   * @returns Updated `Discount` entity
   */
  async attachToAllItems(
    discount: Discount,
    repository: Repository<Discount>,
    attachedTo: DiscountConstraint,
  ): Promise<Discount> {
    let updateResult: Discount;
    switch (attachedTo.itemType) {
      case 'plan':
        const allPrices = await this.dataServices.productPlanPrice.getAll();
        discount.pricesAttachedTo = allPrices.items;
        discount.attachedTo = [
          {
            itemType: 'plan',
            itemIds: ['all'],
          },
        ];
        updateResult = await repository.save(discount);
        break;
      default:
        throw new HttpException(
          `Invalid item type to attach discount to: ${attachedTo.itemType}`,
          HttpStatus.BAD_REQUEST,
        );
    }
    await this.chargebeeService.updateCouponConstraints(discount.code, [
      {
        itemIds: ['all'],
        itemType: attachedTo.itemType,
      },
    ]);
    return updateResult;
  }

  /**
   * Detaches the discount from all items where items can be `ProductPlanPrices` or `Offers`
   * @param discount Discount from which items need to be detached
   * @param repository Discount repository
   * @param attachedTo `DiscountConstraints` containing items which are to be detached from the Discount
   * @returns Updated `Discount` entity
   */
  async detachFromAllItems(
    discount: Discount,
    repository: Repository<Discount>,
    attachedTo: DiscountConstraint,
  ): Promise<Discount> {
    let updateResult: Discount;

    switch (attachedTo.itemType) {
      case 'plan':
        discount.pricesAttachedTo = [];
        discount.attachedTo = [
          {
            itemType: 'plan',
            itemIds: ['none'],
          },
        ];
        updateResult = await repository.save(discount);
        break;
      default:
        throw new HttpException(
          `Invalid item type to attach discount to: ${attachedTo.itemType}`,
          HttpStatus.BAD_REQUEST,
        );
    }
    await this.chargebeeService.updateCouponConstraints(discount.code, [
      {
        itemIds: ['none'],
        itemType: attachedTo.itemType,
      },
    ]);
    return updateResult;
  }

  /**
   * Attaches the discount to specific items where items can be `ProductPlanPrices` or `Offers`
   * @param discount Discount to which items need to be attached
   * @param repository Discount repository
   * @param attachedTo `DiscountConstraints` containing items which are to be attached to the Discount
   * @returns Updated `Discount` entity
   */
  async attachToSpecificItems(
    discount: Discount,
    repository: Repository<Discount>,
    attachedTo: DiscountConstraint,
  ): Promise<Discount> {
    let updateResult: Discount;
    const chargebeeConstraints: DiscountConstraint[] = [];
    switch (attachedTo.itemType) {
      case 'plan':
        const newPrices: ProductPlanPrice[] = [];
        for (const itemId of attachedTo.itemIds) {
          const price = await this.dataServices.productPlanPrice.getOneBy({
            chargebeeId: itemId,
          });
          newPrices.push(price);
          discount.pricesAttachedTo = newPrices;
          discount.attachedTo = [
            {
              itemType: 'plan',
              itemIds: ['specific'],
            },
          ];
          updateResult = await repository.save(discount);
        }
        chargebeeConstraints.push(attachedTo);
    }
    await this.chargebeeService.updateCouponConstraints(
      discount.code,
      chargebeeConstraints,
    );
    return updateResult;
  }

  /**
   * Handles attachment of the discount to `ProductPlanPrice`
   * @param discount Discount to which `ProductPlanPrice` items need to be attached
   * @param repository Discount repository
   * @param attachedTo `DiscountConstraints` containing `ProductPlanPrice`s which are to be attached to the Discount
   * @returns Updated `Discount` entity
   */
  async attachToPrices(
    discount: Discount,
    repository: Repository<Discount>,
    attachedTo: DiscountConstraint,
  ): Promise<Discount> {
    if (attachedTo.itemIds[0] === 'all') {
      return this.attachToAllItems(discount, repository, attachedTo);
    }
    if (attachedTo.itemIds[0] === 'none') {
      return this.detachFromAllItems(discount, repository, attachedTo);
    }
    if (attachedTo.itemIds.length >= 1) {
      return this.attachToSpecificItems(discount, repository, attachedTo);
    }

    throw new HttpException(
      `Invalid discount constraints: ${attachedTo}`,
      HttpStatus.BAD_REQUEST,
    );
  }

  async searchAll(
    name?: string,
    lineId?: number,
    familyId?: number,
    productId?: number,
    planId?: number,
    status?: DiscountStatus,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: Discount[]; total: number }> {
    const queryBuilder = this.dataServices.discount
      .getRepository()
      .createQueryBuilder('discount');
    queryBuilder
      .leftJoinAndSelect('discount.pricesAttachedTo', 'price')
      .leftJoin('price.productPlan', 'plan')
      .leftJoin('plan.product', 'product')
      .leftJoin('product.productFamily', 'family')
      .leftJoin('family.productLine', 'line');

    if (lineId) {
      queryBuilder.where('line.id = :lineId', { lineId });
    }

    if (familyId) {
      queryBuilder.andWhere('family.id = :familyId', { familyId });
    }

    if (productId) {
      queryBuilder.andWhere('product.id = :productId', { productId });
    }

    if (planId) {
      queryBuilder.andWhere('plan.id = :planId', { planId });
    }

    if (name) {
      queryBuilder.andWhere('discount.name ILIKE :name', { name: `%${name}%` });
    }

    if (status) {
      queryBuilder.andWhere('discount.status = :status', { status });
    }

    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.orderBy('discount.createdAt', orderBy || 'DESC');

    const [items, total] = await queryBuilder.getManyAndCount();
    return {
      items,
      total,
    };
  }

  async syncDiscountWithChargeBee(
    chargebeeCouponUpdate: Coupon,
  ): Promise<PXActionResult> {
    const updatedDiscount =
      this.discountFactory.generateUpdateFromChargeBeeEvent(
        chargebeeCouponUpdate,
      );
    const discountCode = updatedDiscount.code;
    const result = await this.dataServices.discount.updateAndReturnItem(
      'code',
      discountCode,
      {
        status: updatedDiscount.status,
        maxRedemptions: updatedDiscount.maxRedemptions,
      },
    );

    if (!result.success) {
      return {
        data: { id: discountCode },
        success: false,
        message: `Discount: ${updatedDiscount.code} not updated`,
      };
    }
    return {
      data: { id: discountCode },
      success: true,
      message: `Discount: ${updatedDiscount.code} updated successfully`,
    };
  }

  /**
   * Disables a discount
   * @param id `id` of the discount to disable
   * @returns A promise that resolves to the disabled discount
   */
  async disable(id: number): Promise<any> {
    const discount = await this.getOne(id);
    if (!discount) {
      throw new HttpException(
        `Discount not found with ID: ${id}`,
        HttpStatus.NOT_FOUND,
      );
    }
    if (discount.status === DiscountStatus.Active) {
      await this.chargebeeService.deleteCoupon(discount.code);
    }
    return this.dataServices.discount.updateAndReturnItem('id', id.toString(), {
      status: DiscountStatus.Disabled,
    });
  }
}
