import {
  IRoleBase,
  IListRolesResponse,
  ICreateRole,
  IUpdateRole,
  UpdateResultWithItemInfo,
  IPaginationParams,
  RoleGroup,
  PXActionResult,
  Roles,
  IFilterRole,
} from "@px-shared-account/hermes";

/**
 * Type for role list parameters
 */
export type ListRolesParams = IPaginationParams &
  IFilterRole & {
    search?: string;
  };

/**
 * Core API endpoints for role operations
 * These are the base paths and methods used by both client and server implementations
 */
export const ROLE_API_ENDPOINTS = {
  /**
   * List roles endpoint
   * @param params Query parameters for filtering and pagination
   * @returns Formatted URL with query parameters
   */
  list: (params: ListRolesParams = {}) => {
    const { group, page = 1, limit = 10, sortBy, sortOrder, search } = params;
    const queryParams = new URLSearchParams();

    // Add params that are supported by the backend
    if (group) queryParams.append("group", group);
    if (search) queryParams.append("search", search);
    if (sortBy) queryParams.append("sortBy", sortBy);
    if (sortOrder) queryParams.append("sortOrder", sortOrder);
    queryParams.append("page", String(page));
    queryParams.append("limit", String(limit));

    return {
      url: `/role?${queryParams.toString()}`,
      method: "GET",
    };
  },

  /**
   * Get role by ID endpoint
   * @param id Role ID
   * @returns Endpoint configuration
   */
  getById: (id: number) => ({
    url: `/role/${id}`,
    method: "GET",
  }),

  /**
   * Create role endpoint
   * @returns Endpoint configuration
   */
  create: () => ({
    url: "/role",
    method: "POST",
  }),

  /**
   * Update role endpoint
   * @param id Role ID
   * @returns Endpoint configuration
   */
  update: (id: number) => ({
    url: `/role/${id}`,
    method: "PATCH",
  }),

  /**
   * Delete role endpoint
   * @param id Role ID
   * @returns Endpoint configuration
   */
  delete: (id: number) => ({
    url: `/role/${id}`,
    method: "DELETE",
  }),
};

/**
 * Type definitions for API responses
 */
export type RoleApiTypes = {
  list: IListRolesResponse;
  getById: IRoleBase;
  create: IRoleBase;
  update: UpdateResultWithItemInfo<IRoleBase>;
  delete: PXActionResult;
};

/**
 * Type definitions for API request payloads
 */
export type RoleApiPayloads = {
  create: ICreateRole;
  update: IUpdateRole;
};
