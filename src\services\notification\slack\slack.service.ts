import { ChatPostMessageResponse, WebClient } from '@slack/web-api';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@config';

@Injectable()
export class SlackService {
  private readonly slackApp: WebClient;
  constructor(private readonly configService: ConfigService) {
    const notificationSecrets = this.configService.notificationSecrets;
    this.slackApp = new WebClient(notificationSecrets.SLACK_TOKEN);
  }

  /**
   * Sends a Slack message using `Blocks`
   * @param message Message to be sent
   * @param channelId Slack channel ID to send the message to
   * @param thread thread id, Needed for sending the message as a response to a thread
   */
  sendMessage(
    message: string,
    channelId: string,
    thread?: string,
  ): Promise<ChatPostMessageResponse> {
    try {
      return this.slackApp.chat.postMessage({
        channel: channelId,
        text: message,
        thread_ts: thread || undefined,
      });
    } catch (err) {
      console.error(err);
    }
  }

  /**
   * Sends a Slack message with an attachment
   * @param channelId Slack channel ID to send the message to
   * @param message Message to be sent
   * @param attachmentPath Path to the attachment file
   * @param attachmentName Name of the attachment
   * @param attachmentType Type of the attachment
   */
  async sendMessageWithAttachment(
    channelId: string,
    message: string,
    attachmentPath: string,
    attachmentName: string,
    attachmentType = 'auto',
  ): Promise<any> {
    try {
      return this.slackApp.filesUploadV2({
        channel_id: channelId,
        initial_comment: message,
        file: attachmentPath,
        filetype: attachmentType,
        filename: attachmentName,
      });
    } catch (err) {
      console.error(err);
    }
  }

  /**
   * Generates markdown message content for the Slack message
   * sent for failed overdue invoice tasks
   * @param data Data to include in message
   */
  getFailedOverdueInvoiceNotification(data: any): string {
    return (
      'Failed to process an overdue invoice\n\n' +
      '*Subscription ID:* ' +
      data?.subscriptionId +
      '\n*Overdue Invoice ID:* ' +
      data?.invoiceId +
      '\n*Invoice Chargebee link:* ' +
      `https://paradox-institute-test.chargebee.com/d/invoices/${data?.invoiceId}` +
      '\n*Subscription marked overdue in PX OS?:* ' +
      data?.subscriptionUpdated +
      '\n*Warning/Error:* ' +
      data?.message +
      '\n'
    );
  }
}
