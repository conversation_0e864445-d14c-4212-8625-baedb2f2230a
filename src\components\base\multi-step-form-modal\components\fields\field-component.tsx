"use client";

import { useFormContext } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FieldConfig,
  SelectField,
  DataTableField as DataTableFieldType,
  LayoutField as LayoutFieldType,
  ComboboxField as ComboboxFieldType,
  TreeSelectField as TreeSelectFieldType,
} from "../../types";
import { AccordionSelect } from "./accordion-select";
import { DataTableField } from "./data-table";
import { LayoutField } from "./layout";
import { ImageUploadField } from "./image-upload-field";
import PermissionsEditor from "@/app/[lang]/(auth)/user-management/roles/components/PermissionsEditor";
import { cn } from "@/lib/utils";
import { DateField } from "./date";
import { ComboboxField } from "./combobox";
import { TreeSelectField } from "./tree-select";
import { Textarea } from "@/components/ui/textarea";

export const FieldComponent = ({ field }: { field: FieldConfig }) => {
  const {
    register,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();

  const value = watch(field.id);
  const error = errors[field.id];

  const renderError = () => {
    if (!error) return null;
    return error.message?.toString();
  };

  // Check field dependencies
  const shouldRender =
    field.dependencies?.every((dep) => {
      const depValue = watch(dep.field);
      return dep.condition(depValue);
    }) ?? true;

  if (!shouldRender) return null;

  const renderField = () => {
    switch (field.type) {
      case "text":
        return (
          <Input
            {...register(field.id, {
              required: field.required,
              onChange: (e) => field.onChange?.(e.target.value),
            })}
            placeholder={field.placeholder}
            disabled={field.disabled}
            aria-invalid={!!error}
            className={error ? "border-destructive" : ""}
          />
        );
      case "date":
        return <DateField field={field} />;
      case "number":
        return (
          <Input
            {...register(field.id, {
              required: field.required,
              valueAsNumber: true,
              onChange: (e) => field.onChange?.(parseFloat(e.target.value)),
            })}
            type="number"
            placeholder={field.placeholder}
            disabled={field.disabled}
            aria-invalid={!!error}
            className={error ? "border-destructive" : ""}
          />
        );
      case "select":
        const selectField = field as SelectField;
        return (
          <Select
            {...register(field.id)}
            value={value?.toString() || ""}
            onValueChange={(val) => {
              setValue(field.id, val, { shouldValidate: true });
              field.onChange?.(val);
            }}
            disabled={field.disabled}
          >
            <SelectTrigger className={error ? "border-destructive" : ""}>
              <SelectValue placeholder={field.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {selectField.options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case "combobox":
        return <ComboboxField field={field as ComboboxFieldType} />;
      case "switch":
        return (
          <Switch
            {...register(field.id)}
            checked={value || false}
            onCheckedChange={(checked) => {
              setValue(field.id, checked, { shouldValidate: true });
              field.onChange?.(checked);
            }}
            disabled={field.disabled}
          />
        );
      case "accordion-select":
        return <AccordionSelect field={field} />;
      case "data-table":
        return <DataTableField field={field as DataTableFieldType} />;
      case "layout":
        return <LayoutField field={field as LayoutFieldType} />;
      case "permissions":
        return (
          <PermissionsEditor
            onChange={(permissions) => {
              field.onChange?.(permissions as string[]);
            }}
          />
        );
      case "image-upload":
        return <ImageUploadField field={field} error={error?.message?.toString()} />;
      case "tree-select":
        return <TreeSelectField field={field as TreeSelectFieldType} />;
      case "long-text":
        return (
          <Textarea
            {...register(field.id, {
              required: field.required,
              onChange: (e) => field.onChange?.(e.target.value),
            })}
            placeholder={field.placeholder}
            disabled={field.disabled}
            aria-invalid={!!error}
            className={cn(error ? "border-destructive" : "", field.meta?.className)}
            rows={field.rows || 3}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div
      className={cn("flex w-full flex-col overflow-x-auto", field.type !== "layout" && "gap-2 p-2")}
    >
      <div>
        <Label htmlFor={field.id} className={error ? "text-destructive" : ""}>
          {field.label}
          {field.required && <span className="text-destructive">*</span>}
        </Label>
        {field.description && <p className="text-xs text-muted-foreground">{field.description}</p>}
      </div>
      {renderField()}
      {error && <p className="text-sm font-medium text-destructive">{renderError()}</p>}
    </div>
  );
};
