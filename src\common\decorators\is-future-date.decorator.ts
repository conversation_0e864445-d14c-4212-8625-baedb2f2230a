import {
    registerDecorator,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ async: false })
export class IsFutureDateConstraint implements ValidatorConstraintInterface {
    validate(dateString: string) {
        if (typeof dateString !== 'string') {
            return false;
        }
        const scheduledDate = new Date(dateString);
        if (isNaN(scheduledDate.getTime())) {
            return true;
        }
        return scheduledDate.getTime() > new Date().getTime();
    }

    defaultMessage() {
        return 'Scheduling time must be in the future';
    }
}

export function IsFutureDate(validationOptions?: ValidationOptions) {
    return function (object: object, propertyName: string) {
        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: IsFutureDateConstraint,
        });
    };
} 