"use client";

/**
 * A cell component for displaying text or numeric values with alignment options
 * Used for basic text display in the DataTable
 */

interface TextCellProps {
  /** The text or numeric value to display */
  value: string | number;
  /** Text alignment within the cell */
  align?: "left" | "center" | "right";
}

export function TextCell({ value, align = "left" }: TextCellProps) {
  return (
    <span className="truncate" style={{ textAlign: align }}>
      {String(value)}
    </span>
  );
}
