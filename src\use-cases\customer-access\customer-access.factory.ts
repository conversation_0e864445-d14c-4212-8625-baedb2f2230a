import { Injectable } from '@nestjs/common';
import { CustomerAccess } from '@entities';
import { CustomerAccessType } from '@enums';
import { AccessDetails } from '@types';
import { CustomerAccessStatus } from '@px-shared-account/hermes';

@Injectable()
export class CustomerAccessFactory {
  /**
   * Generates a customer access entity
   * @param accessInfo Info about the customer's access
   * @returns A customer access entity
   */
  generate(
    customerId: number,
    subscriptionId: number,
    offerId: number,
    status: CustomerAccessStatus = CustomerAccessStatus.PENDING,
    type: CustomerAccessType,
    details: AccessDetails,
  ): CustomerAccess {
    const customerAccess = new CustomerAccess();
    customerAccess.customerId = customerId;
    customerAccess.type = type;
    customerAccess.status = status;
    customerAccess.details = details;
    customerAccess.subscriptionId = subscriptionId;
    customerAccess.offerId = offerId;
    customerAccess.details = details;
    return customerAccess;
  }
}
