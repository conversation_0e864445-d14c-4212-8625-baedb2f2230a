import { auth } from "@clerk/nextjs/server";
import serverFetcher from "@/lib/server/server-fetcher";
import { USER_API_ENDPOINTS, UserApiTypes, ListUsersParams, UserApiPayloads } from "./core";

/**
 * Creates a server-side token getter for the fetcher
 * @returns Promise with authentication token or null
 */
const getServerToken = async () => {
  const session = await auth();
  return session?.getToken() || null;
};

// Initialize the fetcher with server-side token getter
const apiFetcher = serverFetcher(getServerToken);

/**
 * Server-side function for fetching user list
 * @param params Filter and pagination parameters
 * @returns Promise with user list data
 */
export async function getUserList(params: ListUsersParams = {}): Promise<UserApiTypes["list"]> {
  const { url } = USER_API_ENDPOINTS.list(params);
  return apiFetcher(url);
}

/**
 * Server-side function for fetching a specific user by ID
 * @param id User ID to fetch
 * @returns Promise with user data
 */
export async function getUserById(id: number): Promise<UserApiTypes["getById"]> {
  const { url } = USER_API_ENDPOINTS.getById(id);
  return apiFetcher(url);
}

/**
 * Server-side function for fetching a specific user by SSO ID
 * @param id User SSO ID to fetch
 * @returns Promise with user data
 */
export async function getUserBySSOId(id: string): Promise<UserApiTypes["getBySSOId"]> {
  const { url } = USER_API_ENDPOINTS.getBySSOId(id);
  return apiFetcher(url);
}
/**
 * Alias for getUserById to maintain consistency with other services
 * @param id User ID to fetch
 * @returns Promise with user data
 */
export const getUserDetails = getUserById;

/**
 * Server-side function for creating a new user
 * @param data User creation data
 * @returns Promise with created user data
 */
export async function createUser(data: UserApiPayloads["create"]): Promise<UserApiTypes["create"]> {
  const { url, method } = USER_API_ENDPOINTS.create();
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for updating an existing user
 * @param id User ID to update
 * @param data User update data
 * @returns Promise with update result
 */
export async function updateUser(
  id: number,
  data: UserApiPayloads["update"],
): Promise<UserApiTypes["update"]> {
  const { url, method } = USER_API_ENDPOINTS.update(id);
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for bulk updating users
 * @param data Bulk update data including target role and filters
 * @returns Promise with update result
 */
export async function bulkUpdateUsers(
  data: UserApiPayloads["bulkUpdate"],
): Promise<UserApiTypes["bulkUpdate"]> {
  const { url, method } = USER_API_ENDPOINTS.bulkUpdate();
  return apiFetcher(url, {
    method,
    body: data,
  });
}
