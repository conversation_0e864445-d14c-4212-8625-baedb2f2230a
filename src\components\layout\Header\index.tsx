"use client";

import { SignedIn, SignedOut, SignIn<PERSON><PERSON>on, UserButton, useUser } from "@clerk/nextjs";
import { Bell } from "lucide-react";
import { useEffect, useState } from "react";

import { Button } from "@/components/base/button";
import { Input } from "@/components/base/input";
import { Badge } from "@/components/base/badge";
import { cn } from "@/lib/utils";
import LocaleSwitcher from "@/components/base/LocaleSwitcher";
import { BaseProps } from "@/types";

export interface HeaderProps extends BaseProps {
  title?: string;
  notificationCount?: number;
}

export const Header = ({ className, title, notificationCount = 0 }: HeaderProps) => {
  const { user } = useUser();
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle header transparency on scroll
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <header
      className={cn(
        "sticky top-0 z-40 hidden w-full transition-colors duration-100 md:block",
        // Desktop: Transparent by default, white when scrolled
        "bg-transparent",
        isScrolled && "bg-background",
        className,
      )}
    >
      {/* Desktop Layout */}
      <div className="hidden h-16 items-center gap-6 px-6 md:flex md:justify-end">
        <div className="hidden flex-1">
          <Input type="search" placeholder="Rechercher..." className="max-w-md" />
        </div>
        <div className="flex items-center gap-4">
          <LocaleSwitcher />
          <SignedOut>
            <SignInButton />
          </SignedOut>
        </div>
      </div>

      {/* Mobile Layout */}

      {/* Page Title (Desktop) */}
      {title && (
        <div className="hidden px-6 py-4 md:block">
          <h1 className="text-2xl font-semibold">{title}</h1>
        </div>
      )}
    </header>
  );
};

Header.displayName = "Header";
