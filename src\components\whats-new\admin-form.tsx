import {
  ICreateWhatsNewPayload,
  IThumbnailImage,
  IUpdateWhatsNewPayload,
  IWhatsNewVersion,
} from "@px-shared-account/hermes";
import { X, ArrowLeft, ArrowRight, Save } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Button } from "@/components/base/button";
import { DatePicker } from "@/components/base/date-picker";
import { Input } from "@/components/base/input";
import { Textarea } from "@/components/ui/textarea";
import { ImageUpload } from "./image-upload";
import ProgressPills from "./progress-pills";
import FeatureInput from "./feature-input";
import ChangelogEntry from "./changelog-entry";

interface FormData {
  titleEn: string;
  titleFr: string;
  descriptionEn: string;
  descriptionFr: string;
  versionCode: string;
  date: string;
  addedEn: string[];
  addedFr: string[];
  changedEn: string[];
  changedFr: string[];
  fixedEn: string[];
  fixedFr: string[];
  removedEn: string[];
  removedFr: string[];
  thumbnails: IThumbnailImage[];
}

interface AdminFormProps {
  onSubmit: {
    create: (data: ICreateWhatsNewPayload) => Promise<void>;
    update: (data: IUpdateWhatsNewPayload) => Promise<void>;
  };
  onCancel: () => void;
  initialData?: IWhatsNewVersion;
  isEditing?: boolean;
}

export default function AdminForm({ onSubmit, onCancel, initialData, isEditing }: AdminFormProps) {
  const t = useTranslations();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    titleEn: initialData?.titleEn || "",
    titleFr: initialData?.titleFr || "",
    descriptionEn: initialData?.descriptionEn || "",
    descriptionFr: initialData?.descriptionFr || "",
    versionCode: initialData?.versionCode || "",
    date: initialData?.date
      ? new Date(initialData.date).toISOString().split("T")[0]
      : new Date().toISOString().split("T")[0],
    addedEn: initialData?.addedEn || [],
    addedFr: initialData?.addedFr || [],
    changedEn: initialData?.changedEn || [],
    changedFr: initialData?.changedFr || [],
    fixedEn: initialData?.fixedEn || [],
    fixedFr: initialData?.fixedFr || [],
    removedEn: initialData?.removedEn || [],
    removedFr: initialData?.removedFr || [],
    thumbnails: initialData?.thumbnails || [],
  });

  const nextStep = (e: React.MouseEvent) => {
    // Prevent any form submission
    e.preventDefault();
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Only proceed with form submission if we're on the final step (3)
    // AND this is an actual form submission (not a Next button click)
    const submitter = (e.nativeEvent as SubmitEvent).submitter as HTMLButtonElement;
    if (currentStep !== 3 || submitter?.textContent?.includes("Next")) {
      return;
    }

    setIsSubmitting(true);

    const basePayload = {
      titleEn: formData.titleEn,
      titleFr: formData.titleFr,
      descriptionEn: formData.descriptionEn,
      descriptionFr: formData.descriptionFr,
      versionCode: formData.versionCode,
      date: formData.date,
      addedEn: formData.addedEn,
      addedFr: formData.addedFr,
      changedEn: formData.changedEn,
      changedFr: formData.changedFr,
      fixedEn: formData.fixedEn,
      fixedFr: formData.fixedFr,
      removedEn: formData.removedEn,
      removedFr: formData.removedFr,
      thumbnails: formData.thumbnails,
    };

    try {
      if (isEditing && initialData?.id) {
        await onSubmit.update({
          id: initialData.id,
          ...basePayload,
        });
      } else {
        await onSubmit.create(basePayload);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div>
              <label htmlFor="versionCode" className="text-muted-foreground mb-2 block text-sm">
                {t("whats-new.admin.version-code")}
              </label>
              <Input
                id="versionCode"
                name="versionCode"
                value={formData.versionCode}
                onChange={(e) => setFormData({ ...formData, versionCode: e.target.value })}
                autoFocus
              />
            </div>

            <div>
              <label htmlFor="date" className="text-muted-foreground mb-2 block text-sm">
                {t("whats-new.admin.date")}
              </label>
              <DatePicker
                value={new Date(formData.date)}
                onChange={(date) =>
                  setFormData({
                    ...formData,
                    date: date ? date.toISOString() : new Date().toISOString(),
                  })
                }
                disablePastDates={false}
              />
            </div>

            <div>
              <label className="text-muted-foreground mb-2 block text-sm">
                {t("whats-new.admin.fields.thumbnails")}
              </label>
              <ImageUpload
                thumbnails={formData.thumbnails}
                onThumbnailsChange={(thumbnails) => setFormData({ ...formData, thumbnails })}
              />
            </div>
          </div>
        );
      case 1:
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <label className="text-muted-foreground mb-2 block text-sm">
                {t("whats-new.admin.fields.title-en")} *
              </label>
              <Input
                placeholder={t("whats-new.admin.fields.title-en-placeholder")}
                value={formData.titleEn}
                onChange={(e) => setFormData({ ...formData, titleEn: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-muted-foreground mb-2 block text-sm">
                {t("whats-new.admin.fields.description-en")}
              </label>
              <Textarea
                placeholder={t("whats-new.admin.fields.description-en-placeholder")}
                value={formData.descriptionEn}
                onChange={(e) => setFormData({ ...formData, descriptionEn: e.target.value })}
                className="min-h-[100px]"
              />
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium">
                  <div className="h-2 w-2 rounded-full bg-green-500" />
                  {t("whats-new.admin.features.added")}
                </label>
                <FeatureInput
                  features={formData.addedEn}
                  onChange={(features) => setFormData({ ...formData, addedEn: features })}
                  placeholder={t("whats-new.admin.features.placeholder")}
                  type="added"
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium">
                  <div className="h-2 w-2 rounded-full bg-blue-500" />
                  {t("whats-new.admin.features.changed")}
                </label>
                <FeatureInput
                  features={formData.changedEn}
                  onChange={(features) => setFormData({ ...formData, changedEn: features })}
                  placeholder={t("whats-new.admin.features.placeholder")}
                  type="changed"
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium">
                  <div className="h-2 w-2 rounded-full bg-yellow-500" />
                  {t("whats-new.admin.features.fixed")}
                </label>
                <FeatureInput
                  features={formData.fixedEn}
                  onChange={(features) => setFormData({ ...formData, fixedEn: features })}
                  placeholder={t("whats-new.admin.features.placeholder")}
                  type="fixed"
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium">
                  <div className="h-2 w-2 rounded-full bg-red-500" />
                  {t("whats-new.admin.features.removed")}
                </label>
                <FeatureInput
                  features={formData.removedEn}
                  onChange={(features) => setFormData({ ...formData, removedEn: features })}
                  placeholder={t("whats-new.admin.features.placeholder")}
                  type="removed"
                />
              </div>
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <label className="text-muted-foreground mb-2 block text-sm">
                {t("whats-new.admin.fields.title-fr")} *
              </label>
              <Input
                placeholder={formData.titleEn}
                value={formData.titleFr}
                onChange={(e) => setFormData({ ...formData, titleFr: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-muted-foreground mb-2 block text-sm">
                {t("whats-new.admin.fields.description-fr")}
              </label>
              <Textarea
                placeholder={formData.descriptionEn}
                value={formData.descriptionFr}
                onChange={(e) => setFormData({ ...formData, descriptionFr: e.target.value })}
                className="min-h-[100px]"
              />
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium">
                  <div className="h-2 w-2 rounded-full bg-green-500" />
                  {t("whats-new.admin.features.added-fr")}
                </label>
                <FeatureInput
                  features={formData.addedFr}
                  onChange={(features) => setFormData({ ...formData, addedFr: features })}
                  placeholder={t("whats-new.admin.features.placeholder")}
                  type="added"
                  ghostItems={formData.addedEn}
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium">
                  <div className="h-2 w-2 rounded-full bg-blue-500" />
                  {t("whats-new.admin.features.changed-fr")}
                </label>
                <FeatureInput
                  features={formData.changedFr}
                  onChange={(features) => setFormData({ ...formData, changedFr: features })}
                  placeholder={t("whats-new.admin.features.placeholder")}
                  type="changed"
                  ghostItems={formData.changedEn}
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium">
                  <div className="h-2 w-2 rounded-full bg-yellow-500" />
                  {t("whats-new.admin.features.fixed-fr")}
                </label>
                <FeatureInput
                  features={formData.fixedFr}
                  onChange={(features) => setFormData({ ...formData, fixedFr: features })}
                  placeholder={t("whats-new.admin.features.placeholder")}
                  type="fixed"
                  ghostItems={formData.fixedEn}
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium">
                  <div className="h-2 w-2 rounded-full bg-red-500" />
                  {t("whats-new.admin.features.removed-fr")}
                </label>
                <FeatureInput
                  features={formData.removedFr}
                  onChange={(features) => setFormData({ ...formData, removedFr: features })}
                  placeholder={t("whats-new.admin.features.placeholder")}
                  type="removed"
                  ghostItems={formData.removedEn}
                />
              </div>
            </div>
          </div>
        );
      case 3:
        return (
          <div className="space-y-6">
            <div className="border-secondary-foreground bg-secondary/80 rounded-lg border p-6">
              <ChangelogEntry
                version={{
                  id: initialData?.id || 0,
                  versionCode: formData.versionCode,
                  date: formData.date,
                  title: formData.titleEn,
                  description: formData.descriptionEn,
                  added: formData.addedEn,
                  changed: formData.changedEn,
                  fixed: formData.fixedEn,
                  removed: formData.removedEn,
                  thumbnails: formData.thumbnails,
                }}
                isAdmin={false}
              />
            </div>
            <div className="border-secondary-foreground bg-secondary/80 rounded-lg border p-6">
              <ChangelogEntry
                version={{
                  id: initialData?.id || 0,
                  versionCode: formData.versionCode,
                  date: formData.date,
                  title: formData.titleFr,
                  description: formData.descriptionFr,
                  added: formData.addedFr,
                  changed: formData.changedFr,
                  fixed: formData.fixedFr,
                  removed: formData.removedFr,
                  thumbnails: formData.thumbnails,
                }}
                isAdmin={false}
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="border-secondary-foreground bg-secondary/80 mb-8 space-y-6 rounded-lg border p-8"
    >
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">
          {isEditing ? t("whats-new.admin.edit-version") : t("whats-new.admin.add-version")}
        </h2>
        <p className="text-muted-foreground text-sm">{t("whats-new.admin.required-fields")}</p>
      </div>

      <ProgressPills currentStep={currentStep} />

      {renderStepContent()}

      <div className="flex justify-between gap-3">
        <div>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
            className="border-foreground text-foreground hover:border-foreground hover:bg-foreground/10 hover:text-foreground bg-transparent disabled:opacity-50"
          >
            <X className="mr-2 h-4 w-4" />
            {t("whats-new.admin.cancel")}
          </Button>
        </div>
        <div className="flex gap-3">
          {currentStep > 0 && (
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={isSubmitting}
              className="border-foreground text-foreground hover:border-foreground hover:bg-foreground/10 hover:text-foreground bg-transparent disabled:opacity-50"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("whats-new.admin.previous")}
            </Button>
          )}
          {currentStep < 3 ? (
            <Button type="button" onClick={(e) => nextStep(e)} disabled={isSubmitting}>
              {t("whats-new.admin.next")}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <span className="flex items-center gap-2">
                  <svg className="h-4 w-4 animate-spin" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  {isEditing ? t("whats-new.admin.updating") : t("whats-new.admin.creating")}
                </span>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {isEditing
                    ? t("whats-new.admin.update-version")
                    : t("whats-new.admin.create-version")}
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </form>
  );
}
