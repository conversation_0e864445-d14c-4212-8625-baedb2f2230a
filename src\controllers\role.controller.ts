import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { IPaginationParams, Permissions } from '@px-shared-account/hermes';
import {
  CreateRoleDto,
  GetRoleByIdDto,
  FilterRoleDto,
  UpdateRoleDto,
} from '@dtos';
import { RoleUseCases } from '@useCases';
import { RequirePermissions } from 'src/auth/decorators';
import { JwtPermissionsGuard } from 'src/auth/guards';

@Controller('role')
@UseGuards(JwtPermissionsGuard)
export class RoleController {
  constructor(private readonly roleUseCases: RoleUseCases) { }

  @Post('/')
  @RequirePermissions(Permissions.Role.CREATE)
  async createRole(@Body() payload: CreateRoleDto) {
    return this.roleUseCases.create(payload);
  }

  @Get('/:id')
  @RequirePermissions(Permissions.Role.READ)
  async getRole(@Param() params: GetRoleByIdDto) {
    return this.roleUseCases.getById(params.id);
  }

  @Get('/')
  @RequirePermissions(Permissions.Role.READ)
  async getRoles(
    @Query() payload: FilterRoleDto & IPaginationParams,
  ): Promise<any> {
    return this.roleUseCases.getAllForRoleGroup(
      payload.group,
      payload.limit,
      payload.page,
      payload.search,
    );
  }

  @Patch('/:id')
  @RequirePermissions(Permissions.Role.UPDATE)
  async updateRolePermissions(
    @Param() params: GetRoleByIdDto,
    @Body() payload: UpdateRoleDto,
  ) {
    return this.roleUseCases.updatePermissions(params.id, payload);
  }

  @Delete('/:id')
  @RequirePermissions(Permissions.Role.DELETE)
  async deleteRole(@Param() params: GetRoleByIdDto) {
    return this.roleUseCases.delete(params.id);
  }
}
