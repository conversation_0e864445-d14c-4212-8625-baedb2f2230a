import { getTranslations } from "next-intl/server";

import TriadsStatsView from "@/components/(triads)/stats/TriadsStatsView";

export default function TriadsStatsPage() {
  return (
    <div className="space-y-4 py-4">
      <div className="space-y-4 border-b pb-4">
        <div className="flex flex-wrap items-center justify-between"></div>
      </div>
      <TriadsStatsView />
    </div>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const locale = (await params).locale;
  const t = await getTranslations({ locale, namespace: "navigation.practice" });

  return {
    title: t("title"),
  };
}
