import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { QueryFailedError, EntityNotFoundError } from 'typeorm';
import { DatabaseErrorFactory } from '@errors';

@Injectable()
export class DatabaseExceptionInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((err) => {
        if (
          err instanceof QueryFailedError ||
          err instanceof EntityNotFoundError
        ) {
          return throwError(() => DatabaseErrorFactory.createFromError(err));
        }

        return throwError(() => err);
      }),
    );
  }
}
