import { getTranslations } from "next-intl/server";
import { ToolCard } from "@/components/cards/ToolCard";
import type { UserAccessDetailsResponse, AccessInfo } from "@/services/customers";
import { Skeleton } from "@/components/ui/skeleton";
import { PageSectionCarousel } from "../base/page-section";

interface ToolDefinition {
  id: string;
  titleKey: string;
  subtitleKey: string;
  href: string;
  requiredAccess?: (accessInfo?: AccessInfo) => boolean;
  requiredCourseNameKey?: string;
}

// Define this data according to your needs and environment variables
const toolsData: ToolDefinition[] = [
  {
    id: "goliaths",
    titleKey: "toolsSection.nav.goliaths",
    subtitleKey: "toolsSection.nav.goliathsDescription",
    href: "/goliaths",
  },
  {
    id: "chatAi",
    titleKey: "toolsSection.nav.chatAi",
    subtitleKey: "toolsSection.nav.chatAiDescription",
    href: process.env.NEXT_PUBLIC_SOCRATES_URL || "#",
    requiredAccess: (accessInfo) => !!(accessInfo?.isPXS || accessInfo?.isParadox),
    requiredCourseNameKey: "toolsSection.requiredCourse.pxs",
  },
  {
    id: "testDisc",
    titleKey: "toolsSection.nav.testDisc",
    subtitleKey: "toolsSection.nav.testDiscDescription",
    href: process.env.NEXT_PUBLIC_TEST_DISC || "#", // Assuming NEXT_PUBLIC_TEST_DISC_URL
  },
  {
    id: "cic",
    titleKey: "toolsSection.nav.cic",
    subtitleKey: "toolsSection.nav.cicDescription",
    href: process.env.NEXT_PUBLIC_CIC_URL || "#",
  },
  {
    id: "ff",
    titleKey: "toolsSection.nav.ff",
    subtitleKey: "toolsSection.nav.ffDescription",
    href: process.env.NEXT_PUBLIC_FF_URL || "#",
    requiredAccess: (accessInfo) => !!(accessInfo?.isPOM || accessInfo?.isParadox),
    requiredCourseNameKey: "toolsSection.requiredCourse.psychologyOfMoney",
  },
  {
    id: "practice",
    titleKey: "toolsSection.nav.practice",
    subtitleKey: "toolsSection.nav.practiceDescription",
    href: "/practice",
    requiredAccess: (accessInfo) =>
      !!(accessInfo?.isPXS || accessInfo?.isPXL || accessInfo?.isParadox),
  },
];

interface ToolsSectionProps {
  customerAccessDetails: UserAccessDetailsResponse | null;
}

export async function ToolsSection({
  customerAccessDetails,
}: ToolsSectionProps): Promise<React.ReactNode> {
  const t = await getTranslations();
  const accessInfo = customerAccessDetails?.accessInfo;

  const availableTools = toolsData.map((tool) => {
    const hasAccess = tool.requiredAccess ? tool.requiredAccess(accessInfo) : true;
    return {
      ...tool,
      disabled: !hasAccess,
      requiredCourseName:
        tool.requiredCourseNameKey && !hasAccess ? t(tool.requiredCourseNameKey) : undefined,
    };
  });

  if (!customerAccessDetails) {
    return Array.from({ length: 4 }).map((_, index) => (
      <Skeleton key={`tool-skeleton-${index}`} className="size-72" />
    ));
  }

  if (availableTools.length === 0) {
    return (
      <p className="col-span-full p-8 text-center">
        {t("toolsSection.emptyState") || "No tools available at the moment."}
      </p>
    );
  }

  return (
    <PageSectionCarousel
      title={t("toolsSection.title")}
      description={t("toolsSection.subtitle")}
      carouselItemsClassName="basis-auto"
    >
      {availableTools.map((tool) => (
        <ToolCard
          key={tool.id}
          title={t(tool.titleKey)}
          subtitle={t(tool.subtitleKey)}
          href={tool.href}
          disabled={tool.disabled}
          requiredCourseName={tool.requiredCourseName}
        />
      ))}
    </PageSectionCarousel>
  );
}
