import { CreateTriadDto } from '@dtos';
import { Injectable } from '@nestjs/common';
import { PXSSession, SessionType } from '@px-shared-account/hermes';
import {
  TriadParticipantTable,
  TriadTable,
  UserTable,
} from '@services/database/postgres';
import {
  HubspotSession,
  REVERSE_SESSION_MAPPING,
  SESSION_MAPPING,
} from '@types';

@Injectable()
export class TriadFactory {
  /**
   * Generates a new triad
   * @param triadInfo - The information of the triad
   * @param organizer - The organizer of the triad
   * @returns The generated triad
   */
  generate(triadInfo: CreateTriadDto, organizer: UserTable): TriadTable {
    const triad = new TriadTable();
    triad.title = triadInfo.title;
    triad.sessionTime = triadInfo.sessionTime;
    triad.duration = triadInfo.duration;
    triad.meetingLink = triadInfo.meetingLink;
    triad.maxParticipants = triadInfo.maxParticipants || 3;
    triad.sessionType = triadInfo.sessionType || SessionType.PXS;
    triad.organizer = organizer;
    triad.sessions = triadInfo?.sessions || [];
    triad.participants = [];
    return triad;
  }

  /**
   * Generates a new triad participant
   * @param userId - The ID of the user to generate the participant for
   * @param triadId - The ID of the triad to generate the participant for
   * @returns The generated triad participant
   */
  generateParticipant(userId: number, triadId: number): TriadParticipantTable {
    const triadParticipant = new TriadParticipantTable();
    triadParticipant.user = new UserTable();
    triadParticipant.user.id = userId;
    triadParticipant.triad = new TriadTable();
    triadParticipant.triad.id = triadId;
    triadParticipant.joinedAt = new Date();
    return triadParticipant;
  }

  /**
   * Normalizes a Hubspot session to a PXSSession
   * @param hubspotSession - The Hubspot session to normalize
   * @returns The normalized PXSSession
   */
  normalizeHubspotSession(hubspotSession: string): PXSSession | undefined {
    return SESSION_MAPPING[hubspotSession];
  }

  /**
   * Normalizes an array of Hubspot sessions to an array of PXSSessions
   * @param hubspotSessions - The Hubspot sessions to normalize
   * @returns The normalized PXSSessions
   */
  normalizeHubspotSessions(hubspotSessions: string[]): PXSSession[] {
    return hubspotSessions
      .map(this.normalizeHubspotSession)
      .filter((session): session is PXSSession => session !== undefined);
  }

  /**
   * Gets the unique normalized sessions from an array of Hubspot sessions
   * @param hubspotSessions - The Hubspot sessions to get unique normalized sessions from
   * @returns The unique normalized sessions
   */
  getUniqueNormalizedSessions(hubspotSessions: HubspotSession[]): PXSSession[] {
    const normalizedSessions = hubspotSessions
      .map((session) => this.normalizeHubspotSession(session.value))
      .filter((session): session is PXSSession => session !== undefined);

    return Array.from(new Set(normalizedSessions));
  }

  /**
   * Gets the session label for a given PXSSession
   * Removes the 'S' prefix and adds it back for consistent formatting
   * @param session - The PXSSession to get the label for
   * @returns The session label
   */
  getSessionLabel(session: PXSSession): string {
    return `S${session.slice(1)}`;
  }

  /**
   * Gets the session labels for an array of PXSSessions
   * @param sessions - The PXSSessions to get the labels for
   * @returns The session labels
   */
  getSessionLabels(sessions: PXSSession[]): string[] {
    return sessions.map(this.getSessionLabel);
  }

  /**
   * Gets all possible Hubspot values for a given internal session
   * @param session - The PXSSession to get the Hubspot values for
   * @returns The Hubspot values
   */
  getHubspotSessionValues(session: PXSSession): string[] {
    return REVERSE_SESSION_MAPPING[session];
  }

  /**
   * Gets the primary (first) Hubspot value for a given internal session
   * @param session - The PXSSession to get the primary Hubspot value for
   * @returns The primary Hubspot value
   */
  getPrimaryHubspotSessionValue(session: PXSSession): string {
    return REVERSE_SESSION_MAPPING[session][0];
  }
}
