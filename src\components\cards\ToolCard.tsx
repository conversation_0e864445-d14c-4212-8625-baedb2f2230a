"use client";

import Link from "next/link";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

interface ToolCardProps {
  title: string;
  subtitle: string;
  href: string;
  disabled?: boolean;
  requiredCourseName?: string;
  className?: string;
}

export function ToolCard({
  title,
  subtitle,
  href,
  disabled = false,
  requiredCourseName,
  className = "",
}: ToolCardProps) {
  const t = useTranslations();

  const lockedBadgeText = t("components.badge.locked");
  const accessibleText = t("components.toolCard.accessibleWithCourse");

  return (
    <Link href={disabled ? "#" : href} className={cn("block", disabled && "cursor-not-allowed")}>
      <Card
        className={cn(
          "bg-background-secondary relative flex size-72 flex-col rounded-3xl border-none p-4 text-white transition-all hover:-translate-y-1 hover:shadow-xl",
          disabled && "filter",
          className,
        )}
      >
        {disabled && (
          <Badge className="bg-muted text-background-secondary-foreground absolute top-4 right-4 z-10 rounded-md p-2 leading-none font-light uppercase">
            {lockedBadgeText}
          </Badge>
        )}

        <div className="absolute inset-0 flex flex-grow flex-col items-center justify-center p-4 text-center">
          <h3
            className={cn(
              "text-background-secondary-foreground mb-1 text-xl font-semibold",
              disabled && "opacity-40",
            )}
          >
            {title}
          </h3>
          <p
            className={cn(
              "text-background-secondary-foreground/60 text-sm",
              disabled && "opacity-40",
            )}
          >
            {subtitle}
          </p>
        </div>

        {disabled ? (
          requiredCourseName && (
            <p className="text-background-secondary-foreground/80 mt-auto pb-2 text-center text-sm">
              {accessibleText} <span className="font-semibold">{requiredCourseName}</span>.
            </p>
          )
        ) : (
          <div className="bg-muted group-hover:bg-primary absolute right-4 bottom-4 flex h-12 w-12 items-center justify-center rounded-full transition-colors">
            <ArrowRight className="h-3 w-3 text-white" />
          </div>
        )}
      </Card>
    </Link>
  );
}
