"use client";

import { useState, useMemo } from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { motion } from "framer-motion";
import { ChevronDown, Download } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { InvoiceRecord } from "@/services/customers";
import { formatCurrency, formatDate, groupBy } from "@/lib/utils";

interface InvoiceListProps {
  invoices: InvoiceRecord[];
}

export function InvoiceList({ invoices = [] }: InvoiceListProps) {
  const t = useTranslations("subscription.invoices");
  const [selectedYear, setSelectedYear] = useState<string>("all");

  // Group invoices by year
  const groupedInvoices = useMemo(() => {
    if (!invoices || invoices.length === 0) {
      return {};
    }

    // Add year property to each invoice
    const invoicesWithYear = invoices.map((invoice) => {
      const date = new Date(invoice.date < 10000000000 ? invoice.date * 1000 : invoice.date);
      return {
        ...invoice,
        year: date.getFullYear().toString(),
      };
    });

    // Sort invoices by date (descending)
    const sortedInvoices = invoicesWithYear.sort((a, b) => b.date - a.date);

    // Group by year
    return groupBy(sortedInvoices, "year");
  }, [invoices]);

  // Get unique years for select dropdown
  const years = useMemo(() => {
    return Object.keys(groupedInvoices).sort((a, b) => parseInt(b) - parseInt(a));
  }, [groupedInvoices]);

  // Get filtered invoices based on selected year
  const filteredInvoices = useMemo(() => {
    if (selectedYear === "all") {
      // Flatten the grouped invoices
      return Object.values(groupedInvoices).flat();
    }
    return groupedInvoices[selectedYear] || [];
  }, [selectedYear, groupedInvoices]);

  // Status badge color mapping
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-green-500";
      case "pending":
        return "bg-amber-500";
      case "failed":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  if (invoices.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("title")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 text-center text-gray-500">{t("no-invoices")}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{t("title")}</CardTitle>
        {years.length > 1 && (
          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t("select-year")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("all-years")}</SelectItem>
              {years.map((year) => (
                <SelectItem key={year} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible className="w-full">
          {filteredInvoices.map((invoice, index) => (
            <motion.div
              key={invoice.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <AccordionItem value={`item-${index}`}>
                <AccordionTrigger className="hover:no-underline">
                  <div className="flex flex-1 items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{formatDate(invoice.date)}</span>
                      <Badge className={getStatusColor(invoice.status)}>
                        {t(`status.${invoice.status.toLowerCase()}`, { fallback: invoice.status })}
                      </Badge>
                    </div>
                    <span className="font-bold">{formatCurrency(invoice.amount)}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 px-4 py-2">
                    {invoice.invoiceNumber && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">{t("invoice-number")}</span>
                        <span>{invoice.invoiceNumber}</span>
                      </div>
                    )}
                    {invoice.pdfUrl && (
                      <div className="flex justify-end">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={invoice.pdfUrl} target="_blank" rel="noopener noreferrer">
                            <Download className="mr-2 h-4 w-4" />
                            {t("download")}
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </motion.div>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
}
