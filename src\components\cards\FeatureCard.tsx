"use client";

import React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Button } from "@/components/base/button";
import Link from "next/link";

interface FeatureCardButtonProps {
  text: string;
  variant?: "secondary" | "outline" | "ghost" | "link" | "default";
  className?: string;
  action?: () => void;
  href?: string;
  ariaLabel?: string;
}

export interface FeatureCardProps {
  imageUrl: string;
  title: string;
  subtitle?: string;
  buttons?: FeatureCardButtonProps[];
  className?: string;
  imageOverlayClassName?: string;
}

export function FeatureCard({
  imageUrl,
  title,
  subtitle,
  buttons,
  className,
  imageOverlayClassName = "bg-background/80", // Default overlay
}: FeatureCardProps) {
  return (
    <div
      className={cn(
        "relative flex h-auto min-h-[40svh] w-full items-center justify-center overflow-hidden rounded-2xl shadow-lg transition-all hover:shadow-xl lg:aspect-[3/1]",
        className,
      )}
    >
      <Image
        src={imageUrl}
        alt={title}
        fill
        className="absolute inset-0 z-8 object-cover"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
      <div className={cn("absolute inset-0 z-9", imageOverlayClassName)} />
      <div className="z-10 flex size-full flex-col items-center justify-center gap-4 p-6 text-center text-white md:p-8">
        <h2 className="font-anton text-shadow-xl lg:text-3xl">{title}</h2>
        {subtitle && (
          <p className="text-shadow-xl mt-4 max-w-xl text-base text-neutral-200 md:text-lg">
            {subtitle}
          </p>
        )}
        {buttons && buttons.length > 0 && (
          <div className="mt-6 flex flex-col gap-3 sm:flex-row md:mt-8">
            {buttons.map((button, index) => {
              const buttonElement = (
                <Button
                  key={index}
                  variant={button.variant}
                  className={cn("px-6 py-3 text-base font-medium drop-shadow-lg", button.className)}
                  onClick={button.action}
                  aria-label={button.ariaLabel || button.text}
                >
                  {button.text}
                </Button>
              );
              return button.href ? (
                <Link key={`link-${index}`} href={button.href} passHref>
                  {buttonElement}
                </Link>
              ) : (
                buttonElement
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

FeatureCard.displayName = "FeatureCard";
