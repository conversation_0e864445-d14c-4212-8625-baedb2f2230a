import { MigrationInterface, QueryRunner } from 'typeorm';

export class ExchangeRateApp1732172475486 implements MigrationInterface {
  name = 'ExchangeRateApp1732172475486';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."exchangeRateCurrencies" AS ENUM('ZAR', 'AUD', 'USD', 'EUR', 'CAD', 'CHF', 'GBP', 'AED')`,
    );
    await queryRunner.query(
      `CREATE TABLE "exchangeRates" ("id" SERIAL NOT NULL, "from" "public"."exchangeRateCurrencies" NOT NULL, "to" "public"."exchangeRateCurrencies" NOT NULL DEFAULT 'AED', "rate" numeric(10,6) NOT NULL, "date" bigint NOT NULL, "metaData" jsonb NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_a5de1194b00a503b47f4bb89db2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."cronLogs_type_enum" RENAME TO "cronLogs_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum" AS ENUM('OVERDUE_INVOICE_CHECK', 'ACCESS_SUSPENSION_CHECK', 'ERRORED_CUSTOMER_ACCESS_CHECK', 'EXCHANGE_RATE_CRAWLER')`,
    );
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ALTER COLUMN "type" TYPE "public"."cronLogs_type_enum" USING "type"::"text"::"public"."cronLogs_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum_old" AS ENUM('OVERDUE_INVOICE_CHECK', 'ACCESS_SUSPENSION_CHECK')`,
    );
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ALTER COLUMN "type" TYPE "public"."cronLogs_type_enum_old" USING "type"::"text"::"public"."cronLogs_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."cronLogs_type_enum_old" RENAME TO "cronLogs_type_enum"`,
    );
    await queryRunner.query(`DROP TABLE "exchangeRates"`);
    await queryRunner.query(`DROP TYPE "public"."exchangeRateCurrencies"`);
  }
}
