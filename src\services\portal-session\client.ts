import { useCallback, useState } from "react";

import { useApi } from "@/hooks/store/use-api";
import { PORTAL_SESSION_API_ENDPOINTS, PortalSessionApiPayloads } from "./core";

export function useCreateChargebeePortalSession() {
  const [error, setError] = useState<Error | null>(null);
  const { url, method } = PORTAL_SESSION_API_ENDPOINTS.create();
  const { trigger, isLoading } = useApi<PortalSessionApiPayloads["create"]>(url, {
    method: method as "POST",
  });

  const create = useCallback(
    async (data: PortalSessionApiPayloads["create"]) => {
      if (!trigger) throw new Error("API not initialized");

      setError(null);

      try {
        const response = await trigger(data);
        return response;
      } catch (error) {
        console.error("Error creating Chargebee portal session:", error);
        throw error;
      }
    },
    [trigger],
  );

  return { create, isLoading, error };
}
