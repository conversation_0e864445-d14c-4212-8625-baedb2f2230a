/**
 * Represents a configuration of offers where the key is the `billingCycle`
 * and the value is the `planId` of the plan chosen for that specific billing cycle.
 * @type {Object.<string, string>}
 */
export type CycleWithPlan = Record<string, string>;

/**
 * Represents a product map in the offer config where keys are `id`s of the products
 * and value is `CycleWithPlan[]`.
 * @type {Record<string, CycleWithPlan[]>}
 */
export type AttachedProduct = Record<string, CycleWithPlan[]>;

/**
 * Represents a quantities map in the offer config where keys are `id`s of the products
 * and value is `boolean` representing if the product is allowed to update the quantity.
 * @type {Record<string, boolean>}
 */
export type ProductQuantities = Record<string, boolean>;

/**
 * Represents an attachment map in the offer config where keys are Chargebee `id`s of
 * the plan attached and value is Chargebee `id` of the attachment. This is needed for
 * later when deleting an attached item from Chargebee.
 * @link [See docs](https://apidocs.chargebee.com/docs/api/attached_items?lang=curl#delete_an_attached_item)
 * @type {Record<string, string>}
 */
export type AttachmentMap = Record<string, string>;

export type OfferConfig = {
  products?: AttachedProduct;
  recommended?: AttachedProduct | null;
  quantityInfo?: {
    products: ProductQuantities;
    recommended?: ProductQuantities | null;
  };
  display: 'products' | 'offer';
  defaultBillingCycle?: number;
  attachmentMap?: AttachmentMap;
  monthly?: string[];
  yearly?: string[];
};
