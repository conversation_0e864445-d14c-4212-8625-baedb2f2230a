import { useCallback, useMemo } from "react";
import { useQueryParams } from "@/hooks/use-query-params";
import { RoleGroup } from "@px-shared-account/hermes";
import { TableState as CoreTableState } from "@/types/table";

export type RolesTableState = {
  page: number;
  search?: string;
  group?: RoleGroup;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
};

const itemsPerPage = 10;

export function useRolesTable() {
  const { params, updateParams } = useQueryParams();

  // Convert URL params to table state
  const tableState = useMemo<RolesTableState>(() => {
    return {
      page: parseInt(params.page as string) || 1,
      search: (params.search as string) || undefined,
      group: (params.group as RoleGroup) || undefined,
      sortBy: (params.sortBy as string) || undefined,
      sortOrder: (params.sortOrder as "asc" | "desc") || undefined,
    };
  }, [params]);

  // Memoize state change handler
  const handleStateChange = useCallback(
    (state: Partial<RolesTableState>) => {
      // Create a new object with string values for the query params
      const queryParams: Record<string, string | string[]> = {};

      // Handle each property correctly
      Object.entries(state).forEach(([key, value]) => {
        if (value === undefined) {
          return; // Skip undefined values
        }

        if (key === "page" && typeof value === "number") {
          queryParams[key] = String(value);
        } else if (typeof value === "string") {
          queryParams[key] = value;
        } else if (value !== null) {
          queryParams[key] = String(value);
        }
      });

      updateParams(queryParams);
    },
    [updateParams],
  );

  // Handle search input
  const handleSearch = useCallback(
    (search: string) => {
      handleStateChange({
        search: search || undefined,
        page: 1, // Reset to first page on search
      });
    },
    [handleStateChange],
  );

  // Handle filter changes
  const handleFilterChange = useCallback(
    (filters: Partial<RolesTableState>) => {
      handleStateChange({
        ...filters,
        page: 1, // Reset to first page on filter change
      });
    },
    [handleStateChange],
  );

  // Function to get a unique identifier for each row
  const getRowId = useCallback((row: any) => row.id, []);

  // Adapter for converting our URL-based state to DataTable state
  const adaptStateChange = useCallback(
    (state: CoreTableState) => {
      handleStateChange({
        page: state.pageIndex + 1,
        search: state.searchQuery || undefined,
        // If we had sort information, we would map it here
      });
    },
    [handleStateChange],
  );

  // Memoize table configuration
  const tableConfig = useMemo(() => {
    return {
      pageIndex: tableState.page - 1,
      pageSize: itemsPerPage,
      onStateChange: adaptStateChange,
    };
  }, [tableState.page, adaptStateChange]);

  return {
    tableState,
    tableConfig,
    itemsPerPage,
    getRowId,
    handleSearch,
    handleFilterChange,
  };
}
