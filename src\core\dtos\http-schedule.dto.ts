import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>nt,
    IsISO8601,
    <PERSON><PERSON>ot<PERSON><PERSON><PERSON>,
    IsOptional,
    IsUrl,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsFutureDate } from '../../common/decorators/is-future-date.decorator';

export class ScheduleRequestDto {
    @ApiProperty({ description: 'URL to be called in the scheduled request' })
    @Transform(({ value }) => {
        if (typeof value === 'string') {
            try {
                // Decode the URL parameter before validation
                return decodeURIComponent(value);
            } catch (e) {
                // If decoding fails, return the original value to let IsUrl validator handle the error
                return value;
            }
        }
        return value;
    })
    @IsNotEmpty({ message: 'URL parameter is required' })
    @IsUrl({ require_tld: false }, { message: 'URL parameter must be a valid URL' })
    url: string;

    @ApiProperty({ description: 'ISO datetime string when to execute the request' })
    @IsNotEmpty({ message: 'Scheduling time parameter "at" is required' })
    @IsISO8601(
        {},
        { message: 'Scheduling time must be a valid ISO 8601 datetime string' },
    )
    @IsFutureDate()
    at: string;
}

export enum JobStatus {
    created = 'created',
    retry = 'retry',
    active = 'active',
    completed = 'completed',
    expired = 'expired',
    cancelled = 'cancelled',
    failed = 'failed',
}

export class ListJobsDto {
    @ApiPropertyOptional({
        enum: JobStatus,
        description: 'Filter jobs by status.',
    })
    @IsOptional()
    @IsEnum(JobStatus)
    status?: JobStatus;

    @ApiPropertyOptional({
        description: 'The maximum number of jobs to return.',
        default: 20,
    })
    @IsOptional()
    @IsInt()
    @Transform(({ value }) => parseInt(value, 10))
    limit?: number = 20;

    @ApiPropertyOptional({
        description: 'The page number to retrieve.',
        default: 1,
    })
    @IsOptional()
    @IsInt()
    @Transform(({ value }) => parseInt(value, 10))
    page?: number = 1;

    @ApiPropertyOptional({
        enum: ['asc', 'desc'],
        description: 'Sort order for job creation time.',
        default: 'desc',
    })
    @IsOptional()
    @IsEnum(['asc', 'desc'])
    sort?: 'asc' | 'desc' = 'desc';
} 