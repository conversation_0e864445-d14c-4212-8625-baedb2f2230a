"use client";

import { useTranslations } from "next-intl";
import { FilterBar } from "@/components/base/filter-bar";
import { FilterStep } from "@/components/base/filter-bar/types";
import { useRoleList } from "@/services/role";
import { useMemo } from "react";

interface FilterProps {
  onChange?: (values: Record<string, string>) => void;
  value?: Record<string, string>;
}

const Filter = ({ onChange, value }: FilterProps) => {
  const t = useTranslations("gestion.users");

  // Fetch roles using the role service
  const { data: rolesData, isLoading: isLoadingRoles } = useRoleList();

  // Create options for the filter dropdown from the fetched roles
  const roleOptions = useMemo(() => {
    if (!rolesData?.data || isLoadingRoles) return [];

    return rolesData.data.map((role) => ({
      label: role.name,
      value: String(role.id), // Convert to string for FilterBar compatibility
    }));
  }, [rolesData, isLoadingRoles]);

  // Handler to convert string values to appropriate types
  const handleFilterChange = (values: Record<string, string>) => {
    if (onChange) {
      // Convert role ID to number if present
      const processedValues = { ...values };
      if (processedValues.role) {
        processedValues.role = processedValues.role; // Keep as string here, parent will parse it
      }
      onChange(processedValues);
    }
  };

  const filterConfig: { groups: FilterStep[] } = {
    groups: [
      {
        id: "role",
        label: t("filter-by-role"),
        type: "filter",
        options: roleOptions,
      },
    ],
  };

  return (
    <div className="flex items-center justify-end gap-4">
      <FilterBar
        steps={filterConfig.groups}
        forceModal={true}
        triggerText={t("filters")}
        onChange={handleFilterChange}
        value={value}
      />
    </div>
  );
};

export default Filter;
