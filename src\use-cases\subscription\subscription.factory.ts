import { Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import _ from 'lodash';
import { SubscriptionItem } from 'chargebee-typescript/lib/resources/subscription';
import { Subscription as ChargeBeeSubscription } from 'chargebee-typescript/lib/resources';
import {
  DiscountType,
  SubscriptionOrderStatus,
  SubscriptionStatus,
} from '@enums';
import { ChargebeeBillingService } from '@services/billing';
import { ProductPlanPrice, Subscription } from '@entities';
import { IDataServices } from '@abstracts';
import { ChargeBeeSubscriptionDTO } from '@dtos';

type OrderStatusParams = {
  currentOrderStatus?: SubscriptionOrderStatus;
  remainingBillingCycles?: number;
  incomingStatus: SubscriptionStatus;
  totalDues?: number;
  amountRemaining?: number;
  amountPaid?: number;
  amountRefunded?: number;
  isForever?: boolean;
  cancellationReasonCode?: string;
};

@Injectable()
export class SubscriptionFactoryService {
  private readonly NO_CARD_CODE = 'No Card';
  private readonly BANK_TRANSFER_CODE = 'Paid in full via Bank Transfer';

  constructor(
    private readonly dataServices: IDataServices,
    private readonly chargebeeService: ChargebeeBillingService,
  ) {}

  /**
   * Generates a new subscription based on subscription data coming from ChargeBee Webhook Notification
   * @param chargebeeSubscription Subscription data from ChargeBee
   */
  async generateFromChargebeeEvent(
    chargebeeSubscription: ChargeBeeSubscription,
  ): Promise<Subscription> {
    const subscriptionDTO = plainToClass(
      ChargeBeeSubscriptionDTO,
      chargebeeSubscription,
    );
    const subscription = Object.assign(new Subscription(), {
      ...subscriptionDTO,
    });
    const {
      amountPaid,
      amountRemaining,
      amountDue = 0,
      amountRefunded,
    } = await this.getAmountInfo(chargebeeSubscription);
    const totalAmount = amountPaid + amountDue + amountRemaining;
    subscription.amountPaid = amountPaid;
    subscription.totalOrderAmount = totalAmount;
    subscription.amountRemaining = amountRemaining;
    subscription.amountRefunded = amountRefunded;
    subscription.initialBillingCycles =
      subscription.remainingBillingCycles === 0
        ? 1
        : subscription.remainingBillingCycles + 1;
    subscription.totalBillingCycles = subscription.initialBillingCycles;
    const calculatedMRR = Math.round(
      totalAmount / subscription.totalBillingCycles,
    );
    subscription.mrr = !chargebeeSubscription.mrr
      ? calculatedMRR
      : chargebeeSubscription.mrr;

    subscription.attachedPrices = await this.getAttachedPlanPrices(
      subscription.subscriptionItems,
    );
    subscription.orderStatus = this.getOrderStatus({
      remainingBillingCycles: subscription.remainingBillingCycles,
      incomingStatus: subscription.status,
      totalDues: subscription.totalDues,
      amountRemaining,
      amountPaid,
      amountRefunded,
      isForever: subscription?.isForever,
    });
    return subscription;
  }

  /**
   * Returns the `orderStatus` for Subscription or `undefined`
   * @param params - Object containing order status parameters
   * @param params.currentOrderStatus - Current `orderStatus` of the subscription
   * @param params.remainingBillingCycles - Remaining billing cycles of the Subscription
   * @param params.incomingStatus - Status of the subscription reported by Chargebee
   * @param params.totalDues - Total dues on the subscription reported by Chargebee
   * @param params.amountRemaining - Total amount remaining to be paid by customer for this subscription
   * @param params.amountPaid - Total amount paid by customer for this subscription so far
   * @param params.amountRefunded - Total amount refunded against this subscription
   * @param params.isForever - Whether the subscription is forever
   * @param params.cancellationReasonCode - Reason code for cancelling the subscription
   * @returns The `SubscriptionOrderStatus`
   */
  getOrderStatus({
    currentOrderStatus,
    remainingBillingCycles = 0,
    incomingStatus,
    totalDues = 0,
    amountRemaining = 0,
    amountPaid = 0,
    amountRefunded = 0,
    isForever = false,
    cancellationReasonCode,
  }: OrderStatusParams): SubscriptionOrderStatus {
    const isCancelled = incomingStatus === SubscriptionStatus.CANCELLED;
    const isNonRenewing = incomingStatus === SubscriptionStatus.NON_RENEWING;
    const isActive = isForever || remainingBillingCycles > 0;
    const paidViaBankTransfer =
      cancellationReasonCode === this.BANK_TRANSFER_CODE;
    const isStopped =
      Boolean(cancellationReasonCode) &&
      cancellationReasonCode !== this.NO_CARD_CODE &&
      !paidViaBankTransfer;
    const hasNoTransactions = amountPaid === 0 && amountRefunded === 0;

    if (this.isSubscriptionInTrial(incomingStatus)) {
      return SubscriptionOrderStatus.IN_TRIAL;
    }

    if (this.isSubscriptionOverdue(totalDues, isStopped)) {
      return SubscriptionOrderStatus.OVERDUE;
    }

    const isPaid = this.isSubscriptionPaid(
      remainingBillingCycles,
      totalDues,
      amountPaid,
      amountRemaining,
      isNonRenewing,
      paidViaBankTransfer,
    );

    if (isCancelled) {
      const isRefunded = this.isSubscriptionRefunded(
        amountRefunded,
        amountPaid,
      );

      if (isStopped && isRefunded) {
        return SubscriptionOrderStatus.REFUNDED;
      }
      if (isStopped && (!isRefunded || hasNoTransactions)) {
        return SubscriptionOrderStatus.STOPPED;
      }
      if (isPaid) {
        return SubscriptionOrderStatus.PAID;
      }
      return SubscriptionOrderStatus.STOPPED;
    }

    if (isPaid) {
      return SubscriptionOrderStatus.PAID;
    }

    if (incomingStatus === SubscriptionStatus.ACTIVE || isActive) {
      return SubscriptionOrderStatus.ACTIVE;
    }

    return currentOrderStatus;
  }

  /**
   * Checks if the subscription is in trial
   * @param status Subscription status
   * @returns A boolean indicating if the subscription is in trial
   */
  private isSubscriptionInTrial(status: SubscriptionStatus): boolean {
    return status === SubscriptionStatus.IN_TRIAL;
  }

  /**
   * Checks if the subscription is overdue
   * @param totalDues Total dues on the subscription
   * @param isStopped Whether the subscription is stopped
   * @returns A boolean indicating if the subscription is overdue
   */
  private isSubscriptionOverdue(
    totalDues: number,
    isStopped: boolean,
  ): boolean {
    return totalDues > 0 && !isStopped;
  }

  /**
   * Checks if the subscription is refunded
   * @param amountRefunded Amount refunded on the subscription
   * @param amountPaid Amount paid on the subscription
   * @returns A boolean indicating if the subscription is refunded
   */
  private isSubscriptionRefunded(
    amountRefunded: number,
    amountPaid: number,
  ): boolean {
    return amountRefunded >= amountPaid && amountPaid > 0;
  }

  /**
   * Checks if the subscription is paid
   * @param remainingBillingCycles Remaining billing cycles on the subscription
   * @param totalDues Total dues on the subscription
   * @param amountPaid Amount paid on the subscription
   * @param amountRemaining Amount remaining on the subscription
   * @param isNonRenewing Whether the subscription is status `non_renewing`
   * @param paidViaBankTransfer Defines whether the subscription was fully paid via bank transfer
   * @returns A boolean indicating if the subscription is paid
   */
  private isSubscriptionPaid(
    remainingBillingCycles: number,
    totalDues: number,
    amountPaid: number,
    amountRemaining: number,
    isNonRenewing: boolean,
    paidViaBankTransfer: boolean,
  ): boolean {
    return (
      (remainingBillingCycles === 0 && totalDues === 0 && amountPaid > 0) ||
      (amountRemaining === 0 && isNonRenewing) ||
      paidViaBankTransfer
    );
  }

  async getAttachedPlanPrices(
    items: SubscriptionItem[],
  ): Promise<ProductPlanPrice[]> {
    const attachedPrices: ProductPlanPrice[] = [];
    for (const item of items) {
      if (item.item_type === 'addon') {
        const price = await this.dataServices.productPlanPrice.getOneBy({
          chargebeeId: item.item_price_id,
        });
        attachedPrices.push(price);
      }
    }
    return attachedPrices;
  }

  /**
   * Returns the amount info for a subscription
   * @param subscription Chargebee subscription
   * @returns A promise that resolves to an object containing amount info for the Subscription
   */
  async getAmountInfo(subscription: ChargeBeeSubscription): Promise<{
    amountPaid: number;
    amountRemaining: number;
    amountDue: number;
    amountRefunded: number;
  }> {
    const { subscription_items, total_dues, coupon } = subscription;
    let amountRemaining = 0;
    for (const {
      billing_cycles,
      quantity = 0,
      amount = 0,
    } of subscription_items) {
      if (!billing_cycles) continue;

      amountRemaining += billing_cycles * quantity * amount;
    }

    const { amountPaid, amountRefunded } =
      await this.chargebeeService.getSubscriptionAmountInfo(subscription.id);
    const discountInfo = await this.chargebeeService.getCouponInfo(coupon);
    if (discountInfo.couponApplicable && amountRemaining !== 0) {
      amountRemaining = this.applyDiscountToAmountRemaining(
        amountRemaining,
        discountInfo.couponType,
        discountInfo.couponAmount,
        discountInfo.couponPercentageAmount,
      );
    }

    return {
      amountPaid,
      amountRemaining,
      amountDue: total_dues,
      amountRefunded,
    };
  }

  /**
   * Applies discount to amount remaining
   * @param amountRemaining Amount remaining, in cents, to apply the discount to
   * @param discountType Type of discount
   * @param discountAmount Amount of discount (for fixed amount discounts)
   * @param percentageAmount Percentage of discount (for percentage discounts)
   * @returns Amount remaining after applying discount
   */
  applyDiscountToAmountRemaining(
    amountRemaining: number,
    discountType: DiscountType,
    discountAmount: number,
    percentageAmount: number,
  ): number {
    if (discountType === DiscountType.PERCENTAGE) {
      const amountToDiscount = Math.floor(
        (amountRemaining * percentageAmount) / 100,
      );
      return amountRemaining - amountToDiscount;
    }
    return amountRemaining - discountAmount;
  }

  async getAttachedPlans(
    planPrices: ProductPlanPrice[] = [],
  ): Promise<ProductPlanPrice[]> {
    const attachedPlans: ProductPlanPrice[] = [];
    for (const item of planPrices) {
      const price = await this.dataServices.productPlanPrice.getOneWithLeftJoin(
        'id',
        item.id,
        'productPlan',
        'productPlan',
      );
      attachedPlans.push(price);
    }
    return attachedPlans;
  }

  /**
   * Returns the fields that were updated in a Subscription. It only checks specific
   * fields that are important for Hubspot and other purposes, rather than all fields.
   * @param updatedVersion Updated version of the Subscription, from Chargebee
   * @param currentVersion Current version of the Subscription, stored in our own DB
   * @returns A Subscription containing only the updated fields of interest
   */
  async getUpdatedFields(
    updatedVersion: ChargeBeeSubscription,
    currentVersion: Subscription,
  ): Promise<Subscription> {
    const updatedSubscription = new Subscription();
    const { amountPaid, amountRemaining, amountRefunded } =
      await this.getAmountInfo(updatedVersion);

    const newOrderStatus = this.getOrderStatus({
      currentOrderStatus: currentVersion.orderStatus,
      remainingBillingCycles: updatedVersion.remaining_billing_cycles,
      incomingStatus: SubscriptionStatus[updatedVersion.status.toUpperCase()],
      totalDues: updatedVersion.total_dues || 0,
      amountRemaining,
      amountPaid,
      amountRefunded,
      isForever: currentVersion?.isForever,
      cancellationReasonCode: updatedVersion?.cancel_reason_code,
    });

    const fieldsToUpdate: Partial<Subscription> = {
      dueInvoicesCount: updatedVersion.due_invoices_count,
      totalDues: updatedVersion.total_dues || 0,
      orderStatus: newOrderStatus,
      amountRemaining: amountRemaining,
      amountPaid: amountPaid,
      amountRefunded,
      nextBillingAt: updatedVersion.next_billing_at,
      updatedAt: updatedVersion.updated_at,
      cancelledAt: updatedVersion.cancelled_at || null,
      cancelReasonCode: updatedVersion.cancel_reason_code,
      resourceVersion: updatedVersion.resource_version,
      status: SubscriptionStatus[updatedVersion.status.toUpperCase()],
      remainingBillingCycles: updatedVersion.remaining_billing_cycles || 0,
      autoCollection: updatedVersion.auto_collection,
      totalBillingCycles:
        currentVersion.remainingBillingCycles -
          updatedVersion.remaining_billing_cycles ===
        1
          ? currentVersion.totalBillingCycles
          : updatedVersion.remaining_billing_cycles,
    };

    for (const [fieldToUpdate, updatedValue] of Object.entries(
      fieldsToUpdate,
    )) {
      if (currentVersion[fieldToUpdate] !== updatedValue) {
        updatedSubscription[fieldToUpdate] = updatedValue;
      }
    }

    return updatedSubscription;
  }

  /**
   * Finds the difference between existing and new line items on a Subscription and returns
   * the updated
   * @param currentLineItems Current line items of the Subscription from database
   * @param newLineItems Updated line items from Chargebee
   * @returns Updated subscription line items
   */
  getUpdatedLineItems(
    currentLineItems: SubscriptionItem[],
    newLineItems: SubscriptionItem[],
  ): SubscriptionItem[] {
    if (currentLineItems?.length !== newLineItems?.length) {
      return newLineItems;
    }

    const updatedItems = newLineItems.filter(
      (newItem) =>
        !currentLineItems.some((currentItem) =>
          _.isEqual(newItem, currentItem),
        ),
    );

    if (updatedItems?.length === 0) {
      return null;
    }

    const existingItemsNotChanged = newLineItems.filter((newItem) =>
      currentLineItems.some((currentItem) => _.isEqual(newItem, currentItem)),
    );
    return [...updatedItems, ...existingItemsNotChanged];
  }
}
