import {
  IsString,
  IsNotEmpty,
  IsEmail,
  IsNumber,
  IsEnum,
  IsInt,
  IsOptional,
} from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { BankTransferStatus, Currencies } from '@enums';

export class CreateBankTransferDTO {
  @ApiProperty()
  @Expose({ name: 'banks_statements' })
  @Type(() => BankStatementDTO)
  banksStatements: BankStatementDTO[];
}

export class BankStatementDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'bank_name' })
  bankName: string;

  @ApiProperty()
  @Expose({ name: 'payments' })
  @Type(() => BankTransferPayment)
  payments: BankTransferPayment[];

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'entity_id' })
  entityId: string;
}

export class BankTransferPayment {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'payment_id' })
  paymentId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'payment_date' })
  paymentDate: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'payment_description' })
  paymentDescription: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'sender_firstname' })
  senderFirstName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'sender_lastname' })
  senderLastName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'sender_address' })
  senderAddress: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  @Expose({ name: 'sender_email' })
  senderEmail: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(Currencies)
  @Expose({ name: 'currency' })
  currency: Currencies;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Expose({ name: 'payment_amount' })
  paymentAmount: number;
}

export class UpdateTransferStatusDTO {
  @ApiProperty()
  @IsEnum(BankTransferStatus)
  @IsNotEmpty()
  status: BankTransferStatus;
}

export class ListBankTransfersDTO {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsString()
  bankName?: string;

  @IsOptional()
  @IsString()
  paymentDate?: string;

  @IsOptional()
  @IsEnum(Currencies)
  currency?: Currencies;

  @IsOptional()
  @IsEnum(BankTransferStatus)
  status?: BankTransferStatus;

  @IsOptional()
  @IsString()
  senderName?: string;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  limit?: number;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => Number(value))
  page?: number;

  @IsOptional()
  @IsEnum(['DESC', 'ASC'])
  orderBy?: 'DESC' | 'ASC';
}
