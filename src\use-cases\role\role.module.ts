import { Module } from '@nestjs/common';
import { DataServicesModule } from '@services/database';
import { RoleUseCases } from './role.use-cases';
import { RoleFactory } from './role.factory';
import {
  RedisClientsService,
  RedisPublisher,
  RedisClientsModule,
} from '@services/event-publishers';

@Module({
  imports: [DataServicesModule, RedisClientsModule],
  providers: [
    RoleUseCases,
    RoleFactory,
    {
      provide: RedisPublisher,
      useFactory: async (redisClientsService: RedisClientsService) => {
        const redisClient = await redisClientsService.getVegaRedisClient();
        return new RedisPublisher(redisClient);
      },
      inject: [RedisClientsService],
    },
  ],
  exports: [RoleUseCases],
})
export class RoleModule {}
