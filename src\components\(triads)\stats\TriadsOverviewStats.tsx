"use client";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { Loader2, TrendingUp, Users, Target, CheckCircle } from "lucide-react";
import useS<PERSON> from "swr";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import { anton } from "@/lib/fonts/anton";
import { useTranslations } from "next-intl";
import { PlatformTriadStats } from "@/types/triad";
import { usePlatformTriadStats } from "@/services/triad/client";

interface OverviewStats {
  totalTriads: number;
  totalParticipants: number;
  averageParticipantsPerTriad: number;
  completionRate: number;
}

interface TimeSeriesData {
  triadsCreated: Array<{ date: string; value: number }>;
  participantsJoined: Array<{ date: string; value: number }>;
}

interface StatsResponse {
  overview: OverviewStats;
  timeSeriesData: TimeSeriesData;
}

interface TimeRange {
  start: Date;
  end: Date;
  type: string;
}

interface TriadsOverviewStatsProps {
  timeRange: TimeRange;
  onDataChange?: (data: PlatformTriadStats | null) => void;
}

export default function TriadsOverviewStats({ timeRange, onDataChange }: TriadsOverviewStatsProps) {
  const t = useTranslations("triad.stats");
  const {
    stats: triadStats,
    isLoading,
    error,
  } = usePlatformTriadStats(timeRange.start.toISOString(), timeRange.end.toISOString());

  useEffect(() => {
    if (triadStats && onDataChange) {
      onDataChange(triadStats);
    }
  }, [triadStats, onDataChange]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-white/50" />
      </div>
    );
  }

  if (error) {
    return <div className="text-center text-red-400">{t("overview.error")}</div>;
  }

  if (!triadStats) return null;

  const { overview, timeSeriesData } = triadStats;

  const metrics = [
    {
      title: t("overview.metrics.total_triads"),
      value: overview.totalTriads,
      icon: TrendingUp,
      gradient: "from-indigo-500/10 to-purple-600/10",
      iconBg: "bg-indigo-500/20",
      iconColor: "text-indigo-400",
      textColor: "text-indigo-400",
    },
    {
      title: t("overview.metrics.total_participants"),
      value: overview.totalParticipants,
      icon: Users,
      gradient: "from-cyan-500/10 to-blue-600/10",
      iconBg: "bg-cyan-500/20",
      iconColor: "text-cyan-400",
      textColor: "text-cyan-400",
    },
    {
      title: t("overview.metrics.avg_participants"),
      value: overview.averageParticipantsPerTriad.toFixed(1),
      icon: Target,
      gradient: "from-emerald-500/10 to-teal-600/10",
      iconBg: "bg-emerald-500/20",
      iconColor: "text-emerald-400",
      textColor: "text-emerald-400",
    },
    {
      title: t("overview.metrics.completion_rate"),
      value: `${(overview.completionRate * 100).toFixed(1)}%`,
      icon: CheckCircle,
      gradient: "from-amber-500/10 to-orange-600/10",
      iconBg: "bg-amber-500/20",
      iconColor: "text-amber-400",
      textColor: "text-amber-400",
    },
  ];

  // Helper function to get translated time period
  const getTranslatedTimePeriod = (type: string) => {
    return t(`time_periods.${type.toLowerCase()}`);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <Card key={index} className={`bg-gradient-to-br ${metric.gradient} border-0`}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div>
                  <p className={`text-sm font-medium ${metric.textColor}`}>{metric.title}</p>
                  <h3 className={cn("mt-2 text-3xl font-bold text-white", anton.className)}>
                    {metric.value}
                  </h3>
                </div>
                <div className={`p-2 ${metric.iconBg} rounded-xl`}>
                  <metric.icon className={`h-5 w-5 ${metric.iconColor}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card className="overflow-hidden border-0 bg-[#1a1a1a]">
          <CardContent className="p-6">
            <div className="mb-6 flex items-center justify-between">
              <h3 className={cn("text-xl font-semibold text-white", anton.className)}>
                {t("overview.charts.growth_trends")}
              </h3>
              <div className="rounded-full bg-indigo-500/20 px-2.5 py-1 text-xs font-medium text-indigo-400">
                {t("time_periods.last", {
                  period: getTranslatedTimePeriod(timeRange.type),
                })}
              </div>
            </div>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={timeSeriesData.triadsCreated}
                  margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#2a2a2a" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(date) => new Date(date).toLocaleDateString()}
                    stroke="#666"
                    fontSize={12}
                  />
                  <YAxis stroke="#666" fontSize={12} />
                  <Tooltip
                    labelFormatter={(date) => new Date(date).toLocaleDateString()}
                    contentStyle={{
                      backgroundColor: "#1a1a1a",
                      border: "1px solid #2a2a2a",
                      borderRadius: "6px",
                    }}
                    labelStyle={{ color: "#fff" }}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    name={t("overview.charts.triads_created")}
                    stroke="#818cf8"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-0 bg-[#1a1a1a]">
          <CardContent className="p-6">
            <div className="mb-6 flex items-center justify-between">
              <h3 className={cn("text-xl font-semibold text-white", anton.className)}>
                {t("overview.charts.participant_growth")}
              </h3>
              <div className="rounded-full bg-cyan-500/20 px-2.5 py-1 text-xs font-medium text-cyan-400">
                {t("time_periods.last", {
                  period: getTranslatedTimePeriod(timeRange.type),
                })}
              </div>
            </div>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={timeSeriesData.participantsJoined}
                  margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#2a2a2a" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(date) => new Date(date).toLocaleDateString()}
                    stroke="#666"
                    fontSize={12}
                  />
                  <YAxis stroke="#666" fontSize={12} />
                  <Tooltip
                    labelFormatter={(date) => new Date(date).toLocaleDateString()}
                    contentStyle={{
                      backgroundColor: "#1a1a1a",
                      border: "1px solid #2a2a2a",
                      borderRadius: "6px",
                    }}
                    labelStyle={{ color: "#fff" }}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    name={t("overview.charts.participants_joined")}
                    stroke="#22d3ee"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
