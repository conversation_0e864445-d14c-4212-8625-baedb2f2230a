import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import * as Sentry from '@sentry/node';
import { HttpException } from '@nestjs/common';
import { ConfigService } from '@config';

@Injectable()
export class SentryService implements OnModuleInit {
  private readonly logger = new Logger(SentryService.name);
  constructor(private readonly configService: ConfigService) {}

  onModuleInit() {
    const monitoringSecrets = this.configService.monitoringSecrets;
    const dsn = monitoringSecrets.SENTRY_DSN;
    const environment = this.configService.appSecrets.NODE_ENV;

    if (!dsn) {
      this.logger.warn('Sentry DSN not provided, error tracking is disabled');
      return;
    }

    Sentry.init({
      dsn,
      environment,
      tracesSampleRate: 1.0,
      maxValueLength: 1000,
      beforeSend(event) {
        if (event.type === 'error' || event.type === 'exception') {
          return SentryService.sanitizeEvent(event) as Sentry.ErrorEvent;
        }
        return event;
      },
    });

    this.logger.log(`Sentry initialized in ${environment} environment`);
  }

  /**
   * Captures an exception for Sentry
   * @param exception - The exception to capture
   * @param context - Additional context to attach to the exception
   */
  captureException(exception: Error, context?: Record<string, any>) {
    if (exception instanceof HttpException && exception.getStatus() < 500) {
      return;
    }

    return Sentry.captureException(exception, {
      contexts: context ? { additional: context } : undefined,
    });
  }

  /**
   * Captures a message for Sentry
   * @param message - The message to capture
   * @param level - The severity level of the message
   * @param context - Additional context to attach to the message
   */
  captureMessage(
    message: string,
    level: Sentry.SeverityLevel = 'info',
    context?: Record<string, any>,
  ) {
    return Sentry.captureMessage(message, {
      level,
      contexts: context ? { additional: context } : undefined,
    });
  }

  /**
   * Captures a breadcrumb for Sentry
   * @param breadcrumb - The breadcrumb to capture
   */
  addBreadcrumb(breadcrumb: Sentry.Breadcrumb) {
    Sentry.addBreadcrumb(breadcrumb);
  }

  /**
   * Sets a tag for Sentry
   * @param key - The key of the tag
   * @param value - The value of the tag
   */
  setTag(key: string, value: string) {
    Sentry.setTag(key, value);
  }

  /**
   * Sets the user context for Sentry
   * @param user - The user object containing the user's ID and optionally their email and username
   */
  setUser(user: { id: string; email?: string; username?: string }) {
    Sentry.setUser({
      id: user.id,
      username: user.username,
      email: user.email,
    });
  }

  /**
   * Sanitizes the event data by removing sensitive information
   * @param event - The event to sanitize
   * @returns The sanitized event
   */
  private static sanitizeEvent(event: Sentry.Event): Sentry.Event | null {
    if (!event) return null;

    const sanitizedEvent = JSON.parse(JSON.stringify(event));

    if (sanitizedEvent.request?.data) {
      const sensitiveFields = ['password', 'token', 'secret'];

      try {
        let data = sanitizedEvent.request.data;

        if (typeof data === 'string') {
          try {
            data = JSON.parse(data);
          } catch {
            sensitiveFields.forEach((field) => {
              data = data.replace(
                new RegExp(`"${field}"\\s*:\\s*"[^"]*"`, 'g'),
                `"${field}":"[REDACTED]"`,
              );
            });
            sanitizedEvent.request.data = data;
            return sanitizedEvent;
          }
        }

        if (typeof data === 'object' && data !== null) {
          for (const field of sensitiveFields) {
            if (data[field]) {
              data[field] = '[REDACTED]';
            }
          }
          sanitizedEvent.request.data = data;
        }
      } catch (e) {
        sanitizedEvent.request.data = '[REDACTED]';
      }
    }

    if (sanitizedEvent.request?.headers) {
      const sensitiveHeaders = [
        'authorization',
        'cookie',
        'x-api-key',
        'x-auth-token',
      ];

      for (const header of sensitiveHeaders) {
        if (sanitizedEvent.request.headers[header]) {
          sanitizedEvent.request.headers[header] = '[REDACTED]';
        }
      }
    }

    return sanitizedEvent;
  }
}
