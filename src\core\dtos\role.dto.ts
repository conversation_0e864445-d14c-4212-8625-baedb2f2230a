import { createZodDto } from 'nestjs-zod';
import {
  BulkUpdateRoleSchema,
  CreateRoleSchema,
  FilterRoleSchema,
  GetRoleByIdSchema,
  UpdateRoleSchema,
} from '@px-shared-account/hermes';

export class CreateRoleDto extends createZodDto(CreateRoleSchema) {}
export class UpdateRoleDto extends createZodDto(UpdateRoleSchema) {}
export class GetRoleByIdDto extends createZodDto(GetRoleByIdSchema) {}
export class FilterRoleDto extends createZodDto(FilterRoleSchema) {}
export class BulkUpdateRoleDto extends createZodDto(BulkUpdateRoleSchema) {}
