import { Injectable } from '@nestjs/common';
import { WhatsNewVersionEntity, WhatsNewReactionEntity } from '@entities';
import {
    IWhatsNewVersion,
    ICreateWhatsNewPayload,
    IUpdateWhatsNewPayload,
} from '@px-shared-account/hermes';

@Injectable()
export class WhatsNewFactory {
    /**
     * Convert database entity to API version format
     * @param entity Database entity
     * @returns API version object
     */
    toApiVersion(entity: WhatsNewVersionEntity): IWhatsNewVersion {
        return {
            id: entity.id,
            versionCode: entity.versionCode,
            date: entity.date.toISOString(),
            titleEn: entity.titleEn,
            titleFr: entity.titleFr,
            descriptionEn: entity.descriptionEn,
            descriptionFr: entity.descriptionFr,
            addedEn: entity.addedEn || [],
            addedFr: entity.addedFr || [],
            changedEn: entity.changedEn || [],
            changedFr: entity.changedFr || [],
            fixedEn: entity.fixedEn || [],
            fixedFr: entity.fixedFr || [],
            removedEn: entity.removedEn || [],
            removedFr: entity.removedFr || [],
            thumbnails: entity.thumbnails || [],
            createdAt: entity.createdAt?.toISOString(),
            updatedAt: entity.updatedAt?.toISOString(),
        };
    }

    /**
     * Generate entity from create payload
     * @param payload Create payload
     * @returns Entity for database
     */
    generateFromPayload(payload: ICreateWhatsNewPayload): WhatsNewVersionEntity {
        const whatsNewVersion = new WhatsNewVersionEntity();

        whatsNewVersion.versionCode = payload.versionCode;
        whatsNewVersion.date = new Date(payload.date);
        whatsNewVersion.titleEn = payload.titleEn;
        whatsNewVersion.titleFr = payload.titleFr;
        whatsNewVersion.descriptionEn = payload.descriptionEn || '';
        whatsNewVersion.descriptionFr = payload.descriptionFr || '';
        whatsNewVersion.addedEn = payload.addedEn || [];
        whatsNewVersion.addedFr = payload.addedFr || [];
        whatsNewVersion.changedEn = payload.changedEn || [];
        whatsNewVersion.changedFr = payload.changedFr || [];
        whatsNewVersion.fixedEn = payload.fixedEn || [];
        whatsNewVersion.fixedFr = payload.fixedFr || [];
        whatsNewVersion.removedEn = payload.removedEn || [];
        whatsNewVersion.removedFr = payload.removedFr || [];
        whatsNewVersion.thumbnails = payload.thumbnails || [];

        return whatsNewVersion;
    }

    /**
     * Generate update data from update payload
     * @param payload Update payload
     * @returns Partial entity for update
     */
    generateUpdateData(payload: IUpdateWhatsNewPayload): Partial<WhatsNewVersionEntity> {
        const updateData: Partial<WhatsNewVersionEntity> = {};

        if (payload.versionCode !== undefined) updateData.versionCode = payload.versionCode;
        if (payload.date !== undefined) updateData.date = new Date(payload.date);
        if (payload.titleEn !== undefined) updateData.titleEn = payload.titleEn;
        if (payload.titleFr !== undefined) updateData.titleFr = payload.titleFr;
        if (payload.descriptionEn !== undefined) updateData.descriptionEn = payload.descriptionEn;
        if (payload.descriptionFr !== undefined) updateData.descriptionFr = payload.descriptionFr;
        if (payload.addedEn !== undefined) updateData.addedEn = payload.addedEn;
        if (payload.addedFr !== undefined) updateData.addedFr = payload.addedFr;
        if (payload.changedEn !== undefined) updateData.changedEn = payload.changedEn;
        if (payload.changedFr !== undefined) updateData.changedFr = payload.changedFr;
        if (payload.fixedEn !== undefined) updateData.fixedEn = payload.fixedEn;
        if (payload.fixedFr !== undefined) updateData.fixedFr = payload.fixedFr;
        if (payload.removedEn !== undefined) updateData.removedEn = payload.removedEn;
        if (payload.removedFr !== undefined) updateData.removedFr = payload.removedFr;
        if (payload.thumbnails !== undefined) updateData.thumbnails = payload.thumbnails;

        return updateData;
    }

    /**
     * Generate reaction entity
     * @param data Reaction data
     * @returns Reaction entity for database
     */
    generateReaction(data: {
        userId: string;
        versionId: number;
        emoji: string;
    }): WhatsNewReactionEntity {
        const whatsNewReaction = new WhatsNewReactionEntity();

        whatsNewReaction.userId = data.userId;
        whatsNewReaction.versionId = data.versionId;
        whatsNewReaction.emoji = data.emoji;

        return whatsNewReaction;
    }
} 