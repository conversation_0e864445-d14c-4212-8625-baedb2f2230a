"use client";

import { routing } from "@/i18n/routing";

export const isActiveLink = (pathname: string, href: string) => {
  if (!href) return false;
  // Get the localized path from our routing configuration
  const routeConfig = routing.pathnames[href as keyof typeof routing.pathnames];
  if (!routeConfig) return false;

  // If it's a string, it's not localized
  if (typeof routeConfig === "string") {
    return pathname === routeConfig;
  }

  // If it's an object, it's localized
  const retrievedPath = Object.values(routeConfig).find((path) => path === pathname);

  // If the path is found, return true, otherwise return false
  return retrievedPath ? pathname === retrievedPath : false;
};
