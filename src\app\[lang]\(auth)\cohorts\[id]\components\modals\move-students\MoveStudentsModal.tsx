"use client";

import { useTranslations } from "next-intl";
import { useState, useMemo } from "react";
import { IUserBase } from "@px-shared-account/hermes";
import { useCohortList, useCohortStudents, useCohortDetails } from "@/services/cohort";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface MoveStudentsModalProps {
  sourceCohortId: number;
  students: IUserBase[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete?: () => void;
}

export function MoveStudentsModal({
  sourceCohortId,
  students,
  isOpen,
  onOpenChange,
  onComplete,
}: MoveStudentsModalProps) {
  const t = useTranslations("cohorts");
  const { toast } = useToast();
  const [targetCohortId, setTargetCohortId] = useState<number | null>(null);
  const [isMoving, setIsMoving] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Get list of cohorts to move students to
  const { data: cohortsResponse, isLoading: isLoadingCohorts } = useCohortList({
    limit: 100, // Fetch a reasonable number of cohorts
  });

  // Get target cohort details when selected
  const { data: targetCohortData, isLoading: isLoadingTargetCohort } =
    useCohortDetails(targetCohortId);

  const cohorts = cohortsResponse?.data || [];
  // Filter out the current cohort
  const availableCohorts = cohorts.filter((cohort) => cohort.id !== sourceCohortId);

  // Get hooks for adding/removing students
  const { removeStudents } = useCohortStudents(sourceCohortId);
  const { addStudents } = useCohortStudents();

  // Check if adding students would exceed the target cohort's maximum participant limit
  const canAddStudents = useMemo(() => {
    if (!targetCohortData) return true;

    const currentStudentCount = targetCohortData.students?.length || 0;
    const maxParticipants = targetCohortData.maxParticipants || 0;
    const remainingSlots = maxParticipants - currentStudentCount;

    return students.length <= remainingSlots;
  }, [targetCohortData, students.length]);

  // Update validation error when target cohort changes
  useMemo(() => {
    if (!targetCohortData) {
      setValidationError(null);
      return;
    }

    const currentStudentCount = targetCohortData.students?.length || 0;
    const maxParticipants = targetCohortData.maxParticipants || 0;
    const remainingSlots = maxParticipants - currentStudentCount;

    if (students.length > remainingSlots) {
      setValidationError(
        t("students.max_participants_exceeded", {
          max: maxParticipants,
          current: currentStudentCount,
          needed: students.length,
          remaining: remainingSlots,
        }),
      );
    } else {
      setValidationError(null);
    }
  }, [targetCohortData, students.length, t]);

  const handleMoveStudents = async () => {
    if (!targetCohortId) {
      toast({
        title: "Error",
        description: t("students.select_target_cohort"),
        variant: "destructive",
      });
      return;
    }

    // Double-check validation on the client side before sending the request
    if (!canAddStudents) {
      toast({
        title: t("students.error_title"),
        description: t("students.max_participants_exceeded", {
          max: targetCohortData?.maxParticipants,
        }),
        variant: "destructive",
      });
      return;
    }

    try {
      setIsMoving(true);
      const studentIds = students.map((student) => student.id);

      // First add students to the target cohort
      await addStudents(targetCohortId, studentIds);

      // Then remove them from the source cohort
      await removeStudents(sourceCohortId, studentIds);

      toast({
        title: t("students.moved_title"),
        description: t("students.moved_description"),
      });

      if (onComplete) {
        onComplete();
      }

      onOpenChange(false);
    } catch (error) {
      console.error("[MoveStudentsModal] Error moving students:", error);
      toast({
        title: "Error",
        description: t("students.move_error"),
        variant: "destructive",
      });
    } finally {
      setIsMoving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("students.move_title")}</DialogTitle>
          <DialogDescription>
            {students.length === 1
              ? t("students.move_description_single", {
                  name: `${students[0].firstName || ""} ${students[0].lastName || ""}`,
                })
              : t("students.move_description", { count: students.length })}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="targetCohort" className="col-span-4">
              {t("students.target_cohort")}
            </Label>
            <Select
              value={targetCohortId?.toString()}
              onValueChange={(value) => setTargetCohortId(Number(value))}
              disabled={isMoving || isLoadingCohorts || availableCohorts.length === 0}
            >
              <SelectTrigger className="col-span-4">
                <SelectValue placeholder={t("students.select_cohort_placeholder")} />
              </SelectTrigger>
              <SelectContent>
                {availableCohorts.map((cohort: any) => (
                  <SelectItem key={cohort.id} value={cohort.id}>
                    {cohort.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {validationError && (
            <Alert variant="destructive" className="mt-2">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}

          {targetCohortData && !validationError && (
            <div className="mt-2 text-sm text-muted-foreground">
              {t("students.current_capacity", {
                current: targetCohortData.students?.length || 0,
                max: targetCohortData.maxParticipants,
                remaining:
                  (targetCohortData.maxParticipants || 0) -
                  (targetCohortData.students?.length || 0),
              })}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isMoving}>
            {t("students.cancel")}
          </Button>
          <Button
            onClick={handleMoveStudents}
            disabled={!targetCohortId || isMoving || !!validationError}
            variant="secondary"
            className="hover:bg-accent"
          >
            {isMoving ? t("students.moving") : t("students.move_confirm")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
