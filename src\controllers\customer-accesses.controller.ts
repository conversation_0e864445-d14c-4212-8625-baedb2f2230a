import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { CustomerAccessUseCases, ProductDeliveryService } from '@useCases';
import { JwtPermissionsGuard } from '@auth';

@ApiTags('customer-accesses')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('customer-accesses')
export class CustomerAccessesController {
  constructor(
    private readonly customerAccesses: CustomerAccessUseCases,
    private readonly productDeliveryService: ProductDeliveryService,
  ) {}

  @ApiOperation({ summary: 'Get by customer email' })
  @Get('/customer/email')
  async listByCustomerEmail(@Query('email') email: string) {
    return this.customerAccesses.listForSubscriptionsByCustomerEmail(email);
  }

  @ApiOperation({ summary: 'Get by customer id' })
  @Get('/customer/:customerId')
  async listByCustomerId(customerId: string) {
    return this.customerAccesses.listByCustomerId(Number(customerId));
  }

  @ApiOperation({ summary: 'Get by subscription id' })
  @Get('/subscription/:subscriptionId')
  async listBySubscriptionId(
    @Param('subscriptionId') subscriptionId: string,
    @Query('limit') limit = 500,
    @Query('page') page = 1,
    @Query('sortBy') sortBy: 'ASC' | 'DESC',
  ) {
    return this.customerAccesses.listBySubscriptionId(
      Number(subscriptionId),
      limit,
      page,
      sortBy,
    );
  }

  @ApiOperation({ summary: 'Get by offer id' })
  @Get('/offer/:offerId')
  async listByOfferId(
    @Param('offerId') offerId: string,
    @Query('limit') limit = 500,
    @Query('page') page = 1,
    @Query('sortBy') sortBy: 'ASC' | 'DESC',
  ) {
    return this.customerAccesses.listByOfferId(
      Number(offerId),
      limit,
      page,
      sortBy,
    );
  }

  @ApiOperation({ summary: 'Get by access id' })
  @Get('/:accessId')
  async getByAccessId(@Param('accessId') accessId: string) {
    return this.customerAccesses.getById(Number(accessId));
  }

  @ApiOperation({ summary: 'Grant a specific access' })
  @Post('/:accessId/grant')
  async grantAccess(@Param('accessId') accessId: number) {
    return this.customerAccesses.grantAccess(accessId);
  }

  @ApiOperation({ summary: 'Revoke a specific access' })
  @Post('/:accessId/revoke')
  async revokeAccess(@Param('accessId') accessId: number) {
    return this.customerAccesses.revokeAccess(accessId);
  }

  @ApiOperation({ summary: 'Retry granting a failed access' })
  @Post('/:accessId/retry')
  async retryAccess(@Param('accessId') accessId: number) {
    return this.customerAccesses.grantAccess(accessId);
  }

  @ApiOperation({ summary: 'Suspend access of a customer' })
  @Post('/:accessId/suspend')
  async suspendAccess(@Param('accessId') accessId: number) {
    return this.productDeliveryService.suspendAccess(accessId);
  }

  @ApiOperation({ summary: 'unSuspend access of a customer' })
  @Post('/:accessId/unsuspend')
  async unSuspendAccess(@Param('accessId') accessId: number) {
    return this.productDeliveryService.unSuspendAccess(accessId);
  }
}
