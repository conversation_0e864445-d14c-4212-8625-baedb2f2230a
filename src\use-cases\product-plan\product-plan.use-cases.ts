import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { FindOptionsWhere } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { CreatePlanWithPriceDTO } from '@dtos';
import { ProductPlanFactory } from '.';
import { ProductPlanPriceUseCases } from '../product-plan-price';
import { Product, ProductPlan, ProductPlanPrice } from '@entities';
import { ChargebeeBillingService } from '@services/billing';
import { UpdateResultWithItemInfo } from '@types';
import { HubspotService } from '@services/crm';
import { IDataServices } from '@abstracts';

@Injectable()
export class ProductPlanUseCases {
  constructor(
    private readonly dataServices: IDataServices,
    private readonly chargeBeeService: ChargebeeBillingService,
    private readonly productPlanFactory: ProductPlanFactory,
    private readonly productPlanPriceUseCases: ProductPlanPriceUseCases,
    private readonly hubspotService: HubspotService,
  ) {}

  /**
   * Creates a product plan
   * @param planInfo Info of the product plan
   */
  async create(
    planInfo: ProductPlan,
    productFamily: string,
    productLine: string,
  ): Promise<ProductPlan> {
    try {
      await this.chargeBeeService.createAddon(
        planInfo,
        productFamily,
        productLine,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to create product plan.',
        HttpStatus.PRECONDITION_FAILED,
      );
    }
    const createdPlan = await this.dataServices.productPlan.create(planInfo);
    return createdPlan;
  }

  /**
   * Creates a plan with associated price and syncs the new plan with Chargebee and Hubspot
   * @returns Created Plan and Price
   */
  async createWithPrice(
    planAndPriceInfo: CreatePlanWithPriceDTO,
    product: Product,
  ): Promise<[ProductPlan, ProductPlanPrice]> {
    let createdPlan: ProductPlan;
    let createdPrice: ProductPlanPrice;
    try {
      const { productFamily, productLine, taxProfile, ...planInfo } =
        await this.productPlanFactory.generate(planAndPriceInfo.planInfo);

      createdPlan = await this.create(planInfo, productFamily, productLine);
      createdPrice = await this.productPlanPriceUseCases.create(
        planAndPriceInfo.priceInfo,
        createdPlan,
        taxProfile,
      );
      const hubspotProdId = await this.hubspotService.createProduct(
        createdPlan,
        createdPrice,
        product,
        productLine,
        productFamily,
      );
      if (!hubspotProdId) {
        await this.revertPlanAndPriceCreation(createdPlan, createdPrice);
        throw new HttpException(
          'Failed to sync with Hubspot.',
          HttpStatus.PRECONDITION_FAILED,
        );
      }
      const updatedPlan = await this.updateById(createdPlan.id, {
        crmId: hubspotProdId,
      });
      if (updatedPlan?.updatedItem?.crmId) {
        createdPlan.crmId = updatedPlan.updatedItem.crmId;
      }
      return [createdPlan, createdPrice];
    } catch (error) {
      if (!!createdPrice) {
        await this.productPlanPriceUseCases.delete(
          createdPrice.chargebeeId,
          createdPrice.id,
        );
      }
      if (!!createdPlan) {
        await this.permenantlyDelete(createdPlan.chargebeeId, createdPlan.id);
      }
      throw new HttpException(
        error.message || 'Failed to create product plan with price.',
        HttpStatus.PRECONDITION_FAILED,
      );
    }
  }

  /**
   * Returns all product plans for specified product ID
   * @returns Product plan records from database
   */
  async getAll(
    productId: number,
    limit: number,
    page: number,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: ProductPlan[]; total: number }> {
    return this.dataServices.productPlan.getAllBy(
      {
        product: {
          id: productId,
        },
      },
      limit,
      page,
      {
        createdAt: orderBy,
      },
    );
  }

  /**
   * Updates a product plan
   * @param name Name of the plan to update
   * @param updates Updates for the product plan
   */
  async updateById(
    id: number,
    updates: QueryDeepPartialEntity<ProductPlan>,
  ): Promise<UpdateResultWithItemInfo<ProductPlan>> {
    const { updatedItem, success } =
      await this.dataServices.productPlan.updateAndReturnItem(
        'id',
        id.toString(),
        updates,
      );

    await this.chargeBeeService.updateAddon(updatedItem.chargebeeId, updates);
    return {
      updatedItem,
      success,
    };
  }

  /**
   * Syncs changes to a plan's product's `externalName` & `description`
   * @param productId ID of the product to which plans belong
   * @param externalName New external name for the plan
   * @param description New description for the plan
   */
  async syncProductNameChanges(
    productId: number,
    externalName: string,
    description: string,
  ): Promise<void> {
    const plansToUpdate = await this.getAll(productId, 100, 1);
    console.log('Plans to update: ', plansToUpdate.total);
    console.log('Plans: ', plansToUpdate.items);
    for (const plan of plansToUpdate.items) {
      console.log('updating plan with id: ', plan.id);
      await this.updateById(plan.id, {
        externalName,
        description,
      });
    }
  }

  /**
   * Retrieves a product plan matching provided filters
   * @param filter Properties of plan to search for
   */
  async searchOne(filter: FindOptionsWhere<ProductPlan>): Promise<ProductPlan> {
    return this.dataServices.productPlan.getOneBy(filter);
  }

  /**
   * Retrieves a product plan matching provided `id``
   * @param id `id` of plan to fetch
   */
  async getOne(id: number): Promise<ProductPlan> {
    return this.dataServices.productPlan.getOneBy({
      id,
    });
  }

  /**
   * Retrieves all product plans matching provided filters
   * @param filter Properties of plan to search for
   */
  async searchAll(
    filter: FindOptionsWhere<ProductPlan>,
    limit = 100,
    page = 1,
    orderBy?: 'DESC' | 'ASC',
  ): Promise<{ items: ProductPlan[]; total: number }> {
    return this.dataServices.productPlan.getAllBy(filter, limit, page, {
      createdAt: orderBy,
    });
  }

  /**
   * Gets the product and attached plan price info for an add-on
   * @param chargebeeId Chargebee id of the add-on
   * @returns A promise that resolves to a `ProductPlan` object
   */
  async getProductInfoForForeverOffer(
    chargebeeId: string,
  ): Promise<ProductPlan> {
    const queryBuilder = this.dataServices.productPlan
      .getRepository()
      .createQueryBuilder('plan');
    return queryBuilder
      .where('plan.chargebeeId = :chargebeeId', { chargebeeId })
      .leftJoinAndSelect('plan.product', 'product')
      .leftJoinAndSelect('plan.prices', 'prices')
      .select([
        'plan.id',
        'product.externalName',
        'product.image',
        'prices.chargebeeId',
      ])
      .getOneOrFail();
  }

  /**
   * Deletes a product plan in our database and in Chargebee
   * @param id ID of the plan to delete
   * @param chargebeeId Chargebee ID of the plan to delete
   */
  async permenantlyDelete(chargebeeId: string, id: number): Promise<void> {
    const res = await this.chargeBeeService.deletePlan(chargebeeId);
    console.log('Chargebee delete response: ', res);
    await this.dataServices.productPlan.hardDelete({ id: id });
  }

  /**
   * Reverts creation of a plan and its associated price
   * @param createdPlan Plan to delete
   * @param createdPrice Price to delete
   */
  async revertPlanAndPriceCreation(
    createdPlan: ProductPlan,
    createdPrice: ProductPlanPrice,
  ) {
    await this.productPlanPriceUseCases.delete(
      createdPrice.chargebeeId,
      createdPrice.id,
    );
    await this.permenantlyDelete(createdPlan.chargebeeId, createdPlan.id);
  }
}
