"use client";

import { format } from "date-fns";

import { CalendarEvent } from "@/hooks/store/calendar";

const datetimeFormat = "yyyy/MM/dd HH:mm:ss";

/**
 * Create an event object from the parameters
 * @param params - The parameters for the event
 * @returns The event object
 */
export default function createEvent(params: CalendarEvent): CalendarEvent {
  return {
    // Part 1 - Event Details
    id: params.id,
    title: params.title,
    type: params.type,
    replay: params.replay,
    recurrence: params.recurrence,
    description: params.description,
    begin: format(params.begin, datetimeFormat),
    end: format(params.end, datetimeFormat),
    status: params.status,
    // Part 2 - Speakers
    mainSpeaker: params.mainSpeaker,
    otherSpeakers: params.otherSpeakers,
    technicalSupport: params.technicalSupport,
    // Part 3 - Audience
    cohort: params.cohort,
    participantMin: params.participantMin,
    participantMax: params.participantMax,
    program: params.program,
    course: params.course,
    module: params.module,
    // Part 4 - Comments
    comments: params.comments,
  };
}
