import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  WarrantySectionFactoryService,
  WarrantySectionUseCases,
} from '@useCases';
import { CreateWarrantySectionDTO, UpdateWarrantySectionDTO } from '@dtos';
import { JwtPermissionsGuard } from '@auth';

@ApiTags('checkout')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('checkout')
export class CheckoutPageController {
  constructor(
    private readonly warrantySectionFactory: WarrantySectionFactoryService,
    private readonly warrantySectionUseCases: WarrantySectionUseCases,
  ) {}

  @ApiOperation({ summary: 'Create a warranty section' })
  @Post('/warranty-section')
  async create(@Body() sectionInfo: CreateWarrantySectionDTO) {
    const warrantySection = this.warrantySectionFactory.generate(sectionInfo);
    return this.warrantySectionUseCases.create(warrantySection);
  }

  @ApiOperation({ summary: 'Get a warranty section' })
  @Get('/warranty-section/:id')
  async getOne(@Param('id') id: number) {
    return this.warrantySectionUseCases.getOne(id);
  }

  @ApiOperation({ summary: 'List all warranty sections for a Checkout Page' })
  @Get('/:checkoutId/warranty-section')
  async listByCheckoutId(@Param('checkoutId') checkoutId: number) {
    return this.warrantySectionUseCases.getAllForCheckoutPage(checkoutId);
  }

  @ApiOperation({ summary: 'Update a warranty section' })
  @Patch('/warranty-section/:id')
  async update(
    @Param('id') id: number,
    @Body() updates: UpdateWarrantySectionDTO,
  ) {
    return this.warrantySectionUseCases.update(id, updates);
  }

  @ApiOperation({ summary: 'Delete a warranty section' })
  @Delete('/warranty-section/:id')
  async delete(@Param('id') id: number) {
    return this.warrantySectionUseCases.delete(id);
  }
}
