import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  AttachItemsToDiscountDTO,
  CreateDiscountDTO,
  UpdateDiscountDTO,
} from '@dtos';
import { DiscountFactoryService, DiscountUseCases } from '@useCases';
import { DiscountStatus } from '@enums';
import { JwtPermissionsGuard } from '@auth';

@ApiTags('discounts')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('discounts')
export class DiscountsController {
  constructor(
    private readonly discountUseCases: DiscountUseCases,
    private readonly discountFactory: DiscountFactoryService,
  ) {}

  @ApiOperation({ summary: 'Create a discount' })
  @Post('/')
  async createDiscount(@Body() discountInfo: CreateDiscountDTO) {
    const discount = this.discountFactory.generate(discountInfo);
    return this.discountUseCases.create(discount);
  }

  @ApiOperation({ summary: 'List all discounts' })
  @Get('/')
  async listDiscounts(
    @Query('status') status: DiscountStatus,
    @Query('limit') limit = 500,
    @Query('page') page = 1,
  ) {
    return this.discountUseCases.getAll(status, limit, page);
  }

  @ApiOperation({ summary: 'Disable a discount' })
  @Patch('/:id/disable')
  async disableDiscount(@Param('id') id: number) {
    return this.discountUseCases.disable(id);
  }

  @ApiOperation({ summary: 'Update a discount' })
  @Patch('/:id')
  async updateDiscount(
    @Param('id') id: number,
    @Body() updates: UpdateDiscountDTO,
  ) {
    return this.discountUseCases.update(id, updates);
  }

  @ApiOperation({ summary: 'Attach a discount to plans or offers' })
  @Post('/:id/attach')
  async attachToItems(
    @Param('id') id: number,
    @Body() attachedTo: AttachItemsToDiscountDTO[],
  ) {
    return this.discountUseCases.attachToItems(id, attachedTo);
  }

  @ApiOperation({ summary: 'Search discounts' })
  @Get('/search')
  async searchDiscounts(
    @Query('name') name: string,
    @Query('line') lineId: number,
    @Query('family') familyId: number,
    @Query('product') productId: number,
    @Query('plan') planId: number,
    @Query('status') status: DiscountStatus,
    @Query('limit') limit = 500,
    @Query('page') page = 1,
    @Query('order') orderBy: 'DESC' | 'ASC',
  ) {
    return this.discountUseCases.searchAll(
      name,
      lineId,
      familyId,
      productId,
      planId,
      status,
      limit,
      page,
      orderBy,
    );
  }

  @ApiOperation({ summary: 'Get a discount' })
  @Get('/:id')
  async getDiscount(@Param('id') id: number) {
    return this.discountUseCases.getOne(id);
  }
}
