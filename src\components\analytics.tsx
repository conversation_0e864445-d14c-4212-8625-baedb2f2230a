"use client";

import { useEffect, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { identifyUser, resetAnalytics, trackEvent, trackPage } from "@/lib/segment";
import { routing } from "@/i18n/routing";
import { useUser } from "@clerk/nextjs";

export default function Analytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { user, isSignedIn, isLoaded } = useUser();
  const hasIdentifiedRef = useRef(false);

  // 🔐 Identify
  useEffect(() => {
    if (!isLoaded) return;

    if (user && isSignedIn && !hasIdentifiedRef.current) {
      identifyUser(user.id, {
        email: user.primaryEmailAddress?.emailAddress,
        firstName: user.firstName,
      });
      hasIdentifiedRef.current = true;
    }

    if (!isSignedIn && hasIdentifiedRef.current) {
      trackEvent("User Logged Out");
      resetAnalytics();
      hasIdentifiedRef.current = false;
    }
  }, [user, isSignedIn, isLoaded]);

  // 📈 Track page views
  useEffect(() => {
    if (!pathname) return;

    // Get current locale
    const locale = pathname.startsWith("/en") ? "en" : "fr";

    // Strip locale prefix from pathname if present
    const cleanPathname = pathname.replace(/^\/(fr|en)/, "") || "/";

    // Find canonical path (e.g., "/calendar")
    const canonicalPath =
      Object.entries(routing.pathnames).find(([_, localized]) => {
        if (typeof localized === "string") return localized === cleanPathname;
        return Object.values(localized as Record<string, string>).includes(cleanPathname);
      })?.[0] || cleanPathname;

    trackPage({
      path: canonicalPath, // normalized route, e.g., "/calendar"
      locale,
      fullPath: pathname + (searchParams?.toString() ? `?${searchParams}` : ""),
    });
  }, [pathname, searchParams]);

  return null;
}
