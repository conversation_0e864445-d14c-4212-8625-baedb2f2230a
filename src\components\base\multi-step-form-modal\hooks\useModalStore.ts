import { create } from "zustand";
import { ModalConfig, ModalState, ModalStore } from "../types";

const initialModalState: ModalState = {
  isOpen: false,
  currentStep: 0,
  formData: {},
  errors: {},
  loading: {
    isSubmitting: false,
    isLoadingStep: false,
  },
  isDirty: false,
  hasUnsavedChanges: false,
};

const createInitialState = (config: ModalConfig): ModalState => ({
  ...initialModalState,
  formData: config.initialData || {},
});

export const useModalStore = create<ModalStore>((set) => ({
  modals: {},

  registerModal: (key: string, config: ModalConfig) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: createInitialState(config),
      },
    })),

  unregisterModal: (key: string) =>
    set((state) => {
      const { [key]: _, ...rest } = state.modals;
      return { modals: rest };
    }),

  setModalData: (key: string, data: Partial<ModalState>) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          ...data,
        },
      },
    })),

  openModal: (key: string) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          isOpen: true,
        },
      },
    })),

  closeModal: (key: string) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          isOpen: false,
        },
      },
    })),

  nextStep: (key: string) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          currentStep: state.modals[key].currentStep + 1,
        },
      },
    })),

  previousStep: (key: string) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          currentStep: Math.max(0, state.modals[key].currentStep - 1),
        },
      },
    })),

  setStep: (key: string, step: number) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          currentStep: step,
        },
      },
    })),

  setFormData: (key: string, data: Record<string, any>, setDirtyState: boolean = true) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          formData: {
            ...state.modals[key].formData,
            ...data,
          },
          isDirty: setDirtyState ? true : state.modals[key].isDirty,
        },
      },
    })),

  setErrors: (key: string, errors: Record<string, string>) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          errors,
        },
      },
    })),

  setLoading: (key: string, loading: Partial<ModalState["loading"]>) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          loading: {
            ...state.modals[key].loading,
            ...loading,
          },
        },
      },
    })),

  resetForm: (key: string, config: ModalConfig) =>
    set((state) => {
      // Create empty values for all fields
      const emptyValues: Record<string, any> = {};
      config.steps.forEach((step) => {
        step.fields.forEach((field) => {
          emptyValues[field.id] = field.type === "switch" ? false : "";
        });
      });

      return {
        modals: {
          ...state.modals,
          [key]: {
            ...initialModalState,
            isOpen: state.modals[key].isOpen,
            formData: emptyValues,
          },
        },
      };
    }),

  setDirty: (key: string, isDirty: boolean) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          isDirty,
        },
      },
    })),

  setUnsavedChanges: (key: string, hasUnsavedChanges: boolean) =>
    set((state) => ({
      modals: {
        ...state.modals,
        [key]: {
          ...state.modals[key],
          hasUnsavedChanges,
        },
      },
    })),
}));
