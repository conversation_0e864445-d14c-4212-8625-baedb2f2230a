import Redis from "ioredis";

const redisClient = new Redis(process.env.KV_URL!, {
  connectTimeout: 10000,
});

const permissionsKey = "permissions";

export async function GET() {
  const permissions = await redisClient.hgetall(permissionsKey);

  return new Response(JSON.stringify(permissions), {
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "no-cache, no-transform",
    },
  });
}
