"use client";

import { useState } from "react";
import { Input } from "@/components/base/input";
import { Button } from "@/components/base/button";
import { Search, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { BaseProps } from "@/types";

export interface SearchBarProps extends BaseProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  fullWidth?: boolean;
}

export const SearchBar = ({
  className,
  placeholder = "Rechercher...",
  value: controlledValue,
  onChange,
  onSearch,
  fullWidth,
}: SearchBarProps) => {
  // Internal state for uncontrolled mode
  const [internalValue, setInternalValue] = useState("");

  // Use controlled value if provided, otherwise use internal state
  const value = controlledValue ?? internalValue;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    // Update internal state if uncontrolled
    if (controlledValue === undefined) {
      setInternalValue(newValue);
    }
    // Call onChange handler if provided
    onChange?.(newValue);
  };

  const handleClear = () => {
    // Update internal state if uncontrolled
    if (controlledValue === undefined) {
      setInternalValue("");
    }
    // Call onChange handler if provided
    onChange?.("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && onSearch) {
      onSearch(value);
    }
  };

  return (
    <div className={cn("relative flex items-center", fullWidth && "w-full", className)}>
      {/* Search Icon */}
      <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />

      {/* Input */}
      <Input
        type="search"
        className={cn("pl-9", value && "pr-9")}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        fullWidth={fullWidth}
      />

      {/* Clear Button */}
      {value && (
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-1 h-7 w-7 hover:bg-transparent"
          onClick={handleClear}
        >
          <X className="h-4 w-4 text-muted-foreground" />
        </Button>
      )}
    </div>
  );
};

SearchBar.displayName = "SearchBar";
