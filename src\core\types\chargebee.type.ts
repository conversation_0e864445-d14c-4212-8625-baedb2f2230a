import {
  Coupon,
  CreditNote,
  Customer,
  Invoice,
  Subscription,
  Transaction,
} from 'chargebee-typescript/lib/resources';
import {
  DiscountApplication,
  DiscountDurationType,
  DiscountType,
} from '@enums';

export interface ChargeBeeCoupon {
  id: string;
  name: string;
  invoice_name: string;
  discount_type: DiscountType;
  discount_amount: number;
  duration_type: DiscountDurationType;
  duration_month: number;
  valid_till: number;
  max_redemptions: number;
  status: 'active' | 'expired' | 'archived' | 'deleted';
  apply_discount_on: 'not_applicable';
  apply_on: DiscountApplication;
  created_at: number;
  updated_at: number;
  period: number;
  period_unit: 'month';
  resource_version: number;
  object: 'coupon';
  redemptions: number;
  currency_code: 'EUR';
  item_constraints: {
    item_type: 'plan' | 'addon' | 'charge';
    constraint: 'none' | 'specific' | 'all';
    item_price_ids?: string[];
  }[];
}

export interface ChargeBeeWebhookPayload {
  id: string;
  occurred_at: number;
  source: 'api';
  user: string;
  object: 'event';
  api_version: 'v2';
  content: {
    coupon?: Coupon;
    transaction?: Transaction;
    invoice?: Invoice;
    credit_note?: CreditNote;
    subscription: Subscription;
    customer?: Customer;
  };
  event_type: ChargeBeeEventType;
}

export type ChargeBeeEventType =
  | 'coupon_updated'
  | 'transaction_created'
  | 'transaction_updated'
  | 'invoice_generated'
  | 'invoice_updated'
  | 'credit_note_created'
  | 'credit_note_updated'
  | 'payment_refunded'
  | 'payment_succeeded'
  | 'payment_failed'
  | 'subscription_created'
  | 'subscription_changed'
  | 'subscription_renewed'
  | 'subscription_cancelled';

export type SubscriptionDownsellParams = {
  id?: string;
  business_entity_id?: string;
  billing_cycles: number;
  start_date: number;
  auto_collection?: 'on' | 'off';
  meta_data?: any;
  cf_os_offer_id: string;
  subscription_items: SubsciptionDownsellItem[];
};

type SubsciptionDownsellItem = {
  item_price_id: string;
  quantity?: number;
  billing_cycles?: number;
  unit_price?: number;
};
