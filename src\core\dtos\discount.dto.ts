import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsNumber,
  IsOptional,
  IsNotEmpty,
  IsDateString,
  IsEmpty,
  IsArray,
  ArrayMinSize,
} from 'class-validator';
import {
  DiscountType,
  DiscountStatus,
  DiscountDurationType,
  DiscountDurationPeriodUnit,
  DiscountApplication,
  DiscountAttachableItems,
} from '@enums';

export class CreateDiscountDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiPropertyOptional({ enum: DiscountStatus })
  @IsOptional()
  @IsEnum(DiscountStatus)
  status?: DiscountStatus;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  invoiceName?: string;

  @ApiProperty({ enum: DiscountType })
  @IsEnum(DiscountType)
  @IsNotEmpty()
  type: DiscountType;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({ enum: DiscountApplication })
  @IsNotEmpty()
  @IsEnum(DiscountApplication)
  applyOn: DiscountApplication;

  @ApiPropertyOptional({ enum: DiscountDurationType })
  @IsEnum(DiscountDurationType)
  @IsOptional()
  durationType: DiscountDurationType;

  @ApiPropertyOptional({ enum: DiscountDurationPeriodUnit })
  @IsOptional()
  @IsEnum(DiscountDurationPeriodUnit)
  durationPeriodUnit?: DiscountDurationPeriodUnit;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  durationPeriodAmount?: number;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  validUntil?: Date;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  maxRedemptions?: number;
}

export class UpdateDiscountDTO extends PartialType(CreateDiscountDTO) {
  @ApiProperty()
  @IsEmpty({ message: "Code for a discount can't be updated" })
  code: string;
}

export class AttachItemsToDiscountDTO {
  @ApiProperty({ enum: DiscountAttachableItems })
  @IsEnum(DiscountAttachableItems)
  @IsNotEmpty()
  itemType: DiscountAttachableItems;

  @ApiProperty({
    description:
      'Contains `id`s of items to which this discount applies OR the values (`all` | `none`)',
  })
  @IsArray()
  @ArrayMinSize(1)
  itemIds: string[];
}
