"use client";

import { ICreateR<PERSON>, RoleGroup } from "@px-shared-account/hermes";
import { useTranslations } from "next-intl";
import { useMemo, useState } from "react";
import { z } from "zod";

import { ModalConfig, MultiStepFormModal } from "@/components/base/multi-step-form-modal";
import { useToast } from "@/hooks/use-toast";
import { useCreateRole } from "@/services/role";

type CreateRoleModalProps = {
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  onComplete: () => void;
};

export default function CreateRoleModal({
  isModalOpen,
  setIsModalOpen,
  onComplete,
}: CreateRoleModalProps) {
  const t = useTranslations("gestion.users.roles");
  const commonT = useTranslations("common");
  const { toast } = useToast();
  const { create, isLoading } = useCreateRole();
  const [permissions, setPermissions] = useState<ICreateRole["permissions"]>([]);
  const config: ModalConfig = useMemo(
    () => ({
      key: "create-role",
      title: t("create"),
      confirmClose: true,
      confirmReset: true,
      initialData: {
        name: "",
        group: "",
        permissions: [],
      },
      steps: [
        {
          id: "basic",
          title: t("name"),
          fields: [
            {
              id: "name",
              type: "text",
              label: t("name"),
              placeholder: t("name_description"),
              required: true,
              validation: z.string().min(3, { message: t("name_validation") }),
            },
            {
              id: "group",
              type: "select",
              label: t("group"),
              placeholder: t("group_description"),
              required: true,
              options: Object.keys(RoleGroup).map((group) => ({
                label: group,
                value: group,
              })),
            },
            {
              id: "permissions",
              type: "permissions",
              label: t("permissions"),
              required: true,
              onChange: (permissions) => {
                setPermissions(permissions);
              },
            },
          ],
        },
      ],
    }),
    [t],
  );

  return (
    <>
      {isLoading && <div className="my-4 text-center">{commonT("table.loading")}</div>}
      <MultiStepFormModal
        config={config}
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        events={{
          onComplete: async (data: ICreateRole) => {
            try {
              await create({
                name: data.name,
                group: data.group,
                permissions: permissions,
              });
              toast({
                title: t("created"),
                description: t("created-description"),
              });
              onComplete();
              setIsModalOpen(false);
            } catch (error) {
              console.error("Failed to create role:", error);
            }
          },
        }}
      />
    </>
  );
}
