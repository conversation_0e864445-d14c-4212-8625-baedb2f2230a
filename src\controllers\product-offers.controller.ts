import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  CreateOfferWithCheckoutDTO,
  UpdateOfferWithCheckoutDTO,
  CreateOfferConfigDTO,
  DisableOfferDTO,
  UpdateOfferAccessDTO,
} from '@dtos';
import {
  CheckoutPageUseCases,
  ProductOfferFactoryService,
  ProductOfferUseCases,
} from '@useCases';
import { JwtPermissionsGuard } from '@auth';
import { ProductOfferStatus } from '@px-shared-account/hermes';

@ApiTags('products')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('product-offers')
export class ProductsOffersController {
  constructor(
    private readonly checkoutUseCases: CheckoutPageUseCases,
    private readonly productOfferFactory: ProductOfferFactoryService,
    private readonly productOfferUseCases: ProductOfferUseCases,
  ) {}

  @ApiOperation({
    summary: 'Create a product offer and attached checkout page',
  })
  @Post('/')
  async createOfferAndCheckout(
    @Body() offerAndCheckoutInfo: CreateOfferWithCheckoutDTO,
  ) {
    const offer = this.productOfferFactory.generate(offerAndCheckoutInfo);
    return this.productOfferUseCases.create(offer);
  }

  @ApiOperation({
    summary: 'Get the list of all product offers, accepts pagination',
  })
  @Get('/list')
  async listOffers(@Query('limit') limit = 500, @Query('page') page = 1) {
    return this.productOfferUseCases.getAll(limit, page);
  }

  @ApiOperation({
    summary: 'Get the list of all product offers, no pagination',
  })
  @Get('/listAll')
  async listAllOffers() {
    return this.productOfferUseCases.getAll();
  }

  @ApiOperation({
    summary: 'Get the list of all product offers and attached checkout pages',
  })
  @Get('/search')
  async listOfferAndCheckout(
    @Query('name') name: string,
    @Query('line') lineId: number,
    @Query('family') familyId: number,
    @Query('product') productId: number,
    @Query('plan') planId: number,
    @Query('status') status: ProductOfferStatus,
    @Query('limit') limit = 500,
    @Query('page') page = 1,
    @Query('order') orderBy?: 'DESC' | 'ASC',
  ) {
    return this.productOfferUseCases.searchAll(
      name,
      lineId,
      familyId,
      productId,
      planId,
      status,
      limit,
      page,
      orderBy,
    );
  }

  @ApiOperation({ summary: 'Get a product offer and attached checkout page' })
  @Get('/:id')
  async getOfferAndCheckout(@Param('id') id: number) {
    return this.productOfferUseCases.getOneBy({
      id,
    });
  }

  @ApiOperation({
    summary: 'Activate a product offer its Checkout page',
  })
  @Patch('/:id/activate')
  async activateOffer(@Param('id') id: number) {
    return this.productOfferUseCases.activateOffer(id);
  }

  @ApiOperation({
    summary: 'Disable an product offer and its checkout page',
  })
  @Patch('/:id/disable')
  async disableOffer(
    @Param('id') id: number,
    @Body() disableConfig: DisableOfferDTO,
  ) {
    return this.productOfferUseCases.disableOffer(
      id,
      disableConfig.redirectIfDisabled,
    );
  }

  @ApiOperation({
    summary: 'Update a product offer and attached checkout page',
  })
  @Patch('/')
  async updateOfferAndCheckout(@Body() updates: UpdateOfferWithCheckoutDTO) {
    return this.productOfferUseCases.updateWithCheckout(updates);
  }

  @ApiOperation({ summary: 'Publish an offer' })
  @Post('/:id/publish')
  async publish(@Param('id') id: number) {
    return this.productOfferUseCases.publishOffer(id);
  }

  @ApiOperation({ summary: 'Attache products to an offer' })
  @Post('/:id/config')
  async createOfferConfig(
    @Param('id') id: number,
    @Body() config: CreateOfferConfigDTO,
  ) {
    return this.productOfferUseCases.attachProducts(id, config);
  }

  @ApiOperation({ summary: 'Update products attached to an offer' })
  @Patch('/:id/config')
  async updateOfferConfig(
    @Param('id') id: number,
    @Body() config: CreateOfferConfigDTO,
  ) {
    return this.productOfferUseCases.attachProducts(id, config);
  }

  @ApiOperation({ summary: 'Update access config for an offer' })
  @Post('/:id/access')
  async updateAccessConfig(
    @Param('id') id: number,
    @Body() config: UpdateOfferAccessDTO,
  ) {
    return this.productOfferUseCases.updateAccessConfig(id, config);
  }

  @ApiOperation({
    summary: 'Get a product offer and attached checkout page by chargebeeId',
  })
  @Get('/by-chargebee-id/:chargebeeId')
  async getOfferAndCheckoutByChargebeeId(
    @Param('chargebeeId') chargebeeId: string,
  ) {
    return this.productOfferUseCases.getOneBy({
      chargebeeId,
    });
  }

  @ApiOperation({
    summary: 'Get the details of an offer for downsell',
  })
  @Get('/downsell/:id')
  async getDownsellOffer(@Param('id') id: number) {
    try {
      const result =
        await this.productOfferUseCases.getOfferProductsForDownsell(id);
      return result;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get downsell offer.',
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
