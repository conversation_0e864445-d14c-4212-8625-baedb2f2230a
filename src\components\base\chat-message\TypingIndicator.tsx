"use client";

import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export interface TypingIndicatorProps {
  sender: {
    name: string;
    avatar?: string;
  };
  className?: string;
}

export function TypingIndicator({ sender, className }: TypingIndicatorProps) {
  return (
    <div className={cn("flex w-full gap-3 p-4", className)}>
      <Avatar className="h-8 w-8">
        {sender.avatar && <AvatarImage src={sender.avatar} alt={sender.name} />}
        <AvatarFallback>{sender.name[0]}</AvatarFallback>
      </Avatar>
      <div className="flex max-w-[70%] flex-col gap-1">
        <div className="text-sm text-muted-foreground">{sender.name}</div>
        <div className="flex items-center gap-1 rounded-lg bg-muted p-3">
          <span className="animate-bounce">•</span>
          <span className="animate-bounce" style={{ animationDelay: "0.2s" }}>
            •
          </span>
          <span className="animate-bounce" style={{ animationDelay: "0.4s" }}>
            •
          </span>
        </div>
      </div>
    </div>
  );
}
