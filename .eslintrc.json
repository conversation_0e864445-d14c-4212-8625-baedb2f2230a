{
  "extends": ["next/core-web-vitals", "next/typescript", "prettier"],
  // Disabled some rules for development purposes, will be enabled later when we have a cleaner codebase
  "rules": {
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-unused-expressions": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "react-hooks/exhaustive-deps": "off",
    "max-lines": ["warn", { "max": 180, "skipComments": true, "skipBlankLines": true }]
  }
}
