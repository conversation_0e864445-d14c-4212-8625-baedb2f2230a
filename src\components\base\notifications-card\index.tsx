import { cn } from "@/lib/utils";
import { BaseProps } from "@/types";
import { Avatar } from "@/components/base/avatar";
import { ThumbsUp } from "lucide-react";

export interface NotificationCardProps extends BaseProps {
  title: string;
  timestamp: string;
  avatar?: {
    src?: string;
    fallback: string;
  };
  icon?: React.ReactNode;
  variant?: "default" | "success" | "info" | "warning" | "danger";
}

export const NotificationCard = ({
  title,
  timestamp,
  avatar,
  icon = <ThumbsUp className="h-4 w-4" />,
  variant = "default",
  className,
}: NotificationCardProps) => {
  // Variant styles
  const variantStyles = {
    default: "bg-gray-500",
    success: "bg-green-50",
    info: "bg-blue-50",
    warning: "bg-yellow-50",
    danger: "bg-red-50",
  };

  // Icon background styles
  const iconStyles = {
    default: "bg-gray-100 text-gray-600",
    success: "bg-green-100 text-green-600",
    info: "bg-blue-100 text-blue-600",
    warning: "bg-yellow-100 text-yellow-600",
    danger: "bg-red-100 text-red-600",
  };

  return (
    <div
      className={cn(
        "flex items-center gap-4 rounded-lg p-4 shadow-sm transition-colors hover:bg-accent/5",
        variantStyles[variant],
        className,
      )}
    >
      {/* Avatar or Icon */}
      {avatar ? (
        <div className="relative">
          <Avatar src={avatar.src} fallback={avatar.fallback} className="h-10 w-10" />
          <div
            className={cn(
              iconStyles[variant],
              "absolute bottom-0 right-0 flex size-5 items-center justify-center rounded-full bg-slate-300 text-white",
            )}
          >
            <div className="flex size-3 items-center justify-center">{icon}</div>
          </div>
        </div>
      ) : (
        <div
          className={cn(
            "flex h-10 w-10 items-center justify-center rounded-full",
            iconStyles[variant],
          )}
        >
          {icon}
        </div>
      )}

      {/* Content */}
      <div className="flex min-w-0 flex-1 flex-col">
        <p className="text-sm font-medium text-foreground">{title}</p>
        <p className="text-xs text-muted-foreground">{timestamp}</p>
      </div>
    </div>
  );
};

NotificationCard.displayName = "NotificationCard";
