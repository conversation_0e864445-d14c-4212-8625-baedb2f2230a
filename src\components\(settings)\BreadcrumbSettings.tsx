"use client";

import { Slash } from "lucide-react";
import React, { act, Fragment } from "react";
import { useTranslations } from "next-intl";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

type BreadcrumbSettingsProps = {
  active: string;
};

export const BreadcrumbSettings = ({ active }: BreadcrumbSettingsProps) => {
  const t = useTranslations("profile");

  const breadcrumbItems = [
    {
      title: t("settings.payments.title"),
      href: "/payments",
    },
    {
      title: t("settings.payments-details.title"),
      href: "/payments/details",
    },
    {
      title: t("settings.account.title"),
      href: "/account",
    },
    {
      title: t("settings.whats-new.title"),
      href: "/whats-new",
    },
    {
      title: t("settings.goliaths.title"),
      href: "/goliaths",
    },
    {
      title: t("settings.faq.title"),
      href: "/faq",
    },
    {
      title: t("settings.faq.pxs"),
      href: "/faq/pxs",
    },
    {
      title: t("settings.faq.pxl"),
      href: "/faq/pxl",
    },
    {
      title: t("settings.faq.goliaths"),
      href: "/faq/goliaths",
    },
    {
      title: t("settings.courses.title"),
      href: "/courses",
    },
    {
      title: t("settings.courses.titleId", { id: active.split("/")[2] }),
      href: `/courses/${active.split("/")[2]}`,
    },
    {
      title: t("settings.cohorts.title"),
      href: "/cohorts",
    },
    {
      title: t("settings.cohorts.titleId", { id: active.split("/")[2] }),
      href: `/cohorts/${active.split("/")[2]}`,
    },
  ];

  const itemsToShow = breadcrumbItems.filter((item) => active.startsWith(item.href));

  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="/profile">{t("title")}</BreadcrumbLink>
        </BreadcrumbItem>
        {itemsToShow.map((item, index) => (
          <Fragment key={item.href}>
            <BreadcrumbSeparator>
              <Slash />
            </BreadcrumbSeparator>
            {index < itemsToShow.length - 1 ? (
              <BreadcrumbItem>
                <BreadcrumbLink href={item.href}>{item.title}</BreadcrumbLink>
              </BreadcrumbItem>
            ) : (
              <BreadcrumbItem>
                <BreadcrumbPage>{item.title}</BreadcrumbPage>
              </BreadcrumbItem>
            )}
          </Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};
