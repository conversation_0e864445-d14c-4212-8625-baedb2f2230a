import { type PurchaseRecord } from "@/services/customers";

export const communityUrls = {
  pxl: "https://paradox-learning.circle.so/home",
  pxs: "https://paradox-school.circle.so/home",
};

export const learningTerms = ["learning", "pxl", "paradox learning"];
export const schoolTerms = ["school", "pxs", "paradox school"];

/**
 * Retrieves the URL for a specific community.
 *
 * @param community - The name of the community.
 * @param plan - The plan details from the purchase record.
 * @returns The URL of the community.
 */
export function getCommunityUrl(purchaseRecord: PurchaseRecord) {
  if (!purchaseRecord || !purchaseRecord.plan || !purchaseRecord.item) return;

  const planName = purchaseRecord.plan.externalName?.toLowerCase();
  const productLine = purchaseRecord.item.cfProductLine?.toLowerCase();

  // Determine target based on plan name or product line
  if (planName?.includes("pxl") || (productLine && learningTerms.includes(productLine)))
    return communityUrls.pxl;
  else if (planName?.includes("pxs") || (productLine && schoolTerms.includes(productLine)))
    return communityUrls.pxs;

  // Fallback or default logic if needed
  return undefined;
}
