import { CreateCourseDto, UpdateCourseDto } from '@dtos';
import {
  CourseEntity,
  ProductFamily,
  ProductOffer,
  UserEntity,
} from '@entities';
import { Injectable } from '@nestjs/common';
import { CourseStatus } from '@px-shared-account/hermes';

@Injectable()
export class CourseFactory {
  /**
   * Generates a new course
   * @param courseInfo Course information
   * @returns CourseEntity
   */
  generate(courseInfo: CreateCourseDto): CourseEntity {
    const course = new CourseEntity();
    course.name = courseInfo.name;
    course.description = courseInfo.description;
    course.bannerImage = courseInfo.bannerImage;
    course.cardImage = courseInfo.cardImage;
    course.thumbnail = courseInfo.thumbnail;
    course.status = CourseStatus.DRAFT;
    course.offers = courseInfo.offers
      ? this.generateCourseOffers(
          courseInfo.offers.filter((offer) => offer.id) as {
            id: number;
            name?: string;
          }[],
        )
      : [];
    course.managers = courseInfo.managers
      ? this.generateCourseManagers(courseInfo.managers)
      : [];
    course.lmsId = courseInfo.lmsId;
    const productFamily = new ProductFamily();
    productFamily.id = courseInfo.productFamilyId;
    course.productFamily = productFamily;
    course.ctaLink = courseInfo.ctaLink;
    return course;
  }

  /**
   * Generates updates for a course
   * @param courseUpdates Course updates
   * @returns Course updates
   */
  generateCourseUpdates(courseUpdates: UpdateCourseDto): Partial<CourseEntity> {
    const updates: Partial<CourseEntity> = {};
    for (const [key, value] of Object.entries(courseUpdates)) {
      if (value === undefined) continue; // Skip undefined values from DTO

      if (key === 'managers' && Array.isArray(value)) {
        updates.managers = this.generateCourseManagers(value as number[]);
      } else if (key === 'offers' && Array.isArray(value)) {
        updates.offers = this.generateCourseOffers(
          value as Array<{ id: number; name?: string }>,
        );
      } else {
        updates[key] = value;
      }
    }
    return updates;
  }

  /**
   * Generates a list of course managers
   * @param managersIds List of manager IDs
   * @returns List of course managers
   */
  generateCourseManagers(managersIds: number[]): UserEntity[] {
    const managers: UserEntity[] = [];
    if (managersIds) {
      managersIds.forEach((managerId) => {
        const user = new UserEntity();
        user.id = managerId;
        managers.push(user);
      });
    }
    return managers;
  }

  /**
   * Generates a list of product offers for a course
   * @param offersData List of offer data containing at least an id
   * @returns List of ProductOffer entities
   */
  generateCourseOffers(
    offersData: Array<{ id: number; name?: string }>,
  ): ProductOffer[] {
    const offers: ProductOffer[] = [];
    if (offersData) {
      offersData.forEach((offerDto) => {
        const productOffer = new ProductOffer();
        productOffer.id = offerDto.id;
        if (offerDto.name) {
          productOffer.name = offerDto.name;
        }
        offers.push(productOffer);
      });
    }
    return offers;
  }
}
