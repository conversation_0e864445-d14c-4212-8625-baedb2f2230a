import { Injectable } from '@nestjs/common';
import { InfisicalSDK, ListSecretsResult } from '@infisical/sdk';
import {
  AppSecrets,
  AppSecretsSchema,
  AuthSecrets,
  AuthSecretsSchema,
  CacheSecrets,
  CacheSecretsSchema,
  ChargebeeSecrets,
  ChargebeeSecretsSchema,
  CircleSecrets,
  CircleSecretsSchema,
  DBSecrets,
  DBSecretsSchema,
  GoliathsSecrets,
  GoliathsSecretsSchema,
  HubspotSecrets,
  HubspotSecretsSchema,
  LearnworldsSecrets,
  LearnworldsSecretsSchema,
  MonitoringSecrets,
  MonitoringSecretsSchema,
  NotificationSecrets,
  NotificationSecretsSchema,
  CustomerioSecrets,
  CustomerioSecretsSchema,
} from './config.schema';

@Injectable()
export class ConfigService {
  private readonly infisicalClient: InfisicalSDK;
  private readonly infisicalSiteUrl = 'https://infisical.paradoxgroup.com';
  private thirdPartySecrets: ListSecretsResult;
  appSecrets: AppSecrets;
  authSecrets: AuthSecrets;
  dbSecrets: DBSecrets;
  cacheSecrets: CacheSecrets;
  chargebeeSecrets: ChargebeeSecrets;
  circleSecrets: CircleSecrets;
  hubspotSecrets: HubspotSecrets;
  learnworldsSecrets: LearnworldsSecrets;
  monitoringSecrets: MonitoringSecrets;
  notificationSecrets: NotificationSecrets;
  goliathsSecrets: GoliathsSecrets;
  customerioSecrets: CustomerioSecrets;

  constructor() {
    this.infisicalClient = new InfisicalSDK({
      siteUrl: this.infisicalSiteUrl,
    });
  }

  /**
   * Fetch and store secrets from Infisical on application bootstrap
   */
  async loadSecrets() {
    await this.authenticateInfisical();
    this.appSecrets = await this.getAppSecrets();
    this.dbSecrets = await this.getDBSecrets();
    this.authSecrets = await this.getAuthSecrets();
    this.cacheSecrets = await this.getCacheSecrets();
    this.monitoringSecrets = await this.getMonitoringSecrets();
    this.thirdPartySecrets = await this.fetchThirdPartySecrets();
    this.chargebeeSecrets = await this.getChargebeeSecrets();
    this.circleSecrets = await this.getCircleSecrets();
    this.hubspotSecrets = await this.getHubspotSecrets();
    this.learnworldsSecrets = await this.getLearnworldsSecrets();
    this.notificationSecrets = await this.getNotificationSecrets();
    this.goliathsSecrets = await this.getGoliathsSecrets();
    this.customerioSecrets = await this.getCustomerioSecrets();
  }

  /**
   * Authenticate the Infisical client using Universal Auth
   */
  async authenticateInfisical() {
    if (!this.canAuthenticateInfisical()) {
      throw new Error(
        'INFISICAL_MACHINE_IDENTITY_CLIENT_ID or INFISICAL_MACHINE_IDENTITY_CLIENT_SECRET is not set',
      );
    }
    await this.infisicalClient.auth().universalAuth.login({
      clientId: process.env.INFISICAL_MACHINE_IDENTITY_CLIENT_ID,
      clientSecret: process.env.INFISICAL_MACHINE_IDENTITY_CLIENT_SECRET,
    });
  }

  /**
   * Check if the Infisical client can be authenticated
   */
  canAuthenticateInfisical() {
    return (
      !!process.env.INFISICAL_MACHINE_IDENTITY_CLIENT_ID &&
      !!process.env.INFISICAL_MACHINE_IDENTITY_CLIENT_SECRET
    );
  }

  /** Get the general app secrets from Infisical
   * @returns {AppSecrets} The app secrets
   */
  async getAppSecrets(): Promise<AppSecrets> {
    const allSecrets = await this.infisicalClient.secrets().listSecrets({
      environment: process.env.INFISICAL_ENVIRONMENT,
      projectId: process.env.INFISICAL_PROJECT_ID,
      secretPath: '/px_app',
    });

    const appSecrets: Record<string, string> = {};
    allSecrets.secrets.forEach((secret) => {
      appSecrets[secret.secretKey] = secret.secretValue;
    });

    try {
      const appSecretsValidated = AppSecretsSchema.parse(appSecrets);
      return appSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate application secrets');
    }
  }

  /** Get the app's auth secrets from Infisical
   * @returns {AuthSecrets} The auth secrets
   */
  async getAuthSecrets(): Promise<AuthSecrets> {
    const allSecrets = await this.infisicalClient.secrets().listSecrets({
      environment: process.env.INFISICAL_ENVIRONMENT,
      projectId: process.env.INFISICAL_PROJECT_ID,
      secretPath: '/px_auth',
    });

    const authSecrets: Partial<AuthSecrets> = {};
    allSecrets.secrets.forEach((secret) => {
      authSecrets[secret.secretKey as keyof AuthSecrets] = secret.secretValue;
    });

    try {
      const authSecretsValidated = AuthSecretsSchema.parse(authSecrets);
      return authSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate auth secrets');
    }
  }

  /**
   * Get the DB secrets from Infisical
   * @returns {DBSecrets} The DB secrets
   */
  async getDBSecrets(): Promise<DBSecrets> {
    const allSecrets = await this.infisicalClient.secrets().listSecrets({
      environment: process.env.INFISICAL_ENVIRONMENT,
      projectId: process.env.INFISICAL_PROJECT_ID,
      secretPath: '/px_db',
    });

    const dbSecrets: Partial<DBSecrets> = {};
    allSecrets.secrets.forEach((secret) => {
      dbSecrets[secret.secretKey as keyof DBSecrets] = secret.secretValue;
    });

    try {
      const dbSecretsValidated = DBSecretsSchema.parse(dbSecrets);
      return dbSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate database secrets');
    }
  }

  /**
   * Get the monitoring secrets from Infisical
   * @returns {MonitoringSecrets} The monitoring secrets
   */
  async getMonitoringSecrets(): Promise<MonitoringSecrets> {
    const allSecrets = await this.infisicalClient.secrets().listSecrets({
      environment: process.env.INFISICAL_ENVIRONMENT,
      projectId: process.env.INFISICAL_PROJECT_ID,
      secretPath: '/px_monitoring',
    });

    const monitoringSecrets: Partial<MonitoringSecrets> = {};
    allSecrets.secrets.forEach((secret) => {
      monitoringSecrets[secret.secretKey as keyof MonitoringSecrets] =
        secret.secretValue;
    });

    try {
      const monitoringSecretsValidated =
        MonitoringSecretsSchema.parse(monitoringSecrets);
      return monitoringSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate monitoring secrets');
    }
  }

  /**
   * Get the third party secrets from Infisical
   * @returns {ListSecretsResult} The third party secrets
   */
  async fetchThirdPartySecrets(): Promise<ListSecretsResult> {
    return await this.infisicalClient.secrets().listSecrets({
      environment: process.env.INFISICAL_ENVIRONMENT,
      projectId: process.env.INFISICAL_PROJECT_ID,
      recursive: true,
      secretPath: '/px_third_party',
    });
  }

  /**
   * Get the chargebee secrets from Infisical
   * @returns {ChargebeeSecrets} The chargebee secrets
   */
  async getChargebeeSecrets(): Promise<ChargebeeSecrets> {
    const allChargebeeSecrets = this.thirdPartySecrets.secrets.filter(
      (secret) => secret.secretPath.includes('chargebee'),
    );
    const chargebeeSecrets: Partial<ChargebeeSecrets> = {};
    allChargebeeSecrets.forEach((secret) => {
      chargebeeSecrets[secret.secretKey as keyof ChargebeeSecrets] =
        secret.secretValue;
    });

    try {
      const chargebeeSecretsValidated =
        ChargebeeSecretsSchema.parse(chargebeeSecrets);
      return chargebeeSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate chargebee secrets');
    }
  }

  /**
   * Get the cache secrets from Infisical
   * @returns {CacheSecrets} The cache secrets
   */
  async getCacheSecrets(): Promise<CacheSecrets> {
    const allCacheSecrets = await this.infisicalClient.secrets().listSecrets({
      environment: process.env.INFISICAL_ENVIRONMENT,
      projectId: process.env.INFISICAL_PROJECT_ID,
      secretPath: '/px_cache',
    });
    const cacheSecrets: Partial<CacheSecrets> = {};
    allCacheSecrets.secrets.forEach((secret) => {
      cacheSecrets[secret.secretKey as keyof CacheSecrets] = secret.secretValue;
    });

    try {
      const cacheSecretsValidated = CacheSecretsSchema.parse(cacheSecrets);
      return cacheSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate cache secrets');
    }
  }

  /**
   * Get the circle secrets from Infisical
   * @returns {CircleSecrets} The circle secrets
   */
  async getCircleSecrets(): Promise<CircleSecrets> {
    const allCircleSecrets = this.thirdPartySecrets.secrets.filter((secret) =>
      secret.secretPath.includes('circle'),
    );
    const circleSecrets: Partial<CircleSecrets> = {};
    allCircleSecrets.forEach((secret) => {
      circleSecrets[secret.secretKey as keyof CircleSecrets] =
        secret.secretValue;
    });

    try {
      const circleSecretsValidated = CircleSecretsSchema.parse(circleSecrets);
      return circleSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate circle secrets');
    }
  }

  /**
   * Get the hubspot secrets from Infisical
   * @returns {HubspotSecrets} The hubspot secrets
   */
  async getHubspotSecrets(): Promise<HubspotSecrets> {
    const allHubspotSecrets = this.thirdPartySecrets.secrets.filter((secret) =>
      secret.secretPath.includes('hubspot'),
    );
    const hubspotSecrets: Partial<HubspotSecrets> = {};
    allHubspotSecrets.forEach((secret) => {
      hubspotSecrets[secret.secretKey as keyof HubspotSecrets] =
        secret.secretValue;
    });

    try {
      const hubspotSecretsValidated =
        HubspotSecretsSchema.parse(hubspotSecrets);
      return hubspotSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate hubspot secrets');
    }
  }

  /**
   * Get the learnworlds secrets from Infisical
   * @returns {LearnworldsSecrets} The learnworlds secrets
   */
  async getLearnworldsSecrets(): Promise<LearnworldsSecrets> {
    const allLearnworldsSecrets = this.thirdPartySecrets.secrets.filter(
      (secret) => secret.secretPath.includes('learnworlds'),
    );
    const learnworldsSecrets: Partial<LearnworldsSecrets> = {};
    allLearnworldsSecrets.forEach((secret) => {
      learnworldsSecrets[secret.secretKey as keyof LearnworldsSecrets] =
        secret.secretValue;
    });

    try {
      const learnworldsSecretsValidated =
        LearnworldsSecretsSchema.parse(learnworldsSecrets);
      return learnworldsSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate learnworlds secrets');
    }
  }

  /**
   * Get the notification secrets from Infisical
   * @returns {NotificationSecrets} The notification secrets
   */
  async getNotificationSecrets(): Promise<NotificationSecrets> {
    const allSecrets = await this.infisicalClient.secrets().listSecrets({
      environment: process.env.INFISICAL_ENVIRONMENT,
      projectId: process.env.INFISICAL_PROJECT_ID,
      secretPath: '/px_notifications',
    });

    const notificationSecrets: Partial<NotificationSecrets> = {};
    allSecrets.secrets.forEach((secret) => {
      notificationSecrets[secret.secretKey as keyof NotificationSecrets] =
        secret.secretValue;
    });

    try {
      const notificationSecretsValidated =
        NotificationSecretsSchema.parse(notificationSecrets);
      return notificationSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate notification secrets');
    }
  }

  /**
   * Get the goliaths secrets from Infisical
   * @returns {GoliathsSecrets} The goliaths secrets
   */
  async getGoliathsSecrets(): Promise<GoliathsSecrets> {
    const allGoliathsSecrets = this.thirdPartySecrets.secrets.filter((secret) =>
      secret.secretPath.includes('goliaths'),
    );
    const goliathsSecrets: Partial<GoliathsSecrets> = {};
    allGoliathsSecrets.forEach((secret) => {
      goliathsSecrets[secret.secretKey as keyof GoliathsSecrets] =
        secret.secretValue;
    });

    try {
      const goliathsSecretsValidated =
        GoliathsSecretsSchema.parse(goliathsSecrets);
      return goliathsSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate Goliaths secrets');
    }
  }

  /**
   * Get the customerio secrets from Infisical
   * @returns {CustomerioSecrets} The customerio secrets
   * @throws {Error} If the secrets are invalid
   */
  async getCustomerioSecrets(): Promise<CustomerioSecrets> {
    const allCustomerioSecrets = this.thirdPartySecrets.secrets.filter(
      (secret) => secret.secretPath.includes('customerio'),
    );
    const customerioSecrets: Partial<CustomerioSecrets> = {};
    allCustomerioSecrets.forEach((secret) => {
      customerioSecrets[secret.secretKey as keyof CustomerioSecrets] =
        secret.secretValue;
    });

    try {
      const customerioSecretsValidated =
        CustomerioSecretsSchema.parse(customerioSecrets);
      return customerioSecretsValidated;
    } catch (error) {
      console.error('Invalid secrets:', error);
      throw new Error('Failed to validate customerio secrets');
    }
  }

  /**
   * Check if all secrets are loaded
   * @returns {boolean} True if all secrets are loaded, false otherwise
   */
  allSecretsLoaded(): boolean {
    return [
      this.appSecrets,
      this.dbSecrets,
      this.authSecrets,
      this.cacheSecrets,
      this.monitoringSecrets,
      this.thirdPartySecrets,
      this.chargebeeSecrets,
      this.circleSecrets,
      this.hubspotSecrets,
      this.learnworldsSecrets,
      this.notificationSecrets,
      this.goliathsSecrets,
      this.customerioSecrets,
    ].every((secret) => secret !== null);
  }
}
