import { Injectable, NotFoundException } from '@nestjs/common';
import { FindOptionsWhere } from 'typeorm';
import {
  CohortStatus,
  IListCohortsResponse,
  PXActionResult,
  UpdateResultWithItemInfo,
} from '@px-shared-account/hermes';
import { CohortTable, CourseTable } from '@tables';
import { CohortEntity } from '@entities';
import { CreateCohortDto, UpdateCohortDto } from '@dtos';
import { CohortFactory } from './cohort.factory';
import { IDataServices } from '@abstracts';

@Injectable()
export class CohortUseCases {
  constructor(
    private cohortFactory: CohortFactory,
    private readonly databaseService: IDataServices,
  ) {}

  /**
   * Creates a new cohort
   * @param cohortInfo - Cohort information
   * @returns {Promise<CohortEntity>} - A promise that resolves with the created cohort
   */
  async create(cohortInfo: CreateCohortDto): Promise<CohortEntity> {
    const newCourse = this.cohortFactory.generate(cohortInfo);
    return this.databaseService.cohort.create(newCourse);
  }

  /**
   * Gets a cohort by id
   * @param id - The id of the cohort
   * @returns {Promise<CohortEntity>} - A promise that resolves with the cohort
   */
  async getById(id: number): Promise<CohortEntity> {
    return this.databaseService.cohort.getOneBy({ id });
  }

  /**
   * Gets all cohorts for a course
   * @param courseId - The id of the course
   * @returns {Promise<IListCohortsResponse>} - A promise that resolves with the cohorts
   * for the course
   */
  async getAllForCourse(
    courseId: number,
    limit = 10,
    page = 1,
  ): Promise<IListCohortsResponse> {
    const result = await this.databaseService.cohort.getAllBy(
      {
        course: {
          id: courseId,
        },
      },
      limit,
      page,
    );

    return {
      data: result.items,
      page,
      total: result.total,
    };
  }

  /**
   * Searches for cohorts based on query, course & status where
   * query is matched against cohort name
   * @param query Query to search for
   * @param courseId `uuid` of the Course to search for
   * @param status Cohort status to filter by
   * @param limit Limit of results
   * @param page Page number
   * @returns CohortEntity[]
   */
  async search(
    query?: string,
    courseId?: number,
    status?: CohortStatus,
    limit?: number,
    page?: number,
  ): Promise<IListCohortsResponse> {
    const columnsToQuery = ['name'] as unknown as (keyof CohortEntity)[];
    const filters: FindOptionsWhere<CohortTable>[] = [];

    if (courseId) {
      const course = new CourseTable();
      course.id = courseId;
      filters.push({
        course,
      });
    }

    if (status) {
      filters.push({
        status,
      });
    }

    const searchResult = await this.databaseService.cohort.search(
      CohortTable,
      query,
      columnsToQuery,
      filters,
      true,
      { limit, page },
    );

    return {
      data: searchResult.items,
      page,
      total: searchResult.total,
    };
  }

  /**
   * Adds students to a cohort
   * @param cohortId - The id of the cohort
   * @param userIds - The ids of the users to add
   */
  async addStudents(
    cohortId: number,
    userIds: number[],
  ): Promise<PXActionResult> {
    const cohort = await this.databaseService.cohort.getOneBy({ id: cohortId });
    if (!cohort) {
      throw new NotFoundException('Cohort not found');
    }

    // Load current students to check max participants
    const currentStudents = await this.databaseService.cohort
      .getRepository()
      .createQueryBuilder('cohort')
      .leftJoin('cohort.students', 'students')
      .where('cohort.id = :cohortId', { cohortId })
      .getMany();

    if (currentStudents.length + userIds.length > cohort.maxParticipants) {
      return {
        success: false,
        message: `Cohort only allows ${cohort.maxParticipants} participants`,
      };
    }

    try {
      // Add students using relation query builder
      await this.databaseService.cohort
        .getRepository()
        .createQueryBuilder('cohort')
        .relation(CohortTable, 'students')
        .of(cohortId)
        .add(userIds);

      // Update currentParticipants count
      await this.databaseService.cohort.update(
        { id: cohortId },
        {
          currentParticipants: currentStudents.length + userIds.length,
        },
      );

      return {
        success: true,
        message: `Students were successfully added to the cohort`,
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to add students to the cohort: ${error.message}`,
      };
    }
  }

  /**
   * Removes students from a cohort
   * @param cohortId - The id of the cohort
   * @param userIds - The ids of the users to remove
   */
  async removeStudents(
    cohortId: number,
    userIds: number[],
  ): Promise<PXActionResult> {
    const cohort = await this.databaseService.cohort.getOneBy({ id: cohortId });
    if (!cohort) {
      throw new NotFoundException('Cohort not found');
    }

    try {
      const currentStudents = await this.databaseService.cohort
        .getRepository()
        .createQueryBuilder()
        .relation(CohortTable, 'students')
        .of(cohortId)
        .loadMany();

      await this.databaseService.cohort
        .getRepository()
        .createQueryBuilder()
        .relation(CohortTable, 'students')
        .of(cohortId)
        .remove(userIds);

      // Calculate new participant count
      // Make sure we don't go below zero by using Math.max
      const newTotalParticipants = Math.max(
        0,
        currentStudents.length - userIds.length,
      );

      // Update currentParticipants count separately
      await this.databaseService.cohort.update(
        { id: cohortId },
        {
          currentParticipants: newTotalParticipants,
        },
      );

      return {
        success: true,
        message: `Students were successfully removed from the cohort`,
      };
    } catch (error: any) {
      console.error('Failed to remove students from cohort:', error);
      return {
        success: false,
        message: `Failed to remove students from the cohort: ${error.message}`,
      };
    }
  }

  /**
   * Updates a cohort
   * @param id - The id of the cohort
   * @param cohortInfo - The updated cohort information
   * @returns {Promise<UpdateResultWithItemInfo<CohortEntity>>} - A promise that resolves with the updated cohort
   */
  async update(
    id: number,
    cohortInfo: UpdateCohortDto,
  ): Promise<UpdateResultWithItemInfo<CohortEntity>> {
    const updates = this.cohortFactory.generateCourseUpdates(cohortInfo);
    return this.databaseService.cohort.updateAndReturnItem(
      'id',
      id.toString(),
      updates,
    );
  }

  /**
   * Gets all cohorts
   * @param query Query to search for
   * @param status Cohort status to filter by
   * @param limit Number of cohorts to return
   * @param page Page number
   * @returns A list of cohorts
   */
  async list(
    query?: string,
    status?: CohortStatus,
    limit = 10,
    page = 1,
  ): Promise<IListCohortsResponse> {
    const filters: FindOptionsWhere<CohortEntity>[] = [];

    if (status) {
      filters.push({
        status,
      });
    }
    const searchResult = await this.databaseService.cohort.search(
      CourseTable,
      query,
      ['name'],
      filters,
      true,
      { limit, page },
    );

    return {
      data: searchResult.items,
      page,
      total: searchResult.total,
    };
  }
}
