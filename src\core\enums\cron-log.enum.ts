export enum CronLogType {
  OVERDUE_INVOICE_CHECK = 'OVERDUE_INVOICE_CHECK',
  ACCESS_SUSPENSION_CHECK = 'ACCESS_SUSPENSION_CHECK',
  ERRORED_CUSTOMER_ACCESS_CHECK = 'ERRORED_CUSTOMER_ACCESS_CHECK',
  EXCHANGE_RATE_CRAWLER = 'EXCHANGE_RATE_CRAWLER',
  FAILED_WEBHOOKS = 'FAILED_WEBHOOKS',
  FAILED_HUBSPOT_SYNC = 'FAILED_HUBSPOT_SYNC',
}

export enum CronLogStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  RUNNING = 'RUNNING',
}

export enum CronLogRefPrefix {
  OVERDUE_INVOICE = 'OVERDUE_INV_',
  ACCESS_SUSPENSION_CHECK = 'ACCESS_SUSPENSION_',
  ERRORED_CUSTOMER_ACCESS = 'ERRORED_CUSTOMER_ACCESS_',
  EXCHANGE_RATE = 'EXCHANGE_RATE_',
  FAILED_WEBHOOKS = 'FAILED_WEBHOOK_',
  HS_SYNC = 'HS_SYNC_',
}
