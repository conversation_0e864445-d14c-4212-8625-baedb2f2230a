import { Module } from '@nestjs/common';
import { DiscountFactoryService, DiscountUseCases } from '.';
import { ChargebeeBillingModule } from '@services/billing';
import { DataServicesModule } from '@services/database';

@Module({
  imports: [DataServicesModule, ChargebeeBillingModule],
  providers: [DiscountFactoryService, DiscountUseCases],
  exports: [DiscountFactoryService, DiscountUseCases],
})
export class DiscountUseCasesModule {}
