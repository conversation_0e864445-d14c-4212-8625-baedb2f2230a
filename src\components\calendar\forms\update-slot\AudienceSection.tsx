"use client";

import { UseFormReturn } from "react-hook-form";

import { Form<PERSON>ield, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import mockData from "@/app/[lang]/(auth)/calendar/components/mock-data";
import { CreateNewSlotFormType } from "../types";

type AudienceSectionProps = {
  t: (key: string) => string;
  form: UseFormReturn<CreateNewSlotFormType>;
};

export default function AudienceSection({ t, form }: AudienceSectionProps) {
  return (
    <div className="mb-5 border-none">
      <div className="text-xl font-semibold">{t("what-kind-of-audience")}</div>
      <div className="grid gap-4 md:grid-cols-2">
        {/* Cohort */}
        <FormField
          control={form.control}
          name="cohort"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("cohort")}</FormLabel>
              <Input value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
        {/* Number of participants */}
        <div className="flex flex-col gap-4">
          <FormLabel>{t("participants")}</FormLabel>
          <div className="flex items-center gap-2">
            <FormField
              control={form.control}
              name="participantMin"
              render={({ field }) => (
                <FormItem>
                  <Input type="number" min={1} value={field.value} onChange={field.onChange} />
                </FormItem>
              )}
            />
            <span>-</span>
            <FormField
              control={form.control}
              name="participantMax"
              render={({ field }) => (
                <FormItem>
                  <Input type="number" min={1} value={field.value} onChange={field.onChange} />
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
      <div className="grid gap-4 md:grid-cols-3">
        {/* Program */}
        <FormField
          control={form.control}
          name="program"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("program")}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("program")} />
                </SelectTrigger>
                <SelectContent>
                  {mockData.courses.bachelor.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        {/* Course */}
        <FormField
          control={form.control}
          name="course"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("course")}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("course")} />
                </SelectTrigger>
                <SelectContent>
                  {mockData.courses.bachelor.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        {/* Module */}
        <FormField
          control={form.control}
          name="module"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("module")}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("module")} />
                </SelectTrigger>
                <SelectContent>
                  {mockData.modules.math101.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
