import { Module } from '@nestjs/common';
import { ChargebeeBillingService } from '@services/billing';
import { ChargeBeeWebhooksHandler } from './chargebee-webhooks.service';
import { DataServicesModule } from '@services/database';
import { CreditNoteUseCasesModule } from '../credit-note';
import { DiscountUseCasesModule, DiscountUseCases } from '../discount';
import { InvoiceUseCasesModule } from '../invoice';
import { SubscriptionUseCasesModule } from '../subscription';
import { TransactionUseCasesModule } from '../transaction';
import { ClerkWebhooksHandler } from './clerk-webhooks.service';
import { UserModule } from '../user';
import { WebhookUseCases } from './webhook.use-cases.service';
import { BankTransferModule } from '../bank-transfer';

@Module({
  imports: [
    UserModule,
    DataServicesModule,
    DiscountUseCasesModule,
    TransactionUseCasesModule,
    CreditNoteUseCasesModule,
    InvoiceUseCasesModule,
    SubscriptionUseCasesModule,
    BankTransferModule,
  ],
  providers: [
    WebhookUseCases,
    DiscountUseCases,
    ChargeBeeWebhooksHandler,
    ChargebeeBillingService,
    ClerkWebhooksHandler,
  ],
  exports: [ChargeBeeWebhooksHandler, ClerkWebhooksHandler, WebhookUseCases],
})
export class WebhooksUseCasesModule {}
