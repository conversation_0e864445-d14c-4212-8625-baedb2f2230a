import {
  CreditNoteLineItem,
  CreditNoteLinkedRefund,
} from 'chargebee-typescript/lib/resources';
import {
  Allocation,
  BillingAddress,
  ShippingAddress,
} from 'chargebee-typescript/lib/resources/credit_note';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  AfterLoad,
} from 'typeorm';
import {
  CreditNoteStatus,
  CreditNoteType,
  Currencies,
  InvoicePriceType,
} from '@enums';
import { CreditNote } from '@entities';

@Entity({
  name: 'creditNotes',
})
export class CreditNoteTable implements CreditNote {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', unique: true })
  chargebeeId: string;

  @Column({ type: 'enum', enum: CreditNoteStatus })
  status: CreditNoteStatus;

  @Column({ type: 'int' })
  date: number;

  @Column({ type: 'int' })
  generatedAt: number;

  @Column({ type: 'int', nullable: true })
  refundedAt?: number;

  @Column({ type: 'enum', enum: Currencies })
  currencyCode: Currencies;

  @Column({ type: 'int' })
  total: number;

  @Column({ type: 'decimal' })
  exchangeRate: number;

  @Column({ type: 'varchar' })
  customerId: string;

  @Column({ type: 'varchar' })
  customerEmail: string;

  @Column({ type: 'varchar' })
  customerName: string;

  @Column({ type: 'varchar', nullable: true })
  subscriptionId?: string;

  @Column({ type: 'varchar', nullable: true })
  referenceInvoiceId?: string;

  @Column({ type: 'enum', enum: CreditNoteType })
  type: CreditNoteType;

  @Column({ type: 'varchar' })
  businessEntityId: string;

  @Column({ type: 'int' })
  amountAvailable: number;

  @Column({ type: 'int' })
  amountAllocated: number;

  @Column({ type: 'int' })
  amountRefunded: number;

  @Column({ type: 'varchar', length: 100 })
  createReason: string;

  @Column({ type: 'varchar' })
  channel: string;

  @Column({ type: 'varchar' })
  priceType: InvoicePriceType;

  @Column({ type: 'jsonb', nullable: true, default: [] })
  allocations?: Allocation[];

  @Column({ type: 'jsonb', nullable: true, default: [] })
  lineItems?: CreditNoteLineItem[];

  @Column({ type: 'jsonb', nullable: true, default: [] })
  linkedRefunds?: CreditNoteLinkedRefund[];

  @Column({ type: 'jsonb', nullable: true, default: {} })
  billingAddress?: BillingAddress;

  @Column({ type: 'jsonb', nullable: true, default: {} })
  shippingAddress?: ShippingAddress;

  @Column({ type: 'text', nullable: true })
  customerNotes?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp', nullable: true })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @AfterLoad()
  convertExchangeRateToNumber() {
    this.exchangeRate = parseFloat(this.exchangeRate.toString());
  }
}
