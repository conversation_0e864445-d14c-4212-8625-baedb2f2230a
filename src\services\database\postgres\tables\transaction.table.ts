import {
  LinkedCreditNote,
  LinkedInvoice,
  LinkedRefund,
} from 'chargebee-typescript/lib/resources/transaction';
import {
  AfterLoad,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import {
  Currencies,
  PaymentMethod,
  TransactionStatus,
  TransactionType,
} from '@enums';
import { Transaction } from '@entities';

@Entity({
  name: 'transactions',
})
export class TransactionTable implements Transaction {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', unique: true })
  chargebeeId: string;

  @Column({ type: 'enum', enum: TransactionStatus })
  status: TransactionStatus;

  @Column({ type: 'int' })
  date: number;

  @Column({ type: 'enum', enum: TransactionType })
  type: TransactionType;

  @Column({ type: 'enum', enum: Currencies })
  currency: Currencies;

  @Column({ type: 'int' })
  amount: number;

  @Column({ type: 'int', default: 0 })
  amountUnused: number;

  @Column({ type: 'varchar', nullable: true })
  subscriptionId?: string;

  @Column({ type: 'varchar' })
  customerId: string;

  @Column({ type: 'varchar' })
  customerEmail: string;

  @Column({ type: 'varchar' })
  customerName: string;

  @Column({ type: 'decimal', nullable: true })
  exchangeRate?: number;

  @Column({ type: 'varchar', nullable: true })
  referenceNumber?: string;

  @Column({ type: 'jsonb', nullable: true, default: [] })
  linkedInvoices: LinkedInvoice[];

  @Column({ type: 'jsonb', nullable: true, default: [] })
  linkedCreditNotes: LinkedCreditNote[];

  @Column({ type: 'jsonb', nullable: true, default: [] })
  linkedRefunds: LinkedRefund[];

  @Column({ type: 'enum', enum: PaymentMethod, nullable: true })
  paymentMethod: PaymentMethod;

  @Column({ type: 'varchar', nullable: true })
  maskedCardNumber?: string;

  @Column({ type: 'json', nullable: true })
  paymentMethodDetails?: any;

  @Column({ type: 'varchar' })
  gateway: string;

  @Column({ type: 'varchar', nullable: true })
  idAtGateway?: string;

  @Column({ type: 'varchar' })
  businessEntityId: string;

  @Column({ type: 'varchar', nullable: true })
  errorCode?: string;

  @Column({ type: 'varchar', nullable: true })
  errorText?: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deletedAt?: Date;

  @AfterLoad()
  convertExchangeRateToNumber() {
    this.exchangeRate = parseFloat(this.exchangeRate.toString());
  }
}
