"use client";

import { create } from "zustand";

import { defaultEvents } from "@/app/[lang]/(auth)/calendar/components/mock-data";
import { CreateNewSlotFormType } from "@/components/calendar/forms/types";

export type CalendarEvent = {
  // Part 1 - Event Details
  id: number;
  title: string;
  type: CreateNewSlotFormType["type"];
  replay: CreateNewSlotFormType["replay"];
  recurrence: CreateNewSlotFormType["recurrence"];
  description: string;
  begin: string | Date;
  end: string | Date;
  status: "pending" | "confirmed" | "cancelled";
  // Part 2 - Speakers
  mainSpeaker: CreateNewSlotFormType["mainSpeaker"];
  otherSpeakers: CreateNewSlotFormType["otherSpeakers"];
  technicalSupport: CreateNewSlotFormType["technicalSupport"];
  // Part 3 - Audience
  cohort: CreateNewSlotFormType["cohort"];
  participantMin: CreateNewSlotFormType["participantMin"];
  participantMax: CreateNewSlotFormType["participantMax"];
  program: CreateNewSlotFormType["program"];
  course: CreateNewSlotFormType["course"];
  module: CreateNewSlotFormType["module"];
  // Part 4 - Comments
  comments: CreateNewSlotFormType["comment"];
};

type SetDialogParams = {
  id: number;
  top?: number;
  len?: number;
  sq?: number;
};

export type CalendarStore = {
  defaultEventDuration: number;
  events: CalendarEvent[];
  layout: "day" | "week" | "month";
  openDialog: {
    id: number;
    top?: number;
    len?: number;
    sq?: number;
  };
  selectedDate: Date;
  setDialog: (params: SetDialogParams) => void;
  setLayout: (layout: CalendarStore["layout"]) => void;
  setSelectedDate: (state: CalendarStore["selectedDate"]) => void;
  setEvents: (events: CalendarEvent[]) => void;
};

const useCalendarStore = create<CalendarStore>((set) => ({
  defaultEventDuration: 60,
  events: defaultEvents,
  layout: "week",
  selectedDate: new Date(),
  openDialog: {
    id: -1,
    top: 0,
    len: 0,
    sq: 0,
  },
  setDialog: (params: SetDialogParams) => set({ openDialog: params }),
  setLayout: (layout: CalendarStore["layout"]) => set({ layout }),
  setSelectedDate: (state: CalendarStore["selectedDate"]) => set({ selectedDate: state }),
  setEvents: (events: CalendarEvent[]) => set({ events }),
}));

if (typeof window !== "undefined") {
  // @ts-ignore
  window.calendarStore = useCalendarStore;
}
export default useCalendarStore;
