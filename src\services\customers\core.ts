/**
 * Represents the core access flags for a user.
 */
export interface AccessInfo {
  isPXS: boolean;
  hubspotSession: string | null;
  isPXL: boolean;
  isParadox: boolean;
  hasBothAccess: boolean;
  isPOM: boolean;
}

/**
 * Represents an LMS Course entry.
 */
export interface LmsCourse {
  id: string;
  name: string;
}

/**
 * Represents LMS entries associated with an offer.
 */
export interface LmsEntries {
  products: LmsCourse[];
  recommended: LmsCourse[];
}

/**
 * Represents core data from a Chargebee Subscription object.
 */
export interface SubscriptionData {
  id: string;
  status: string;
  cfIsForever?: boolean | undefined;
  nextBillingAt?: number | undefined;
  cancelledAt?: number | undefined;
  cancelScheduleCreatedAt?: number | undefined;
  mrr?: number | undefined;
  lastPaymentDate?: number | undefined;
  metaData?: Record<string, any> | undefined;
  startedAt?: number | undefined;
  currentTermEnd?: number | undefined;
  currentTermStart?: number | undefined;
}

/**
 * Represents core data from a Chargebee ItemPrice object (Plan/Addon).
 */
export interface PlanData {
  id: string;
  externalName?: string | undefined;
  itemId: string;
  name: string;
}

/**
 * Represents core data from a Chargebee Item object.
 */
export interface ItemData {
  id: string;
  cfProductLine?: string | undefined;
  name: string;
}

/**
 * Represents community entries associated with an offer.
 */
export interface CommunityEntries {
  products?: Array<{
    spaces?: Array<{
      id: number;
      name: string;
      slug: string;
    }>;
    community: string;
  }>;
  recommended?: Array<{
    spaces?: Array<{
      id: number;
      name: string;
      slug: string;
    }>;
    community: string;
  }>;
}

/**
 * Represents the enriched Offer/Purchase details.
 */
export interface OfferData {
  orderStatus: string;
  bannerImage?: string | null | undefined;
  cardImage?: string | null | undefined;
  chargebeeId?: string | null | undefined;
  description?: string | null | undefined;
  lmsIds?: LmsEntries | null | undefined;
  name?: string | null | undefined;
  usp?: string | null | undefined;
  communityIds?: CommunityEntries | null | undefined;
}

/**
 * Represents a single denormalized purchase record for the user.
 */
export interface PurchaseRecord {
  subscription: SubscriptionData;
  plan: PlanData;
  item: ItemData;
  offer: OfferData;
}

/**
 * Represents the complete response structure from the PX API User Access & Details endpoint.
 */
export interface UserAccessDetailsResponse {
  email: string;
  accessInfo: AccessInfo;
  purchases: PurchaseRecord[];
}

/**
 * Represents an invoice for a subscription
 */
export interface InvoiceRecord {
  id: string;
  date: number;
  amount: number;
  status: string;
  pdfUrl: string;
  subscriptionId: string;
  invoiceNumber?: string;
}

/**
 * Represents the order status response
 */
export interface OrderStatusResponse {
  orderStatus: string;
}

/**
 * Core API endpoints for customer access operations
 * These are the base paths and methods used by both client and server implementations
 */
export const CUSTOMER_ACCESS_API_ENDPOINTS = {
  /**
   * Get customer access details
   * @param email The email of the customer
   * @returns Endpoint configuration
   */
  getAccessDetails: (email: string) => ({
    url: `/users/${encodeURIComponent(email)}/access-details`,
    method: "GET",
  }),

  /**
   * Get subscription details
   * @param id The ID of the subscription
   * @returns Endpoint configuration
   */
  getSubscription: (id: string) => ({
    url: `/api/subscriptions/${id}`,
    method: "GET",
  }),

  /**
   * Get invoices for a subscription
   * @param id The ID of the subscription
   * @returns Endpoint configuration
   */
  getInvoices: (id: string) => ({
    url: `/api/subscriptions/${id}/invoices`,
    method: "GET",
  }),

  /**
   * Get order status for a subscription
   * @param id The ID of the subscription
   * @returns Endpoint configuration
   */
  getOrderStatus: (id: string) => ({
    url: `/api/subscriptions/${id}/order-status`,
    method: "GET",
  }),

  /**
   * Cancel a subscription
   * @param id The ID of the subscription
   * @returns Endpoint configuration
   */
  cancelSubscription: (id: string) => ({
    url: `/api/subscriptions/${id}/cancel`,
    method: "POST",
  }),
};

/**
 * Type definitions for API responses
 */
export type CustomerAccessApiTypes = {
  getAccessDetails: UserAccessDetailsResponse;
  getSubscription: PurchaseRecord;
  getInvoices: InvoiceRecord[];
  getOrderStatus: OrderStatusResponse;
  cancelSubscription: { success: boolean; message: string };
};
