import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Logger } from 'nestjs-pino';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import {
  AllExceptionsFilter,
  DatabaseExceptionInterceptor,
  SentryService,
  ZodValidationInterceptor,
} from '@services/monitoring';
import { ConfigService } from '@config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
    rawBody: true,
  });
  const config = new DocumentBuilder()
    .setTitle('Paradox API')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);
  app.useLogger(app.get(Logger));
  const sentryService = app.get(SentryService);
  app.useGlobalPipes(new ValidationPipe({ transform: true }));
  app.useGlobalFilters(new AllExceptionsFilter(sentryService));
  app.useGlobalInterceptors(
    new ZodValidationInterceptor(),
    new DatabaseExceptionInterceptor(),
  );
  app.enableCors();
  const configService = app.get(ConfigService);
  await app.listen(configService.appSecrets.PORT);
  const server = app.getHttpServer();
  const router = server._events.request._router;
  const availableRoutes: [] = router.stack
    .map((layer) => {
      if (layer.route) {
        return {
          route: {
            path: layer.route?.path,
            method: layer.route?.stack[0].method,
          },
        };
      }
    })
    .filter((item) => item !== undefined);
  console.table(availableRoutes);
}
bootstrap();
