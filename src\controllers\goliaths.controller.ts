import { Controller, Get, ParseIntPipe, Post, Query } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { GoliathService } from '@useCases';

@ApiTags('goliaths')
@Controller('goliaths')
export class GoliathsController {
  constructor(private readonly goliathService: GoliathService) {}

  @ApiOperation({
    summary:
      'Based on a user email, we should return if the user purchase POM or not',
  })
  @Get('/is-pom-student')
  async getGoliaths(@Query('email') email: string) {
    return this.goliathService.isPOMStudent(email);
  }
  @ApiOperation({ summary: 'Generate n unique 4-char promo codes' })
  @Post('/generate-codes')
  async generateCodes(@Query('n', ParseIntPipe) n: number) {
    const generatedCodes = await this.goliathService.generateCodes(n);
    return {
      codes: generatedCodes.codes,
      count: generatedCodes.codes.length,
      total_codes: generatedCodes.totalCodes,
    };
  }

  @ApiOperation({
    summary:
      'Issue and push a discount code to Goliath. This endpoint is not used in production.',
  })
  @Post('/issue-and-push')
  async issueAndPush(@Query('email') email: string) {
    const discountCode = await this.goliathService.issueAndPush(email);
    return discountCode;
  }

  /**
   * Resend Goliaths promo to all users who have one.
   */
  @ApiOperation({ summary: 'Resend Goliaths promo for all users' })
  @Post('/resend-goliaths-notifications')
  async resendGoliathsNotifications(): Promise<{
    successCount: number;
    failureCount: number;
  }> {
    return this.goliathService.resendAllNotifications();
  }
}
