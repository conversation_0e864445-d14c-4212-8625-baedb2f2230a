export namespace Permissions {
    export enum User {
        CREATE = "user:create",
        READ = "user:read",
        UPDATE = "user:update",
        DELETE = "user:delete",
    }
    export enum Role {
        CREATE = "role:create",
        READ = "role:read",
        UPDATE = "role:update",
        DELETE = "role:delete",
    }
    export enum Course {
        CREATE = "course:create",
        READ = "course:read",
        UPDATE = "course:update",
        ARCHIVE = "course:archive",
    }
    export enum Cohort {
        CREATE = "cohort:create",
        READ = "cohort:read",
        UPDATE = "cohort:update",
        DELETE = "cohort:delete",
    }
    export enum Triad {
        CREATE = "triad:create",
        READ = "triad:read",
        UPDATE = "triad:update",
        DELETE = "triad:delete",
        JOIN = "triad:join",
    }
    export enum WhatsNew {
        CREATE = "whats-new:create",
        READ = "whats-new:read",
        UPDATE = "whats-new:update",
        DELETE = "whats-new:delete",
        UPLOAD = "whats-new:upload",
        REACT = "whats-new:react",
    }
}
