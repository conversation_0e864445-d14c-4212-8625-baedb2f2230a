"use server";

import { auth } from "@clerk/nextjs/server";
import { cookies } from "next/headers";
import {
  CUSTOMER_ACCESS_API_ENDPOINTS,
  CustomerAccessApiTypes,
  UserAccessDetailsResponse,
  PurchaseRecord,
  InvoiceRecord,
} from "./core";
import { getImpersonationFromNextCookie } from "@/lib/impersonation-utils";
import serverFetcher from "@/lib/server/server-fetcher";

/**
 * Creates a server-side token getter for the fetcher
 * @returns Promise with authentication token or null
 */
const getServerToken = async () => {
  const session = await auth();
  return session?.getToken() || null;
};

// Initialize the fetcher with server-side token getter
const apiFetcher = serverFetcher(getServerToken);

/**
 * Gets the effective email for a user, considering impersonation if applicable.
 * @param email Optional email override. If provided, this will be used directly.
 * @returns The effective email to use (impersonated or actual).
 */
export const getEffectiveEmail = async (email?: string | null): Promise<string | null> => {
  // If email explicitly provided, use it (allows override)
  if (email !== undefined) {
    return email;
  }

  try {
    const session = await auth();
    const userId = session?.userId;
    const sessionClaims = session?.sessionClaims;
    const actualEmail = sessionClaims?.emailAddress as string | null;

    if (!userId || !actualEmail) {
      console.warn("[getEffectiveEmail] No user ID or actual email in session.");
      return null;
    }

    // Check if the current actual user is a Paradox admin
    const currentUserAccessDetails = await fetchUserDetailsByEmail(actualEmail);
    const isAdmin = currentUserAccessDetails?.accessInfo?.isParadox ?? false;

    if (!isAdmin) {
      // Not an admin, so they cannot be impersonating. Their own email is effective.
      return actualEmail;
    }

    // User is an admin, now check for impersonation
    const cookieStore = await cookies();
    const impersonation = getImpersonationFromNextCookie(cookieStore);

    if (impersonation && impersonation.impersonatedEmail) {
      return impersonation.impersonatedEmail;
    }

    // Admin is not impersonating, so their own email is effective.
    return actualEmail;
  } catch (error) {
    console.error("[getEffectiveEmail] Error determining effective email:", error);
    return null;
  }
};

/**
 * Fetches customer access details from the PX API.
 * @param email The email of the customer, or undefined to use effective email.
 * @returns Promise with customer access details.
 */
export async function getCustomerAccessDetails(
  email?: string | null,
): Promise<CustomerAccessApiTypes["getAccessDetails"] | null> {
  // Get effective email (impersonated or actual)
  const effectiveEmail = await getEffectiveEmail(email);

  if (!effectiveEmail) {
    return null;
  }

  try {
    const { url } = CUSTOMER_ACCESS_API_ENDPOINTS.getAccessDetails(effectiveEmail);
    return await apiFetcher(url);
  } catch (error) {
    console.error(`Error fetching customer access details for ${effectiveEmail}:`, error);
    throw error;
  }
}

/**
 * Fetches customer access details directly for a specific email address.
 * @param email The email of the customer.
 * @returns Promise with customer access details or null if not found/error.
 */
export async function fetchUserDetailsByEmail(
  email: string,
): Promise<UserAccessDetailsResponse | null> {
  if (!email || !email.includes("@")) {
    // Basic email validation
    console.error("fetchUserDetailsByEmail: Invalid email provided.");
    return null;
  }

  try {
    const { url } = CUSTOMER_ACCESS_API_ENDPOINTS.getAccessDetails(email);
    // apiFetcher is already initialized with getServerToken
    const response = await apiFetcher(url);
    return response as UserAccessDetailsResponse | null; // Assuming apiFetcher returns null on error as per server-fetcher.ts
  } catch (error) {
    // serverFetcher already logs the error and returns null.
    // We can add specific logging here if needed, but it might be redundant.
    // console.error(`Error in fetchUserDetailsByEmail for ${email}:`, error);
    return null; // Ensure null is returned on any unexpected throw from apiFetcher itself
  }
}

/**
 * Gets a subscription by ID.
 * @param id The subscription ID.
 * @returns Promise with subscription details or null if not found/error.
 */
export async function getSubscription(id: string): Promise<PurchaseRecord | null> {
  if (!id) {
    console.error("getSubscription: No subscription ID provided.");
    return null;
  }

  try {
    const { url } = CUSTOMER_ACCESS_API_ENDPOINTS.getSubscription(id);
    return await apiFetcher(url);
  } catch (error) {
    console.error(`Error fetching subscription details for ${id}:`, error);
    return null;
  }
}

/**
 * Gets the invoices for a subscription.
 * @param id The subscription ID.
 * @returns Promise with invoice list or empty array if not found/error.
 */
export async function getInvoices(id: string): Promise<InvoiceRecord[]> {
  if (!id) {
    console.error("getInvoices: No subscription ID provided.");
    return [];
  }

  try {
    const { url } = CUSTOMER_ACCESS_API_ENDPOINTS.getInvoices(id);
    const invoices = await apiFetcher(url);
    return invoices || [];
  } catch (error) {
    console.error(`Error fetching invoices for subscription ${id}:`, error);
    return [];
  }
}

/**
 * Confirms that the specified subscription belongs to the current user
 * @param id The subscription ID
 * @returns Promise with boolean indicating whether the subscription belongs to the user
 */
export async function isSubscriptionOwner(id: string): Promise<boolean> {
  if (!id) {
    return false;
  }

  try {
    const accessDetails = await getCustomerAccessDetails();
    if (!accessDetails) {
      return false;
    }

    return accessDetails.purchases.some((purchase) => purchase.subscription.id === id);
  } catch (error) {
    console.error(`Error verifying subscription ownership for ${id}:`, error);
    return false;
  }
}
