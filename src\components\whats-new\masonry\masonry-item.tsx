"use client";

import { useState } from "react";
import { useMasonryContext } from "./use-masonry";
import Image from "next/image";

interface MasonryItemProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  onClick?: () => void;
}

export function MasonryItem({ src, alt, width, height, onClick }: MasonryItemProps) {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  const { openLightbox } = useMasonryContext();

  // Calculate aspect ratio if both dimensions are provided
  const aspectRatio = width && height ? width / height : undefined;

  // Determine if image is landscape, portrait, or square
  // This helps with CSS grid placement optimization
  const orientation = !aspectRatio
    ? "unknown"
    : aspectRatio > 1.5
      ? "landscape"
      : aspectRatio < 0.7
        ? "portrait"
        : "square";

  // Handle image click
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onClick) {
      onClick();
    } else {
      openLightbox(src, alt);
    }
  };

  return (
    <div
      className={`masonry-item relative overflow-hidden rounded-md shadow-md transition-all duration-300 hover:shadow-lg ${orientation}`}
    >
      <div onClick={handleClick} className="block cursor-pointer">
        <div
          className={`relative w-full overflow-hidden transition-opacity duration-300 ${
            loaded ? "opacity-100" : "opacity-0"
          }`}
          style={aspectRatio ? { paddingBottom: `${(1 / aspectRatio) * 100}%` } : undefined}
        >
          <Image
            src={src}
            alt={alt}
            width={width || 0}
            height={height || 0}
            sizes={
              orientation === "landscape"
                ? "(max-width: 768px) 100vw, 66vw"
                : "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            }
            className="h-auto w-full rounded-md transition-opacity hover:opacity-90"
            style={
              aspectRatio
                ? {
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }
                : {
                    width: "100%",
                    height: "auto",
                  }
            }
            unoptimized
            onLoad={() => setLoaded(true)}
            onError={() => setError(true)}
          />
        </div>
        {!loaded && !error && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="border-secondary/30 border-t-secondary h-8 w-8 animate-spin rounded-full border-2"></div>
          </div>
        )}
      </div>
    </div>
  );
}
