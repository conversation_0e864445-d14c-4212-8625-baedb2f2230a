import { auth } from "@clerk/nextjs/server";
import serverFetcher from "@/lib/server/server-fetcher";
import { COHORT_API_ENDPOINTS, CohortApiTypes, ListCohortsParams, CohortApiPayloads } from "./core";

/**
 * Creates a server-side token getter for the fetcher
 * @returns Promise with authentication token or null
 */
const getServerToken = async () => {
  const session = await auth();
  return session?.getToken() || null;
};

// Initialize the fetcher with server-side token getter
const apiFetcher = serverFetcher(getServerToken);

/**
 * Server-side function for fetching cohort list
 * @param params Filter and pagination parameters
 * @returns Promise with cohort list data
 */
export async function getCohortList(
  params: ListCohortsParams = {},
): Promise<CohortApiTypes["list"]> {
  const { url } = COHORT_API_ENDPOINTS.list(params);
  return apiFetcher(url);
}

/**
 * Server-side function for fetching a specific cohort by ID
 * @param id Cohort ID to fetch
 * @returns Promise with cohort data
 */
export async function getCohortById(id: number): Promise<CohortApiTypes["getById"]> {
  const { url } = COHORT_API_ENDPOINTS.getById(id);
  return apiFetcher(url);
}

/**
 * Alias for getCohortById to maintain consistency with course service
 * @param id Cohort ID to fetch
 * @returns Promise with cohort data
 */
export const getCohortDetails = getCohortById;

/**
 * Server-side function for creating a new cohort
 * @param data Cohort creation data
 * @returns Promise with created cohort data
 */
export async function createCohort(
  data: CohortApiPayloads["create"],
): Promise<CohortApiTypes["create"]> {
  const { url, method } = COHORT_API_ENDPOINTS.create();
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for updating an existing cohort
 * @param id Cohort ID to update
 * @param data Cohort update data
 * @returns Promise with update result
 */
export async function updateCohort(
  id: number,
  data: CohortApiPayloads["update"],
): Promise<CohortApiTypes["update"]> {
  const { url, method } = COHORT_API_ENDPOINTS.update(id);
  return apiFetcher(url, {
    method,
    body: data,
  });
}

/**
 * Server-side function for adding students to a cohort
 * @param id Cohort ID
 * @param studentIds Array of student IDs to add
 * @returns Promise with action result
 */
export async function addStudentsToCohort(
  id: number,
  studentIds: number[],
): Promise<CohortApiTypes["addStudents"]> {
  const { url, method } = COHORT_API_ENDPOINTS.addStudents(id);
  return apiFetcher(url, {
    method,
    body: { studentIds },
  });
}

/**
 * Server-side function for removing students from a cohort
 * @param id Cohort ID
 * @param studentIds Array of student IDs to remove
 * @returns Promise with action result
 */
export async function removeStudentsFromCohort(
  id: number,
  studentIds: number[],
): Promise<CohortApiTypes["removeStudents"]> {
  const { url, method } = COHORT_API_ENDPOINTS.removeStudents(id);
  return apiFetcher(url, {
    method,
    body: { studentIds },
  });
}
