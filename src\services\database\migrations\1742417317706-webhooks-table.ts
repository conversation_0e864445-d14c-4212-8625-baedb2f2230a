import { MigrationInterface, QueryRunner } from 'typeorm';

export class WebhooksTable1742417317706 implements MigrationInterface {
  name = 'WebhooksTable1742417317706';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."webhooks_status_enum" AS ENUM('processed', 'failed')`,
    );
    await queryRunner.query(
      `CREATE TABLE "webhooks" ("id" SERIAL NOT NULL, "sender" character varying NOT NULL, "senderWebhookId" character varying NOT NULL, "occurredAt" bigint NOT NULL, "status" "public"."webhooks_status_enum" NOT NULL, "outcome" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_9e8795cfc899ab7bdaa831e8527" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "webhooks" ADD CONSTRAINT "UQ_7ec2e271f3c7a66752acbe6aa56" UNIQUE ("senderWebhookId")`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."cronLogs_type_enum" RENAME TO "cronLogs_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum" AS ENUM('OVERDUE_INVOICE_CHECK', 'ACCESS_SUSPENSION_CHECK', 'ERRORED_CUSTOMER_ACCESS_CHECK', 'EXCHANGE_RATE_CRAWLER', 'FAILED_WEBHOOKS')`,
    );
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ALTER COLUMN "type" TYPE "public"."cronLogs_type_enum" USING "type"::"text"::"public"."cronLogs_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."cronLogs_type_enum_old" AS ENUM('OVERDUE_INVOICE_CHECK', 'ACCESS_SUSPENSION_CHECK', 'ERRORED_CUSTOMER_ACCESS_CHECK', 'EXCHANGE_RATE_CRAWLER')`,
    );
    await queryRunner.query(
      `ALTER TABLE "cronLogs" ALTER COLUMN "type" TYPE "public"."cronLogs_type_enum_old" USING "type"::"text"::"public"."cronLogs_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."cronLogs_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."cronLogs_type_enum_old" RENAME TO "cronLogs_type_enum"`,
    );
    await queryRunner.query(`DROP TABLE "webhooks"`);
    await queryRunner.query(`DROP TYPE "public"."webhooks_status_enum"`);
  }
}
