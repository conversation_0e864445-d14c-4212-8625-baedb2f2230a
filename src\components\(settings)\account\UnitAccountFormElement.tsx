"use client";

import { useUser } from "@clerk/nextjs";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import But<PERSON> from "@/components/base/button";
import { FormControl, Form, FormItem } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { FieldType } from "./UpdateAccountInfoForm";
import ChangePasswordForm from "./ChangePasswordForm";
import ChangePhoneNumberForm from "./ChangePhoneNumberForm";

type UnitAccountFormElementProps = {
  name: string;
  className?: string;
  fieldType: FieldType[];
  onEditBillingAddress?: () => void;
};

export default function UnitAccountFormElement({
  name,
  className,
  fieldType,
  onEditBillingAddress,
}: UnitAccountFormElementProps) {
  const { user } = useUser();
  const t = useTranslations("profile.settings.account.form");
  const [mode, setMode] = useState<"edit" | "view">("view");
  const field = fieldType.find((field) => field.key === name);
  const unableToEdit = ["email"];

  let defaultValue: string | undefined = field?.defaultValue;
  if (field?.use === "clerk") {
    switch (field.defaultValue) {
      case "firstName":
        defaultValue = user?.firstName ?? "";
        break;
      case "lastName":
        defaultValue = user?.lastName ?? "";
        break;
      case "email":
        defaultValue = user?.primaryEmailAddress?.emailAddress ?? t("no-email");
        break;
      case "password":
        defaultValue = "********";
        break;
      case "phone":
        defaultValue = user?.phoneNumbers?.[0]?.phoneNumber ?? t("no-phone");
        break;
      default:
        defaultValue = "";
    }
  }

  const form = useForm({
    defaultValues: {
      [name]: defaultValue,
    },
  });

  const onSubmit = async (data: any) => {
    if (name === "firstName" || name === "lastName") {
      await user?.update({
        [name]: data[name],
      });
      toast.success(t("success"));
    } else {
      console.log("onSubmit", data);
    }
    setMode("view");
  };

  return (
    <div className={cn("mb-0.5 bg-[#181818] p-4", className)}>
      <div className="flex w-full flex-row justify-between">
        <div className="flex flex-col gap-2">
          <div>{t(`${name}.label`)}</div>
          <AnimatePresence mode="wait">
            {mode === "view" && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.1 }}
                className="text-[#8c8c8c]"
              >
                {defaultValue}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        {!unableToEdit.includes(name) && (
          <AnimatePresence mode="wait">
            {mode === "view" && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.15 }}
              >
                <Button
                  variant="secondary"
                  onClick={() =>
                    name === "billingAddress" ? onEditBillingAddress?.() : setMode("edit")
                  }
                >
                  {t("modify")}
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>
      <AnimatePresence mode="wait">
        {mode === "edit" && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            {name === "password" ? (
              <ChangePasswordForm
                onSuccess={() => setMode("view")}
                onCancel={() => setMode("view")}
              />
            ) : name === "phone" ? (
              <ChangePhoneNumberForm
                currentPhoneNumber={user?.phoneNumbers?.[0]?.phoneNumber ?? null}
                onCancel={() => setMode("view")}
              />
            ) : (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="flex w-full flex-col gap-2">
                  <FormItem>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder={t(`${name}.placeholder`)}
                        {...form.register(name)}
                      />
                    </FormControl>
                  </FormItem>
                  <div className="mt-2 flex gap-2">
                    <Button type="button" variant="secondary" onClick={() => setMode("view")}>
                      {t("cancel")}
                    </Button>
                    <Button type="submit">{t("save")}</Button>
                  </div>
                </form>
              </Form>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
