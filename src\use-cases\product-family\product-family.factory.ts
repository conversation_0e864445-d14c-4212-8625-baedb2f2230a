import { Injectable } from '@nestjs/common';
import { CreateProductFamilyDTO } from '@dtos';
import { ProductFamily, ProductLine } from '@entities';

@Injectable()
export class ProductFamilyFactory {
  generate(familyInfo: CreateProductFamilyDTO): ProductFamily {
    const productFamily = new ProductFamily();
    productFamily.name = familyInfo.name;
    productFamily.description = familyInfo.description;
    productFamily.status = 'active';
    const relatedProductLine = new ProductLine();
    relatedProductLine.id = familyInfo.productLine?.id;
    productFamily.productLine = relatedProductLine;
    productFamily.chargebeeId = productFamily.name;
    return productFamily;
  }
}
