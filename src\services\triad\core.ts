import {
  ITriadBase,
  ICreateTriad,
  IUpdateTriad,
  IJoinTriad,
  ILeaveTriad,
  SessionType,
  IUserStats,
} from "@px-shared-account/hermes";

export type ListTriadsParams = {
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: string;
  sessionType?: SessionType;
};

export const TRIAD_API_ENDPOINTS = {
  list: (params: ListTriadsParams = {}) => {
    const { search, page = 1, limit = 100, sortBy, sortOrder } = params;
    const queryParams = new URLSearchParams();
    if (search) queryParams.append("search", search);
    if (sortBy) queryParams.append("sortBy", sortBy);
    if (sortOrder) queryParams.append("sortOrder", sortOrder);
    queryParams.append("page", String(page));
    queryParams.append("limit", String(limit));
    return {
      url: `/triads?${queryParams.toString()}`,
      method: "GET",
    };
  },
  getById: (id: number) => ({
    url: `/triads/${id}`,
    method: "GET",
  }),
  create: () => ({
    url: "/triads",
    method: "POST",
  }),
  update: (id: number) => ({
    url: `/triads/${id}`,
    method: "PATCH",
  }),
  delete: (id: number) => ({
    url: `/triads/${id}`,
    method: "DELETE",
  }),
  join: () => ({
    url: "/triads/join",
    method: "POST",
  }),
  leave: () => ({
    url: "/triads/leave",
    method: "POST",
  }),
  getUpcoming: () => ({
    url: `/triads/user/upcoming`,
    method: "GET",
  }),
  getUserStats: () => ({
    url: `/triads/user/statistics`,
    method: "GET",
  }),
  getPlatformStats: (start?: string, end?: string) => {
    const params = [];
    if (start) params.push(`start=${encodeURIComponent(start)}`);
    if (end) params.push(`end=${encodeURIComponent(end)}`);
    const query = params.length ? `?${params.join("&")}` : "";
    return {
      url: `/triads/platform/statistics${query}`,
      method: "GET",
    };
  },
};

export type TriadApiTypes = {
  list: { items: ITriadBase[]; total: number };
  getById: ITriadBase;
  create: ITriadBase;
  update: ITriadBase;
  delete: { success: boolean };
  join: { success: boolean };
  leave: { success: boolean };
  getUpcoming: ITriadBase;
  getUserStats: IUserStats;
};

export type TriadApiPayloads = {
  create: ICreateTriad;
  update: IUpdateTriad;
  join: IJoinTriad;
  leave: ILeaveTriad;
};
