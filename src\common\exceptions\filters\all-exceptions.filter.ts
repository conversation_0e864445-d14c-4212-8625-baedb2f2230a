import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { QueryFailedError, EntityNotFoundError } from 'typeorm';
import { ZodError } from 'zod';
import { SentryService } from '../../../services/monitoring/sentry/sentry.service';
import { DatabaseErrorFactory } from '../errors/database-error.factory';
import { DatabaseError } from '../errors/database.errors';
import { ValidationError } from '../errors/validation.errors';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  constructor(private readonly sentryService: SentryService) {}

  /**
   * Catches all exceptions and processes them
   * @param exception - The exception to process
   * @param host - The context of the request
   */
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const processedError = this.processException(exception);
    const status = this.getHttpStatus(processedError);
    const responseBody = this.buildResponseBody(
      processedError,
      request,
      status,
    );

    if (status >= 500) {
      this.logError(processedError, request);
      this.captureExceptionForSentry(processedError, request);
    }

    response.status(status).json(responseBody);
  }

  /**
   * Processes the exception to ensure it's in the right format
   * @param exception - The exception to process
   * @returns An error instance
   */
  private processException(exception: unknown): Error {
    if (
      exception instanceof QueryFailedError ||
      exception instanceof EntityNotFoundError
    ) {
      return DatabaseErrorFactory.createFromError(exception);
    }

    if (exception instanceof ZodError) {
      return ValidationError.fromZodError(exception);
    }
    return exception as Error;
  }

  /**
   * Gets the HTTP status code from the exception
   * @param exception - The exception to get the status code from
   * @returns The HTTP status code
   */
  private getHttpStatus(exception: Error): number {
    if (exception instanceof HttpException) {
      return exception.getStatus();
    }

    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  /**
   * Builds the response body for the exception
   * @param exception - The exception to build the response body for
   * @param request - The request object
   * @param status - The HTTP status code
   * @returns The response body
   */
  private buildResponseBody(
    exception: Error,
    request: Request,
    status: number,
  ): any {
    if (exception instanceof DatabaseError) {
      return {
        statusCode: status,
        message: exception.getPublicMessage(),
        path: request.url,
        timestamp: new Date().toISOString(),
      };
    }

    if (exception instanceof ValidationError) {
      return exception.getResponse();
    }

    if (exception instanceof HttpException) {
      return exception.getResponse();
    }

    return {
      statusCode: status,
      message:
        `Error message: ${exception?.message}` || 'Internal server error',
      path: request.url,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Logs the error to the console
   * @param exception - The exception to log
   * @param request - The request object
   */
  private logError(exception: Error, request: Request): void {
    const message = `[${request.method}] ${request.url} - [${exception.name}] - ${exception.message}`;
    this.logger.error(message, exception.stack);
  }

  /**
   * Captures the exception for Sentry
   * @param exception - The exception to capture
   * @param request - The request object
   */
  private captureExceptionForSentry(exception: Error, request: Request): void {
    let errorToLog: Error = exception;
    let context: Record<string, any> = {
      request: {
        method: request.method,
        url: request.url,
        query: request.query,
        params: request.params,
      },
    };

    if (exception instanceof DatabaseError) {
      errorToLog = exception.getOriginalErrorForLogging();
      context = { ...context, ...exception.getContextForSentry() };
    } else if (exception instanceof ValidationError) {
      errorToLog = exception.getOriginalErrorForLogging();
      context = { ...context, ...exception.getContextForSentry() };
    }

    if (request.user) {
      this.sentryService.setUser({
        id: request.user['id'] || 'unknown',
        username: request.user['username'],
      });
    }

    this.sentryService.addBreadcrumb({
      category: 'http',
      type: 'http',
      level: 'info',
      data: {
        method: request.method,
        url: request.url,
        status_code: this.getHttpStatus(exception),
      },
    });

    this.sentryService.captureException(errorToLog, context);
  }
}
