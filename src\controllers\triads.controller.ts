import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
} from '@nestjs/common';
import {
  LeaveTriadDto,
  JoinTriadDto,
  PaginationDTO,
  CreateTriadDto,
  UpdateTriadDto,
} from '@dtos';
import { ApiBody, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Permissions } from '@px-shared-account/hermes';
import { CurrentUser, RequirePermissions } from 'src/auth/decorators';
import { CurrentUserInterceptor, JwtPermissionsGuard } from '@auth';
import { TriadsUseCases } from '@useCases';
import { UserEntity } from '@entities';

@Controller('triads')
@UseGuards(JwtPermissionsGuard)
export class TriadsController {
  constructor(private readonly triadsUseCases: TriadsUseCases) {}

  @ApiOperation({ summary: 'Create a triad' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        description: { type: 'string' },
        sessionTime: { type: 'string' },
        maxParticipants: { type: 'number' },
        organizerId: { type: 'number' },
      },
    },
  })
  @RequirePermissions(Permissions.Triad.CREATE)
  @UseInterceptors(CurrentUserInterceptor)
  @Post('/')
  async createTriad(
    @Body() body: CreateTriadDto,
    @CurrentUser() user: UserEntity,
  ) {
    return this.triadsUseCases.create(body, user.id);
  }

  @ApiOperation({ summary: 'Get triad statistics for a user' })
  @RequirePermissions(Permissions.Triad.READ)
  @UseInterceptors(CurrentUserInterceptor)
  @Get('/user/statistics')
  async getTriadStatistics(@CurrentUser() user: UserEntity) {
    return this.triadsUseCases.getTriadStatistics(user.id);
  }

  @ApiOperation({
    summary: 'Get platform-wide triad statistics (Paradox only)',
  })
  @ApiQuery({
    schema: {
      type: 'object',
      properties: {
        start: { type: 'string', description: 'Start date (ISO string)' },
        end: { type: 'string', description: 'End date (ISO string)' },
      },
    },
  })

  @ApiOperation({ summary: 'Get one upcoming triad for the current user' })
  @RequirePermissions(Permissions.Triad.READ)
  @UseInterceptors(CurrentUserInterceptor)
  @Get('/user/upcoming')
  async getUpcomingTriad(@CurrentUser() user: UserEntity) {
    return this.triadsUseCases.getOneUpcomingTriadForUser(user.id);
  }

  @Get('/platform/statistics')
  async getPlatformStatistics(
    @Query('start') start?: string,
    @Query('end') end?: string,
    @Request() req?: any,
  ) {
    return this.triadsUseCases.getPlatformStatistics(
      req?.user?.emailAddress,
      start,
      end,
    );
  }

  @ApiOperation({ summary: 'Get all triads' })
  @ApiQuery({ name: 'search', type: String, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiQuery({ name: 'page', type: Number, required: false })
  @ApiQuery({ name: 'sortBy', type: String, required: false })
  @ApiQuery({ name: 'sortOrder', type: String, required: false })
  @RequirePermissions(Permissions.Triad.READ)
  @Get('/')
  async getAllTriads(@Query() query: PaginationDTO & { search?: string }) {
    return this.triadsUseCases.listAll(
      query?.search,
      query?.limit,
      query?.page,
      query?.sortBy,
      query?.sortOrder?.toUpperCase() as 'ASC' | 'DESC',
    );
  }

  @RequirePermissions(Permissions.Triad.JOIN)
  @UseInterceptors(CurrentUserInterceptor)
  @Post('/join')
  async joinTriad(@Body() body: JoinTriadDto, @CurrentUser() user: UserEntity) {
    return this.triadsUseCases.joinTriad(body.triadId, user.id);
  }

  @ApiOperation({ summary: 'Leave a triad' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string' },
        triadId: { type: 'number' },
      },
    },
  })
  @UseInterceptors(CurrentUserInterceptor)
  @Post('/leave')
  async leaveTriad(
    @Body() body: LeaveTriadDto,
    @CurrentUser() user: UserEntity,
  ) {
    return this.triadsUseCases.leaveTriad(body.triadId, user.id);
  }

  @ApiOperation({ summary: 'Get a triad by ID' })
  @ApiParam({ name: 'id', type: Number, required: true })
  @RequirePermissions(Permissions.Triad.READ)
  @Get('/:id')
  async getTriadById(@Param('id', ParseIntPipe) id: number) {
    return this.triadsUseCases.getById(id);
  }

  @ApiOperation({ summary: 'Join a triad' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string' },
        triadId: { type: 'number' },
      },
    },
  })


  @ApiOperation({ summary: 'Delete a triad' })
  @ApiParam({ name: 'id', type: Number, required: true })
  @RequirePermissions(Permissions.Triad.DELETE)
  @UseInterceptors(CurrentUserInterceptor)
  @Delete('/:id')
  async deleteTriad(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: UserEntity) {
    return this.triadsUseCases.deleteById(id, user.id);
  }

  @ApiOperation({ summary: 'Update a triad' })
  @ApiParam({ name: 'id', type: Number, required: true })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        description: { type: 'string' },
        sessionTime: { type: 'date' },
        maxParticipants: { type: 'number' },
        organizerId: { type: 'number' },
      },
    },
  })
  @RequirePermissions(Permissions.Triad.UPDATE)
  @Patch('/:id')
  async updateTriad(
    @Param('id', ParseIntPipe) id: number,
    @Body() updates: UpdateTriadDto,
  ) {
    return this.triadsUseCases.updateTriad(id, updates);
  }
}
