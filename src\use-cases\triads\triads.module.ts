import { DataServicesModule } from '@services/database';
import { Modu<PERSON> } from '@nestjs/common';
import { TriadsUseCases } from './triads.use-cases';
import { TriadFactory } from './triad.factory';
import {
  TriadParticipantsFactory,
  TriadParticipantsModule,
  TriadParticipantsUseCases,
} from '../triad-participants';
import { UserModule } from '../user';
@Module({
  imports: [DataServicesModule, TriadParticipantsModule, UserModule],
  providers: [
    TriadsUseCases,
    TriadFactory,
    TriadParticipantsUseCases,
    TriadParticipantsFactory,
  ],
  exports: [TriadsUseCases],
})
export class TriadsModule {}
