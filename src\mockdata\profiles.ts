import { Profile } from "@/types/user-management/users";

export const ProfilesMockData: Profile[] = [
  {
    name: "Students",
    description: "This is a description for the admin profile",
    role: "student",
    members: 15,
    permissions: ["manage resources"],
    id: "1",
  },
  {
    name: "Mini-admin",
    description: "This is a description for the pm profile",
    role: "ma",
    members: 16,
    permissions: ["create user"],
    id: "2",
  },
  {
    name: "Managerial",
    description: "This is a description for the student profile",
    role: "manager",
    members: 26,
    permissions: ["create user", "payroll", "manage students"],
    id: "3",
  },
  {
    name: "Internal",
    description: "This is a description for the coach profile",
    role: "internal",
    members: 37,
    permissions: ["create user", "enroll students", "manage resources"],
    id: "4",
  },
];
