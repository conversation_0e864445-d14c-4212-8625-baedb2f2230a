import { Injectable } from '@nestjs/common';
import { CheckoutPage, ProductOffer, WarrantySection } from '@entities';
import { CreateOfferWithCheckoutDTO } from '@dtos';
import { ConfigService } from '@config';
import {
  ProductOfferStatus,
  ProductOfferVersion,
} from '@px-shared-account/hermes';
@Injectable()
export class ProductOfferFactoryService {
  private readonly CHECKOUT_URL: string;

  constructor(private readonly configService: ConfigService) {
    this.CHECKOUT_URL = this.configService.appSecrets.CHECKOUT_URL;
  }
  generate(offerWithCheckout: CreateOfferWithCheckoutDTO): ProductOffer {
    const { offerInfo, checkoutInfo } = offerWithCheckout;
    const offer = new ProductOffer();
    offer.name = offerInfo.name;
    offer.externalName = offerInfo.externalName;
    offer.description = offerInfo.description;
    offer.usp = offerInfo.usp;
    offer.currency = offerInfo.currency;
    offer.fiscalEntity = offerInfo.fiscalEntity;
    offer.image = offerInfo.image;
    offer.cardImage = offerInfo.cardImage;
    offer.bannerImage = offerInfo.bannerImage;
    offer.redirectUrl = offerInfo.redirectUrl;
    offer.status = offerInfo.status || ProductOfferStatus.DISABLED;
    offer.chargebeeId = crypto.randomUUID();
    offer.isForever = offerInfo.isForever;
    offer.trialDaysMonthly = offerInfo.trialDaysMonthly || 28;
    offer.trialDaysYearly = offerInfo.trialDaysYearly || 300;
    const checkout = new CheckoutPage();
    checkout.bannerImage = checkoutInfo.bannerImage;
    checkout.mainColor = checkoutInfo.mainColor;
    checkout.url = `${this.CHECKOUT_URL}/${offerInfo.slug}`;
    checkout.template = checkoutInfo.template;
    checkout.cancellationPolicy = checkoutInfo.cancellationPolicy;
    checkout.testimony = checkoutInfo.testimony;
    offer.checkoutPage = checkout;
    const defaultWarrantySection = new WarrantySection();
    defaultWarrantySection.message =
      'Une question? Contacter <u>le service client</u>';
    defaultWarrantySection.icon = 'MessageOutlined';
    checkout.warrantySections = [defaultWarrantySection];
    offer.version = ProductOfferVersion.PREVIEW;
    offer.slug = offerInfo.slug;
    offer.paymentGateway = offerInfo.paymentGateway;
    offer.withProductDelivery = offerInfo?.withProductDelivery;
    offer.lmsIds = offerInfo?.lmsIDs;
    offer.communityIds = offerInfo?.communityIds;
    offer.suspendable = offerInfo?.suspendable;
    offer.delayPeriod = offerInfo?.delayPeriod;
    return offer;
  }

  /**
   * Gets the configured billing cycles for an offer.
   * @param offer The offer to get the billing cycles from
   * @returns A string array containing all the configured billing cycles
   */
  getOfferBillingCycles(offer: ProductOffer): string[] {
    const offerProds = offer?.config?.products;
    const firstProduct = Object.values(offerProds)[0];
    const productKeys = Object.keys(firstProduct);
    const billingCycles = [];
    productKeys.forEach((val) => {
      const productBillingCycle = Object.keys(firstProduct[val]);
      billingCycles.push(productBillingCycle[0]);
    });
    return billingCycles;
  }
}
